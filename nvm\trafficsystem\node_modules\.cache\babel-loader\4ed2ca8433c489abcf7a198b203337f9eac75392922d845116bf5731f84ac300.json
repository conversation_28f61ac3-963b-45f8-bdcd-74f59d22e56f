{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport request from '@/utils/request';\n\n/**\n * 决策支持API\n */\nconst decisionApi = {\n  /**\n   * 生成决策建议\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {Object} data.traffic_data - 交通数据\n   * @param {Object} data.current_conditions - 当前条件\n   * @returns {Promise} API响应\n   */\n  generateSuggestions(data) {\n    return request({\n      url: '/api/decision/suggestions/generate',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 获取实时决策支持\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {Object} data.current_state - 当前状态\n   * @returns {Promise} API响应\n   */\n  getRealtimeSupport(data) {\n    return request({\n      url: '/api/decision/realtime',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 应用决策建议\n   * @param {string} suggestionId - 建议ID\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {string} data.user_id - 用户ID\n   * @returns {Promise} API响应\n   */\n  applySuggestion(suggestionId, data) {\n    return request({\n      url: `/api/decision/suggestions/${suggestionId}/apply`,\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 获取决策历史\n   * @param {Object} params - 查询参数\n   * @param {string} params.simulation_id - 仿真ID（可选）\n   * @param {string} params.user_id - 用户ID（可选）\n   * @param {number} params.page - 页码\n   * @param {number} params.size - 页大小\n   * @returns {Promise} API响应\n   */\n  getDecisionHistory(params) {\n    return request({\n      url: '/api/decision/history',\n      method: 'get',\n      params\n    });\n  },\n  /**\n   * 获取决策建议详情\n   * @param {string} suggestionId - 建议ID\n   * @returns {Promise} API响应\n   */\n  getSuggestionDetail(suggestionId) {\n    return request({\n      url: `/api/decision/suggestions/${suggestionId}`,\n      method: 'get'\n    });\n  },\n  /**\n   * 评估决策效果\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {string} data.decision_id - 决策ID\n   * @param {Object} data.before_data - 决策前数据\n   * @param {Object} data.after_data - 决策后数据\n   * @returns {Promise} API响应\n   */\n  evaluateDecisionEffect(data) {\n    return request({\n      url: '/api/decision/evaluation',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 获取决策统计信息\n   * @param {Object} params - 查询参数\n   * @param {string} params.simulation_id - 仿真ID（可选）\n   * @param {string} params.user_id - 用户ID（可选）\n   * @returns {Promise} API响应\n   */\n  getDecisionStatistics(params) {\n    return request({\n      url: '/api/decision/statistics',\n      method: 'get',\n      params\n    });\n  },\n  /**\n   * 检查决策支持服务状态\n   * @returns {Promise} API响应\n   */\n  checkServiceStatus() {\n    return request({\n      url: '/api/decision/service/status',\n      method: 'get'\n    });\n  },\n  /**\n   * 信号灯控制\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {Object} data.control_request - 控制请求\n   * @returns {Promise} API响应\n   */\n  controlTrafficLights(data) {\n    return request({\n      url: '/api/decision/traffic-lights/control',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 手动信号灯控制\n   * @param {string} simulationId - 仿真ID\n   * @param {Object} data - 控制数据\n   * @param {Object} data.lights - 信号灯状态\n   * @param {string} data.timestamp - 时间戳\n   * @returns {Promise} API响应\n   */\n  manualTrafficLightControl(simulationId, data) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/manual`,\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 恢复自动信号灯控制\n   * @param {string} simulationId - 仿真ID\n   * @returns {Promise} API响应\n   */\n  autoTrafficLightControl(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/auto`,\n      method: 'post'\n    });\n  },\n  /**\n   * 紧急信号灯控制\n   * @param {string} simulationId - 仿真ID\n   * @returns {Promise} API响应\n   */\n  emergencyTrafficLightControl(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/emergency`,\n      method: 'post'\n    });\n  },\n  /**\n   * 获取信号灯状态\n   * @param {string} simulationId - 仿真ID\n   * @returns {Promise} API响应\n   */\n  getTrafficLightStatus(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/status`,\n      method: 'get'\n    });\n  },\n  /**\n   * 获取信号灯历史记录\n   * @param {string} simulationId - 仿真ID\n   * @param {Object} params - 查询参数\n   * @returns {Promise} API响应\n   */\n  getTrafficLightHistory(simulationId, params) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/history`,\n      method: 'get',\n      params\n    });\n  },\n  /**\n   * 优化信号配时\n   * @param {Object} data - 请求数据\n   * @param {Object} data.traffic_data - 交通数据\n   * @param {Object} data.current_timing - 当前配时\n   * @returns {Promise} API响应\n   */\n  optimizeSignalTiming(data) {\n    return request({\n      url: '/api/simulation/optimization/signal',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 流量平衡优化\n   * @param {Object} data - 请求数据\n   * @param {Object} data.traffic_data - 交通数据\n   * @returns {Promise} API响应\n   */\n  optimizeFlowBalance(data) {\n    return request({\n      url: '/api/simulation/optimization/flow',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 综合优化分析\n   * @param {Object} data - 请求数据\n   * @param {Object} data.traffic_data - 交通数据\n   * @returns {Promise} API响应\n   */\n  comprehensiveOptimization(data) {\n    return request({\n      url: '/api/simulation/optimization/comprehensive',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 获取优化建议\n   * @param {string} simulationId - 仿真ID\n   * @param {Object} trafficData - 交通数据\n   * @returns {Promise} API响应\n   */\n  getOptimizationSuggestions(simulationId, trafficData) {\n    return this.generateSuggestions({\n      simulation_id: simulationId,\n      traffic_data: trafficData,\n      current_conditions: {}\n    });\n  },\n  /**\n   * 批量应用决策建议\n   * @param {Array} suggestionIds - 建议ID数组\n   * @param {Object} data - 请求数据\n   * @returns {Promise} API响应\n   */\n  async batchApplySuggestions(suggestionIds, data) {\n    const results = [];\n    for (const suggestionId of suggestionIds) {\n      try {\n        const result = await this.applySuggestion(suggestionId, data);\n        results.push({\n          suggestionId,\n          result,\n          success: true\n        });\n      } catch (error) {\n        results.push({\n          suggestionId,\n          error,\n          success: false\n        });\n      }\n    }\n    return {\n      status: 'success',\n      results,\n      total: suggestionIds.length,\n      successful: results.filter(r => r.success).length,\n      failed: results.filter(r => !r.success).length\n    };\n  }\n};\nexport default decisionApi;", "map": {"version": 3, "names": ["request", "decisionApi", "generateSuggestions", "data", "url", "method", "getRealtimeSupport", "applySuggestion", "suggestionId", "getDecisionHistory", "params", "getSuggestionDetail", "evaluateDecisionEffect", "getDecisionStatistics", "checkServiceStatus", "controlTrafficLights", "manualTrafficLightControl", "simulationId", "autoTrafficLightControl", "emergencyTrafficLightControl", "getTrafficLightStatus", "getTrafficLightHistory", "optimizeSignalTiming", "optimizeFlowBalance", "comprehensiveOptimization", "getOptimizationSuggestions", "trafficData", "simulation_id", "traffic_data", "current_conditions", "batchApplySuggestions", "suggestionIds", "results", "result", "push", "success", "error", "status", "total", "length", "successful", "filter", "r", "failed"], "sources": ["D:/code/nvm/trafficsystem/src/api/decision.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * 决策支持API\n */\nconst decisionApi = {\n  /**\n   * 生成决策建议\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {Object} data.traffic_data - 交通数据\n   * @param {Object} data.current_conditions - 当前条件\n   * @returns {Promise} API响应\n   */\n  generateSuggestions(data) {\n    return request({\n      url: '/api/decision/suggestions/generate',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 获取实时决策支持\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {Object} data.current_state - 当前状态\n   * @returns {Promise} API响应\n   */\n  getRealtimeSupport(data) {\n    return request({\n      url: '/api/decision/realtime',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 应用决策建议\n   * @param {string} suggestionId - 建议ID\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {string} data.user_id - 用户ID\n   * @returns {Promise} API响应\n   */\n  applySuggestion(suggestionId, data) {\n    return request({\n      url: `/api/decision/suggestions/${suggestionId}/apply`,\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 获取决策历史\n   * @param {Object} params - 查询参数\n   * @param {string} params.simulation_id - 仿真ID（可选）\n   * @param {string} params.user_id - 用户ID（可选）\n   * @param {number} params.page - 页码\n   * @param {number} params.size - 页大小\n   * @returns {Promise} API响应\n   */\n  getDecisionHistory(params) {\n    return request({\n      url: '/api/decision/history',\n      method: 'get',\n      params\n    })\n  },\n\n  /**\n   * 获取决策建议详情\n   * @param {string} suggestionId - 建议ID\n   * @returns {Promise} API响应\n   */\n  getSuggestionDetail(suggestionId) {\n    return request({\n      url: `/api/decision/suggestions/${suggestionId}`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 评估决策效果\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {string} data.decision_id - 决策ID\n   * @param {Object} data.before_data - 决策前数据\n   * @param {Object} data.after_data - 决策后数据\n   * @returns {Promise} API响应\n   */\n  evaluateDecisionEffect(data) {\n    return request({\n      url: '/api/decision/evaluation',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 获取决策统计信息\n   * @param {Object} params - 查询参数\n   * @param {string} params.simulation_id - 仿真ID（可选）\n   * @param {string} params.user_id - 用户ID（可选）\n   * @returns {Promise} API响应\n   */\n  getDecisionStatistics(params) {\n    return request({\n      url: '/api/decision/statistics',\n      method: 'get',\n      params\n    })\n  },\n\n  /**\n   * 检查决策支持服务状态\n   * @returns {Promise} API响应\n   */\n  checkServiceStatus() {\n    return request({\n      url: '/api/decision/service/status',\n      method: 'get'\n    })\n  },\n\n  /**\n   * 信号灯控制\n   * @param {Object} data - 请求数据\n   * @param {string} data.simulation_id - 仿真ID\n   * @param {Object} data.control_request - 控制请求\n   * @returns {Promise} API响应\n   */\n  controlTrafficLights(data) {\n    return request({\n      url: '/api/decision/traffic-lights/control',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 手动信号灯控制\n   * @param {string} simulationId - 仿真ID\n   * @param {Object} data - 控制数据\n   * @param {Object} data.lights - 信号灯状态\n   * @param {string} data.timestamp - 时间戳\n   * @returns {Promise} API响应\n   */\n  manualTrafficLightControl(simulationId, data) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/manual`,\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 恢复自动信号灯控制\n   * @param {string} simulationId - 仿真ID\n   * @returns {Promise} API响应\n   */\n  autoTrafficLightControl(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/auto`,\n      method: 'post'\n    })\n  },\n\n  /**\n   * 紧急信号灯控制\n   * @param {string} simulationId - 仿真ID\n   * @returns {Promise} API响应\n   */\n  emergencyTrafficLightControl(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/emergency`,\n      method: 'post'\n    })\n  },\n\n  /**\n   * 获取信号灯状态\n   * @param {string} simulationId - 仿真ID\n   * @returns {Promise} API响应\n   */\n  getTrafficLightStatus(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/status`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 获取信号灯历史记录\n   * @param {string} simulationId - 仿真ID\n   * @param {Object} params - 查询参数\n   * @returns {Promise} API响应\n   */\n  getTrafficLightHistory(simulationId, params) {\n    return request({\n      url: `/api/simulation/${simulationId}/traffic-lights/history`,\n      method: 'get',\n      params\n    })\n  },\n\n  /**\n   * 优化信号配时\n   * @param {Object} data - 请求数据\n   * @param {Object} data.traffic_data - 交通数据\n   * @param {Object} data.current_timing - 当前配时\n   * @returns {Promise} API响应\n   */\n  optimizeSignalTiming(data) {\n    return request({\n      url: '/api/simulation/optimization/signal',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 流量平衡优化\n   * @param {Object} data - 请求数据\n   * @param {Object} data.traffic_data - 交通数据\n   * @returns {Promise} API响应\n   */\n  optimizeFlowBalance(data) {\n    return request({\n      url: '/api/simulation/optimization/flow',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 综合优化分析\n   * @param {Object} data - 请求数据\n   * @param {Object} data.traffic_data - 交通数据\n   * @returns {Promise} API响应\n   */\n  comprehensiveOptimization(data) {\n    return request({\n      url: '/api/simulation/optimization/comprehensive',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 获取优化建议\n   * @param {string} simulationId - 仿真ID\n   * @param {Object} trafficData - 交通数据\n   * @returns {Promise} API响应\n   */\n  getOptimizationSuggestions(simulationId, trafficData) {\n    return this.generateSuggestions({\n      simulation_id: simulationId,\n      traffic_data: trafficData,\n      current_conditions: {}\n    })\n  },\n\n  /**\n   * 批量应用决策建议\n   * @param {Array} suggestionIds - 建议ID数组\n   * @param {Object} data - 请求数据\n   * @returns {Promise} API响应\n   */\n  async batchApplySuggestions(suggestionIds, data) {\n    const results = []\n    \n    for (const suggestionId of suggestionIds) {\n      try {\n        const result = await this.applySuggestion(suggestionId, data)\n        results.push({ suggestionId, result, success: true })\n      } catch (error) {\n        results.push({ suggestionId, error, success: false })\n      }\n    }\n    \n    return {\n      status: 'success',\n      results,\n      total: suggestionIds.length,\n      successful: results.filter(r => r.success).length,\n      failed: results.filter(r => !r.success).length\n    }\n  }\n}\n\nexport default decisionApi\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA,MAAMC,WAAW,GAAG;EAClB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,mBAAmBA,CAACC,IAAI,EAAE;IACxB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,oCAAoC;MACzCC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEG,kBAAkBA,CAACH,IAAI,EAAE;IACvB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,wBAAwB;MAC7BC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEI,eAAeA,CAACC,YAAY,EAAEL,IAAI,EAAE;IAClC,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,6BAA6BI,YAAY,QAAQ;MACtDH,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEM,kBAAkBA,CAACC,MAAM,EAAE;IACzB,OAAOV,OAAO,CAAC;MACbI,GAAG,EAAE,uBAAuB;MAC5BC,MAAM,EAAE,KAAK;MACbK;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,mBAAmBA,CAACH,YAAY,EAAE;IAChC,OAAOR,OAAO,CAAC;MACbI,GAAG,EAAE,6BAA6BI,YAAY,EAAE;MAChDH,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEO,sBAAsBA,CAACT,IAAI,EAAE;IAC3B,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,0BAA0B;MAC/BC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEU,qBAAqBA,CAACH,MAAM,EAAE;IAC5B,OAAOV,OAAO,CAAC;MACbI,GAAG,EAAE,0BAA0B;MAC/BC,MAAM,EAAE,KAAK;MACbK;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEI,kBAAkBA,CAAA,EAAG;IACnB,OAAOd,OAAO,CAAC;MACbI,GAAG,EAAE,8BAA8B;MACnCC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEU,oBAAoBA,CAACZ,IAAI,EAAE;IACzB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,sCAAsC;MAC3CC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEa,yBAAyBA,CAACC,YAAY,EAAEd,IAAI,EAAE;IAC5C,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBa,YAAY,wBAAwB;MAC5DZ,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEe,uBAAuBA,CAACD,YAAY,EAAE;IACpC,OAAOjB,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBa,YAAY,sBAAsB;MAC1DZ,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEc,4BAA4BA,CAACF,YAAY,EAAE;IACzC,OAAOjB,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBa,YAAY,2BAA2B;MAC/DZ,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEe,qBAAqBA,CAACH,YAAY,EAAE;IAClC,OAAOjB,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBa,YAAY,wBAAwB;MAC5DZ,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEgB,sBAAsBA,CAACJ,YAAY,EAAEP,MAAM,EAAE;IAC3C,OAAOV,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBa,YAAY,yBAAyB;MAC7DZ,MAAM,EAAE,KAAK;MACbK;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEY,oBAAoBA,CAACnB,IAAI,EAAE;IACzB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,qCAAqC;MAC1CC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEoB,mBAAmBA,CAACpB,IAAI,EAAE;IACxB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,mCAAmC;MACxCC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEqB,yBAAyBA,CAACrB,IAAI,EAAE;IAC9B,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,4CAA4C;MACjDC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEsB,0BAA0BA,CAACR,YAAY,EAAES,WAAW,EAAE;IACpD,OAAO,IAAI,CAACxB,mBAAmB,CAAC;MAC9ByB,aAAa,EAAEV,YAAY;MAC3BW,YAAY,EAAEF,WAAW;MACzBG,kBAAkB,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,qBAAqBA,CAACC,aAAa,EAAE5B,IAAI,EAAE;IAC/C,MAAM6B,OAAO,GAAG,EAAE;IAElB,KAAK,MAAMxB,YAAY,IAAIuB,aAAa,EAAE;MACxC,IAAI;QACF,MAAME,MAAM,GAAG,MAAM,IAAI,CAAC1B,eAAe,CAACC,YAAY,EAAEL,IAAI,CAAC;QAC7D6B,OAAO,CAACE,IAAI,CAAC;UAAE1B,YAAY;UAAEyB,MAAM;UAAEE,OAAO,EAAE;QAAK,CAAC,CAAC;MACvD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdJ,OAAO,CAACE,IAAI,CAAC;UAAE1B,YAAY;UAAE4B,KAAK;UAAED,OAAO,EAAE;QAAM,CAAC,CAAC;MACvD;IACF;IAEA,OAAO;MACLE,MAAM,EAAE,SAAS;MACjBL,OAAO;MACPM,KAAK,EAAEP,aAAa,CAACQ,MAAM;MAC3BC,UAAU,EAAER,OAAO,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACP,OAAO,CAAC,CAACI,MAAM;MACjDI,MAAM,EAAEX,OAAO,CAACS,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACP,OAAO,CAAC,CAACI;IAC1C,CAAC;EACH;AACF,CAAC;AAED,eAAetC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}