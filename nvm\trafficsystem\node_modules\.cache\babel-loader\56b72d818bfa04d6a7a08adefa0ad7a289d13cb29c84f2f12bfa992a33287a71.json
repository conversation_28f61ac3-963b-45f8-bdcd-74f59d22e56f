{"ast": null, "code": "import { computed } from 'vue';\nimport { ElTag, ElText } from 'element-plus';\nexport default {\n  name: 'TrafficLightDisplay',\n  components: {\n    ElTag,\n    ElText\n  },\n  props: {\n    state: {\n      type: Object,\n      required: true,\n      default: () => ({\n        red: false,\n        yellow: false,\n        green: false\n      })\n    },\n    isManual: {\n      type: Boolean,\n      default: false\n    },\n    direction: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['manual-change'],\n  setup(props, {\n    emit\n  }) {\n    const getStatusType = () => {\n      if (props.state.red) return 'danger';\n      if (props.state.yellow) return 'warning';\n      if (props.state.green) return 'success';\n      return 'info';\n    };\n    const getStatusText = () => {\n      if (props.state.red) return '停止';\n      if (props.state.yellow) return '警告';\n      if (props.state.green) return '通行';\n      return '未知';\n    };\n    const onLightClick = color => {\n      if (!props.isManual) return;\n\n      // 创建新的状态对象\n      const newState = {\n        red: false,\n        yellow: false,\n        green: false\n      };\n\n      // 设置选中的颜色为true\n      newState[color] = true;\n\n      // 发射变更事件\n      emit('manual-change', props.direction, newState);\n    };\n    return {\n      getStatusType,\n      getStatusText,\n      onLightClick\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "ElTag", "ElText", "name", "components", "props", "state", "type", "Object", "required", "default", "red", "yellow", "green", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "direction", "String", "emits", "setup", "emit", "getStatusType", "getStatusText", "onLightClick", "color", "newState"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\TrafficLightDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"traffic-light-display\">\n    <div class=\"traffic-light\">\n      <!-- 红灯 -->\n      <div \n        class=\"light red\"\n        :class=\"{ active: state.red, clickable: isManual }\"\n        @click=\"onLightClick('red')\"\n      >\n        <div class=\"light-inner\"></div>\n      </div>\n      \n      <!-- 黄灯 -->\n      <div \n        class=\"light yellow\"\n        :class=\"{ active: state.yellow, clickable: isManual }\"\n        @click=\"onLightClick('yellow')\"\n      >\n        <div class=\"light-inner\"></div>\n      </div>\n      \n      <!-- 绿灯 -->\n      <div \n        class=\"light green\"\n        :class=\"{ active: state.green, clickable: isManual }\"\n        @click=\"onLightClick('green')\"\n      >\n        <div class=\"light-inner\"></div>\n      </div>\n    </div>\n    \n    <!-- 状态指示 -->\n    <div class=\"status-indicator\">\n      <el-tag \n        :type=\"getStatusType()\" \n        size=\"small\"\n        effect=\"plain\"\n      >\n        {{ getStatusText() }}\n      </el-tag>\n    </div>\n    \n    <!-- 手动控制提示 -->\n    <div v-if=\"isManual\" class=\"manual-hint\">\n      <el-text size=\"small\" type=\"info\">\n        点击切换状态\n      </el-text>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { ElTag, ElText } from 'element-plus'\n\nexport default {\n  name: 'TrafficLightDisplay',\n  components: {\n    ElTag,\n    ElText\n  },\n  props: {\n    state: {\n      type: Object,\n      required: true,\n      default: () => ({\n        red: false,\n        yellow: false,\n        green: false\n      })\n    },\n    isManual: {\n      type: Boolean,\n      default: false\n    },\n    direction: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['manual-change'],\n  setup(props, { emit }) {\n    \n    const getStatusType = () => {\n      if (props.state.red) return 'danger'\n      if (props.state.yellow) return 'warning'\n      if (props.state.green) return 'success'\n      return 'info'\n    }\n    \n    const getStatusText = () => {\n      if (props.state.red) return '停止'\n      if (props.state.yellow) return '警告'\n      if (props.state.green) return '通行'\n      return '未知'\n    }\n    \n    const onLightClick = (color) => {\n      if (!props.isManual) return\n      \n      // 创建新的状态对象\n      const newState = {\n        red: false,\n        yellow: false,\n        green: false\n      }\n      \n      // 设置选中的颜色为true\n      newState[color] = true\n      \n      // 发射变更事件\n      emit('manual-change', props.direction, newState)\n    }\n    \n    return {\n      getStatusType,\n      getStatusText,\n      onLightClick\n    }\n  }\n}\n</script>\n\n<style scoped>\n.traffic-light-display {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.traffic-light {\n  display: flex;\n  flex-direction: column;\n  background: #2c3e50;\n  border-radius: 20px;\n  padding: 8px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  gap: 6px;\n}\n\n.light {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  transition: all 0.3s ease;\n  border: 2px solid #34495e;\n}\n\n.light-inner {\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.light.red .light-inner {\n  background: #2c3e50;\n}\n\n.light.red.active .light-inner {\n  background: #e74c3c;\n  box-shadow: 0 0 20px #e74c3c, inset 0 0 10px #c0392b;\n}\n\n.light.yellow .light-inner {\n  background: #2c3e50;\n}\n\n.light.yellow.active .light-inner {\n  background: #f39c12;\n  box-shadow: 0 0 20px #f39c12, inset 0 0 10px #e67e22;\n}\n\n.light.green .light-inner {\n  background: #2c3e50;\n}\n\n.light.green.active .light-inner {\n  background: #27ae60;\n  box-shadow: 0 0 20px #27ae60, inset 0 0 10px #229954;\n}\n\n.light.clickable {\n  cursor: pointer;\n}\n\n.light.clickable:hover {\n  transform: scale(1.05);\n  border-color: #3498db;\n}\n\n.status-indicator {\n  margin-top: 4px;\n}\n\n.manual-hint {\n  margin-top: 4px;\n  text-align: center;\n}\n\n/* 动画效果 */\n.light.active {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.02);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .light {\n    width: 32px;\n    height: 32px;\n  }\n  \n  .light-inner {\n    width: 24px;\n    height: 24px;\n  }\n  \n  .traffic-light {\n    padding: 6px;\n    gap: 4px;\n  }\n}\n</style>\n"], "mappings": "AAoDA,SAASA,QAAO,QAAS,KAAI;AAC7B,SAASC,KAAK,EAAEC,MAAK,QAAS,cAAa;AAE3C,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE;IACVH,KAAK;IACLC;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAEA,CAAA,MAAO;QACdC,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE;MACT,CAAC;IACH,CAAC;IACDC,QAAQ,EAAE;MACRP,IAAI,EAAEQ,OAAO;MACbL,OAAO,EAAE;IACX,CAAC;IACDM,SAAS,EAAE;MACTT,IAAI,EAAEU,MAAM;MACZR,QAAQ,EAAE;IACZ;EACF,CAAC;EACDS,KAAK,EAAE,CAAC,eAAe,CAAC;EACxBC,KAAKA,CAACd,KAAK,EAAE;IAAEe;EAAK,CAAC,EAAE;IAErB,MAAMC,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAIhB,KAAK,CAACC,KAAK,CAACK,GAAG,EAAE,OAAO,QAAO;MACnC,IAAIN,KAAK,CAACC,KAAK,CAACM,MAAM,EAAE,OAAO,SAAQ;MACvC,IAAIP,KAAK,CAACC,KAAK,CAACO,KAAK,EAAE,OAAO,SAAQ;MACtC,OAAO,MAAK;IACd;IAEA,MAAMS,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAIjB,KAAK,CAACC,KAAK,CAACK,GAAG,EAAE,OAAO,IAAG;MAC/B,IAAIN,KAAK,CAACC,KAAK,CAACM,MAAM,EAAE,OAAO,IAAG;MAClC,IAAIP,KAAK,CAACC,KAAK,CAACO,KAAK,EAAE,OAAO,IAAG;MACjC,OAAO,IAAG;IACZ;IAEA,MAAMU,YAAW,GAAKC,KAAK,IAAK;MAC9B,IAAI,CAACnB,KAAK,CAACS,QAAQ,EAAE;;MAErB;MACA,MAAMW,QAAO,GAAI;QACfd,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE;MACT;;MAEA;MACAY,QAAQ,CAACD,KAAK,IAAI,IAAG;;MAErB;MACAJ,IAAI,CAAC,eAAe,EAAEf,KAAK,CAACW,SAAS,EAAES,QAAQ;IACjD;IAEA,OAAO;MACLJ,aAAa;MACbC,aAAa;MACbC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}