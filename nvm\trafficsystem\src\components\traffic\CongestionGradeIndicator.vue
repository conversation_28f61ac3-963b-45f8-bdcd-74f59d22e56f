<template>
  <div class="congestion-grade-indicator">
    <div class="grade-display">
      <div 
        class="grade-badge"
        :style="{ 
          backgroundColor: gradeConfig.color,
          boxShadow: `0 0 20px ${gradeConfig.color}40`
        }"
      >
        {{ grade }}
      </div>
      <div class="grade-info">
        <div class="grade-label">{{ gradeConfig.label }}</div>
        <div class="grade-description">{{ gradeConfig.description }}</div>
      </div>
    </div>
    
    <div class="progress-container">
      <div class="progress-bar">
        <div 
          class="progress-fill"
          :style="{ 
            width: `${percentage}%`,
            backgroundColor: gradeConfig.color
          }"
        ></div>
      </div>
      <div class="progress-text">拥挤度: {{ percentage.toFixed(1) }}%</div>
    </div>
    
    <div class="vehicle-count">
      <span class="count-number">{{ vehicleCount }}</span>
      <span class="count-label">辆车</span>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { getCongestionGradeConfig, calculateCongestionPercentage } from '@/utils/trafficAnalysisUtils'

export default {
  name: 'CongestionGradeIndicator',
  props: {
    grade: {
      type: String,
      required: true,
      validator: (value) => ['A', 'B', 'C', 'D', 'E', 'F'].includes(value)
    },
    vehicleCount: {
      type: Number,
      required: true,
      default: 0
    }
  },
  setup(props) {
    const gradeConfig = computed(() => getCongestionGradeConfig(props.grade))
    const percentage = computed(() => calculateCongestionPercentage(props.grade, props.vehicleCount))
    
    return {
      gradeConfig,
      percentage
    }
  }
}
</script>

<style scoped>
.congestion-grade-indicator {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.grade-display {
  display: flex;
  align-items: center;
  gap: 16px;
}

.grade-badge {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.grade-info {
  flex: 1;
}

.grade-label {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.grade-description {
  font-size: 14px;
  color: #666;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease, background-color 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.vehicle-count {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.count-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.count-label {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .congestion-grade-indicator {
    padding: 16px;
  }
  
  .grade-badge {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .grade-label {
    font-size: 16px;
  }
  
  .count-number {
    font-size: 24px;
  }
}
</style>
