<template>
  <div class="traffic-light-controller">
    <el-card class="controller-card">
      <template #header>
        <div class="card-header">
          <span class="title">
            <el-icon><Traffic /></el-icon>
            信号灯控制器
          </span>
          <div class="control-mode">
            <el-switch
              v-model="isManualMode"
              :disabled="!isConnected"
              active-text="手动"
              inactive-text="自动"
              @change="onModeChange"
            />
          </div>
        </div>
      </template>

      <!-- 连接状态 -->
      <div class="connection-status">
        <el-tag :type="isConnected ? 'success' : 'danger'" size="small">
          <el-icon><Connection /></el-icon>
          {{ isConnected ? '已连接' : '未连接' }}
        </el-tag>
        <span class="last-update" v-if="lastUpdateTime">
          最后更新: {{ formatTime(lastUpdateTime) }}
        </span>
      </div>

      <!-- 十字路口信号灯状态 -->
      <div class="intersection-layout">
        <!-- 北方向 -->
        <div class="direction north">
          <div class="direction-label">北</div>
          <traffic-light-display
            :state="trafficLights.north"
            :is-manual="isManualMode"
            direction="north"
            @manual-change="onManualLightChange"
          />
        </div>

        <!-- 西方向 -->
        <div class="direction west">
          <div class="direction-label">西</div>
          <traffic-light-display
            :state="trafficLights.west"
            :is-manual="isManualMode"
            direction="west"
            @manual-change="onManualLightChange"
          />
        </div>

        <!-- 中央路口 -->
        <div class="intersection-center">
          <el-icon class="intersection-icon"><Grid /></el-icon>
        </div>

        <!-- 东方向 -->
        <div class="direction east">
          <div class="direction-label">东</div>
          <traffic-light-display
            :state="trafficLights.east"
            :is-manual="isManualMode"
            direction="east"
            @manual-change="onManualLightChange"
          />
        </div>

        <!-- 南方向 -->
        <div class="direction south">
          <div class="direction-label">南</div>
          <traffic-light-display
            :state="trafficLights.south"
            :is-manual="isManualMode"
            direction="south"
            @manual-change="onManualLightChange"
          />
        </div>
      </div>

      <!-- 当前相位信息 -->
      <div class="phase-info">
        <el-descriptions title="当前相位信息" :column="2" size="small" border>
          <el-descriptions-item label="相位ID">
            {{ currentPhase.phaseId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="剩余时间">
            {{ currentPhase.remainingTime || 0 }}秒
          </el-descriptions-item>
          <el-descriptions-item label="相位描述">
            {{ currentPhase.description || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="下一相位">
            {{ currentPhase.nextPhase || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 控制按钮 -->
      <div class="control-buttons" v-if="isManualMode">
        <el-button-group>
          <el-button 
            type="success" 
            :disabled="!isConnected"
            @click="applyManualControl"
          >
            <el-icon><Check /></el-icon>
            应用设置
          </el-button>
          <el-button 
            type="warning" 
            :disabled="!isConnected"
            @click="resetToAuto"
          >
            <el-icon><Refresh /></el-icon>
            恢复自动
          </el-button>
          <el-button 
            type="danger" 
            :disabled="!isConnected"
            @click="emergencyStop"
          >
            <el-icon><Warning /></el-icon>
            紧急停止
          </el-button>
        </el-button-group>
      </div>

      <!-- 操作日志 -->
      <div class="operation-log">
        <el-collapse v-model="logCollapsed">
          <el-collapse-item title="操作日志" name="log">
            <div class="log-container">
              <div 
                v-for="(log, index) in operationLogs" 
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Traffic, 
  Connection, 
  Grid, 
  Check, 
  Refresh, 
  Warning 
} from '@element-plus/icons-vue'
import stompService from '@/utils/stomp-service'
import TrafficLightDisplay from './TrafficLightDisplay.vue'

export default {
  name: 'TrafficLightController',
  components: {
    TrafficLightDisplay,
    Traffic,
    Connection,
    Grid,
    Check,
    Refresh,
    Warning
  },
  props: {
    simulationId: {
      type: String,
      required: true
    }
  },
  emits: ['status-change', 'control-change'],
  setup(props, { emit }) {
    // 响应式数据
    const isConnected = ref(false)
    const isManualMode = ref(false)
    const lastUpdateTime = ref(null)
    const logCollapsed = ref(['log'])
    
    // 信号灯状态
    const trafficLights = reactive({
      north: { red: true, yellow: false, green: false },
      south: { red: true, yellow: false, green: false },
      east: { red: false, yellow: false, green: true },
      west: { red: false, yellow: false, green: true }
    })
    
    // 当前相位信息
    const currentPhase = reactive({
      phaseId: '',
      remainingTime: 0,
      description: '',
      nextPhase: ''
    })
    
    // 操作日志
    const operationLogs = ref([])
    
    // WebSocket订阅
    let trafficLightSubscription = null
    
    // 计算属性
    const formatTime = computed(() => {
      return (timestamp) => {
        if (!timestamp) return ''
        return new Date(timestamp).toLocaleTimeString()
      }
    })
    
    // 方法
    const addLog = (message, type = 'info') => {
      operationLogs.value.unshift({
        timestamp: new Date(),
        message,
        type
      })
      
      // 保持日志数量在合理范围内
      if (operationLogs.value.length > 50) {
        operationLogs.value = operationLogs.value.slice(0, 50)
      }
    }
    
    const onModeChange = async (manual) => {
      try {
        addLog(`切换到${manual ? '手动' : '自动'}模式`, 'info')
        
        if (!manual) {
          // 切换到自动模式时重置所有手动设置
          await resetToAuto()
        }
        
        emit('control-change', { mode: manual ? 'manual' : 'auto' })
        
      } catch (error) {
        console.error('模式切换失败:', error)
        ElMessage.error('模式切换失败')
        addLog(`模式切换失败: ${error.message}`, 'error')
      }
    }
    
    const onManualLightChange = (direction, state) => {
      if (!isManualMode.value) return
      
      trafficLights[direction] = { ...state }
      addLog(`手动设置${direction}方向信号灯`, 'warning')
    }
    
    const applyManualControl = async () => {
      try {
        // 发送手动控制指令到后端
        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/manual`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            lights: trafficLights,
            timestamp: new Date().toISOString()
          })
        })
        
        if (response.ok) {
          ElMessage.success('手动控制设置已应用')
          addLog('手动控制设置已应用', 'success')
        } else {
          throw new Error('应用手动控制失败')
        }
        
      } catch (error) {
        console.error('应用手动控制失败:', error)
        ElMessage.error('应用手动控制失败')
        addLog(`应用手动控制失败: ${error.message}`, 'error')
      }
    }
    
    const resetToAuto = async () => {
      try {
        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/auto`, {
          method: 'POST'
        })
        
        if (response.ok) {
          isManualMode.value = false
          ElMessage.success('已恢复自动控制')
          addLog('已恢复自动控制', 'success')
        } else {
          throw new Error('恢复自动控制失败')
        }
        
      } catch (error) {
        console.error('恢复自动控制失败:', error)
        ElMessage.error('恢复自动控制失败')
        addLog(`恢复自动控制失败: ${error.message}`, 'error')
      }
    }
    
    const emergencyStop = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要执行紧急停止吗？这将使所有方向显示红灯。',
          '紧急停止确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/emergency`, {
          method: 'POST'
        })

        if (response.ok) {
          // 设置所有方向为红灯
          Object.keys(trafficLights).forEach(direction => {
            trafficLights[direction] = { red: true, yellow: false, green: false }
          })

          ElMessage.warning('紧急停止已执行，所有方向红灯')
          addLog('执行紧急停止，所有方向红灯', 'error')
        } else {
          throw new Error('紧急停止执行失败')
        }

      } catch (error) {
        if (error !== 'cancel') {
          console.error('紧急停止失败:', error)
          ElMessage.error('紧急停止失败')
          addLog(`紧急停止失败: ${error.message}`, 'error')
        }
      }
    }

    const setupWebSocketConnection = async () => {
      try {
        await stompService.connect()
        isConnected.value = true
        addLog('WebSocket连接已建立', 'success')

        // 订阅信号灯状态更新
        trafficLightSubscription = await stompService.subscribe(
          `/topic/simulation/${props.simulationId}/traffic-lights`,
          (message) => {
            try {
              const data = JSON.parse(message.body)
              handleTrafficLightUpdate(data)
            } catch (error) {
              console.error('解析信号灯数据失败:', error)
            }
          }
        )

        emit('status-change', { connected: true })

      } catch (error) {
        console.error('WebSocket连接失败:', error)
        isConnected.value = false
        addLog(`WebSocket连接失败: ${error.message}`, 'error')
        emit('status-change', { connected: false })
      }
    }

    const handleTrafficLightUpdate = (data) => {
      if (!data) return

      // 更新信号灯状态
      if (data.lights) {
        Object.keys(data.lights).forEach(direction => {
          if (trafficLights[direction]) {
            trafficLights[direction] = { ...data.lights[direction] }
          }
        })
      }

      // 更新相位信息
      if (data.phase) {
        Object.assign(currentPhase, data.phase)
      }

      lastUpdateTime.value = new Date()

      // 如果是自动模式下的更新，记录日志
      if (!isManualMode.value && data.source === 'auto') {
        addLog(`自动更新: ${data.phase?.description || '信号灯状态更新'}`, 'info')
      }
    }

    const cleanup = () => {
      if (trafficLightSubscription) {
        stompService.unsubscribe(trafficLightSubscription)
        trafficLightSubscription = null
      }

      if (stompService.isConnected()) {
        stompService.disconnect()
      }

      isConnected.value = false
    }

    // 生命周期钩子
    onMounted(() => {
      setupWebSocketConnection()
      addLog('信号灯控制器已初始化', 'info')
    })

    onUnmounted(() => {
      cleanup()
    })

    return {
      isConnected,
      isManualMode,
      lastUpdateTime,
      logCollapsed,
      trafficLights,
      currentPhase,
      operationLogs,
      formatTime,
      onModeChange,
      onManualLightChange,
      applyManualControl,
      resetToAuto,
      emergencyStop
    }
  }
}
</script>

<style scoped>
.traffic-light-controller {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.controller-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.control-mode {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.last-update {
  font-size: 12px;
  color: #6c757d;
}

.intersection-layout {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 120px 1fr;
  grid-template-rows: 1fr 120px 1fr;
  gap: 20px;
  margin: 30px 0;
  min-height: 300px;
}

.direction {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.direction.north {
  grid-column: 2;
  grid-row: 1;
}

.direction.south {
  grid-column: 2;
  grid-row: 3;
}

.direction.west {
  grid-column: 1;
  grid-row: 2;
}

.direction.east {
  grid-column: 3;
  grid-row: 2;
}

.direction-label {
  font-weight: 600;
  font-size: 14px;
  color: #495057;
  background: #e9ecef;
  padding: 4px 12px;
  border-radius: 20px;
}

.intersection-center {
  grid-column: 2;
  grid-row: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
}

.intersection-icon {
  font-size: 48px;
  color: #6c757d;
}

.phase-info {
  margin: 20px 0;
}

.control-buttons {
  margin: 20px 0;
  text-align: center;
}

.operation-log {
  margin-top: 20px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 4px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.success .log-message {
  color: #28a745;
}

.log-item.warning .log-message {
  color: #ffc107;
}

.log-item.error .log-message {
  color: #dc3545;
}

.log-item.info .log-message {
  color: #17a2b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .intersection-layout {
    grid-template-columns: 80px 100px 80px;
    grid-template-rows: 80px 100px 80px;
    gap: 10px;
    min-height: 260px;
  }

  .intersection-icon {
    font-size: 32px;
  }

  .direction-label {
    font-size: 12px;
    padding: 2px 8px;
  }
}
</style>
