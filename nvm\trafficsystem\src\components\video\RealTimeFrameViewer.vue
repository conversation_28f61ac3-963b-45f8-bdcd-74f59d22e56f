<template>
  <div class="realtime-frame-viewer">
    <div class="viewer-header">
      <h4>实时视频预览</h4>
      <div class="viewer-controls">
        <el-button 
          size="small" 
          :type="isPlaying ? 'danger' : 'primary'"
          @click="togglePlayback"
          :disabled="!hasFrames"
        >
          <el-icon><video-play v-if="!isPlaying" /><video-pause v-else /></el-icon>
          {{ isPlaying ? '暂停' : '播放' }}
        </el-button>
        
        <el-button
          size="small"
          type="info"
          @click="clearFrames"
          :disabled="!hasFrames"
        >
          <el-icon><delete /></el-icon>
          清空
        </el-button>

        <el-button
          size="small"
          type="warning"
          @click="clearLatestDetection"
          :disabled="!latestDetectionFrame"
          title="清除定格显示的检测结果"
        >
          <el-icon><delete /></el-icon>
          清除检测
        </el-button>
        
        <el-switch
          v-model="autoPlay"
          active-text="自动播放"
          inactive-text="手动控制"
          size="small"
        />
      </div>
    </div>

    <div class="viewer-content">
      <!-- 主显示区域 -->
      <div class="frame-display" :class="{ 'no-frames': !hasFrames }">
        <div v-if="!hasFrames" class="no-frames-message">
          <el-icon class="waiting-icon"><loading /></el-icon>
          <p>等待实时帧数据...</p>
        </div>
        
        <!-- 最新检测帧显示（定格显示） -->
        <div v-if="latestDetectionFrame" class="detection-frame-container">
          <img
            :src="getFrameImageUrl(latestDetectionFrame)"
            :alt="`检测帧 ${latestDetectionFrame.frameNumber}`"
            class="detection-frame-image"
            @load="handleImageLoad"
            @error="handleImageError"
          />

          <!-- 检测结果覆盖层 -->
          <div class="detection-overlay">
            <div class="detection-header">
              <el-tag :type="getDetectionTagType(latestDetectionFrame.detectionCount)" size="small">
                🚗 当前帧: {{ latestDetectionFrame.detectionCount }} 辆车
              </el-tag>
              <span class="detection-time">{{ formatTimestamp(latestDetectionFrame.timestamp) }}</span>
            </div>

            <div class="detection-info">
              <span class="frame-number">帧: {{ latestDetectionFrame.frameNumber }}/{{ totalFrames || 0 }}</span>
              <span class="detection-status">实时检测结果</span>
            </div>
          </div>
        </div>

        <!-- 常规帧显示 -->
        <div v-else class="frame-container">
          <img
            v-if="currentFrame"
            :src="getFrameImageUrl(currentFrame)"
            :alt="`帧 ${currentFrame.frameNumber}`"
            class="frame-image"
            @load="handleImageLoad"
            @error="handleImageError"
          />

          <!-- 帧信息覆盖层 -->
          <div class="frame-overlay">
            <div class="frame-info">
              <span class="frame-number">帧: {{ currentFrame?.frameNumber || 0 }}/{{ totalFrames || 0 }}</span>
              <span class="detection-count">当前: {{ currentFrame?.detectionCount || 0 }}辆</span>
              <span class="timestamp">{{ formatTimestamp(currentFrame?.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 进度条和控制 -->
      <div class="playback-controls" v-if="hasFrames">
        <el-slider
          v-model="currentFrameIndex"
          :min="0"
          :max="frames.length - 1"
          :step="1"
          :show-tooltip="false"
          @change="onFrameIndexChange"
          class="frame-slider"
        />
        
        <div class="playback-info">
          <span>播放速度:</span>
          <el-select v-model="playbackSpeed" size="small" style="width: 80px;">
            <el-option label="0.5x" :value="0.5" />
            <el-option label="1x" :value="1" />
            <el-option label="2x" :value="2" />
            <el-option label="4x" :value="4" />
          </el-select>
          
          <span class="buffer-info">缓冲: {{ frames.length }} 帧</span>
          <span class="network-info" :class="networkQualityClass">
            <el-icon><connection /></el-icon>
            {{ networkQualityText }}
          </span>
        </div>
      </div>
    </div>

    <!-- 缩略图条 -->
    <div class="thumbnail-strip" v-if="hasFrames && frames.length > 1">
      <div class="thumbnail-container">
        <div
          v-for="(frame, index) in visibleThumbnails"
          :key="frame.frameNumber"
          class="thumbnail-item"
          :class="{ 'active': index === currentFrameIndex }"
          @click="selectFrame(index)"
        >
          <img
            :src="getFrameImageUrl(frame)"
            :alt="`缩略图 ${frame.frameNumber}`"
            class="thumbnail-image"
          />
          <div class="thumbnail-label">{{ frame.frameNumber }}</div>
        </div>
      </div>
    </div>

    <!-- 智能交通状态面板 -->
    <div class="traffic-panel-container" v-if="showTrafficPanel && latestDetectionFrame">
      <IntelligentTrafficPanel
        :current-vehicle-count="latestDetectionFrame.detectionCount || 0"
        :auto-update="true"
        @strategy-applied="handleStrategyApplied"
        @data-updated="handleTrafficDataUpdated"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  VideoPlay,
  VideoPause,
  Delete,
  Loading,
  Connection
} from '@element-plus/icons-vue'
import IntelligentTrafficPanel from '@/components/traffic/IntelligentTrafficPanel.vue'

export default {
  name: 'RealTimeFrameViewer',
  components: {
    VideoPlay,
    VideoPause,
    Delete,
    Loading,
    Connection,
    IntelligentTrafficPanel
  },
  props: {
    // 任务ID，用于接收对应的帧数据
    taskId: {
      type: String,
      required: true
    },
    // 是否自动开始播放
    autoStart: {
      type: Boolean,
      default: true
    },
    // 最大缓冲帧数
    maxBufferFrames: {
      type: Number,
      default: 30
    },
    // 是否显示智能交通状态面板
    showTrafficPanel: {
      type: Boolean,
      default: true
    }
  },
  emits: ['frame-received', 'playback-state-change', 'strategy-applied', 'traffic-data-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const frames = ref([])
    const currentFrameIndex = ref(0)
    const isPlaying = ref(false)
    const autoPlay = ref(props.autoStart)
    const playbackSpeed = ref(1)
    const totalFrames = ref(0)

    // 最新检测帧（定格显示）
    const latestDetectionFrame = ref(null)

    // 网络状况监控（优化帧率计算）
    const networkStats = ref({
      quality: 'good',
      frameRate: 0,
      averageSize: 0,
      lastFrameTime: 0,
      frameIntervals: [],
      realTimeFrameRate: 0
    })

    // 播放控制
    let playbackTimer = null
    let networkStatsTimer = null
    
    // 计算属性
    const hasFrames = computed(() => frames.value.length > 0)
    const currentFrame = computed(() => frames.value[currentFrameIndex.value] || null)
    
    // 可见缩略图（最多显示10个）
    const visibleThumbnails = computed(() => {
      const maxThumbnails = 10
      if (frames.value.length <= maxThumbnails) {
        return frames.value
      }

      const step = Math.floor(frames.value.length / maxThumbnails)
      return frames.value.filter((_, index) => index % step === 0).slice(0, maxThumbnails)
    })

    // 网络质量相关计算属性
    const networkQualityClass = computed(() => {
      return `network-${networkStats.value.quality}`
    })

    const networkQualityText = computed(() => {
      const quality = networkStats.value.quality
      const rate = networkStats.value.realTimeFrameRate > 0
        ? networkStats.value.realTimeFrameRate.toFixed(1)
        : networkStats.value.frameRate.toFixed(1)

      switch (quality) {
        case 'good':
          return `良好 ${rate}fps`
        case 'fair':
          return `一般 ${rate}fps`
        case 'poor':
          return `较差 ${rate}fps`
        default:
          return `实时 ${rate}fps`
      }
    })
    
    // 方法
    const getFrameImageUrl = (frame) => {
      if (!frame || !frame.imageData) {
        console.log('🖼️ getFrameImageUrl: 无效的帧数据', { frame: !!frame, hasImageData: frame ? !!frame.imageData : false })
        return ''
      }
      const imageUrl = `data:image/jpeg;base64,${frame.imageData}`
      console.log('🖼️ getFrameImageUrl: 生成图像URL', {
        frameNumber: frame.frameNumber,
        imageDataLength: frame.imageData.length,
        urlLength: imageUrl.length,
        urlPrefix: imageUrl.substring(0, 50) + '...'
      })
      return imageUrl
    }
    
    const formatTimestamp = (timestamp) => {
      if (!timestamp) return ''
      try {
        const date = new Date(timestamp)
        return date.toLocaleTimeString()
      } catch (e) {
        return timestamp
      }
    }

    // 获取检测标签类型
    const getDetectionTagType = (detectionCount) => {
      if (detectionCount === 0) return 'info'
      if (detectionCount <= 2) return 'success'
      if (detectionCount <= 5) return 'warning'
      return 'danger'
    }
    
    const addFrame = (frameData) => {
      try {
        // 计算实时帧率
        const currentTime = Date.now()
        if (networkStats.value.lastFrameTime > 0) {
          const interval = currentTime - networkStats.value.lastFrameTime
          networkStats.value.frameIntervals.push(interval)

          // 保持最近10个间隔用于计算平均帧率
          if (networkStats.value.frameIntervals.length > 10) {
            networkStats.value.frameIntervals.shift()
          }

          // 计算实时帧率
          const avgInterval = networkStats.value.frameIntervals.reduce((a, b) => a + b, 0) / networkStats.value.frameIntervals.length
          networkStats.value.realTimeFrameRate = 1000 / avgInterval
        }
        networkStats.value.lastFrameTime = currentTime

        // 调试日志：检查接收到的帧数据
        console.log('🎬 RealTimeFrameViewer接收到帧数据:', {
          frameNumber: frameData.frameNumber,
          detectionCount: frameData.detectionCount,
          hasImageData: !!frameData.imageData,
          imageDataLength: frameData.imageData ? frameData.imageData.length : 0,
          realTimeFrameRate: networkStats.value.realTimeFrameRate.toFixed(2) + ' fps'
        })

        // 添加新帧到缓冲区
        frames.value.push(frameData)

        // 更新总帧数
        if (frameData.totalFrames) {
          totalFrames.value = frameData.totalFrames
        }

        // 检查是否有车辆检测，如果有则更新最新检测帧（定格显示）
        if (frameData.detectionCount && frameData.detectionCount > 0) {
          console.log(`🎯 更新最新检测帧: 帧${frameData.frameNumber}, 车辆${frameData.detectionCount}`)
          latestDetectionFrame.value = { ...frameData }
        }

        // 限制缓冲区大小
        while (frames.value.length > props.maxBufferFrames) {
          frames.value.shift()
          if (currentFrameIndex.value > 0) {
            currentFrameIndex.value--
          }
        }

        // 如果启用自动播放且当前在最后一帧，自动跳到新帧
        if (autoPlay.value && currentFrameIndex.value === frames.value.length - 2) {
          currentFrameIndex.value = frames.value.length - 1
        }

        // 发出帧接收事件
        emit('frame-received', frameData)

      } catch (error) {
        console.error('添加帧数据失败:', error)
        ElMessage.error('处理帧数据失败')
      }
    }
    
    const togglePlayback = () => {
      if (isPlaying.value) {
        stopPlayback()
      } else {
        startPlayback()
      }
    }
    
    const startPlayback = () => {
      if (!hasFrames.value) return
      
      isPlaying.value = true
      emit('playback-state-change', { playing: true, speed: playbackSpeed.value })
      
      const interval = 1000 / playbackSpeed.value // 基础间隔1秒，根据速度调整
      
      playbackTimer = setInterval(() => {
        if (currentFrameIndex.value < frames.value.length - 1) {
          currentFrameIndex.value++
        } else {
          // 播放完毕，停止播放
          stopPlayback()
        }
      }, interval)
    }
    
    const stopPlayback = () => {
      isPlaying.value = false
      emit('playback-state-change', { playing: false, speed: playbackSpeed.value })
      
      if (playbackTimer) {
        clearInterval(playbackTimer)
        playbackTimer = null
      }
    }
    
    const clearFrames = () => {
      stopPlayback()
      frames.value = []
      currentFrameIndex.value = 0
      totalFrames.value = 0
      latestDetectionFrame.value = null  // 清除定格显示的检测帧
      ElMessage.success('已清空帧缓冲区')
    }

    // 清除最新检测帧
    const clearLatestDetection = () => {
      latestDetectionFrame.value = null
      console.log('🧹 已清除最新检测帧')
      ElMessage.success('已清除最新检测结果')
    }
    
    const selectFrame = (index) => {
      if (index >= 0 && index < frames.value.length) {
        currentFrameIndex.value = index
      }
    }
    
    const onFrameIndexChange = (value) => {
      currentFrameIndex.value = value
    }
    
    const handleImageLoad = () => {
      // 图像加载成功
    }
    
    const handleImageError = () => {
      console.error('帧图像加载失败')
    }

    // 处理策略应用事件
    const handleStrategyApplied = (strategyData) => {
      console.log('交通策略已应用:', strategyData)
      emit('strategy-applied', strategyData)
    }

    // 处理交通数据更新事件
    const handleTrafficDataUpdated = (trafficData) => {
      console.log('交通数据已更新:', trafficData)
      emit('traffic-data-updated', trafficData)
    }

    // 更新网络状况统计
    const updateNetworkStats = () => {
      try {
        // 从STOMP服务获取网络统计
        const stats = window.stompService?.getNetworkStats?.() || {}

        networkStats.value = {
          quality: stats.connectionQuality || 'good',
          frameRate: stats.frameReceiveRate || 0,
          averageSize: stats.averageFrameSize || 0
        }

      } catch (error) {
        console.error('更新网络统计失败:', error)
      }
    }

    // 启动网络状况监控
    const startNetworkMonitoring = () => {
      // 每2秒更新一次网络统计
      networkStatsTimer = setInterval(updateNetworkStats, 2000)
    }

    // 停止网络状况监控
    const stopNetworkMonitoring = () => {
      if (networkStatsTimer) {
        clearInterval(networkStatsTimer)
        networkStatsTimer = null
      }
    }
    
    // 监听播放速度变化
    watch(playbackSpeed, (newSpeed) => {
      if (isPlaying.value) {
        stopPlayback()
        nextTick(() => {
          startPlayback()
        })
      }
    })
    
    // 监听自动播放设置变化
    watch(autoPlay, (newValue) => {
      if (!newValue && isPlaying.value) {
        stopPlayback()
      }
    })
    
    // 组件挂载时启动网络监控
    onMounted(() => {
      startNetworkMonitoring()
    })

    // 组件卸载时清理
    onUnmounted(() => {
      stopPlayback()
      stopNetworkMonitoring()
    })
    
    // 暴露方法给父组件
    const addFrameData = addFrame
    const clearFrameData = clearFrames
    const getFrameCount = () => frames.value.length
    const getCurrentFrame = () => currentFrame.value
    
    return {
      // 响应式数据
      frames,
      currentFrameIndex,
      isPlaying,
      autoPlay,
      playbackSpeed,
      totalFrames,
      latestDetectionFrame,
      
      // 计算属性
      hasFrames,
      currentFrame,
      visibleThumbnails,
      networkQualityClass,
      networkQualityText,
      
      // 方法
      getFrameImageUrl,
      formatTimestamp,
      getDetectionTagType,
      togglePlayback,
      clearFrames,
      clearLatestDetection,
      selectFrame,
      onFrameIndexChange,
      handleImageLoad,
      handleImageError,
      handleStrategyApplied,
      handleTrafficDataUpdated,

      // 暴露给父组件的方法
      addFrameData,
      clearFrameData,
      getFrameCount,
      getCurrentFrame
    }
  }
}
</script>

<style scoped>
.realtime-frame-viewer {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.viewer-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.viewer-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.viewer-content {
  padding: 20px;
}

.frame-display {
  position: relative;
  width: 100%;
  height: 400px;
  background: #000;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.frame-display.no-frames {
  background: #f5f5f5;
  border: 2px dashed #d9d9d9;
}

.no-frames-message {
  text-align: center;
  color: #999;
}

.no-frames-message .waiting-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.frame-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 检测帧容器样式 */
.detection-frame-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-radius: 8px;
}

.detection-frame-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.detection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.detection-header {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.detection-time {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
}

.detection-info {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  border-left: 3px solid #10b981;
}

.detection-status {
  color: #10b981;
  font-weight: 600;
  font-size: 11px;
}

.frame-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.frame-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.frame-info {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.frame-info span {
  white-space: nowrap;
}

.playback-controls {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.frame-slider {
  margin-bottom: 12px;
}

.playback-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

.playback-info > span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.buffer-info {
  color: #999;
  font-size: 12px;
}

.network-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.network-good {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.network-fair {
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}

.network-poor {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.network-info .el-icon {
  font-size: 14px;
}

.thumbnail-strip {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  overflow-x: auto;
}

.thumbnail-container {
  display: flex;
  gap: 8px;
  min-height: 60px;
}

.thumbnail-item {
  flex-shrink: 0;
  width: 80px;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.thumbnail-item:hover {
  border-color: #409eff;
  transform: translateY(-2px);
}

.thumbnail-item.active {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.thumbnail-image {
  width: 100%;
  height: 45px;
  object-fit: cover;
  display: block;
}

.thumbnail-label {
  padding: 2px 4px;
  background: #fff;
  text-align: center;
  font-size: 10px;
  color: #666;
  border-top: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .viewer-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .viewer-controls {
    justify-content: center;
  }

  .frame-display {
    height: 300px;
  }

  .playback-info {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .thumbnail-item {
    width: 60px;
  }

  .thumbnail-image {
    height: 35px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .realtime-frame-viewer {
    background: #1f1f1f;
    color: #fff;
  }

  .viewer-header {
    background: #2d2d2d;
    border-bottom-color: #404040;
  }

  .viewer-header h4 {
    color: #fff;
  }

  .frame-display.no-frames {
    background: #2d2d2d;
    border-color: #404040;
  }

  .no-frames-message {
    color: #ccc;
  }

  .playback-controls {
    background: #2d2d2d;
  }

  .thumbnail-strip {
    background: #2d2d2d;
    border-top-color: #404040;
  }

  .thumbnail-label {
    background: #2d2d2d;
    color: #ccc;
    border-top-color: #404040;
  }
}

/* 交通面板容器样式 */
.traffic-panel-container {
  margin-top: 20px;
  padding: 0;
}

/* 响应式设计 - 交通面板 */
@media (max-width: 768px) {
  .traffic-panel-container {
    margin-top: 16px;
  }
}
</style>
