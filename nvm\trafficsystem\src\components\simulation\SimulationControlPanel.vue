<template>
  <div class="simulation-control-panel">
    <el-card class="control-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3><i class="el-icon-setting"></i> 仿真控制面板</h3>
          <el-tag :type="getStatusTagType(simulationStatus)" size="large">
            {{ getStatusText(simulationStatus) }}
          </el-tag>
        </div>
      </template>

      <!-- 仿真配置区域 -->
      <div class="config-section" v-if="!isSimulationRunning">
        <h4><i class="el-icon-tools"></i> 仿真配置</h4>
        <el-form :model="simulationConfig" label-width="120px" size="default">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="仿真类型">
                <el-select v-model="simulationConfig.simulationType" placeholder="选择仿真类型">
                  <el-option label="信号灯配时优化" value="signal_timing"></el-option>
                  <el-option label="流量平衡优化" value="flow_balance"></el-option>
                  <el-option label="综合优化分析" value="comprehensive"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="仿真时长">
                <el-input-number 
                  v-model="simulationConfig.duration" 
                  :min="300" 
                  :max="7200" 
                  :step="300"
                  controls-position="right">
                </el-input-number>
                <span class="unit-text">秒</span>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="使用GUI">
                <el-switch v-model="simulationConfig.useGui"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="随机种子">
                <el-input-number 
                  v-model="simulationConfig.randomSeed" 
                  :min="1" 
                  :max="9999"
                  controls-position="right">
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 控制按钮区域 -->
      <div class="control-buttons">
        <el-button-group>
          <el-button 
            type="primary" 
            :icon="isSimulationRunning ? 'el-icon-video-pause' : 'el-icon-video-play'"
            :loading="isStarting"
            @click="toggleSimulation"
            :disabled="!canStartSimulation">
            {{ isSimulationRunning ? '停止仿真' : '启动仿真' }}
          </el-button>
          
          <el-button 
            type="warning" 
            icon="el-icon-refresh"
            :loading="isRestarting"
            @click="restartSimulation"
            :disabled="!simulationId || isStarting">
            重启仿真
          </el-button>
          
          <el-button 
            type="info" 
            icon="el-icon-download"
            @click="exportResults"
            :disabled="!hasResults">
            导出结果
          </el-button>
        </el-button-group>
      </div>

      <!-- 仿真进度区域 -->
      <div class="progress-section" v-if="simulationId">
        <h4><i class="el-icon-time"></i> 仿真进度</h4>
        <el-progress 
          :percentage="simulationProgress" 
          :status="getProgressStatus()"
          :stroke-width="8">
        </el-progress>
        
        <div class="progress-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">仿真时间:</span>
                <span class="value">{{ formatTime(currentSimulationTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">车辆数量:</span>
                <span class="value">{{ currentVehicleCount }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">平均速度:</span>
                <span class="value">{{ formatSpeed(averageSpeed) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 实时状态监控 -->
      <div class="status-monitor" v-if="isSimulationRunning">
        <h4><i class="el-icon-monitor"></i> 实时监控</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总车辆数" :value="totalVehicles" suffix="辆">
              <template #prefix>
                <i class="el-icon-truck" style="color: #409eff"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="通行能力" :value="throughput" suffix="辆/小时">
              <template #prefix>
                <i class="el-icon-right" style="color: #67c23a"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="等待时间" :value="waitingTime" suffix="秒">
              <template #prefix>
                <i class="el-icon-timer" style="color: #e6a23c"></i>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic title="改善效果" :value="improvementRate" suffix="%">
              <template #prefix>
                <i class="el-icon-trend-charts" style="color: #f56c6c"></i>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>

      <!-- 错误信息显示 -->
      <div class="error-section" v-if="errorMessage">
        <el-alert
          :title="errorMessage"
          type="error"
          :closable="false"
          show-icon>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import simulationApi from '@/api/simulation'

export default {
  name: 'SimulationControlPanel',
  props: {
    analysisTaskId: {
      type: String,
      required: true
    },
    trafficData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['simulation-started', 'simulation-stopped', 'simulation-completed', 'status-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const simulationId = ref('')
    const simulationStatus = ref('idle') // idle, running, completed, failed, stopped
    const simulationProgress = ref(0)
    const isStarting = ref(false)
    const isRestarting = ref(false)
    const errorMessage = ref('')
    
    // 仿真配置
    const simulationConfig = reactive({
      simulationType: 'comprehensive',
      duration: 3600,
      useGui: false,
      randomSeed: 42
    })
    
    // 实时监控数据
    const currentSimulationTime = ref(0)
    const currentVehicleCount = ref(0)
    const averageSpeed = ref(0)
    const totalVehicles = ref(0)
    const throughput = ref(0)
    const waitingTime = ref(0)
    const improvementRate = ref(0)
    
    // 状态轮询定时器
    let statusTimer = null
    
    // 计算属性
    const isSimulationRunning = computed(() => simulationStatus.value === 'running')
    const canStartSimulation = computed(() => 
      props.analysisTaskId && !isStarting.value && simulationStatus.value !== 'running'
    )
    const hasResults = computed(() => 
      simulationStatus.value === 'completed' && simulationId.value
    )
    
    // 方法
    const getStatusTagType = (status) => {
      const statusMap = {
        'idle': 'info',
        'running': 'success',
        'completed': 'success',
        'failed': 'danger',
        'stopped': 'warning'
      }
      return statusMap[status] || 'info'
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'idle': '待启动',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'stopped': '已停止'
      }
      return statusMap[status] || '未知'
    }
    
    const getProgressStatus = () => {
      if (simulationStatus.value === 'failed') return 'exception'
      if (simulationStatus.value === 'completed') return 'success'
      return null
    }
    
    const formatTime = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    
    const formatSpeed = (speed) => {
      return `${speed.toFixed(1)} m/s`
    }
    
    const toggleSimulation = async () => {
      if (isSimulationRunning.value) {
        await stopSimulation()
      } else {
        await startSimulation()
      }
    }
    
    const startSimulation = async () => {
      try {
        isStarting.value = true
        errorMessage.value = ''
        
        // 创建仿真任务
        const createResponse = await simulationApi.createSimulation({
          analysis_task_id: props.analysisTaskId,
          simulation_type: simulationConfig.simulationType,
          user_id: localStorage.getItem('userId'),
          username: localStorage.getItem('username')
        })
        
        if (createResponse.status === 'success') {
          simulationId.value = createResponse.simulation_task.simulationId
          
          // 启动仿真
          const startResponse = await simulationApi.startSimulation(simulationId.value, {
            use_gui: simulationConfig.useGui
          })
          
          if (startResponse.status === 'success') {
            simulationStatus.value = 'running'
            startStatusPolling()
            emit('simulation-started', simulationId.value)
            ElMessage.success('仿真启动成功')
          } else {
            throw new Error(startResponse.message || '启动仿真失败')
          }
        } else {
          throw new Error(createResponse.message || '创建仿真任务失败')
        }
        
      } catch (error) {
        console.error('启动仿真失败:', error)
        errorMessage.value = error.message || '启动仿真失败'
        ElMessage.error(errorMessage.value)
      } finally {
        isStarting.value = false
      }
    }
    
    const stopSimulation = async () => {
      try {
        const response = await simulationApi.stopSimulation(simulationId.value)
        
        if (response.status === 'success') {
          simulationStatus.value = 'stopped'
          stopStatusPolling()
          emit('simulation-stopped', simulationId.value)
          ElMessage.success('仿真已停止')
        } else {
          throw new Error(response.message || '停止仿真失败')
        }
        
      } catch (error) {
        console.error('停止仿真失败:', error)
        ElMessage.error(error.message || '停止仿真失败')
      }
    }
    
    const restartSimulation = async () => {
      try {
        isRestarting.value = true
        
        await ElMessageBox.confirm('确定要重启仿真吗？', '确认重启', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await stopSimulation()
        setTimeout(async () => {
          await startSimulation()
          isRestarting.value = false
        }, 2000)
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重启仿真失败:', error)
          ElMessage.error('重启仿真失败')
        }
        isRestarting.value = false
      }
    }
    
    const exportResults = async () => {
      try {
        const response = await simulationApi.exportResults(simulationId.value, 'json')
        
        if (response.status === 'success') {
          ElMessage.success('结果导出成功')
        } else {
          ElMessage.warning('导出功能暂未实现')
        }
        
      } catch (error) {
        console.error('导出结果失败:', error)
        ElMessage.error('导出结果失败')
      }
    }
    
    const startStatusPolling = () => {
      if (statusTimer) {
        clearInterval(statusTimer)
      }
      
      statusTimer = setInterval(async () => {
        try {
          const response = await simulationApi.getSimulationStatus(simulationId.value)
          
          if (response.status) {
            simulationStatus.value = response.status
            simulationProgress.value = response.progress || 0
            
            // 更新实时数据
            if (response.sumo_status) {
              const sumoStatus = response.sumo_status
              currentSimulationTime.value = sumoStatus.current_time || 0
              currentVehicleCount.value = sumoStatus.vehicle_count || 0
              totalVehicles.value = sumoStatus.total_vehicles || 0
            }
            
            // 检查仿真是否完成
            if (simulationStatus.value === 'completed') {
              stopStatusPolling()
              emit('simulation-completed', simulationId.value)
              ElMessage.success('仿真已完成')
            } else if (simulationStatus.value === 'failed') {
              stopStatusPolling()
              errorMessage.value = response.error_message || '仿真执行失败'
              ElMessage.error(errorMessage.value)
            }
            
            emit('status-updated', response)
          }
          
        } catch (error) {
          console.error('获取仿真状态失败:', error)
        }
      }, 2000) // 每2秒轮询一次
    }
    
    const stopStatusPolling = () => {
      if (statusTimer) {
        clearInterval(statusTimer)
        statusTimer = null
      }
    }
    
    // 生命周期
    onMounted(() => {
      // 组件挂载时的初始化逻辑
    })
    
    onUnmounted(() => {
      stopStatusPolling()
    })
    
    return {
      // 响应式数据
      simulationId,
      simulationStatus,
      simulationProgress,
      isStarting,
      isRestarting,
      errorMessage,
      simulationConfig,
      currentSimulationTime,
      currentVehicleCount,
      averageSpeed,
      totalVehicles,
      throughput,
      waitingTime,
      improvementRate,
      
      // 计算属性
      isSimulationRunning,
      canStartSimulation,
      hasResults,
      
      // 方法
      getStatusTagType,
      getStatusText,
      getProgressStatus,
      formatTime,
      formatSpeed,
      toggleSimulation,
      restartSimulation,
      exportResults
    }
  }
}
</script>

<style scoped>
.simulation-control-panel {
  margin-bottom: 20px;
}

.control-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.config-section,
.progress-section,
.status-monitor {
  margin-bottom: 20px;
}

.config-section h4,
.progress-section h4,
.status-monitor h4 {
  margin-bottom: 15px;
  color: #606266;
  font-size: 16px;
}

.control-buttons {
  text-align: center;
  margin: 20px 0;
}

.unit-text {
  margin-left: 8px;
  color: #909399;
}

.progress-info {
  margin-top: 15px;
}

.info-item {
  text-align: center;
}

.info-item .label {
  display: block;
  color: #909399;
  font-size: 12px;
  margin-bottom: 4px;
}

.info-item .value {
  display: block;
  color: #303133;
  font-size: 16px;
  font-weight: bold;
}

.error-section {
  margin-top: 15px;
}

.el-statistic {
  text-align: center;
}
</style>
