<template>
  <div class="simulation-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>仿真分析</el-breadcrumb-item>
        <el-breadcrumb-item>仿真控制台</el-breadcrumb-item>
      </el-breadcrumb>
      
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <i class="el-icon-plus"></i> 创建仿真
        </el-button>
        <el-button @click="refreshData">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <i class="el-icon-cpu" style="color: #409eff"></i>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.total_tasks || 0 }}</div>
                <div class="stat-label">总仿真任务</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <i class="el-icon-video-play" style="color: #67c23a"></i>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.running_tasks || 0 }}</div>
                <div class="stat-label">运行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <i class="el-icon-check" style="color: #e6a23c"></i>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.completed_tasks || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <i class="el-icon-warning" style="color: #f56c6c"></i>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.failed_tasks || 0 }}</div>
                <div class="stat-label">失败</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 仿真任务列表 -->
    <el-card class="task-list-card">
      <template #header>
        <div class="card-header">
          <h3>仿真任务列表</h3>
          <div class="header-filters">
            <el-select v-model="filterStatus" placeholder="状态筛选" clearable size="small">
              <el-option label="全部" value=""></el-option>
              <el-option label="运行中" value="running"></el-option>
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="失败" value="failed"></el-option>
              <el-option label="已停止" value="stopped"></el-option>
            </el-select>
            
            <el-select v-model="filterType" placeholder="类型筛选" clearable size="small">
              <el-option label="全部" value=""></el-option>
              <el-option label="信号配时优化" value="signal_timing"></el-option>
              <el-option label="流量平衡优化" value="flow_balance"></el-option>
              <el-option label="综合优化" value="comprehensive"></el-option>
            </el-select>
          </div>
        </div>
      </template>

      <el-table :data="filteredTasks" v-loading="loading" style="width: 100%">
        <el-table-column prop="taskName" label="任务名称" min-width="200">
          <template #default="scope">
            <div class="task-name">
              <strong>{{ scope.row.taskName }}</strong>
              <div class="task-id">ID: {{ scope.row.simulationId }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="simulationType" label="仿真类型" width="150">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.simulationType)" size="small">
              {{ getTypeText(scope.row.simulationType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="progress" label="进度" width="150">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.progress || 0" 
              :status="getProgressStatus(scope.row.status)"
              :stroke-width="6">
            </el-progress>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="scope">
            {{ formatDuration(scope.row.duration) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button-group size="small">
              <el-button 
                type="primary" 
                @click="viewTask(scope.row)"
                :icon="'el-icon-view'">
                查看
              </el-button>
              
              <el-button 
                v-if="scope.row.status === 'running'"
                type="warning" 
                @click="stopTask(scope.row)"
                :icon="'el-icon-video-pause'">
                停止
              </el-button>
              
              <el-button 
                v-if="scope.row.status === 'failed' || scope.row.status === 'stopped'"
                type="success" 
                @click="restartTask(scope.row)"
                :icon="'el-icon-refresh'">
                重启
              </el-button>
              
              <el-button 
                type="danger" 
                @click="deleteTask(scope.row)"
                :icon="'el-icon-delete'">
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalTasks"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </el-card>

    <!-- 创建仿真对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建仿真任务"
      width="600px"
      :close-on-click-modal="false">
      
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="120px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="createForm.taskName" placeholder="请输入任务名称"></el-input>
        </el-form-item>
        
        <el-form-item label="仿真类型" prop="simulationType">
          <el-select v-model="createForm.simulationType" placeholder="选择仿真类型" style="width: 100%">
            <el-option label="信号灯配时优化" value="signal_timing"></el-option>
            <el-option label="流量平衡优化" value="flow_balance"></el-option>
            <el-option label="综合优化分析" value="comprehensive"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="分析任务ID" prop="analysisTaskId">
          <el-input v-model="createForm.analysisTaskId" placeholder="请输入视频分析任务ID"></el-input>
        </el-form-item>
        
        <el-form-item label="使用GUI">
          <el-switch v-model="createForm.useGui"></el-switch>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="createSimulation" :loading="creating">
            创建并启动
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import simulationApi from '@/api/simulation'

export default {
  name: 'SimulationDashboard',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const loading = ref(false)
    const creating = ref(false)
    const showCreateDialog = ref(false)
    const tasks = ref([])
    const statistics = ref({})
    
    // 筛选和分页
    const filterStatus = ref('')
    const filterType = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const totalTasks = ref(0)
    
    // 创建表单
    const createForm = reactive({
      taskName: '',
      simulationType: 'comprehensive',
      analysisTaskId: '',
      useGui: false
    })
    
    const createRules = {
      taskName: [
        { required: true, message: '请输入任务名称', trigger: 'blur' }
      ],
      simulationType: [
        { required: true, message: '请选择仿真类型', trigger: 'change' }
      ],
      analysisTaskId: [
        { required: true, message: '请输入分析任务ID', trigger: 'blur' }
      ]
    }
    
    const createFormRef = ref(null)
    
    // 计算属性
    const filteredTasks = computed(() => {
      let filtered = tasks.value
      
      if (filterStatus.value) {
        filtered = filtered.filter(task => task.status === filterStatus.value)
      }
      
      if (filterType.value) {
        filtered = filtered.filter(task => task.simulationType === filterType.value)
      }
      
      return filtered
    })
    
    // 方法
    const getStatusTagType = (status) => {
      const statusMap = {
        'created': 'info',
        'running': 'success',
        'completed': 'success',
        'failed': 'danger',
        'stopped': 'warning'
      }
      return statusMap[status] || 'info'
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'created': '已创建',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'stopped': '已停止'
      }
      return statusMap[status] || '未知'
    }
    
    const getTypeTagType = (type) => {
      const typeMap = {
        'signal_timing': 'primary',
        'flow_balance': 'success',
        'comprehensive': 'warning'
      }
      return typeMap[type] || 'info'
    }
    
    const getTypeText = (type) => {
      const typeMap = {
        'signal_timing': '信号配时',
        'flow_balance': '流量平衡',
        'comprehensive': '综合优化'
      }
      return typeMap[type] || '未知'
    }
    
    const getProgressStatus = (status) => {
      if (status === 'failed') return 'exception'
      if (status === 'completed') return 'success'
      return null
    }
    
    const formatTime = (timeStr) => {
      if (!timeStr) return '-'
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    }
    
    const formatDuration = (duration) => {
      if (!duration) return '-'
      const hours = Math.floor(duration / 3600)
      const minutes = Math.floor((duration % 3600) / 60)
      const seconds = duration % 60
      
      if (hours > 0) {
        return `${hours}h ${minutes}m ${seconds}s`
      } else if (minutes > 0) {
        return `${minutes}m ${seconds}s`
      } else {
        return `${seconds}s`
      }
    }
    
    const loadTasks = async () => {
      try {
        loading.value = true
        const userId = localStorage.getItem('userId')
        const response = await simulationApi.getUserSimulations(userId)
        
        if (response.status === 'success') {
          tasks.value = response.simulation_tasks || []
          totalTasks.value = response.total_count || 0
        }
      } catch (error) {
        console.error('加载仿真任务失败:', error)
        ElMessage.error('加载仿真任务失败')
      } finally {
        loading.value = false
      }
    }
    
    const loadStatistics = async () => {
      try {
        const userId = localStorage.getItem('userId')
        const response = await simulationApi.getStatistics(userId)
        statistics.value = response
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    }
    
    const refreshData = async () => {
      await Promise.all([loadTasks(), loadStatistics()])
    }
    
    const createSimulation = async () => {
      try {
        await createFormRef.value.validate()
        creating.value = true
        
        const userId = localStorage.getItem('userId')
        const username = localStorage.getItem('username')
        
        const response = await simulationApi.createSimulation({
          user_id: userId,
          username: username,
          task_name: createForm.taskName,
          simulation_type: createForm.simulationType,
          analysis_task_id: createForm.analysisTaskId,
          traffic_data: {} // 这里应该从分析结果获取
        })
        
        if (response.status === 'success') {
          ElMessage.success('仿真任务创建成功')
          showCreateDialog.value = false
          
          // 如果需要立即启动
          const simulationId = response.simulation_task.simulationId
          await simulationApi.startSimulation(simulationId, {
            use_gui: createForm.useGui
          })
          
          // 跳转到仿真详情页
          router.push(`/simulation/${simulationId}`)
        }
      } catch (error) {
        console.error('创建仿真失败:', error)
        ElMessage.error('创建仿真失败')
      } finally {
        creating.value = false
      }
    }
    
    const viewTask = (task) => {
      router.push(`/simulation/${task.simulationId}`)
    }
    
    const stopTask = async (task) => {
      try {
        await ElMessageBox.confirm('确定要停止此仿真任务吗？', '确认停止', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await simulationApi.stopSimulation(task.simulationId)
        if (response.status === 'success') {
          ElMessage.success('仿真已停止')
          await refreshData()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('停止仿真失败:', error)
          ElMessage.error('停止仿真失败')
        }
      }
    }
    
    const restartTask = async (task) => {
      try {
        await ElMessageBox.confirm('确定要重启此仿真任务吗？', '确认重启', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await simulationApi.restartSimulation(task.simulationId)
        if (response.status === 'success') {
          ElMessage.success('仿真重启成功')
          await refreshData()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重启仿真失败:', error)
          ElMessage.error('重启仿真失败')
        }
      }
    }
    
    const deleteTask = async (task) => {
      try {
        await ElMessageBox.confirm('确定要删除此仿真任务吗？删除后无法恢复。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const userId = localStorage.getItem('userId')
        const response = await simulationApi.deleteSimulation(task.simulationId, userId)
        if (response.status === 'success') {
          ElMessage.success('仿真任务已删除')
          await refreshData()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除仿真失败:', error)
          ElMessage.error('删除仿真失败')
        }
      }
    }
    
    const handleSizeChange = (newSize) => {
      pageSize.value = newSize
      currentPage.value = 1
      loadTasks()
    }
    
    const handleCurrentChange = (newPage) => {
      currentPage.value = newPage
      loadTasks()
    }
    
    // 生命周期
    onMounted(() => {
      refreshData()
    })
    
    return {
      // 响应式数据
      loading,
      creating,
      showCreateDialog,
      tasks,
      statistics,
      filterStatus,
      filterType,
      currentPage,
      pageSize,
      totalTasks,
      createForm,
      createRules,
      createFormRef,
      
      // 计算属性
      filteredTasks,
      
      // 方法
      getStatusTagType,
      getStatusText,
      getTypeTagType,
      getTypeText,
      getProgressStatus,
      formatTime,
      formatDuration,
      refreshData,
      createSimulation,
      viewTask,
      stopTask,
      restartTask,
      deleteTask,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.simulation-dashboard {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.task-list-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-filters {
  display: flex;
  gap: 10px;
}

.task-name {
  line-height: 1.4;
}

.task-id {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
