/**
 * 测试报告数据转换功能
 */

import { transformToReportData } from './reportDataTransformer.js'

// 模拟WebSocket数据
const mockWebSocketData = {
  taskId: 'test-task-123',
  summary: {
    totalVehicles: 85,
    processingDuration: 450,
    peakDirection: 'east'
  },
  directions: {
    east: {
      vehicleCount: 25,
      vehicleTypes: {
        car: 20,
        truck: 3,
        bus: 1,
        motorcycle: 1
      },
      averageFlowDensity: 2.5,
      crowdLevel: '轻度拥堵'
    },
    south: {
      vehicleCount: 18,
      vehicleTypes: {
        car: 15,
        truck: 2,
        bus: 1,
        motorcycle: 0
      },
      averageFlowDensity: 1.8,
      crowdLevel: '畅通'
    },
    west: {
      vehicleCount: 22,
      vehicleTypes: {
        car: 18,
        truck: 2,
        bus: 1,
        motorcycle: 1
      },
      averageFlowDensity: 2.2,
      crowdLevel: '畅通'
    },
    north: {
      vehicleCount: 20,
      vehicleTypes: {
        car: 16,
        truck: 3,
        bus: 1,
        motorcycle: 0
      },
      averageFlowDensity: 2.0,
      crowdLevel: '畅通'
    }
  }
}

// 模拟API数据
const mockApiData = {
  taskId: 'test-task-123',
  status: 'completed',
  totalVehicleCount: 85,
  processingDurationSeconds: 450,
  trafficAnalysis: {
    totalVehicleCount: 85,
    peakDirection: 'east',
    trafficFlowBalance: 0.72,
    congestionLevel: '轻度拥堵',
    recommendations: [
      '建议在高峰时段增加东向绿灯时间',
      '考虑在南北方向设置右转专用道',
      '建议优化信号灯配时方案'
    ],
    signalOptimization: {
      recommendedCycle: 110,
      greenTimeAllocation: {
        east: 35,
        south: 25,
        west: 30,
        north: 20
      },
      expectedImprovement: '通行效率提升15%',
      reason: '东向车流量较大，建议增加绿灯时间'
    }
  }
}

/**
 * 测试数据转换功能
 */
export function testReportDataTransformation() {
  console.log('开始测试报告数据转换功能...')
  
  try {
    // 测试1: 使用WebSocket数据
    console.log('\n=== 测试1: 仅WebSocket数据 ===')
    const result1 = transformToReportData(null, mockWebSocketData)
    console.log('转换结果1:', JSON.stringify(result1, null, 2))
    
    // 测试2: 使用API数据
    console.log('\n=== 测试2: 仅API数据 ===')
    const result2 = transformToReportData(mockApiData, null)
    console.log('转换结果2:', JSON.stringify(result2, null, 2))
    
    // 测试3: 合并数据
    console.log('\n=== 测试3: 合并WebSocket和API数据 ===')
    const result3 = transformToReportData(mockApiData, mockWebSocketData)
    console.log('转换结果3:', JSON.stringify(result3, null, 2))
    
    // 测试4: 空数据处理
    console.log('\n=== 测试4: 空数据处理 ===')
    const result4 = transformToReportData(null, null)
    console.log('转换结果4:', JSON.stringify(result4, null, 2))
    
    // 验证数据结构
    console.log('\n=== 数据结构验证 ===')
    validateReportDataStructure(result3)
    
    console.log('\n✅ 所有测试通过！')
    return true
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return false
  }
}

/**
 * 验证报告数据结构
 */
function validateReportDataStructure(reportData) {
  const requiredFields = [
    'taskId',
    'generatedAt',
    'analysisType',
    'summary',
    'directions',
    'intelligentAnalysis',
    'recommendations',
    'technicalMetrics'
  ]
  
  const summaryFields = [
    'totalVehicles',
    'vehicleIncrease',
    'processingDuration',
    'efficiency',
    'peakDirection',
    'peakPercentage',
    'congestionLevel',
    'congestionTrend'
  ]
  
  const intelligentAnalysisFields = [
    'flowBalance',
    'peakHours',
    'flowTrend',
    'congestionPrediction',
    'signalOptimization'
  ]
  
  const technicalMetricsFields = [
    'accuracy',
    'processingSpeed',
    'stability',
    'dataIntegrity',
    'responseTime',
    'memoryUsage',
    'cpuUsage'
  ]
  
  // 验证顶级字段
  requiredFields.forEach(field => {
    if (!(field in reportData)) {
      throw new Error(`缺少必需字段: ${field}`)
    }
  })
  
  // 验证summary字段
  summaryFields.forEach(field => {
    if (!(field in reportData.summary)) {
      throw new Error(`summary中缺少必需字段: ${field}`)
    }
  })
  
  // 验证intelligentAnalysis字段
  intelligentAnalysisFields.forEach(field => {
    if (!(field in reportData.intelligentAnalysis)) {
      throw new Error(`intelligentAnalysis中缺少必需字段: ${field}`)
    }
  })
  
  // 验证technicalMetrics字段
  technicalMetricsFields.forEach(field => {
    if (!(field in reportData.technicalMetrics)) {
      throw new Error(`technicalMetrics中缺少必需字段: ${field}`)
    }
  })
  
  // 验证方向数据
  const directions = ['east', 'south', 'west', 'north']
  directions.forEach(direction => {
    if (!(direction in reportData.directions)) {
      throw new Error(`directions中缺少方向: ${direction}`)
    }
    
    const directionData = reportData.directions[direction]
    const requiredDirectionFields = ['vehicleCount', 'averageSpeed', 'density', 'congestionIndex', 'crowdLevel', 'status']
    
    requiredDirectionFields.forEach(field => {
      if (!(field in directionData)) {
        throw new Error(`方向${direction}中缺少必需字段: ${field}`)
      }
    })
  })
  
  // 验证建议数据结构
  if (Array.isArray(reportData.recommendations)) {
    reportData.recommendations.forEach((rec, index) => {
      const requiredRecFields = ['title', 'description', 'type', 'priority', 'expectedImprovement']
      requiredRecFields.forEach(field => {
        if (!(field in rec)) {
          throw new Error(`建议${index}中缺少必需字段: ${field}`)
        }
      })
    })
  }
  
  console.log('✅ 数据结构验证通过')
}

/**
 * 在浏览器控制台中运行测试
 */
if (typeof window !== 'undefined') {
  window.testReportDataTransformation = testReportDataTransformation
  console.log('测试函数已添加到window对象，可以在控制台中运行: testReportDataTransformation()')
}

export { mockWebSocketData, mockApiData }
