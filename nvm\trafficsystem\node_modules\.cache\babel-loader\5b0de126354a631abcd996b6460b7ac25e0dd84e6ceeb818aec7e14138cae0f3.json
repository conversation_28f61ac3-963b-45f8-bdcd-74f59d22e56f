{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"simulation-control-panel\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"config-section\"\n};\nconst _hoisted_4 = {\n  class: \"control-buttons\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"progress-section\"\n};\nconst _hoisted_6 = {\n  class: \"progress-info\"\n};\nconst _hoisted_7 = {\n  class: \"info-item\"\n};\nconst _hoisted_8 = {\n  class: \"value\"\n};\nconst _hoisted_9 = {\n  class: \"info-item\"\n};\nconst _hoisted_10 = {\n  class: \"value\"\n};\nconst _hoisted_11 = {\n  class: \"info-item\"\n};\nconst _hoisted_12 = {\n  class: \"value\"\n};\nconst _hoisted_13 = {\n  key: 2,\n  class: \"status-monitor\"\n};\nconst _hoisted_14 = {\n  key: 3,\n  class: \"error-section\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_button_group = _resolveComponent(\"el-button-group\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_statistic = _resolveComponent(\"el-statistic\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"control-card\",\n    shadow: \"hover\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[4] || (_cache[4] = _createElementVNode(\"h3\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-setting\"\n    }), _createTextVNode(\" 仿真控制面板\")], -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n      type: $setup.getStatusTagType($setup.simulationStatus),\n      size: \"large\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText($setup.simulationStatus)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])])]),\n    default: _withCtx(() => [!$setup.isSimulationRunning ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_cache[6] || (_cache[6] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-tools\"\n    }), _createTextVNode(\" 仿真配置\")], -1 /* HOISTED */)), _createVNode(_component_el_form, {\n      model: $setup.simulationConfig,\n      \"label-width\": \"120px\",\n      size: \"default\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_row, {\n        gutter: 20\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_col, {\n          span: 12\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_form_item, {\n            label: \"仿真类型\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_select, {\n              modelValue: $setup.simulationConfig.simulationType,\n              \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.simulationConfig.simulationType = $event),\n              placeholder: \"选择仿真类型\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_option, {\n                label: \"信号灯配时优化\",\n                value: \"signal_timing\"\n              }), _createVNode(_component_el_option, {\n                label: \"流量平衡优化\",\n                value: \"flow_balance\"\n              }), _createVNode(_component_el_option, {\n                label: \"综合优化分析\",\n                value: \"comprehensive\"\n              })]),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_col, {\n          span: 12\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_form_item, {\n            label: \"仿真时长\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_input_number, {\n              modelValue: $setup.simulationConfig.duration,\n              \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.simulationConfig.duration = $event),\n              min: 300,\n              max: 7200,\n              step: 300,\n              \"controls-position\": \"right\"\n            }, null, 8 /* PROPS */, [\"modelValue\"]), _cache[5] || (_cache[5] = _createElementVNode(\"span\", {\n              class: \"unit-text\"\n            }, \"秒\", -1 /* HOISTED */))]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_row, {\n        gutter: 20\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_col, {\n          span: 12\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_form_item, {\n            label: \"使用GUI\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_switch, {\n              modelValue: $setup.simulationConfig.useGui,\n              \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.simulationConfig.useGui = $event)\n            }, null, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_col, {\n          span: 12\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_form_item, {\n            label: \"随机种子\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_input_number, {\n              modelValue: $setup.simulationConfig.randomSeed,\n              \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.simulationConfig.randomSeed = $event),\n              min: 1,\n              max: 9999,\n              \"controls-position\": \"right\"\n            }, null, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button_group, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        icon: $setup.isSimulationRunning ? 'el-icon-video-pause' : 'el-icon-video-play',\n        loading: $setup.isStarting,\n        onClick: $setup.toggleSimulation,\n        disabled: !$setup.canStartSimulation\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.isSimulationRunning ? '停止仿真' : '启动仿真'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"icon\", \"loading\", \"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n        type: \"warning\",\n        icon: \"el-icon-refresh\",\n        loading: $setup.isRestarting,\n        onClick: $setup.restartSimulation,\n        disabled: !$setup.simulationId || $setup.isStarting\n      }, {\n        default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\" 重启仿真 \")])),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"loading\", \"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n        type: \"info\",\n        icon: \"el-icon-download\",\n        onClick: $setup.exportResults,\n        disabled: !$setup.hasResults\n      }, {\n        default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\" 导出结果 \")])),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\", \"disabled\"])]),\n      _: 1 /* STABLE */\n    })]), $setup.simulationId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_cache[12] || (_cache[12] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-time\"\n    }), _createTextVNode(\" 仿真进度\")], -1 /* HOISTED */)), _createVNode(_component_el_progress, {\n      percentage: $setup.simulationProgress,\n      status: $setup.getProgressStatus(),\n      \"stroke-width\": 8\n    }, null, 8 /* PROPS */, [\"percentage\", \"status\"]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n          class: \"label\"\n        }, \"仿真时间:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_8, _toDisplayString($setup.formatTime($setup.currentSimulationTime)), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n          class: \"label\"\n        }, \"车辆数量:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_10, _toDisplayString($setup.currentVehicleCount), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_11, [_cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n          class: \"label\"\n        }, \"平均速度:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_12, _toDisplayString($setup.formatSpeed($setup.averageSpeed)), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])])) : _createCommentVNode(\"v-if\", true), $setup.isSimulationRunning ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_cache[17] || (_cache[17] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-monitor\"\n    }), _createTextVNode(\" 实时监控\")], -1 /* HOISTED */)), _createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"总车辆数\",\n          value: $setup.totalVehicles,\n          suffix: \"辆\"\n        }, {\n          prefix: _withCtx(() => _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n            class: \"el-icon-truck\",\n            style: {\n              \"color\": \"#409eff\"\n            }\n          }, null, -1 /* HOISTED */)])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"通行能力\",\n          value: $setup.throughput,\n          suffix: \"辆/小时\"\n        }, {\n          prefix: _withCtx(() => _cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n            class: \"el-icon-right\",\n            style: {\n              \"color\": \"#67c23a\"\n            }\n          }, null, -1 /* HOISTED */)])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"等待时间\",\n          value: $setup.waitingTime,\n          suffix: \"秒\"\n        }, {\n          prefix: _withCtx(() => _cache[15] || (_cache[15] = [_createElementVNode(\"i\", {\n            class: \"el-icon-timer\",\n            style: {\n              \"color\": \"#e6a23c\"\n            }\n          }, null, -1 /* HOISTED */)])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"改善效果\",\n          value: $setup.improvementRate,\n          suffix: \"%\"\n        }, {\n          prefix: _withCtx(() => _cache[16] || (_cache[16] = [_createElementVNode(\"i\", {\n            class: \"el-icon-trend-charts\",\n            style: {\n              \"color\": \"#f56c6c\"\n            }\n          }, null, -1 /* HOISTED */)])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : _createCommentVNode(\"v-if\", true), $setup.errorMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createVNode(_component_el_alert, {\n      title: $setup.errorMessage,\n      type: \"error\",\n      closable: false,\n      \"show-icon\": \"\"\n    }, null, 8 /* PROPS */, [\"title\"])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "shadow", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_createTextVNode", "_component_el_tag", "type", "$setup", "getStatusTagType", "simulationStatus", "size", "default", "_toDisplayString", "getStatusText", "_", "isSimulationRunning", "_hoisted_3", "_component_el_form", "model", "simulationConfig", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_form_item", "label", "_component_el_select", "modelValue", "simulationType", "_cache", "$event", "placeholder", "_component_el_option", "value", "_component_el_input_number", "duration", "min", "max", "step", "_component_el_switch", "useGui", "randomSeed", "_createCommentVNode", "_hoisted_4", "_component_el_button_group", "_component_el_button", "icon", "loading", "isStarting", "onClick", "toggleSimulation", "disabled", "canStartSimulation", "isRestarting", "restartSimulation", "simulationId", "exportResults", "hasResults", "_hoisted_5", "_component_el_progress", "percentage", "simulationProgress", "status", "getProgressStatus", "_hoisted_6", "_hoisted_7", "_hoisted_8", "formatTime", "currentSimulationTime", "_hoisted_9", "_hoisted_10", "currentVehicleCount", "_hoisted_11", "_hoisted_12", "formatSpeed", "averageSpeed", "_hoisted_13", "_component_el_statistic", "title", "totalVehicles", "suffix", "prefix", "style", "throughput", "waitingTime", "improvementRate", "errorMessage", "_hoisted_14", "_component_el_alert", "closable"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\SimulationControlPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"simulation-control-panel\">\n    <el-card class=\"control-card\" shadow=\"hover\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3><i class=\"el-icon-setting\"></i> 仿真控制面板</h3>\n          <el-tag :type=\"getStatusTagType(simulationStatus)\" size=\"large\">\n            {{ getStatusText(simulationStatus) }}\n          </el-tag>\n        </div>\n      </template>\n\n      <!-- 仿真配置区域 -->\n      <div class=\"config-section\" v-if=\"!isSimulationRunning\">\n        <h4><i class=\"el-icon-tools\"></i> 仿真配置</h4>\n        <el-form :model=\"simulationConfig\" label-width=\"120px\" size=\"default\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"仿真类型\">\n                <el-select v-model=\"simulationConfig.simulationType\" placeholder=\"选择仿真类型\">\n                  <el-option label=\"信号灯配时优化\" value=\"signal_timing\"></el-option>\n                  <el-option label=\"流量平衡优化\" value=\"flow_balance\"></el-option>\n                  <el-option label=\"综合优化分析\" value=\"comprehensive\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"仿真时长\">\n                <el-input-number \n                  v-model=\"simulationConfig.duration\" \n                  :min=\"300\" \n                  :max=\"7200\" \n                  :step=\"300\"\n                  controls-position=\"right\">\n                </el-input-number>\n                <span class=\"unit-text\">秒</span>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          \n          <el-row :gutter=\"20\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"使用GUI\">\n                <el-switch v-model=\"simulationConfig.useGui\"></el-switch>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"随机种子\">\n                <el-input-number \n                  v-model=\"simulationConfig.randomSeed\" \n                  :min=\"1\" \n                  :max=\"9999\"\n                  controls-position=\"right\">\n                </el-input-number>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <!-- 控制按钮区域 -->\n      <div class=\"control-buttons\">\n        <el-button-group>\n          <el-button \n            type=\"primary\" \n            :icon=\"isSimulationRunning ? 'el-icon-video-pause' : 'el-icon-video-play'\"\n            :loading=\"isStarting\"\n            @click=\"toggleSimulation\"\n            :disabled=\"!canStartSimulation\">\n            {{ isSimulationRunning ? '停止仿真' : '启动仿真' }}\n          </el-button>\n          \n          <el-button \n            type=\"warning\" \n            icon=\"el-icon-refresh\"\n            :loading=\"isRestarting\"\n            @click=\"restartSimulation\"\n            :disabled=\"!simulationId || isStarting\">\n            重启仿真\n          </el-button>\n          \n          <el-button \n            type=\"info\" \n            icon=\"el-icon-download\"\n            @click=\"exportResults\"\n            :disabled=\"!hasResults\">\n            导出结果\n          </el-button>\n        </el-button-group>\n      </div>\n\n      <!-- 仿真进度区域 -->\n      <div class=\"progress-section\" v-if=\"simulationId\">\n        <h4><i class=\"el-icon-time\"></i> 仿真进度</h4>\n        <el-progress \n          :percentage=\"simulationProgress\" \n          :status=\"getProgressStatus()\"\n          :stroke-width=\"8\">\n        </el-progress>\n        \n        <div class=\"progress-info\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">仿真时间:</span>\n                <span class=\"value\">{{ formatTime(currentSimulationTime) }}</span>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">车辆数量:</span>\n                <span class=\"value\">{{ currentVehicleCount }}</span>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">平均速度:</span>\n                <span class=\"value\">{{ formatSpeed(averageSpeed) }}</span>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n      </div>\n\n      <!-- 实时状态监控 -->\n      <div class=\"status-monitor\" v-if=\"isSimulationRunning\">\n        <h4><i class=\"el-icon-monitor\"></i> 实时监控</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <el-statistic title=\"总车辆数\" :value=\"totalVehicles\" suffix=\"辆\">\n              <template #prefix>\n                <i class=\"el-icon-truck\" style=\"color: #409eff\"></i>\n              </template>\n            </el-statistic>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"通行能力\" :value=\"throughput\" suffix=\"辆/小时\">\n              <template #prefix>\n                <i class=\"el-icon-right\" style=\"color: #67c23a\"></i>\n              </template>\n            </el-statistic>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"等待时间\" :value=\"waitingTime\" suffix=\"秒\">\n              <template #prefix>\n                <i class=\"el-icon-timer\" style=\"color: #e6a23c\"></i>\n              </template>\n            </el-statistic>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"改善效果\" :value=\"improvementRate\" suffix=\"%\">\n              <template #prefix>\n                <i class=\"el-icon-trend-charts\" style=\"color: #f56c6c\"></i>\n              </template>\n            </el-statistic>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 错误信息显示 -->\n      <div class=\"error-section\" v-if=\"errorMessage\">\n        <el-alert\n          :title=\"errorMessage\"\n          type=\"error\"\n          :closable=\"false\"\n          show-icon>\n        </el-alert>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport simulationApi from '@/api/simulation'\n\nexport default {\n  name: 'SimulationControlPanel',\n  props: {\n    analysisTaskId: {\n      type: String,\n      required: true\n    },\n    trafficData: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['simulation-started', 'simulation-stopped', 'simulation-completed', 'status-updated'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const simulationId = ref('')\n    const simulationStatus = ref('idle') // idle, running, completed, failed, stopped\n    const simulationProgress = ref(0)\n    const isStarting = ref(false)\n    const isRestarting = ref(false)\n    const errorMessage = ref('')\n    \n    // 仿真配置\n    const simulationConfig = reactive({\n      simulationType: 'comprehensive',\n      duration: 3600,\n      useGui: false,\n      randomSeed: 42\n    })\n    \n    // 实时监控数据\n    const currentSimulationTime = ref(0)\n    const currentVehicleCount = ref(0)\n    const averageSpeed = ref(0)\n    const totalVehicles = ref(0)\n    const throughput = ref(0)\n    const waitingTime = ref(0)\n    const improvementRate = ref(0)\n    \n    // 状态轮询定时器\n    let statusTimer = null\n    \n    // 计算属性\n    const isSimulationRunning = computed(() => simulationStatus.value === 'running')\n    const canStartSimulation = computed(() => \n      props.analysisTaskId && !isStarting.value && simulationStatus.value !== 'running'\n    )\n    const hasResults = computed(() => \n      simulationStatus.value === 'completed' && simulationId.value\n    )\n    \n    // 方法\n    const getStatusTagType = (status) => {\n      const statusMap = {\n        'idle': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      }\n      return statusMap[status] || 'info'\n    }\n    \n    const getStatusText = (status) => {\n      const statusMap = {\n        'idle': '待启动',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      }\n      return statusMap[status] || '未知'\n    }\n    \n    const getProgressStatus = () => {\n      if (simulationStatus.value === 'failed') return 'exception'\n      if (simulationStatus.value === 'completed') return 'success'\n      return null\n    }\n    \n    const formatTime = (seconds) => {\n      const hours = Math.floor(seconds / 3600)\n      const minutes = Math.floor((seconds % 3600) / 60)\n      const secs = seconds % 60\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n    }\n    \n    const formatSpeed = (speed) => {\n      return `${speed.toFixed(1)} m/s`\n    }\n    \n    const toggleSimulation = async () => {\n      if (isSimulationRunning.value) {\n        await stopSimulation()\n      } else {\n        await startSimulation()\n      }\n    }\n    \n    const startSimulation = async () => {\n      try {\n        isStarting.value = true\n        errorMessage.value = ''\n        \n        // 创建仿真任务\n        const createResponse = await simulationApi.createSimulation({\n          analysis_task_id: props.analysisTaskId,\n          simulation_type: simulationConfig.simulationType,\n          user_id: localStorage.getItem('userId'),\n          username: localStorage.getItem('username')\n        })\n        \n        if (createResponse.status === 'success') {\n          simulationId.value = createResponse.simulation_task.simulationId\n          \n          // 启动仿真\n          const startResponse = await simulationApi.startSimulation(simulationId.value, {\n            use_gui: simulationConfig.useGui\n          })\n          \n          if (startResponse.status === 'success') {\n            simulationStatus.value = 'running'\n            startStatusPolling()\n            emit('simulation-started', simulationId.value)\n            ElMessage.success('仿真启动成功')\n          } else {\n            throw new Error(startResponse.message || '启动仿真失败')\n          }\n        } else {\n          throw new Error(createResponse.message || '创建仿真任务失败')\n        }\n        \n      } catch (error) {\n        console.error('启动仿真失败:', error)\n        errorMessage.value = error.message || '启动仿真失败'\n        ElMessage.error(errorMessage.value)\n      } finally {\n        isStarting.value = false\n      }\n    }\n    \n    const stopSimulation = async () => {\n      try {\n        const response = await simulationApi.stopSimulation(simulationId.value)\n        \n        if (response.status === 'success') {\n          simulationStatus.value = 'stopped'\n          stopStatusPolling()\n          emit('simulation-stopped', simulationId.value)\n          ElMessage.success('仿真已停止')\n        } else {\n          throw new Error(response.message || '停止仿真失败')\n        }\n        \n      } catch (error) {\n        console.error('停止仿真失败:', error)\n        ElMessage.error(error.message || '停止仿真失败')\n      }\n    }\n    \n    const restartSimulation = async () => {\n      try {\n        isRestarting.value = true\n        \n        await ElMessageBox.confirm('确定要重启仿真吗？', '确认重启', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        await stopSimulation()\n        setTimeout(async () => {\n          await startSimulation()\n          isRestarting.value = false\n        }, 2000)\n        \n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('重启仿真失败:', error)\n          ElMessage.error('重启仿真失败')\n        }\n        isRestarting.value = false\n      }\n    }\n    \n    const exportResults = async () => {\n      try {\n        const response = await simulationApi.exportResults(simulationId.value, 'json')\n        \n        if (response.status === 'success') {\n          ElMessage.success('结果导出成功')\n        } else {\n          ElMessage.warning('导出功能暂未实现')\n        }\n        \n      } catch (error) {\n        console.error('导出结果失败:', error)\n        ElMessage.error('导出结果失败')\n      }\n    }\n    \n    const startStatusPolling = () => {\n      if (statusTimer) {\n        clearInterval(statusTimer)\n      }\n      \n      statusTimer = setInterval(async () => {\n        try {\n          const response = await simulationApi.getSimulationStatus(simulationId.value)\n          \n          if (response.status) {\n            simulationStatus.value = response.status\n            simulationProgress.value = response.progress || 0\n            \n            // 更新实时数据\n            if (response.sumo_status) {\n              const sumoStatus = response.sumo_status\n              currentSimulationTime.value = sumoStatus.current_time || 0\n              currentVehicleCount.value = sumoStatus.vehicle_count || 0\n              totalVehicles.value = sumoStatus.total_vehicles || 0\n            }\n            \n            // 检查仿真是否完成\n            if (simulationStatus.value === 'completed') {\n              stopStatusPolling()\n              emit('simulation-completed', simulationId.value)\n              ElMessage.success('仿真已完成')\n            } else if (simulationStatus.value === 'failed') {\n              stopStatusPolling()\n              errorMessage.value = response.error_message || '仿真执行失败'\n              ElMessage.error(errorMessage.value)\n            }\n            \n            emit('status-updated', response)\n          }\n          \n        } catch (error) {\n          console.error('获取仿真状态失败:', error)\n        }\n      }, 2000) // 每2秒轮询一次\n    }\n    \n    const stopStatusPolling = () => {\n      if (statusTimer) {\n        clearInterval(statusTimer)\n        statusTimer = null\n      }\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      // 组件挂载时的初始化逻辑\n    })\n    \n    onUnmounted(() => {\n      stopStatusPolling()\n    })\n    \n    return {\n      // 响应式数据\n      simulationId,\n      simulationStatus,\n      simulationProgress,\n      isStarting,\n      isRestarting,\n      errorMessage,\n      simulationConfig,\n      currentSimulationTime,\n      currentVehicleCount,\n      averageSpeed,\n      totalVehicles,\n      throughput,\n      waitingTime,\n      improvementRate,\n      \n      // 计算属性\n      isSimulationRunning,\n      canStartSimulation,\n      hasResults,\n      \n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getProgressStatus,\n      formatTime,\n      formatSpeed,\n      toggleSimulation,\n      restartSimulation,\n      exportResults\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simulation-control-panel {\n  margin-bottom: 20px;\n}\n\n.control-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  color: #303133;\n}\n\n.config-section,\n.progress-section,\n.status-monitor {\n  margin-bottom: 20px;\n}\n\n.config-section h4,\n.progress-section h4,\n.status-monitor h4 {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n}\n\n.control-buttons {\n  text-align: center;\n  margin: 20px 0;\n}\n\n.unit-text {\n  margin-left: 8px;\n  color: #909399;\n}\n\n.progress-info {\n  margin-top: 15px;\n}\n\n.info-item {\n  text-align: center;\n}\n\n.info-item .label {\n  display: block;\n  color: #909399;\n  font-size: 12px;\n  margin-bottom: 4px;\n}\n\n.info-item .value {\n  display: block;\n  color: #303133;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.error-section {\n  margin-top: 15px;\n}\n\n.el-statistic {\n  text-align: center;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;EAG1BA,KAAK,EAAC;AAAa;;EAJhCC,GAAA;EAaWD,KAAK,EAAC;;;EAgDNA,KAAK,EAAC;AAAiB;;EA7DlCC,GAAA;EA4FWD,KAAK,EAAC;;;EAQJA,KAAK,EAAC;AAAe;;EAGfA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAO;;EAIhBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAO;;EAIhBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAO;;EArHnCC,GAAA;EA6HWD,KAAK,EAAC;;;EA7HjBC,GAAA;EAgKWD,KAAK,EAAC;;;;;;;;;;;;;;;;;;uBA/JfE,mBAAA,CAwKM,OAxKNC,UAwKM,GAvKJC,YAAA,CAsKUC,kBAAA;IAtKDL,KAAK,EAAC,cAAc;IAACM,MAAM,EAAC;;IACxBC,MAAM,EAAAC,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALNC,UAKM,G,0BAJJD,mBAAA,CAA+C,aAA3CA,mBAAA,CAA+B;MAA5BT,KAAK,EAAC;IAAiB,IALxCW,gBAAA,CAK6C,SAAO,E,sBAC1CP,YAAA,CAESQ,iBAAA;MAFAC,IAAI,EAAEC,MAAA,CAAAC,gBAAgB,CAACD,MAAA,CAAAE,gBAAgB;MAAGC,IAAI,EAAC;;MANlEC,OAAA,EAAAV,QAAA,CAOY,MAAqC,CAPjDG,gBAAA,CAAAQ,gBAAA,CAOeL,MAAA,CAAAM,aAAa,CAACN,MAAA,CAAAE,gBAAgB,kB;MAP7CK,CAAA;;IAAAH,OAAA,EAAAV,QAAA,CAYoB,MAyCoB,C,CAxCCM,MAAA,CAAAQ,mBAAmB,I,cAAtDpB,mBAAA,CA6CM,OA7CNqB,UA6CM,G,0BA5CJd,mBAAA,CAA2C,aAAvCA,mBAAA,CAA6B;MAA1BT,KAAK,EAAC;IAAe,IAdpCW,gBAAA,CAcyC,OAAK,E,sBACtCP,YAAA,CA0CUoB,kBAAA;MA1CAC,KAAK,EAAEX,MAAA,CAAAY,gBAAgB;MAAE,aAAW,EAAC,OAAO;MAACT,IAAI,EAAC;;MAfpEC,OAAA,EAAAV,QAAA,CAgBU,MAsBS,CAtBTJ,YAAA,CAsBSuB,iBAAA;QAtBAC,MAAM,EAAE;MAAE;QAhB7BV,OAAA,EAAAV,QAAA,CAiBY,MAQS,CARTJ,YAAA,CAQSyB,iBAAA;UARAC,IAAI,EAAE;QAAE;UAjB7BZ,OAAA,EAAAV,QAAA,CAkBc,MAMe,CANfJ,YAAA,CAMe2B,uBAAA;YANDC,KAAK,EAAC;UAAM;YAlBxCd,OAAA,EAAAV,QAAA,CAmBgB,MAIY,CAJZJ,YAAA,CAIY6B,oBAAA;cAvB5BC,UAAA,EAmBoCpB,MAAA,CAAAY,gBAAgB,CAACS,cAAc;cAnBnE,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAmBoCvB,MAAA,CAAAY,gBAAgB,CAACS,cAAc,GAAAE,MAAA;cAAEC,WAAW,EAAC;;cAnBjFpB,OAAA,EAAAV,QAAA,CAoBkB,MAA6D,CAA7DJ,YAAA,CAA6DmC,oBAAA;gBAAlDP,KAAK,EAAC,SAAS;gBAACQ,KAAK,EAAC;kBACjCpC,YAAA,CAA2DmC,oBAAA;gBAAhDP,KAAK,EAAC,QAAQ;gBAACQ,KAAK,EAAC;kBAChCpC,YAAA,CAA4DmC,oBAAA;gBAAjDP,KAAK,EAAC,QAAQ;gBAACQ,KAAK,EAAC;;cAtBlDnB,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;YA0BYjB,YAAA,CAWSyB,iBAAA;UAXAC,IAAI,EAAE;QAAE;UA1B7BZ,OAAA,EAAAV,QAAA,CA2Bc,MASe,CATfJ,YAAA,CASe2B,uBAAA;YATDC,KAAK,EAAC;UAAM;YA3BxCd,OAAA,EAAAV,QAAA,CA4BgB,MAMkB,CANlBJ,YAAA,CAMkBqC,0BAAA;cAlClCP,UAAA,EA6B2BpB,MAAA,CAAAY,gBAAgB,CAACgB,QAAQ;cA7BpD,uBAAAN,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA6B2BvB,MAAA,CAAAY,gBAAgB,CAACgB,QAAQ,GAAAL,MAAA;cACjCM,GAAG,EAAE,GAAG;cACRC,GAAG,EAAE,IAAI;cACTC,IAAI,EAAE,GAAG;cACV,mBAAiB,EAAC;+EAEpBpC,mBAAA,CAAgC;cAA1BT,KAAK,EAAC;YAAW,GAAC,GAAC,qB;YAnCzCqB,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAwCUjB,YAAA,CAgBSuB,iBAAA;QAhBAC,MAAM,EAAE;MAAE;QAxC7BV,OAAA,EAAAV,QAAA,CAyCY,MAIS,CAJTJ,YAAA,CAISyB,iBAAA;UAJAC,IAAI,EAAE;QAAE;UAzC7BZ,OAAA,EAAAV,QAAA,CA0Cc,MAEe,CAFfJ,YAAA,CAEe2B,uBAAA;YAFDC,KAAK,EAAC;UAAO;YA1CzCd,OAAA,EAAAV,QAAA,CA2CgB,MAAyD,CAAzDJ,YAAA,CAAyD0C,oBAAA;cA3CzEZ,UAAA,EA2CoCpB,MAAA,CAAAY,gBAAgB,CAACqB,MAAM;cA3C3D,uBAAAX,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2CoCvB,MAAA,CAAAY,gBAAgB,CAACqB,MAAM,GAAAV,MAAA;;YA3C3DhB,CAAA;;UAAAA,CAAA;YA8CYjB,YAAA,CASSyB,iBAAA;UATAC,IAAI,EAAE;QAAE;UA9C7BZ,OAAA,EAAAV,QAAA,CA+Cc,MAOe,CAPfJ,YAAA,CAOe2B,uBAAA;YAPDC,KAAK,EAAC;UAAM;YA/CxCd,OAAA,EAAAV,QAAA,CAgDgB,MAKkB,CALlBJ,YAAA,CAKkBqC,0BAAA;cArDlCP,UAAA,EAiD2BpB,MAAA,CAAAY,gBAAgB,CAACsB,UAAU;cAjDtD,uBAAAZ,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAiD2BvB,MAAA,CAAAY,gBAAgB,CAACsB,UAAU,GAAAX,MAAA;cACnCM,GAAG,EAAE,CAAC;cACNC,GAAG,EAAE,IAAI;cACV,mBAAiB,EAAC;;YApDpCvB,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;sCAAA4B,mBAAA,gBA6DMxC,mBAAA,CA4BM,OA5BNyC,UA4BM,GA3BJ9C,YAAA,CA0BkB+C,0BAAA;MAxF1BjC,OAAA,EAAAV,QAAA,CA+DU,MAOY,CAPZJ,YAAA,CAOYgD,oBAAA;QANVvC,IAAI,EAAC,SAAS;QACbwC,IAAI,EAAEvC,MAAA,CAAAQ,mBAAmB;QACzBgC,OAAO,EAAExC,MAAA,CAAAyC,UAAU;QACnBC,OAAK,EAAE1C,MAAA,CAAA2C,gBAAgB;QACvBC,QAAQ,GAAG5C,MAAA,CAAA6C;;QApExBzC,OAAA,EAAAV,QAAA,CAqEY,MAA2C,CArEvDG,gBAAA,CAAAQ,gBAAA,CAqEeL,MAAA,CAAAQ,mBAAmB,mC;QArElCD,CAAA;qEAwEUjB,YAAA,CAOYgD,oBAAA;QANVvC,IAAI,EAAC,SAAS;QACdwC,IAAI,EAAC,iBAAiB;QACrBC,OAAO,EAAExC,MAAA,CAAA8C,YAAY;QACrBJ,OAAK,EAAE1C,MAAA,CAAA+C,iBAAiB;QACxBH,QAAQ,GAAG5C,MAAA,CAAAgD,YAAY,IAAIhD,MAAA,CAAAyC;;QA7ExCrC,OAAA,EAAAV,QAAA,CA6EoD,MAE1C4B,MAAA,QAAAA,MAAA,OA/EVzB,gBAAA,CA6EoD,QAE1C,E;QA/EVU,CAAA;6DAiFUjB,YAAA,CAMYgD,oBAAA;QALVvC,IAAI,EAAC,MAAM;QACXwC,IAAI,EAAC,kBAAkB;QACtBG,OAAK,EAAE1C,MAAA,CAAAiD,aAAa;QACpBL,QAAQ,GAAG5C,MAAA,CAAAkD;;QArFxB9C,OAAA,EAAAV,QAAA,CAqFoC,MAE1B4B,MAAA,QAAAA,MAAA,OAvFVzB,gBAAA,CAqFoC,QAE1B,E;QAvFVU,CAAA;;MAAAA,CAAA;UA4F0CP,MAAA,CAAAgD,YAAY,I,cAAhD5D,mBAAA,CA8BM,OA9BN+D,UA8BM,G,4BA7BJxD,mBAAA,CAA0C,aAAtCA,mBAAA,CAA4B;MAAzBT,KAAK,EAAC;IAAc,IA7FnCW,gBAAA,CA6FwC,OAAK,E,sBACrCP,YAAA,CAIc8D,sBAAA;MAHXC,UAAU,EAAErD,MAAA,CAAAsD,kBAAkB;MAC9BC,MAAM,EAAEvD,MAAA,CAAAwD,iBAAiB;MACzB,cAAY,EAAE;uDAGjB7D,mBAAA,CAqBM,OArBN8D,UAqBM,GApBJnE,YAAA,CAmBSuB,iBAAA;MAnBAC,MAAM,EAAE;IAAE;MArG7BV,OAAA,EAAAV,QAAA,CAsGY,MAKS,CALTJ,YAAA,CAKSyB,iBAAA;QALAC,IAAI,EAAE;MAAC;QAtG5BZ,OAAA,EAAAV,QAAA,CAuGc,MAGM,CAHNC,mBAAA,CAGM,OAHN+D,UAGM,G,0BAFJ/D,mBAAA,CAAgC;UAA1BT,KAAK,EAAC;QAAO,GAAC,OAAK,sBACzBS,mBAAA,CAAkE,QAAlEgE,UAAkE,EAAAtD,gBAAA,CAA3CL,MAAA,CAAA4D,UAAU,CAAC5D,MAAA,CAAA6D,qBAAqB,kB;QAzGvEtD,CAAA;UA4GYjB,YAAA,CAKSyB,iBAAA;QALAC,IAAI,EAAE;MAAC;QA5G5BZ,OAAA,EAAAV,QAAA,CA6Gc,MAGM,CAHNC,mBAAA,CAGM,OAHNmE,UAGM,G,4BAFJnE,mBAAA,CAAgC;UAA1BT,KAAK,EAAC;QAAO,GAAC,OAAK,sBACzBS,mBAAA,CAAoD,QAApDoE,WAAoD,EAAA1D,gBAAA,CAA7BL,MAAA,CAAAgE,mBAAmB,iB;QA/G1DzD,CAAA;UAkHYjB,YAAA,CAKSyB,iBAAA;QALAC,IAAI,EAAE;MAAC;QAlH5BZ,OAAA,EAAAV,QAAA,CAmHc,MAGM,CAHNC,mBAAA,CAGM,OAHNsE,WAGM,G,4BAFJtE,mBAAA,CAAgC;UAA1BT,KAAK,EAAC;QAAO,GAAC,OAAK,sBACzBS,mBAAA,CAA0D,QAA1DuE,WAA0D,EAAA7D,gBAAA,CAAnCL,MAAA,CAAAmE,WAAW,CAACnE,MAAA,CAAAoE,YAAY,kB;QArH/D7D,CAAA;;MAAAA,CAAA;cAAA4B,mBAAA,gBA6HwCnC,MAAA,CAAAQ,mBAAmB,I,cAArDpB,mBAAA,CAgCM,OAhCNiF,WAgCM,G,4BA/BJ1E,mBAAA,CAA6C,aAAzCA,mBAAA,CAA+B;MAA5BT,KAAK,EAAC;IAAiB,IA9HtCW,gBAAA,CA8H2C,OAAK,E,sBACxCP,YAAA,CA6BSuB,iBAAA;MA7BAC,MAAM,EAAE;IAAE;MA/H3BV,OAAA,EAAAV,QAAA,CAgIU,MAMS,CANTJ,YAAA,CAMSyB,iBAAA;QANAC,IAAI,EAAE;MAAC;QAhI1BZ,OAAA,EAAAV,QAAA,CAiIY,MAIe,CAJfJ,YAAA,CAIegF,uBAAA;UAJDC,KAAK,EAAC,MAAM;UAAE7C,KAAK,EAAE1B,MAAA,CAAAwE,aAAa;UAAEC,MAAM,EAAC;;UAC5CC,MAAM,EAAAhF,QAAA,CACf,MAAoD4B,MAAA,SAAAA,MAAA,QAApD3B,mBAAA,CAAoD;YAAjDT,KAAK,EAAC,eAAe;YAACyF,KAAsB,EAAtB;cAAA;YAAA;;UAnIzCpE,CAAA;;QAAAA,CAAA;UAuIUjB,YAAA,CAMSyB,iBAAA;QANAC,IAAI,EAAE;MAAC;QAvI1BZ,OAAA,EAAAV,QAAA,CAwIY,MAIe,CAJfJ,YAAA,CAIegF,uBAAA;UAJDC,KAAK,EAAC,MAAM;UAAE7C,KAAK,EAAE1B,MAAA,CAAA4E,UAAU;UAAEH,MAAM,EAAC;;UACzCC,MAAM,EAAAhF,QAAA,CACf,MAAoD4B,MAAA,SAAAA,MAAA,QAApD3B,mBAAA,CAAoD;YAAjDT,KAAK,EAAC,eAAe;YAACyF,KAAsB,EAAtB;cAAA;YAAA;;UA1IzCpE,CAAA;;QAAAA,CAAA;UA8IUjB,YAAA,CAMSyB,iBAAA;QANAC,IAAI,EAAE;MAAC;QA9I1BZ,OAAA,EAAAV,QAAA,CA+IY,MAIe,CAJfJ,YAAA,CAIegF,uBAAA;UAJDC,KAAK,EAAC,MAAM;UAAE7C,KAAK,EAAE1B,MAAA,CAAA6E,WAAW;UAAEJ,MAAM,EAAC;;UAC1CC,MAAM,EAAAhF,QAAA,CACf,MAAoD4B,MAAA,SAAAA,MAAA,QAApD3B,mBAAA,CAAoD;YAAjDT,KAAK,EAAC,eAAe;YAACyF,KAAsB,EAAtB;cAAA;YAAA;;UAjJzCpE,CAAA;;QAAAA,CAAA;UAqJUjB,YAAA,CAMSyB,iBAAA;QANAC,IAAI,EAAE;MAAC;QArJ1BZ,OAAA,EAAAV,QAAA,CAsJY,MAIe,CAJfJ,YAAA,CAIegF,uBAAA;UAJDC,KAAK,EAAC,MAAM;UAAE7C,KAAK,EAAE1B,MAAA,CAAA8E,eAAe;UAAEL,MAAM,EAAC;;UAC9CC,MAAM,EAAAhF,QAAA,CACf,MAA2D4B,MAAA,SAAAA,MAAA,QAA3D3B,mBAAA,CAA2D;YAAxDT,KAAK,EAAC,sBAAsB;YAACyF,KAAsB,EAAtB;cAAA;YAAA;;UAxJhDpE,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;YAAA4B,mBAAA,gBAgKuCnC,MAAA,CAAA+E,YAAY,I,cAA7C3F,mBAAA,CAOM,OAPN4F,WAOM,GANJ1F,YAAA,CAKW2F,mBAAA;MAJRV,KAAK,EAAEvE,MAAA,CAAA+E,YAAY;MACpBhF,IAAI,EAAC,OAAO;MACXmF,QAAQ,EAAE,KAAK;MAChB,WAAS,EAAT;4CArKV/C,mBAAA,e;IAAA5B,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}