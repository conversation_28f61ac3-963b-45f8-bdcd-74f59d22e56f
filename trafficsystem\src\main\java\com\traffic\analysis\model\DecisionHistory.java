package com.traffic.analysis.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 决策历史模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "decision_history")
public class DecisionHistory {
    
    @Id
    private String id;
    
    /**
     * 仿真ID
     */
    private String simulationId;
    
    /**
     * 决策建议ID
     */
    private String suggestionId;
    
    /**
     * 决策类型
     */
    private String decisionType;
    
    /**
     * 决策动作
     */
    private String action;
    
    /**
     * 决策描述
     */
    private String description;
    
    /**
     * 决策参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 决策前状态
     */
    private Map<String, Object> beforeState;
    
    /**
     * 决策后状态
     */
    private Map<String, Object> afterState;
    
    /**
     * 决策结果
     */
    private Map<String, Object> result;
    
    /**
     * 效果评估
     */
    private Map<String, Object> effectEvaluation;
    
    /**
     * 决策者ID
     */
    private String decisionMakerId;
    
    /**
     * 决策者类型 (user, system, auto)
     */
    private String decisionMakerType;
    
    /**
     * 决策时间
     */
    private LocalDateTime decisionTime;
    
    /**
     * 执行时间
     */
    private LocalDateTime executionTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completionTime;
    
    /**
     * 决策状态 (pending, executing, completed, failed)
     */
    private String status;
    
    /**
     * 成功标志
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long executionDuration;
    
    /**
     * 影响范围
     */
    private String impactScope;
    
    /**
     * 置信度
     */
    private Double confidence;
    
    /**
     * 实际效果评分
     */
    private Double actualEffectScore;
    
    /**
     * 预期效果评分
     */
    private Double expectedEffectScore;
    
    /**
     * 效果差异
     */
    private Double effectDifference;
    
    /**
     * 相关指标
     */
    private Map<String, Object> metrics;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 是否成功
     */
    public boolean isSuccessful() {
        return Boolean.TRUE.equals(success);
    }
    
    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return "completed".equals(status);
    }
    
    /**
     * 是否失败
     */
    public boolean isFailed() {
        return "failed".equals(status);
    }
    
    /**
     * 是否正在执行
     */
    public boolean isExecuting() {
        return "executing".equals(status);
    }
    
    /**
     * 获取执行耗时（秒）
     */
    public Double getExecutionDurationInSeconds() {
        return executionDuration != null ? executionDuration / 1000.0 : null;
    }
    
    /**
     * 计算效果差异百分比
     */
    public Double getEffectDifferencePercentage() {
        if (expectedEffectScore != null && expectedEffectScore > 0 && effectDifference != null) {
            return (effectDifference / expectedEffectScore) * 100;
        }
        return null;
    }
    
    /**
     * 获取决策摘要
     */
    public String getSummary() {
        return String.format("[%s] %s - %s", 
            decisionType != null ? decisionType : "UNKNOWN",
            action != null ? action : "无动作",
            isSuccessful() ? "成功" : "失败"
        );
    }
    
    /**
     * 标记为执行中
     */
    public void markAsExecuting() {
        this.status = "executing";
        this.executionTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 标记为完成
     */
    public void markAsCompleted(boolean success, Map<String, Object> result) {
        this.status = "completed";
        this.success = success;
        this.result = result;
        this.completionTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        
        // 计算执行耗时
        if (this.executionTime != null) {
            this.executionDuration = java.time.Duration.between(this.executionTime, this.completionTime).toMillis();
        }
    }
    
    /**
     * 标记为失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = "failed";
        this.success = false;
        this.errorMessage = errorMessage;
        this.completionTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        
        // 计算执行耗时
        if (this.executionTime != null) {
            this.executionDuration = java.time.Duration.between(this.executionTime, this.completionTime).toMillis();
        }
    }
    
    /**
     * 更新效果评估
     */
    public void updateEffectEvaluation(Map<String, Object> evaluation) {
        this.effectEvaluation = evaluation;
        this.updatedAt = LocalDateTime.now();
        
        // 提取评分信息
        if (evaluation != null) {
            Object actualScore = evaluation.get("actual_effect_score");
            if (actualScore instanceof Number) {
                this.actualEffectScore = ((Number) actualScore).doubleValue();
            }
            
            Object expectedScore = evaluation.get("expected_effect_score");
            if (expectedScore instanceof Number) {
                this.expectedEffectScore = ((Number) expectedScore).doubleValue();
            }
            
            // 计算效果差异
            if (this.actualEffectScore != null && this.expectedEffectScore != null) {
                this.effectDifference = this.actualEffectScore - this.expectedEffectScore;
            }
        }
    }
    
    /**
     * 添加指标
     */
    public void addMetric(String key, Object value) {
        if (this.metrics == null) {
            this.metrics = new java.util.HashMap<>();
        }
        this.metrics.put(key, value);
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 获取指标值
     */
    public Object getMetric(String key) {
        return this.metrics != null ? this.metrics.get(key) : null;
    }
}
