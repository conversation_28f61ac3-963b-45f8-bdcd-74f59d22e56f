import request from '@/utils/request'

/**
 * 决策支持API
 */
const decisionApi = {
  /**
   * 生成决策建议
   * @param {Object} data - 请求数据
   * @param {string} data.simulation_id - 仿真ID
   * @param {Object} data.traffic_data - 交通数据
   * @param {Object} data.current_conditions - 当前条件
   * @returns {Promise} API响应
   */
  generateSuggestions(data) {
    return request({
      url: '/api/decision/suggestions/generate',
      method: 'post',
      data
    })
  },

  /**
   * 获取实时决策支持
   * @param {Object} data - 请求数据
   * @param {string} data.simulation_id - 仿真ID
   * @param {Object} data.current_state - 当前状态
   * @returns {Promise} API响应
   */
  getRealtimeSupport(data) {
    return request({
      url: '/api/decision/realtime',
      method: 'post',
      data
    })
  },

  /**
   * 应用决策建议
   * @param {string} suggestionId - 建议ID
   * @param {Object} data - 请求数据
   * @param {string} data.simulation_id - 仿真ID
   * @param {string} data.user_id - 用户ID
   * @returns {Promise} API响应
   */
  applySuggestion(suggestionId, data) {
    return request({
      url: `/api/decision/suggestions/${suggestionId}/apply`,
      method: 'post',
      data
    })
  },

  /**
   * 获取决策历史
   * @param {Object} params - 查询参数
   * @param {string} params.simulation_id - 仿真ID（可选）
   * @param {string} params.user_id - 用户ID（可选）
   * @param {number} params.page - 页码
   * @param {number} params.size - 页大小
   * @returns {Promise} API响应
   */
  getDecisionHistory(params) {
    return request({
      url: '/api/decision/history',
      method: 'get',
      params
    })
  },

  /**
   * 获取决策建议详情
   * @param {string} suggestionId - 建议ID
   * @returns {Promise} API响应
   */
  getSuggestionDetail(suggestionId) {
    return request({
      url: `/api/decision/suggestions/${suggestionId}`,
      method: 'get'
    })
  },

  /**
   * 评估决策效果
   * @param {Object} data - 请求数据
   * @param {string} data.simulation_id - 仿真ID
   * @param {string} data.decision_id - 决策ID
   * @param {Object} data.before_data - 决策前数据
   * @param {Object} data.after_data - 决策后数据
   * @returns {Promise} API响应
   */
  evaluateDecisionEffect(data) {
    return request({
      url: '/api/decision/evaluation',
      method: 'post',
      data
    })
  },

  /**
   * 获取决策统计信息
   * @param {Object} params - 查询参数
   * @param {string} params.simulation_id - 仿真ID（可选）
   * @param {string} params.user_id - 用户ID（可选）
   * @returns {Promise} API响应
   */
  getDecisionStatistics(params) {
    return request({
      url: '/api/decision/statistics',
      method: 'get',
      params
    })
  },

  /**
   * 检查决策支持服务状态
   * @returns {Promise} API响应
   */
  checkServiceStatus() {
    return request({
      url: '/api/decision/service/status',
      method: 'get'
    })
  },

  /**
   * 信号灯控制
   * @param {Object} data - 请求数据
   * @param {string} data.simulation_id - 仿真ID
   * @param {Object} data.control_request - 控制请求
   * @returns {Promise} API响应
   */
  controlTrafficLights(data) {
    return request({
      url: '/api/decision/traffic-lights/control',
      method: 'post',
      data
    })
  },

  /**
   * 手动信号灯控制
   * @param {string} simulationId - 仿真ID
   * @param {Object} data - 控制数据
   * @param {Object} data.lights - 信号灯状态
   * @param {string} data.timestamp - 时间戳
   * @returns {Promise} API响应
   */
  manualTrafficLightControl(simulationId, data) {
    return request({
      url: `/api/simulation/${simulationId}/traffic-lights/manual`,
      method: 'post',
      data
    })
  },

  /**
   * 恢复自动信号灯控制
   * @param {string} simulationId - 仿真ID
   * @returns {Promise} API响应
   */
  autoTrafficLightControl(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}/traffic-lights/auto`,
      method: 'post'
    })
  },

  /**
   * 紧急信号灯控制
   * @param {string} simulationId - 仿真ID
   * @returns {Promise} API响应
   */
  emergencyTrafficLightControl(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}/traffic-lights/emergency`,
      method: 'post'
    })
  },

  /**
   * 获取信号灯状态
   * @param {string} simulationId - 仿真ID
   * @returns {Promise} API响应
   */
  getTrafficLightStatus(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}/traffic-lights/status`,
      method: 'get'
    })
  },

  /**
   * 获取信号灯历史记录
   * @param {string} simulationId - 仿真ID
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getTrafficLightHistory(simulationId, params) {
    return request({
      url: `/api/simulation/${simulationId}/traffic-lights/history`,
      method: 'get',
      params
    })
  },

  /**
   * 优化信号配时
   * @param {Object} data - 请求数据
   * @param {Object} data.traffic_data - 交通数据
   * @param {Object} data.current_timing - 当前配时
   * @returns {Promise} API响应
   */
  optimizeSignalTiming(data) {
    return request({
      url: '/api/simulation/optimization/signal',
      method: 'post',
      data
    })
  },

  /**
   * 流量平衡优化
   * @param {Object} data - 请求数据
   * @param {Object} data.traffic_data - 交通数据
   * @returns {Promise} API响应
   */
  optimizeFlowBalance(data) {
    return request({
      url: '/api/simulation/optimization/flow',
      method: 'post',
      data
    })
  },

  /**
   * 综合优化分析
   * @param {Object} data - 请求数据
   * @param {Object} data.traffic_data - 交通数据
   * @returns {Promise} API响应
   */
  comprehensiveOptimization(data) {
    return request({
      url: '/api/simulation/optimization/comprehensive',
      method: 'post',
      data
    })
  },

  /**
   * 获取优化建议
   * @param {string} simulationId - 仿真ID
   * @param {Object} trafficData - 交通数据
   * @returns {Promise} API响应
   */
  getOptimizationSuggestions(simulationId, trafficData) {
    return this.generateSuggestions({
      simulation_id: simulationId,
      traffic_data: trafficData,
      current_conditions: {}
    })
  },

  /**
   * 批量应用决策建议
   * @param {Array} suggestionIds - 建议ID数组
   * @param {Object} data - 请求数据
   * @returns {Promise} API响应
   */
  async batchApplySuggestions(suggestionIds, data) {
    const results = []
    
    for (const suggestionId of suggestionIds) {
      try {
        const result = await this.applySuggestion(suggestionId, data)
        results.push({ suggestionId, result, success: true })
      } catch (error) {
        results.push({ suggestionId, error, success: false })
      }
    }
    
    return {
      status: 'success',
      results,
      total: suggestionIds.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    }
  }
}

export default decisionApi
