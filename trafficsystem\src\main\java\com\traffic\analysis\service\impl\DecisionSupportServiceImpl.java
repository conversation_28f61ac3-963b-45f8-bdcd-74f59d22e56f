package com.traffic.analysis.service.impl;

import com.traffic.analysis.service.DecisionSupportService;
import com.traffic.analysis.model.DecisionSuggestion;
import com.traffic.analysis.model.DecisionHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 决策支持服务实现类
 */
@Slf4j
@Service
public class DecisionSupportServiceImpl implements DecisionSupportService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${python.model.url:http://localhost:5001}")
    private String pythonModelUrl;

    @Value("${python.decision.url:http://localhost:5002}")
    private String pythonDecisionUrl;

    // 拥堵阈值配置
    private static final Map<String, Double> CONGESTION_THRESHOLDS = Map.of(
        "low", 0.3,
        "medium", 0.6,
        "high", 0.8
    );

    // 权重配置
    private static final Map<String, Double> OPTIMIZATION_WEIGHTS = Map.of(
        "delay", 0.4,
        "throughput", 0.3,
        "fuel", 0.2,
        "safety", 0.1
    );

    @Override
    public Map<String, Object> generateIntelligentDecisions(Map<String, Object> trafficData, Map<String, Object> analysisResult) {
        try {
            log.info("生成智能决策建议");

            Map<String, Object> decisions = new HashMap<>();

            // 评估交通状态
            Map<String, Object> trafficState = evaluateTrafficState(trafficData);
            decisions.put("traffic_state", trafficState);

            // 计算拥堵等级
            Map<String, Object> congestionLevel = calculateCongestionLevel(trafficData);
            decisions.put("congestion_analysis", congestionLevel);

            // 生成自适应信号配时
            Map<String, Object> adaptiveTiming = generateAdaptiveSignalTiming(trafficData, null);
            decisions.put("adaptive_timing", adaptiveTiming);

            // 生成优化建议
            List<Map<String, Object>> recommendations = generateOptimizationRecommendations(trafficData, analysisResult);
            decisions.put("recommendations", recommendations);

            // 分析瓶颈
            Map<String, Object> bottlenecks = analyzeBottlenecks(trafficData);
            decisions.put("bottleneck_analysis", bottlenecks);

            // 生成决策摘要
            Map<String, Object> summary = generateDecisionSummary(trafficState, congestionLevel, recommendations);
            decisions.put("decision_summary", summary);

            decisions.put("timestamp", LocalDateTime.now());
            decisions.put("status", "success");

            return decisions;

        } catch (Exception e) {
            log.error("生成智能决策失败: {}", e.getMessage(), e);
            return createErrorResponse("生成智能决策失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> evaluateTrafficState(Map<String, Object> trafficData) {
        try {
            log.info("评估交通状态");

            Map<String, Object> evaluation = new HashMap<>();

            // 提取方向数据
            Map<String, Object> directions = (Map<String, Object>) trafficData.get("directions");
            if (directions == null) {
                directions = new HashMap<>();
            }

            // 评估各方向状态
            Map<String, Object> directionStates = new HashMap<>();
            double totalCongestion = 0.0;
            int directionCount = 0;

            for (Map.Entry<String, Object> entry : directions.entrySet()) {
                String direction = entry.getKey();
                Map<String, Object> directionData = (Map<String, Object>) entry.getValue();

                Map<String, Object> directionState = evaluateDirectionState(directionData);
                directionStates.put(direction, directionState);

                Double congestionIndex = (Double) directionState.get("congestion_index");
                if (congestionIndex != null) {
                    totalCongestion += congestionIndex;
                    directionCount++;
                }
            }

            evaluation.put("direction_states", directionStates);

            // 计算总体状态
            if (directionCount > 0) {
                double avgCongestion = totalCongestion / directionCount;
                evaluation.put("overall_congestion", avgCongestion);
                evaluation.put("overall_level", getCongestionLevel(avgCongestion));
            } else {
                evaluation.put("overall_congestion", 0.0);
                evaluation.put("overall_level", "unknown");
            }

            // 计算性能指标
            Map<String, Object> performance = calculatePerformanceMetrics(trafficData);
            evaluation.put("performance_metrics", performance);

            evaluation.put("evaluation_time", LocalDateTime.now());

            return evaluation;

        } catch (Exception e) {
            log.error("评估交通状态失败: {}", e.getMessage(), e);
            return createErrorResponse("评估交通状态失败: " + e.getMessage());
        }
    }

    private Map<String, Object> evaluateDirectionState(Map<String, Object> directionData) {
        Map<String, Object> state = new HashMap<>();

        // 获取基础数据
        Integer vehicleCount = (Integer) directionData.getOrDefault("vehicleCount", 0);
        Double averageSpeed = (Double) directionData.getOrDefault("averageSpeed", 30.0);
        Double density = (Double) directionData.getOrDefault("density", 0.0);
        Double waitingTime = (Double) directionData.getOrDefault("waitingTime", 0.0);

        // 计算拥堵指数
        double congestionIndex = calculateDirectionCongestion(vehicleCount, averageSpeed, density, waitingTime);

        state.put("vehicle_count", vehicleCount);
        state.put("average_speed", averageSpeed);
        state.put("density", density);
        state.put("waiting_time", waitingTime);
        state.put("congestion_index", congestionIndex);
        state.put("congestion_level", getCongestionLevel(congestionIndex));

        // 计算流量状态
        double flowRate = calculateFlowRate(vehicleCount, 3600); // 假设1小时观测期
        state.put("flow_rate", flowRate);

        // 计算优先级评分
        double priorityScore = calculatePriorityScore(congestionIndex, waitingTime);
        state.put("priority_score", priorityScore);

        return state;
    }

    private double calculateDirectionCongestion(int vehicleCount, double averageSpeed, double density, double waitingTime) {
        // 基于多个指标计算拥堵指数
        double densityFactor = Math.min(density / 50.0, 1.0);
        double speedFactor = Math.max(0, 1.0 - averageSpeed / 50.0);
        double waitFactor = Math.min(waitingTime / 120.0, 1.0);

        // 加权计算
        return Math.min(densityFactor * 0.4 + speedFactor * 0.4 + waitFactor * 0.2, 1.0);
    }

    private String getCongestionLevel(double congestionIndex) {
        if (congestionIndex < CONGESTION_THRESHOLDS.get("low")) {
            return "low";
        } else if (congestionIndex < CONGESTION_THRESHOLDS.get("medium")) {
            return "medium";
        } else if (congestionIndex < CONGESTION_THRESHOLDS.get("high")) {
            return "high";
        } else {
            return "severe";
        }
    }

    private double calculateFlowRate(int vehicleCount, int duration) {
        if (duration > 0) {
            return (vehicleCount / (double) duration) * 3600; // vehicles/hour
        }
        return 0.0;
    }

    private double calculatePriorityScore(double congestionIndex, double waitingTime) {
        double waitScore = Math.min(waitingTime / 120.0, 1.0);
        return congestionIndex * 0.7 + waitScore * 0.3;
    }

    private Map<String, Object> calculatePerformanceMetrics(Map<String, Object> trafficData) {
        Map<String, Object> metrics = new HashMap<>();

        // 计算总体性能指标
        Map<String, Object> directions = (Map<String, Object>) trafficData.get("directions");
        if (directions != null) {
            int totalVehicles = 0;
            double totalSpeed = 0.0;
            double totalWaitingTime = 0.0;
            int directionCount = 0;

            for (Object directionData : directions.values()) {
                Map<String, Object> data = (Map<String, Object>) directionData;
                totalVehicles += (Integer) data.getOrDefault("vehicleCount", 0);
                totalSpeed += (Double) data.getOrDefault("averageSpeed", 0.0);
                totalWaitingTime += (Double) data.getOrDefault("waitingTime", 0.0);
                directionCount++;
            }

            metrics.put("total_vehicles", totalVehicles);
            metrics.put("average_speed", directionCount > 0 ? totalSpeed / directionCount : 0.0);
            metrics.put("total_waiting_time", totalWaitingTime);
            metrics.put("throughput", calculateThroughput(totalVehicles));
        }

        return metrics;
    }

    private double calculateThroughput(int totalVehicles) {
        // 简化的通行能力计算
        return totalVehicles * 1.2; // 假设系数
    }

    @Override
    public Map<String, Object> generateAdaptiveSignalTiming(Map<String, Object> trafficData, Map<String, Object> currentTiming) {
        try {
            log.info("生成自适应信号配时");

            Map<String, Object> adaptiveTiming = new HashMap<>();

            // 分析流量需求
            Map<String, Object> flowDemands = analyzeFlowDemands(trafficData);
            adaptiveTiming.put("flow_demands", flowDemands);

            // 计算最优周期
            int optimalCycle = calculateOptimalCycle(flowDemands);
            adaptiveTiming.put("cycle_time", optimalCycle);

            // 分配绿灯时间
            Map<String, Integer> greenSplits = calculateGreenSplits(flowDemands, optimalCycle);
            adaptiveTiming.put("green_splits", greenSplits);

            // 生成相位方案
            List<Map<String, Object>> phases = generateSignalPhases(greenSplits);
            adaptiveTiming.put("phases", phases);

            // 添加元数据
            adaptiveTiming.put("optimization_method", "adaptive_control");
            adaptiveTiming.put("adaptation_reason", getAdaptationReason(trafficData));
            adaptiveTiming.put("timestamp", LocalDateTime.now());

            return adaptiveTiming;

        } catch (Exception e) {
            log.error("生成自适应信号配时失败: {}", e.getMessage(), e);
            return createErrorResponse("生成自适应信号配时失败: " + e.getMessage());
        }
    }

    private Map<String, Object> analyzeFlowDemands(Map<String, Object> trafficData) {
        Map<String, Object> flowDemands = new HashMap<>();

        Map<String, Object> directions = (Map<String, Object>) trafficData.get("directions");
        if (directions != null) {
            for (Map.Entry<String, Object> entry : directions.entrySet()) {
                String direction = entry.getKey();
                Map<String, Object> directionData = (Map<String, Object>) entry.getValue();

                Map<String, Object> demand = new HashMap<>();

                Integer vehicleCount = (Integer) directionData.getOrDefault("vehicleCount", 0);
                double flowRate = calculateFlowRate(vehicleCount, 3600);
                double demandIntensity = calculateDemandIntensity(directionData);
                double priorityScore = calculatePriorityScore(
                    calculateDirectionCongestion(
                        vehicleCount,
                        (Double) directionData.getOrDefault("averageSpeed", 30.0),
                        (Double) directionData.getOrDefault("density", 0.0),
                        (Double) directionData.getOrDefault("waitingTime", 0.0)
                    ),
                    (Double) directionData.getOrDefault("waitingTime", 0.0)
                );

                demand.put("flow_rate", flowRate);
                demand.put("demand_intensity", demandIntensity);
                demand.put("priority_score", priorityScore);
                demand.put("saturation_ratio", calculateSaturationRatio(flowRate));

                flowDemands.put(direction, demand);
            }
        }

        return flowDemands;
    }

    private double calculateDemandIntensity(Map<String, Object> directionData) {
        Integer vehicleCount = (Integer) directionData.getOrDefault("vehicleCount", 0);
        Double density = (Double) directionData.getOrDefault("density", 0.0);
        Double waitingTime = (Double) directionData.getOrDefault("waitingTime", 0.0);

        double flowFactor = Math.min(vehicleCount / 50.0, 1.0);
        double densityFactor = Math.min(density / 50.0, 1.0);
        double waitFactor = Math.min(waitingTime / 60.0, 1.0);

        return flowFactor * 0.5 + densityFactor * 0.3 + waitFactor * 0.2;
    }

    private double calculateSaturationRatio(double flowRate) {
        double estimatedSaturationFlow = 1800.0; // vehicles/hour
        return Math.min(flowRate / estimatedSaturationFlow, 1.0);
    }

    private int calculateOptimalCycle(Map<String, Object> flowDemands) {
        int totalLostTime = 16; // 假设总损失时间

        double sumCriticalRatios = 0.0;
        for (Object demand : flowDemands.values()) {
            Map<String, Object> demandData = (Map<String, Object>) demand;
            Double saturationRatio = (Double) demandData.get("saturation_ratio");
            if (saturationRatio != null) {
                sumCriticalRatios += saturationRatio;
            }
        }

        if (sumCriticalRatios < 0.9) {
            double optimalCycle = (1.5 * totalLostTime + 5) / (1 - sumCriticalRatios);
            return Math.max(60, Math.min(180, (int) optimalCycle));
        }

        return 120; // 默认周期
    }

    private Map<String, Integer> calculateGreenSplits(Map<String, Object> flowDemands, int cycleTime) {
        Map<String, Integer> greenSplits = new HashMap<>();

        int totalLostTime = 16;
        int effectiveGreen = cycleTime - totalLostTime;

        // 计算总需求强度
        double totalDemand = 0.0;
        for (Object demand : flowDemands.values()) {
            Map<String, Object> demandData = (Map<String, Object>) demand;
            Double demandIntensity = (Double) demandData.get("demand_intensity");
            if (demandIntensity != null) {
                totalDemand += demandIntensity;
            }
        }

        if (totalDemand > 0) {
            for (Map.Entry<String, Object> entry : flowDemands.entrySet()) {
                String direction = entry.getKey();
                Map<String, Object> demandData = (Map<String, Object>) entry.getValue();
                Double demandIntensity = (Double) demandData.get("demand_intensity");

                if (demandIntensity != null) {
                    double proportion = demandIntensity / totalDemand;
                    int greenTime = Math.max(10, (int) (effectiveGreen * proportion));
                    greenSplits.put(direction, greenTime);
                }
            }
        } else {
            // 均匀分配
            int equalGreen = flowDemands.isEmpty() ? 30 : effectiveGreen / flowDemands.size();
            for (String direction : flowDemands.keySet()) {
                greenSplits.put(direction, equalGreen);
            }
        }

        return greenSplits;
    }

    private List<Map<String, Object>> generateSignalPhases(Map<String, Integer> greenSplits) {
        List<Map<String, Object>> phases = new ArrayList<>();

        // 简化的两相位方案
        int ewGreen = greenSplits.getOrDefault("east", 30) + greenSplits.getOrDefault("west", 0);
        int nsGreen = greenSplits.getOrDefault("north", 30) + greenSplits.getOrDefault("south", 0);

        // 东西绿灯相位
        Map<String, Object> phase1 = new HashMap<>();
        phase1.put("phase_id", "phase_1");
        phase1.put("duration", Math.max(10, ewGreen));
        phase1.put("state", "GrGr");
        phase1.put("description", "东西方向绿灯");
        phase1.put("directions", Arrays.asList("east", "west"));
        phases.add(phase1);

        // 东西黄灯相位
        Map<String, Object> phase2 = new HashMap<>();
        phase2.put("phase_id", "phase_2");
        phase2.put("duration", 4);
        phase2.put("state", "yryr");
        phase2.put("description", "东西方向黄灯");
        phase2.put("directions", Arrays.asList("east", "west"));
        phases.add(phase2);

        // 南北绿灯相位
        Map<String, Object> phase3 = new HashMap<>();
        phase3.put("phase_id", "phase_3");
        phase3.put("duration", Math.max(10, nsGreen));
        phase3.put("state", "rGrG");
        phase3.put("description", "南北方向绿灯");
        phase3.put("directions", Arrays.asList("north", "south"));
        phases.add(phase3);

        // 南北黄灯相位
        Map<String, Object> phase4 = new HashMap<>();
        phase4.put("phase_id", "phase_4");
        phase4.put("duration", 4);
        phase4.put("state", "ryry");
        phase4.put("description", "南北方向黄灯");
        phase4.put("directions", Arrays.asList("north", "south"));
        phases.add(phase4);

        return phases;
    }

    private String getAdaptationReason(Map<String, Object> trafficData) {
        List<String> reasons = new ArrayList<>();

        Map<String, Object> directions = (Map<String, Object>) trafficData.get("directions");
        if (directions != null) {
            for (Map.Entry<String, Object> entry : directions.entrySet()) {
                String direction = entry.getKey();
                Map<String, Object> directionData = (Map<String, Object>) entry.getValue();

                Double waitingTime = (Double) directionData.getOrDefault("waitingTime", 0.0);
                Integer vehicleCount = (Integer) directionData.getOrDefault("vehicleCount", 0);

                if (waitingTime > 60) {
                    reasons.add(direction + "方向等待时间过长");
                } else if (vehicleCount > 30) {
                    reasons.add(direction + "方向车辆较多");
                }
            }
        }

        if (reasons.isEmpty()) {
            reasons.add("基于当前流量优化配时");
        }

        return String.join("; ", reasons);
    }

    // 辅助方法
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "error");
        response.put("message", message);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }

    private Map<String, Object> generateDecisionSummary(Map<String, Object> trafficState,
                                                       Map<String, Object> congestionLevel,
                                                       List<Map<String, Object>> recommendations) {
        Map<String, Object> summary = new HashMap<>();

        // 总体状态摘要
        String overallLevel = (String) trafficState.get("overall_level");
        Double overallCongestion = (Double) trafficState.get("overall_congestion");

        summary.put("overall_status", overallLevel);
        summary.put("congestion_score", overallCongestion);
        summary.put("total_recommendations", recommendations.size());

        // 高优先级建议数量
        long highPriorityCount = recommendations.stream()
            .filter(rec -> "high".equals(rec.get("priority")))
            .count();
        summary.put("high_priority_recommendations", highPriorityCount);

        // 主要建议
        if (!recommendations.isEmpty()) {
            summary.put("primary_recommendation", recommendations.get(0));
        }

        // 决策建议
        if ("severe".equals(overallLevel)) {
            summary.put("decision", "立即采取应急措施");
        } else if ("high".equals(overallLevel)) {
            summary.put("decision", "建议优化信号配时");
        } else if ("medium".equals(overallLevel)) {
            summary.put("decision", "监控并适当调整");
        } else {
            summary.put("decision", "保持当前状态");
        }

        return summary;
    }

    @Override
    public Map<String, Object> calculateCongestionLevel(Map<String, Object> trafficData) {
        try {
            log.info("计算拥堵等级");

            Map<String, Object> congestionAnalysis = new HashMap<>();

            // 评估交通状态
            Map<String, Object> trafficState = evaluateTrafficState(trafficData);
            Map<String, Object> directionStates = (Map<String, Object>) trafficState.get("direction_states");

            String overallLevel = "low";
            List<String> bottleneckDirections = new ArrayList<>();
            Map<String, Object> directionLevels = new HashMap<>();

            if (directionStates != null) {
                for (Map.Entry<String, Object> entry : directionStates.entrySet()) {
                    String direction = entry.getKey();
                    Map<String, Object> state = (Map<String, Object>) entry.getValue();

                    String level = (String) state.get("congestion_level");
                    Double congestionIndex = (Double) state.get("congestion_index");

                    directionLevels.put(direction, Map.of(
                        "level", level,
                        "index", congestionIndex,
                        "flow_rate", state.get("flow_rate"),
                        "density", state.get("density"),
                        "speed", state.get("average_speed")
                    ));

                    // 识别瓶颈方向
                    if ("high".equals(level) || "severe".equals(level)) {
                        bottleneckDirections.add(direction);
                    }
                }
            }

            // 确定总体拥堵等级
            overallLevel = (String) trafficState.get("overall_level");
            Double congestionScore = (Double) trafficState.get("overall_congestion");

            congestionAnalysis.put("overall_level", overallLevel);
            congestionAnalysis.put("congestion_score", congestionScore);
            congestionAnalysis.put("direction_levels", directionLevels);
            congestionAnalysis.put("bottleneck_directions", bottleneckDirections);
            congestionAnalysis.put("timestamp", LocalDateTime.now());

            return congestionAnalysis;

        } catch (Exception e) {
            log.error("计算拥堵等级失败: {}", e.getMessage(), e);
            return createErrorResponse("计算拥堵等级失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> generateOptimizationRecommendations(Map<String, Object> trafficData, Map<String, Object> optimizationResult) {
        try {
            log.info("生成优化建议");

            List<Map<String, Object>> recommendations = new ArrayList<>();

            // 基于拥堵等级生成建议
            Map<String, Object> congestionAnalysis = calculateCongestionLevel(trafficData);
            recommendations.addAll(generateCongestionRecommendations(congestionAnalysis));

            // 基于优化结果生成建议
            if (optimizationResult != null) {
                recommendations.addAll(generateOptimizationBasedRecommendations(optimizationResult));
            }

            // 基于流量不平衡生成建议
            recommendations.addAll(generateBalanceRecommendations(trafficData));

            // 按优先级排序
            recommendations.sort((r1, r2) -> {
                int p1 = getPriorityWeight((String) r1.get("priority"));
                int p2 = getPriorityWeight((String) r2.get("priority"));
                return Integer.compare(p2, p1); // 降序
            });

            return recommendations;

        } catch (Exception e) {
            log.error("生成优化建议失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    private List<Map<String, Object>> generateCongestionRecommendations(Map<String, Object> congestionAnalysis) {
        List<Map<String, Object>> recommendations = new ArrayList<>();

        String overallLevel = (String) congestionAnalysis.get("overall_level");
        List<String> bottlenecks = (List<String>) congestionAnalysis.get("bottleneck_directions");

        if ("severe".equals(overallLevel)) {
            recommendations.add(createRecommendation(
                "emergency_control", "high", "启动应急交通控制",
                "检测到严重拥堵，建议启动应急交通控制措施",
                "实施动态信号控制，增加警力疏导",
                "快速缓解拥堵，恢复正常通行", "立即执行"
            ));
        } else if ("high".equals(overallLevel)) {
            recommendations.add(createRecommendation(
                "signal_optimization", "high", "优化信号配时",
                "当前拥堵较为严重，建议调整信号配时方案",
                "应用自适应信号控制算法",
                "减少15-25%的延误时间", "5分钟内"
            ));
        }

        // 针对瓶颈方向的建议
        if (bottlenecks != null) {
            for (String direction : bottlenecks) {
                recommendations.add(createRecommendation(
                    "direction_optimization", "medium", "优化" + direction + "方向通行",
                    direction + "方向出现拥堵瓶颈，需要重点优化",
                    "增加" + direction + "方向绿灯时间，优化车道配置",
                    "改善该方向通行效率", "10分钟内"
                ));
            }
        }

        return recommendations;
    }

    private List<Map<String, Object>> generateOptimizationBasedRecommendations(Map<String, Object> optimizationResult) {
        List<Map<String, Object>> recommendations = new ArrayList<>();

        Double improvement = (Double) optimizationResult.get("improvement_percentage");
        if (improvement != null) {
            if (improvement > 20) {
                recommendations.add(createRecommendation(
                    "high_impact_optimization", "high", "应用高效优化方案",
                    String.format("检测到高效优化方案，可提升%.1f%%的通行效率", improvement),
                    "立即应用优化后的信号配时方案",
                    String.format("显著提升%.1f%%的整体通行效率", improvement), "立即执行"
                ));
            } else if (improvement > 10) {
                recommendations.add(createRecommendation(
                    "moderate_optimization", "medium", "应用优化配时方案",
                    String.format("优化方案可提升%.1f%%的通行效率", improvement),
                    "应用优化后的信号配时",
                    String.format("提升%.1f%%的通行效率", improvement), "5分钟内"
                ));
            }
        }

        return recommendations;
    }

    private List<Map<String, Object>> generateBalanceRecommendations(Map<String, Object> trafficData) {
        List<Map<String, Object>> recommendations = new ArrayList<>();

        Map<String, Object> directions = (Map<String, Object>) trafficData.get("directions");
        if (directions != null && !directions.isEmpty()) {
            List<Double> flowRates = new ArrayList<>();

            for (Object directionData : directions.values()) {
                Map<String, Object> data = (Map<String, Object>) directionData;
                Integer vehicleCount = (Integer) data.getOrDefault("vehicleCount", 0);
                double flowRate = calculateFlowRate(vehicleCount, 3600);
                flowRates.add(flowRate);
            }

            if (!flowRates.isEmpty()) {
                double meanFlow = flowRates.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                double maxFlow = flowRates.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
                double minFlow = flowRates.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);

                if (meanFlow > 0) {
                    double imbalance = (maxFlow - minFlow) / meanFlow;

                    if (imbalance > 0.5) {
                        recommendations.add(createRecommendation(
                            "flow_balance", "medium", "平衡各方向流量",
                            "各方向流量不平衡较为严重，建议调整配时平衡流量",
                            "调整信号配时，增加高流量方向的绿灯时间",
                            "平衡各方向流量，提升整体效率", "10分钟内"
                        ));
                    }
                }
            }
        }

        return recommendations;
    }

    private Map<String, Object> createRecommendation(String type, String priority, String title,
                                                    String description, String action,
                                                    String expectedEffect, String implementationTime) {
        Map<String, Object> recommendation = new HashMap<>();
        recommendation.put("type", type);
        recommendation.put("priority", priority);
        recommendation.put("title", title);
        recommendation.put("description", description);
        recommendation.put("action", action);
        recommendation.put("expected_effect", expectedEffect);
        recommendation.put("implementation_time", implementationTime);
        recommendation.put("timestamp", LocalDateTime.now());
        return recommendation;
    }

    private int getPriorityWeight(String priority) {
        switch (priority) {
            case "high": return 3;
            case "medium": return 2;
            case "low": return 1;
            default: return 1;
        }
    }

    @Override
    public Map<String, Object> predictTrafficTrends(List<Map<String, Object>> historicalData, Map<String, Object> currentData) {
        try {
            log.info("预测交通趋势");

            Map<String, Object> prediction = new HashMap<>();

            if (historicalData == null || historicalData.isEmpty()) {
                prediction.put("status", "insufficient_data");
                prediction.put("message", "历史数据不足，无法进行趋势预测");
                return prediction;
            }

            // 简化的趋势分析
            Map<String, Object> trends = analyzeTrends(historicalData, currentData);
            prediction.put("trends", trends);

            // 预测未来状态
            Map<String, Object> forecast = generateForecast(historicalData, currentData);
            prediction.put("forecast", forecast);

            // 风险评估
            Map<String, Object> riskAssessment = assessRisks(trends, currentData);
            prediction.put("risk_assessment", riskAssessment);

            prediction.put("status", "success");
            prediction.put("timestamp", LocalDateTime.now());

            return prediction;

        } catch (Exception e) {
            log.error("预测交通趋势失败: {}", e.getMessage(), e);
            return createErrorResponse("预测交通趋势失败: " + e.getMessage());
        }
    }

    private Map<String, Object> analyzeTrends(List<Map<String, Object>> historicalData, Map<String, Object> currentData) {
        Map<String, Object> trends = new HashMap<>();

        // 分析车流量趋势
        List<Integer> vehicleCounts = historicalData.stream()
            .map(data -> {
                Map<String, Object> directions = (Map<String, Object>) data.get("directions");
                if (directions != null) {
                    return directions.values().stream()
                        .mapToInt(d -> (Integer) ((Map<String, Object>) d).getOrDefault("vehicleCount", 0))
                        .sum();
                }
                return 0;
            })
            .collect(Collectors.toList());

        if (vehicleCounts.size() >= 2) {
            double trend = calculateTrend(vehicleCounts);
            trends.put("vehicle_count_trend", trend);
            trends.put("trend_direction", trend > 0.1 ? "increasing" : trend < -0.1 ? "decreasing" : "stable");
        }

        return trends;
    }

    private double calculateTrend(List<Integer> values) {
        if (values.size() < 2) return 0.0;

        int n = values.size();
        double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;

        for (int i = 0; i < n; i++) {
            sumX += i;
            sumY += values.get(i);
            sumXY += i * values.get(i);
            sumX2 += i * i;
        }

        double slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        return slope;
    }

    private Map<String, Object> generateForecast(List<Map<String, Object>> historicalData, Map<String, Object> currentData) {
        Map<String, Object> forecast = new HashMap<>();

        // 简化的预测逻辑
        forecast.put("next_hour_congestion", "medium");
        forecast.put("peak_time_prediction", "17:30-18:30");
        forecast.put("recommended_actions", Arrays.asList(
            "监控东西方向流量变化",
            "准备在17:00调整信号配时"
        ));

        return forecast;
    }

    private Map<String, Object> assessRisks(Map<String, Object> trends, Map<String, Object> currentData) {
        Map<String, Object> riskAssessment = new HashMap<>();

        String trendDirection = (String) trends.get("trend_direction");

        if ("increasing".equals(trendDirection)) {
            riskAssessment.put("congestion_risk", "high");
            riskAssessment.put("risk_factors", Arrays.asList("车流量持续增加", "可能出现拥堵"));
        } else {
            riskAssessment.put("congestion_risk", "low");
            riskAssessment.put("risk_factors", Arrays.asList("车流量稳定"));
        }

        return riskAssessment;
    }

    @Override
    public Map<String, Object> evaluateOptimizationEffectiveness(Map<String, Object> beforeData, Map<String, Object> afterData) {
        try {
            log.info("评估优化效果");

            Map<String, Object> evaluation = new HashMap<>();

            // 计算各项改善指标
            double delayReduction = calculateDelayImprovement(beforeData, afterData);
            double throughputIncrease = calculateThroughputImprovement(beforeData, afterData);
            double fuelSaving = calculateFuelSaving(beforeData, afterData);

            // 计算综合改善评分
            double improvementScore = delayReduction * OPTIMIZATION_WEIGHTS.get("delay") +
                                    throughputIncrease * OPTIMIZATION_WEIGHTS.get("throughput") +
                                    fuelSaving * OPTIMIZATION_WEIGHTS.get("fuel");

            evaluation.put("improvement_score", improvementScore);
            evaluation.put("delay_reduction", delayReduction);
            evaluation.put("throughput_increase", throughputIncrease);
            evaluation.put("fuel_saving", fuelSaving);

            // 生成效果评估建议
            List<Map<String, Object>> recommendations = generateEffectivenessRecommendations(
                delayReduction, throughputIncrease, fuelSaving
            );
            evaluation.put("recommendations", recommendations);

            evaluation.put("status", "success");
            evaluation.put("timestamp", LocalDateTime.now());

            return evaluation;

        } catch (Exception e) {
            log.error("评估优化效果失败: {}", e.getMessage(), e);
            return createErrorResponse("评估优化效果失败: " + e.getMessage());
        }
    }

    private double calculateDelayImprovement(Map<String, Object> beforeData, Map<String, Object> afterData) {
        Double beforeDelay = (Double) beforeData.get("average_delay");
        Double afterDelay = (Double) afterData.get("average_delay");

        if (beforeDelay != null && afterDelay != null && beforeDelay > 0) {
            return ((beforeDelay - afterDelay) / beforeDelay) * 100;
        }
        return 0.0;
    }

    private double calculateThroughputImprovement(Map<String, Object> beforeData, Map<String, Object> afterData) {
        Double beforeThroughput = (Double) beforeData.get("throughput");
        Double afterThroughput = (Double) afterData.get("throughput");

        if (beforeThroughput != null && afterThroughput != null && beforeThroughput > 0) {
            return ((afterThroughput - beforeThroughput) / beforeThroughput) * 100;
        }
        return 0.0;
    }

    private double calculateFuelSaving(Map<String, Object> beforeData, Map<String, Object> afterData) {
        Double beforeFuel = (Double) beforeData.get("fuel_consumption");
        Double afterFuel = (Double) afterData.get("fuel_consumption");

        if (beforeFuel != null && afterFuel != null && beforeFuel > 0) {
            return ((beforeFuel - afterFuel) / beforeFuel) * 100;
        }
        return 0.0;
    }

    private List<Map<String, Object>> generateEffectivenessRecommendations(double delayReduction,
                                                                          double throughputIncrease,
                                                                          double fuelSaving) {
        List<Map<String, Object>> recommendations = new ArrayList<>();

        if (delayReduction > 15) {
            recommendations.add(createRecommendation(
                "maintain_optimization", "high", "保持当前优化方案",
                String.format("当前方案显著减少了%.1f%%的延误", delayReduction),
                "继续使用当前信号配时方案",
                "维持良好的通行效率", "持续执行"
            ));
        }

        if (throughputIncrease > 10) {
            recommendations.add(createRecommendation(
                "expand_optimization", "medium", "扩展优化范围",
                String.format("通行能力提升%.1f%%，建议扩展到相邻路口", throughputIncrease),
                "将优化方案应用到相邻交叉口",
                "扩大优化效果覆盖范围", "1周内"
            ));
        }

        return recommendations;
    }

    @Override
    public Map<String, Object> generateRealtimeDecisions(Map<String, Object> realtimeData, Map<String, Object> context) {
        try {
            log.info("生成实时决策");

            Map<String, Object> decisions = new HashMap<>();

            // 实时状态评估
            Map<String, Object> realtimeState = evaluateRealtimeState(realtimeData);
            decisions.put("realtime_state", realtimeState);

            // 紧急情况检测
            Map<String, Object> emergencyDetection = detectEmergencyConditions(realtimeData);
            decisions.put("emergency_detection", emergencyDetection);

            // 实时调整建议
            List<Map<String, Object>> realtimeActions = generateRealtimeActions(realtimeData, context);
            decisions.put("realtime_actions", realtimeActions);

            decisions.put("status", "success");
            decisions.put("timestamp", LocalDateTime.now());

            return decisions;

        } catch (Exception e) {
            log.error("生成实时决策失败: {}", e.getMessage(), e);
            return createErrorResponse("生成实时决策失败: " + e.getMessage());
        }
    }

    private Map<String, Object> evaluateRealtimeState(Map<String, Object> realtimeData) {
        Map<String, Object> state = new HashMap<>();

        // 简化的实时状态评估
        Integer totalVehicles = (Integer) realtimeData.getOrDefault("total_vehicles", 0);
        Double averageSpeed = (Double) realtimeData.getOrDefault("average_speed", 30.0);

        if (totalVehicles > 50 && averageSpeed < 20) {
            state.put("urgency_level", "high");
            state.put("action_required", true);
        } else if (totalVehicles > 30 || averageSpeed < 25) {
            state.put("urgency_level", "medium");
            state.put("action_required", true);
        } else {
            state.put("urgency_level", "low");
            state.put("action_required", false);
        }

        return state;
    }

    private Map<String, Object> detectEmergencyConditions(Map<String, Object> realtimeData) {
        Map<String, Object> detection = new HashMap<>();

        List<String> emergencyConditions = new ArrayList<>();

        Integer totalVehicles = (Integer) realtimeData.getOrDefault("total_vehicles", 0);
        Double averageSpeed = (Double) realtimeData.getOrDefault("average_speed", 30.0);
        Double waitingTime = (Double) realtimeData.getOrDefault("waiting_time", 0.0);

        if (totalVehicles > 80) {
            emergencyConditions.add("车辆数量过多");
        }
        if (averageSpeed < 10) {
            emergencyConditions.add("平均速度过低");
        }
        if (waitingTime > 180) {
            emergencyConditions.add("等待时间过长");
        }

        detection.put("has_emergency", !emergencyConditions.isEmpty());
        detection.put("emergency_conditions", emergencyConditions);
        detection.put("emergency_level", emergencyConditions.size() > 2 ? "severe" :
                                       emergencyConditions.size() > 0 ? "moderate" : "none");

        return detection;
    }

    private List<Map<String, Object>> generateRealtimeActions(Map<String, Object> realtimeData, Map<String, Object> context) {
        List<Map<String, Object>> actions = new ArrayList<>();

        Map<String, Object> emergencyDetection = detectEmergencyConditions(realtimeData);
        boolean hasEmergency = (Boolean) emergencyDetection.get("has_emergency");

        if (hasEmergency) {
            actions.add(createRecommendation(
                "emergency_action", "high", "启动应急响应",
                "检测到紧急交通状况",
                "立即调整信号配时，启动应急疏导",
                "快速缓解拥堵", "立即执行"
            ));
        } else {
            actions.add(createRecommendation(
                "routine_adjustment", "medium", "常规调整",
                "根据实时流量进行微调",
                "适当调整信号配时",
                "保持通行顺畅", "5分钟内"
            ));
        }

        return actions;
    }

    @Override
    public Map<String, Object> analyzeBottlenecks(Map<String, Object> trafficData) {
        try {
            log.info("分析瓶颈路段");

            Map<String, Object> bottleneckAnalysis = new HashMap<>();

            // 识别瓶颈方向
            List<Map<String, Object>> bottlenecks = identifyBottleneckDirections(trafficData);
            bottleneckAnalysis.put("bottleneck_directions", bottlenecks);

            // 分析瓶颈原因
            Map<String, Object> bottleneckCauses = analyzeBottleneckCauses(trafficData, bottlenecks);
            bottleneckAnalysis.put("bottleneck_causes", bottleneckCauses);

            // 生成解决方案
            List<Map<String, Object>> solutions = generateBottleneckSolutions(bottlenecks);
            bottleneckAnalysis.put("solutions", solutions);

            bottleneckAnalysis.put("status", "success");
            bottleneckAnalysis.put("timestamp", LocalDateTime.now());

            return bottleneckAnalysis;

        } catch (Exception e) {
            log.error("分析瓶颈路段失败: {}", e.getMessage(), e);
            return createErrorResponse("分析瓶颈路段失败: " + e.getMessage());
        }
    }

    private List<Map<String, Object>> identifyBottleneckDirections(Map<String, Object> trafficData) {
        List<Map<String, Object>> bottlenecks = new ArrayList<>();

        Map<String, Object> directions = (Map<String, Object>) trafficData.get("directions");
        if (directions != null) {
            for (Map.Entry<String, Object> entry : directions.entrySet()) {
                String direction = entry.getKey();
                Map<String, Object> directionData = (Map<String, Object>) entry.getValue();

                Integer vehicleCount = (Integer) directionData.getOrDefault("vehicleCount", 0);
                Double averageSpeed = (Double) directionData.getOrDefault("averageSpeed", 30.0);
                Double waitingTime = (Double) directionData.getOrDefault("waitingTime", 0.0);

                // 判断是否为瓶颈
                boolean isBottleneck = vehicleCount > 25 || averageSpeed < 20 || waitingTime > 60;

                if (isBottleneck) {
                    Map<String, Object> bottleneck = new HashMap<>();
                    bottleneck.put("direction", direction);
                    bottleneck.put("vehicle_count", vehicleCount);
                    bottleneck.put("average_speed", averageSpeed);
                    bottleneck.put("waiting_time", waitingTime);
                    bottleneck.put("severity", calculateBottleneckSeverity(vehicleCount, averageSpeed, waitingTime));
                    bottlenecks.add(bottleneck);
                }
            }
        }

        return bottlenecks;
    }

    private String calculateBottleneckSeverity(int vehicleCount, double averageSpeed, double waitingTime) {
        int severityScore = 0;

        if (vehicleCount > 40) severityScore += 3;
        else if (vehicleCount > 25) severityScore += 2;
        else if (vehicleCount > 15) severityScore += 1;

        if (averageSpeed < 10) severityScore += 3;
        else if (averageSpeed < 20) severityScore += 2;
        else if (averageSpeed < 30) severityScore += 1;

        if (waitingTime > 120) severityScore += 3;
        else if (waitingTime > 60) severityScore += 2;
        else if (waitingTime > 30) severityScore += 1;

        if (severityScore >= 7) return "severe";
        else if (severityScore >= 4) return "moderate";
        else return "mild";
    }

    private Map<String, Object> analyzeBottleneckCauses(Map<String, Object> trafficData, List<Map<String, Object>> bottlenecks) {
        Map<String, Object> causes = new HashMap<>();

        List<String> primaryCauses = new ArrayList<>();

        if (bottlenecks.size() > 2) {
            primaryCauses.add("整体流量过大");
        }

        for (Map<String, Object> bottleneck : bottlenecks) {
            String direction = (String) bottleneck.get("direction");
            Double waitingTime = (Double) bottleneck.get("waiting_time");

            if (waitingTime > 90) {
                primaryCauses.add(direction + "方向信号配时不合理");
            }
        }

        if (primaryCauses.isEmpty()) {
            primaryCauses.add("局部流量不平衡");
        }

        causes.put("primary_causes", primaryCauses);
        causes.put("analysis_confidence", "medium");

        return causes;
    }

    private List<Map<String, Object>> generateBottleneckSolutions(List<Map<String, Object>> bottlenecks) {
        List<Map<String, Object>> solutions = new ArrayList<>();

        for (Map<String, Object> bottleneck : bottlenecks) {
            String direction = (String) bottleneck.get("direction");
            String severity = (String) bottleneck.get("severity");

            if ("severe".equals(severity)) {
                solutions.add(createRecommendation(
                    "bottleneck_solution", "high", "紧急疏导" + direction + "方向",
                    direction + "方向出现严重瓶颈",
                    "增加绿灯时间，部署人工疏导",
                    "快速缓解瓶颈", "立即执行"
                ));
            } else {
                solutions.add(createRecommendation(
                    "bottleneck_solution", "medium", "优化" + direction + "方向配时",
                    direction + "方向存在瓶颈",
                    "调整信号配时，增加通行时间",
                    "改善通行效率", "10分钟内"
                ));
            }
        }

        return solutions;
    }

    @Override
    public Map<String, Object> generateEmergencyResponse(Map<String, Object> emergencyData, Map<String, Object> trafficData) {
        try {
            log.info("生成应急响应方案");

            Map<String, Object> response = new HashMap<>();

            // 评估应急等级
            String emergencyLevel = assessEmergencyLevel(emergencyData, trafficData);
            response.put("emergency_level", emergencyLevel);

            // 生成应急措施
            List<Map<String, Object>> emergencyMeasures = generateEmergencyMeasures(emergencyLevel, emergencyData, trafficData);
            response.put("emergency_measures", emergencyMeasures);

            // 资源调配建议
            Map<String, Object> resourceAllocation = generateResourceAllocation(emergencyLevel);
            response.put("resource_allocation", resourceAllocation);

            // 预期效果评估
            Map<String, Object> expectedOutcome = assessExpectedOutcome(emergencyMeasures);
            response.put("expected_outcome", expectedOutcome);

            response.put("status", "success");
            response.put("timestamp", LocalDateTime.now());

            return response;

        } catch (Exception e) {
            log.error("生成应急响应方案失败: {}", e.getMessage(), e);
            return createErrorResponse("生成应急响应方案失败: " + e.getMessage());
        }
    }

    private String assessEmergencyLevel(Map<String, Object> emergencyData, Map<String, Object> trafficData) {
        String emergencyType = (String) emergencyData.get("emergency_type");
        Integer affectedVehicles = (Integer) emergencyData.getOrDefault("affected_vehicles", 0);

        if ("accident".equals(emergencyType) && affectedVehicles > 50) {
            return "critical";
        } else if ("congestion".equals(emergencyType) && affectedVehicles > 30) {
            return "high";
        } else {
            return "medium";
        }
    }

    private List<Map<String, Object>> generateEmergencyMeasures(String emergencyLevel, Map<String, Object> emergencyData, Map<String, Object> trafficData) {
        List<Map<String, Object>> measures = new ArrayList<>();

        if ("critical".equals(emergencyLevel)) {
            measures.add(createRecommendation(
                "emergency_control", "high", "启动一级应急响应",
                "检测到严重交通事故或拥堵",
                "立即启动应急信号控制，部署交警现场指挥",
                "快速恢复交通秩序", "立即执行"
            ));
        } else if ("high".equals(emergencyLevel)) {
            measures.add(createRecommendation(
                "enhanced_control", "high", "启动二级应急响应",
                "检测到较严重交通状况",
                "调整信号配时，增加疏导措施",
                "缓解交通压力", "5分钟内"
            ));
        } else {
            measures.add(createRecommendation(
                "routine_response", "medium", "启动常规应急响应",
                "检测到一般交通异常",
                "适当调整信号控制",
                "维持交通秩序", "10分钟内"
            ));
        }

        return measures;
    }

    private Map<String, Object> generateResourceAllocation(String emergencyLevel) {
        Map<String, Object> allocation = new HashMap<>();

        if ("critical".equals(emergencyLevel)) {
            allocation.put("traffic_police", 4);
            allocation.put("emergency_vehicles", 2);
            allocation.put("signal_override", true);
        } else if ("high".equals(emergencyLevel)) {
            allocation.put("traffic_police", 2);
            allocation.put("emergency_vehicles", 1);
            allocation.put("signal_override", true);
        } else {
            allocation.put("traffic_police", 1);
            allocation.put("emergency_vehicles", 0);
            allocation.put("signal_override", false);
        }

        return allocation;
    }

    private Map<String, Object> assessExpectedOutcome(List<Map<String, Object>> emergencyMeasures) {
        Map<String, Object> outcome = new HashMap<>();

        outcome.put("estimated_resolution_time", "15-30分钟");
        outcome.put("expected_improvement", "显著缓解拥堵");
        outcome.put("success_probability", "85%");

        return outcome;
    }

    // ==================== 新增决策支持方法实现 ====================

    @Override
    public List<DecisionSuggestion> generateDecisionSuggestions(String simulationId,
                                                              Map<String, Object> trafficData,
                                                              Map<String, Object> currentConditions) {
        try {
            log.info("生成决策建议: simulationId={}", simulationId);

            // 调用Python决策API
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("simulation_id", simulationId);
            requestData.put("traffic_data", trafficData);
            requestData.put("current_conditions", currentConditions);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(
                pythonDecisionUrl + "/decision/suggestions/generate",
                entity,
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> suggestionsData = (List<Map<String, Object>>) responseBody.get("suggestions");

                List<DecisionSuggestion> suggestions = new ArrayList<>();
                if (suggestionsData != null) {
                    for (Map<String, Object> suggestionData : suggestionsData) {
                        DecisionSuggestion suggestion = convertToDecisionSuggestion(suggestionData);
                        suggestion = saveDecisionSuggestion(suggestion);
                        suggestions.add(suggestion);
                    }
                }

                return suggestions;
            }

            return new ArrayList<>();

        } catch (Exception e) {
            log.error("生成决策建议失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getRealtimeDecisionSupport(String simulationId,
                                                         Map<String, Object> currentState) {
        try {
            log.info("获取实时决策支持: simulationId={}", simulationId);

            Map<String, Object> requestData = new HashMap<>();
            requestData.put("simulation_id", simulationId);
            requestData.put("current_state", currentState);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(
                pythonDecisionUrl + "/decision/realtime",
                entity,
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                return (Map<String, Object>) responseBody.get("decision_support");
            }

            return new HashMap<>();

        } catch (Exception e) {
            log.error("获取实时决策支持失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> applyDecisionSuggestion(String suggestionId,
                                                      String simulationId,
                                                      String userId) {
        try {
            log.info("应用决策建议: suggestionId={}, simulationId={}, userId={}",
                    suggestionId, simulationId, userId);

            // 获取建议详情
            Optional<DecisionSuggestion> suggestionOpt = getDecisionSuggestion(suggestionId);
            if (!suggestionOpt.isPresent()) {
                return createErrorResponse("决策建议不存在");
            }

            DecisionSuggestion suggestion = suggestionOpt.get();

            // 检查建议是否已过期
            if (suggestion.isExpired()) {
                return createErrorResponse("决策建议已过期");
            }

            // 创建决策历史记录
            DecisionHistory history = DecisionHistory.builder()
                .simulationId(simulationId)
                .suggestionId(suggestionId)
                .decisionType(suggestion.getSuggestionType())
                .action(suggestion.getAction())
                .description(suggestion.getDescription())
                .parameters(suggestion.getParameters())
                .decisionMakerId(userId)
                .decisionMakerType("user")
                .decisionTime(LocalDateTime.now())
                .status("executing")
                .confidence(suggestion.getConfidence())
                .expectedEffectScore(suggestion.getImpactScore())
                .createdAt(LocalDateTime.now())
                .build();

            history.markAsExecuting();
            saveDecisionHistory(history);

            // 更新建议状态
            suggestion.markAsApplied(userId);
            saveDecisionSuggestion(suggestion);

            // 模拟应用结果
            Map<String, Object> applicationResult = new HashMap<>();
            applicationResult.put("status", "success");
            applicationResult.put("message", "决策建议应用成功");
            applicationResult.put("suggestion_id", suggestionId);
            applicationResult.put("applied_at", LocalDateTime.now());
            applicationResult.put("applied_by", userId);

            // 更新历史记录
            history.markAsCompleted(true, applicationResult);
            saveDecisionHistory(history);

            return applicationResult;

        } catch (Exception e) {
            log.error("应用决策建议失败: {}", e.getMessage(), e);
            return createErrorResponse("应用决策建议失败: " + e.getMessage());
        }
    }

    @Override
    public List<DecisionHistory> getDecisionHistory(String simulationId,
                                                   String userId,
                                                   int page,
                                                   int size) {
        try {
            Query query = new Query();

            if (simulationId != null && !simulationId.isEmpty()) {
                query.addCriteria(Criteria.where("simulationId").is(simulationId));
            }

            if (userId != null && !userId.isEmpty()) {
                query.addCriteria(Criteria.where("decisionMakerId").is(userId));
            }

            query.with(PageRequest.of(page, size))
                 .with(Sort.by(Sort.Direction.DESC, "decisionTime"));

            return mongoTemplate.find(query, DecisionHistory.class);

        } catch (Exception e) {
            log.error("获取决策历史失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Optional<DecisionSuggestion> getDecisionSuggestion(String suggestionId) {
        try {
            DecisionSuggestion suggestion = mongoTemplate.findById(suggestionId, DecisionSuggestion.class);
            return Optional.ofNullable(suggestion);
        } catch (Exception e) {
            log.error("获取决策建议详情失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Map<String, Object> evaluateDecisionEffect(String simulationId,
                                                     String decisionId,
                                                     Map<String, Object> beforeData,
                                                     Map<String, Object> afterData) {
        try {
            log.info("评估决策效果: simulationId={}, decisionId={}", simulationId, decisionId);

            Map<String, Object> requestData = new HashMap<>();
            requestData.put("simulation_id", simulationId);
            requestData.put("decision_id", decisionId);
            requestData.put("before_data", beforeData);
            requestData.put("after_data", afterData);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(
                pythonDecisionUrl + "/decision/evaluation",
                entity,
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                return (Map<String, Object>) responseBody.get("evaluation");
            }

            return new HashMap<>();

        } catch (Exception e) {
            log.error("评估决策效果失败: {}", e.getMessage(), e);
            return createErrorResponse("评估决策效果失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getDecisionStatistics(String simulationId, String userId) {
        try {
            Query query = new Query();

            if (simulationId != null && !simulationId.isEmpty()) {
                query.addCriteria(Criteria.where("simulationId").is(simulationId));
            }

            if (userId != null && !userId.isEmpty()) {
                query.addCriteria(Criteria.where("decisionMakerId").is(userId));
            }

            List<DecisionHistory> histories = mongoTemplate.find(query, DecisionHistory.class);

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("total_decisions", histories.size());
            statistics.put("successful_decisions", histories.stream().mapToLong(h -> h.isSuccessful() ? 1 : 0).sum());
            statistics.put("failed_decisions", histories.stream().mapToLong(h -> h.isFailed() ? 1 : 0).sum());

            // 计算平均效果评分
            OptionalDouble avgEffectScore = histories.stream()
                .filter(h -> h.getActualEffectScore() != null)
                .mapToDouble(DecisionHistory::getActualEffectScore)
                .average();

            if (avgEffectScore.isPresent()) {
                statistics.put("average_effect_score", avgEffectScore.getAsDouble());
            }

            // 按决策类型统计
            Map<String, Long> typeStats = histories.stream()
                .collect(Collectors.groupingBy(
                    h -> h.getDecisionType() != null ? h.getDecisionType() : "unknown",
                    Collectors.counting()
                ));
            statistics.put("decisions_by_type", typeStats);

            return statistics;

        } catch (Exception e) {
            log.error("获取决策统计失败: {}", e.getMessage(), e);
            return createErrorResponse("获取决策统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> checkServiceStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("service", "decision_support");
        status.put("status", "running");
        status.put("timestamp", LocalDateTime.now());

        // 检查Python决策API状态
        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(
                pythonDecisionUrl + "/decision/status",
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                status.put("python_decision_api", "connected");
            } else {
                status.put("python_decision_api", "disconnected");
            }
        } catch (Exception e) {
            status.put("python_decision_api", "error: " + e.getMessage());
        }

        // 检查数据库连接
        try {
            mongoTemplate.getCollection("decision_suggestions").estimatedDocumentCount();
            status.put("database", "connected");
        } catch (Exception e) {
            status.put("database", "error: " + e.getMessage());
        }

        return status;
    }

    @Override
    public DecisionSuggestion saveDecisionSuggestion(DecisionSuggestion suggestion) {
        try {
            if (suggestion.getCreatedAt() == null) {
                suggestion.setCreatedAt(LocalDateTime.now());
            }
            suggestion.setUpdatedAt(LocalDateTime.now());

            return mongoTemplate.save(suggestion);
        } catch (Exception e) {
            log.error("保存决策建议失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存决策建议失败", e);
        }
    }

    @Override
    public boolean updateSuggestionStatus(String suggestionId, String status, String appliedBy) {
        try {
            Optional<DecisionSuggestion> suggestionOpt = getDecisionSuggestion(suggestionId);
            if (suggestionOpt.isPresent()) {
                DecisionSuggestion suggestion = suggestionOpt.get();
                suggestion.setStatus(status);
                suggestion.setAppliedBy(appliedBy);
                suggestion.setUpdatedAt(LocalDateTime.now());

                if ("applied".equals(status)) {
                    suggestion.setAppliedAt(LocalDateTime.now());
                }

                saveDecisionSuggestion(suggestion);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新决策建议状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public DecisionHistory saveDecisionHistory(DecisionHistory history) {
        try {
            if (history.getCreatedAt() == null) {
                history.setCreatedAt(LocalDateTime.now());
            }
            history.setUpdatedAt(LocalDateTime.now());

            return mongoTemplate.save(history);
        } catch (Exception e) {
            log.error("保存决策历史失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存决策历史失败", e);
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 将Map数据转换为DecisionSuggestion对象
     */
    private DecisionSuggestion convertToDecisionSuggestion(Map<String, Object> suggestionData) {
        DecisionSuggestion suggestion = new DecisionSuggestion();

        suggestion.setId((String) suggestionData.get("id"));
        suggestion.setSimulationId((String) suggestionData.get("simulation_id"));
        suggestion.setSuggestionType((String) suggestionData.get("type"));
        suggestion.setPriority((String) suggestionData.get("priority"));
        suggestion.setTitle((String) suggestionData.get("title"));
        suggestion.setDescription((String) suggestionData.get("description"));
        suggestion.setAction((String) suggestionData.get("action"));
        suggestion.setExpectedEffect((String) suggestionData.get("expected_effect"));
        suggestion.setImplementationTime((String) suggestionData.get("implementation_time"));

        // 处理数值类型
        Object confidence = suggestionData.get("confidence");
        if (confidence instanceof Number) {
            suggestion.setConfidence(((Number) confidence).doubleValue());
        }

        Object impactScore = suggestionData.get("impact_score");
        if (impactScore instanceof Number) {
            suggestion.setImpactScore(((Number) impactScore).doubleValue());
        }

        // 处理复杂对象
        suggestion.setParameters((Map<String, Object>) suggestionData.get("parameters"));
        suggestion.setApplicableConditions((List<String>) suggestionData.get("applicable_conditions"));
        suggestion.setRiskAssessment((Map<String, Object>) suggestionData.get("risk_assessment"));

        // 设置默认值
        suggestion.setStatus("pending");
        suggestion.setGenerationAlgorithm("intelligent_decision_engine");
        suggestion.setCreatedAt(LocalDateTime.now());
        suggestion.setUpdatedAt(LocalDateTime.now());

        // 处理过期时间
        String expiresAt = (String) suggestionData.get("expires_at");
        if (expiresAt != null) {
            try {
                suggestion.setExpiresAt(LocalDateTime.parse(expiresAt));
            } catch (Exception e) {
                log.warn("解析过期时间失败: {}", expiresAt);
            }
        }

        return suggestion;
    }
}