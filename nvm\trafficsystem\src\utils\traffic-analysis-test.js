/**
 * 交通分析功能测试工具
 * 用于测试智能交通状态面板和实时检测功能
 */

// 模拟方向统计数据
export const mockDirectionStats = {
  north: {
    vehicleCount: 3,
    frameRate: '15.2 fps',
    status: 'processing',
    lastUpdate: new Date(),
    lastFrameTime: Date.now(),
    currentFrame: 45,
    totalFrames: 150,
    recentVehicles: [2, 3, 1, 4, 3, 2, 3, 1, 2, 3],
    maxRecentFrames: 10,
    movingAverage: 2.4
  },
  south: {
    vehicleCount: 5,
    frameRate: '14.8 fps',
    status: 'processing',
    lastUpdate: new Date(),
    lastFrameTime: Date.now(),
    currentFrame: 67,
    totalFrames: 200,
    recentVehicles: [4, 5, 6, 3, 5, 4, 6, 5, 4, 5],
    maxRecentFrames: 10,
    movingAverage: 4.7
  },
  east: {
    vehicleCount: 2,
    frameRate: '16.1 fps',
    status: 'processing',
    lastUpdate: new Date(),
    lastFrameTime: Date.now(),
    currentFrame: 89,
    totalFrames: 180,
    recentVehicles: [1, 2, 1, 3, 2, 1, 2, 2, 1, 2],
    maxRecentFrames: 10,
    movingAverage: 1.7
  },
  west: {
    vehicleCount: 1,
    frameRate: '15.5 fps',
    status: 'processing',
    lastUpdate: new Date(),
    lastFrameTime: Date.now(),
    currentFrame: 23,
    totalFrames: 120,
    recentVehicles: [0, 1, 1, 2, 1, 0, 1, 1, 0, 1],
    maxRecentFrames: 10,
    movingAverage: 0.8
  }
}

// 模拟帧数据
export const mockFrameData = {
  north: {
    direction: 'north',
    frameNumber: 46,
    totalFrames: 150,
    detectionCount: 4,
    imageData: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
    timestamp: new Date().toISOString(),
    vehicleTypes: {
      car: 3,
      truck: 1,
      bus: 0,
      motorcycle: 0
    }
  }
}

// 测试进度计算
export function testProgressCalculation(directionStats) {
  console.log('🧪 测试进度计算功能')
  
  const directions = Object.values(directionStats)
  let totalProgress = 0
  let activeDirections = 0
  
  directions.forEach(direction => {
    if (direction.status !== 'waiting') {
      activeDirections++
      if (direction.totalFrames > 0) {
        const frameProgress = Math.min((direction.currentFrame || 0) / direction.totalFrames * 100, 100)
        totalProgress += frameProgress
        console.log(`  ${direction.status} 方向进度: ${frameProgress.toFixed(1)}%`)
      }
    }
  })
  
  const overallProgress = activeDirections === 0 ? 0 : Math.round(totalProgress / 4)
  console.log(`  整体进度: ${overallProgress}%`)
  
  return overallProgress
}

// 测试拥堵等级计算
export function testCongestionCalculation(directionStats) {
  console.log('🧪 测试拥堵等级计算功能')
  
  const directions = ['north', 'south', 'east', 'west']
  const totalVehicles = directions.reduce((sum, dir) => 
    sum + (directionStats[dir]?.vehicleCount || 0), 0)
  const avgMoving = directions.reduce((sum, dir) => 
    sum + (directionStats[dir]?.movingAverage || 0), 0) / 4
  
  console.log(`  总车辆数: ${totalVehicles}`)
  console.log(`  平均移动车辆数: ${avgMoving.toFixed(1)}`)
  
  let level = '畅通'
  if (avgMoving >= 8 || totalVehicles >= 20) level = '严重拥堵'
  else if (avgMoving >= 5 || totalVehicles >= 12) level = '中度拥堵'
  else if (avgMoving >= 3 || totalVehicles >= 6) level = '轻度拥堵'
  
  console.log(`  拥堵等级: ${level}`)
  
  return level
}

// 测试移动平均计算
export function testMovingAverageCalculation(directionStats) {
  console.log('🧪 测试移动平均计算功能')
  
  Object.entries(directionStats).forEach(([direction, stats]) => {
    const recentVehicles = stats.recentVehicles || []
    const movingAverage = recentVehicles.length > 0 
      ? recentVehicles.reduce((sum, count) => sum + count, 0) / recentVehicles.length
      : 0
    
    console.log(`  ${direction}方向:`)
    console.log(`    最近车辆数: [${recentVehicles.join(', ')}]`)
    console.log(`    移动平均: ${movingAverage.toFixed(1)}`)
    console.log(`    当前车辆: ${stats.vehicleCount}`)
  })
}

// 测试交通流平衡度
export function testTrafficFlowBalance(directionStats) {
  console.log('🧪 测试交通流平衡度计算功能')
  
  const directions = ['north', 'south', 'east', 'west']
  const counts = directions.map(dir => directionStats[dir]?.movingAverage || 0)
  const max = Math.max(...counts)
  const min = Math.min(...counts)
  
  console.log(`  各方向移动平均: [${counts.map(c => c.toFixed(1)).join(', ')}]`)
  console.log(`  最大值: ${max.toFixed(1)}, 最小值: ${min.toFixed(1)}`)
  
  if (max === 0) return 100
  
  const balance = ((max - min) / max) * 100
  const balanceScore = Math.round(100 - balance)
  
  console.log(`  平衡度: ${balanceScore}%`)
  
  return balanceScore
}

// 运行所有测试
export function runAllTests() {
  console.log('🚀 开始运行交通分析功能测试')
  console.log('=' * 50)
  
  const stats = mockDirectionStats
  
  // 测试进度计算
  const progress = testProgressCalculation(stats)
  console.log('')
  
  // 测试拥堵等级
  const congestion = testCongestionCalculation(stats)
  console.log('')
  
  // 测试移动平均
  testMovingAverageCalculation(stats)
  console.log('')
  
  // 测试流量平衡
  const balance = testTrafficFlowBalance(stats)
  console.log('')
  
  console.log('📊 测试结果汇总:')
  console.log(`  整体进度: ${progress}%`)
  console.log(`  拥堵等级: ${congestion}`)
  console.log(`  流量平衡: ${balance}%`)
  console.log('')
  console.log('✅ 所有测试完成')
  
  return {
    progress,
    congestion,
    balance,
    stats
  }
}

// 模拟实时数据更新
export function simulateRealtimeUpdate(directionStats, direction, newVehicleCount) {
  console.log(`🔄 模拟${direction}方向实时更新: ${newVehicleCount}辆车`)
  
  if (!directionStats[direction]) {
    console.error(`❌ 方向 ${direction} 不存在`)
    return
  }
  
  const stats = directionStats[direction]
  
  // 更新当前车辆数
  stats.vehicleCount = newVehicleCount
  
  // 更新移动平均数据
  stats.recentVehicles.push(newVehicleCount)
  if (stats.recentVehicles.length > stats.maxRecentFrames) {
    stats.recentVehicles.shift()
  }
  
  // 重新计算移动平均
  stats.movingAverage = stats.recentVehicles.reduce((sum, count) => sum + count, 0) / stats.recentVehicles.length
  
  // 更新帧数
  stats.currentFrame += 1
  
  // 更新时间戳
  stats.lastUpdate = new Date()
  stats.lastFrameTime = Date.now()
  
  console.log(`  更新后移动平均: ${stats.movingAverage.toFixed(1)}`)
  console.log(`  当前帧: ${stats.currentFrame}/${stats.totalFrames}`)
  
  return stats
}

// 性能测试
export function performanceTest(iterations = 1000) {
  console.log(`⚡ 开始性能测试 (${iterations} 次迭代)`)
  
  const startTime = performance.now()
  
  for (let i = 0; i < iterations; i++) {
    // 模拟数据处理
    testProgressCalculation(mockDirectionStats)
    testCongestionCalculation(mockDirectionStats)
    testTrafficFlowBalance(mockDirectionStats)
  }
  
  const endTime = performance.now()
  const duration = endTime - startTime
  const avgTime = duration / iterations
  
  console.log(`  总耗时: ${duration.toFixed(2)}ms`)
  console.log(`  平均耗时: ${avgTime.toFixed(4)}ms/次`)
  console.log(`  处理速度: ${(1000 / avgTime).toFixed(0)} 次/秒`)
  
  return {
    totalTime: duration,
    averageTime: avgTime,
    throughput: 1000 / avgTime
  }
}

export default {
  mockDirectionStats,
  mockFrameData,
  testProgressCalculation,
  testCongestionCalculation,
  testMovingAverageCalculation,
  testTrafficFlowBalance,
  runAllTests,
  simulateRealtimeUpdate,
  performanceTest
}
