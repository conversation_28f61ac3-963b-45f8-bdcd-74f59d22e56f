package com.traffic.analysis.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 决策建议模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "decision_suggestions")
public class DecisionSuggestion {
    
    @Id
    private String id;
    
    /**
     * 仿真ID
     */
    private String simulationId;
    
    /**
     * 建议类型
     */
    private String suggestionType;
    
    /**
     * 优先级 (high, medium, low)
     */
    private String priority;
    
    /**
     * 建议标题
     */
    private String title;
    
    /**
     * 建议描述
     */
    private String description;
    
    /**
     * 具体行动建议
     */
    private String action;
    
    /**
     * 预期效果
     */
    private String expectedEffect;
    
    /**
     * 实施时间要求
     */
    private String implementationTime;
    
    /**
     * 置信度 (0.0 - 1.0)
     */
    private Double confidence;
    
    /**
     * 影响评分 (0.0 - 10.0)
     */
    private Double impactScore;
    
    /**
     * 相关数据
     */
    private Map<String, Object> relatedData;
    
    /**
     * 建议参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 适用条件
     */
    private List<String> applicableConditions;
    
    /**
     * 风险评估
     */
    private Map<String, Object> riskAssessment;
    
    /**
     * 生成算法
     */
    private String generationAlgorithm;
    
    /**
     * 状态 (pending, applied, rejected, expired)
     */
    private String status;
    
    /**
     * 应用结果
     */
    private Map<String, Object> applicationResult;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 应用时间
     */
    private LocalDateTime appliedAt;
    
    /**
     * 创建者ID
     */
    private String createdBy;
    
    /**
     * 应用者ID
     */
    private String appliedBy;
    
    /**
     * 有效期
     */
    private LocalDateTime expiresAt;
    
    /**
     * 标签
     */
    private List<String> tags;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 是否已应用
     */
    public boolean isApplied() {
        return "applied".equals(status);
    }
    
    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    /**
     * 是否为高优先级
     */
    public boolean isHighPriority() {
        return "high".equals(priority);
    }
    
    /**
     * 获取优先级权重
     */
    public int getPriorityWeight() {
        switch (priority != null ? priority.toLowerCase() : "low") {
            case "high":
                return 3;
            case "medium":
                return 2;
            case "low":
            default:
                return 1;
        }
    }
    
    /**
     * 获取建议摘要
     */
    public String getSummary() {
        return String.format("[%s] %s - %s", 
            priority != null ? priority.toUpperCase() : "UNKNOWN",
            title != null ? title : "无标题",
            expectedEffect != null ? expectedEffect : "无预期效果描述"
        );
    }
    
    /**
     * 设置为已应用状态
     */
    public void markAsApplied(String appliedByUserId) {
        this.status = "applied";
        this.appliedBy = appliedByUserId;
        this.appliedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 设置为已拒绝状态
     */
    public void markAsRejected() {
        this.status = "rejected";
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 设置为已过期状态
     */
    public void markAsExpired() {
        this.status = "expired";
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 更新应用结果
     */
    public void updateApplicationResult(Map<String, Object> result) {
        this.applicationResult = result;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 添加标签
     */
    public void addTag(String tag) {
        if (this.tags == null) {
            this.tags = new java.util.ArrayList<>();
        }
        if (!this.tags.contains(tag)) {
            this.tags.add(tag);
        }
    }
    
    /**
     * 移除标签
     */
    public void removeTag(String tag) {
        if (this.tags != null) {
            this.tags.remove(tag);
        }
    }
    
    /**
     * 检查是否包含标签
     */
    public boolean hasTag(String tag) {
        return this.tags != null && this.tags.contains(tag);
    }
}
