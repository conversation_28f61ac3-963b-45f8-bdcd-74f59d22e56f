#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
决策支持API模块

提供实时决策计算、信号灯控制指令生成等功能
"""

import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import Flask, request, jsonify
import requests

from sumo_integration.intelligent_decision import IntelligentDecisionEngine
from sumo_integration.traci_controller import TraciController
from sumo_integration.data_converter import DataConverter
from sumo_integration.optimization_engine import OptimizationEngine

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DecisionSupportAPI:
    """决策支持API类"""
    
    def __init__(self):
        """初始化决策支持API"""
        self.decision_engine = IntelligentDecisionEngine()
        self.traci_controller = TraciController()
        self.data_converter = DataConverter()
        self.optimization_engine = OptimizationEngine()
        
        # Java后端配置
        self.java_backend_url = "http://localhost:8080"
        
        logger.info("决策支持API初始化完成")
    
    def generate_decision_suggestions(self, simulation_id: str, traffic_data: Dict, 
                                    current_conditions: Optional[Dict] = None) -> List[Dict]:
        """
        生成智能决策建议
        
        Args:
            simulation_id: 仿真ID
            traffic_data: 交通数据
            current_conditions: 当前条件
            
        Returns:
            List[Dict]: 决策建议列表
        """
        try:
            logger.info(f"生成决策建议: simulation_id={simulation_id}")
            
            # 分析交通状态
            traffic_states = self.decision_engine.analyze_traffic_state(traffic_data)
            
            # 计算拥堵等级
            congestion_analysis = self.decision_engine.calculate_congestion_level(traffic_states)
            
            # 生成自适应信号配时
            current_timing = current_conditions.get('signal_timing') if current_conditions else None
            adaptive_timing = self.decision_engine.generate_adaptive_signal_timing(
                traffic_states, current_timing
            )
            
            # 执行优化分析
            optimization_result = self.optimization_engine.optimize_signal_timing(
                traffic_data, current_timing
            )
            
            # 生成智能建议
            suggestions = self.decision_engine.generate_intelligent_recommendations(
                traffic_states, optimization_result
            )
            
            # 转换为API响应格式
            formatted_suggestions = []
            for suggestion in suggestions:
                formatted_suggestion = {
                    'id': f"suggestion_{int(time.time() * 1000)}_{len(formatted_suggestions)}",
                    'simulation_id': simulation_id,
                    'type': suggestion.get('type', 'general'),
                    'priority': suggestion.get('priority', 'medium'),
                    'title': suggestion.get('title', ''),
                    'description': suggestion.get('description', ''),
                    'action': suggestion.get('action', ''),
                    'expected_effect': suggestion.get('expected_effect', ''),
                    'implementation_time': suggestion.get('implementation_time', ''),
                    'confidence': self._calculate_confidence(suggestion, congestion_analysis),
                    'impact_score': self._calculate_impact_score(suggestion, optimization_result),
                    'parameters': {
                        'adaptive_timing': adaptive_timing,
                        'congestion_analysis': congestion_analysis,
                        'optimization_result': optimization_result
                    },
                    'applicable_conditions': self._get_applicable_conditions(suggestion, traffic_states),
                    'risk_assessment': self._assess_risk(suggestion, traffic_states),
                    'created_at': datetime.now().isoformat(),
                    'expires_at': self._calculate_expiry_time(suggestion)
                }
                formatted_suggestions.append(formatted_suggestion)
            
            logger.info(f"生成了 {len(formatted_suggestions)} 个决策建议")
            return formatted_suggestions
            
        except Exception as e:
            logger.error(f"生成决策建议失败: {e}")
            return []
    
    def get_realtime_decision_support(self, simulation_id: str, current_state: Dict) -> Dict:
        """
        获取实时决策支持
        
        Args:
            simulation_id: 仿真ID
            current_state: 当前状态
            
        Returns:
            Dict: 实时决策支持数据
        """
        try:
            logger.info(f"获取实时决策支持: simulation_id={simulation_id}")
            
            # 分析当前交通状态
            traffic_states = self.decision_engine.analyze_traffic_state(current_state)
            
            # 计算拥堵等级
            congestion_analysis = self.decision_engine.calculate_congestion_level(traffic_states)
            
            # 生成实时建议
            realtime_suggestions = self._generate_realtime_suggestions(traffic_states, congestion_analysis)
            
            # 计算信号灯优化建议
            signal_optimization = self._calculate_signal_optimization(traffic_states)
            
            # 预测未来趋势
            trend_prediction = self._predict_traffic_trend(traffic_states, current_state)
            
            decision_support = {
                'simulation_id': simulation_id,
                'timestamp': datetime.now().isoformat(),
                'current_congestion': congestion_analysis,
                'realtime_suggestions': realtime_suggestions,
                'signal_optimization': signal_optimization,
                'trend_prediction': trend_prediction,
                'recommended_actions': self._get_recommended_actions(congestion_analysis),
                'alert_level': self._determine_alert_level(congestion_analysis),
                'next_update_in': 30  # 下次更新间隔（秒）
            }
            
            return decision_support
            
        except Exception as e:
            logger.error(f"获取实时决策支持失败: {e}")
            return {}
    
    def generate_traffic_light_control_commands(self, simulation_id: str, 
                                              control_request: Dict) -> Dict:
        """
        生成信号灯控制指令
        
        Args:
            simulation_id: 仿真ID
            control_request: 控制请求
            
        Returns:
            Dict: 控制指令
        """
        try:
            logger.info(f"生成信号灯控制指令: simulation_id={simulation_id}")
            
            control_type = control_request.get('type', 'auto')
            
            if control_type == 'manual':
                # 手动控制模式
                commands = self._generate_manual_control_commands(control_request)
            elif control_type == 'emergency':
                # 紧急控制模式
                commands = self._generate_emergency_control_commands()
            else:
                # 自动控制模式
                traffic_data = control_request.get('traffic_data', {})
                commands = self._generate_auto_control_commands(traffic_data)
            
            # 添加安全检查
            validated_commands = self._validate_control_commands(commands)
            
            control_result = {
                'simulation_id': simulation_id,
                'control_type': control_type,
                'commands': validated_commands,
                'timestamp': datetime.now().isoformat(),
                'estimated_effect': self._estimate_control_effect(validated_commands),
                'safety_check': 'passed' if validated_commands else 'failed'
            }
            
            return control_result
            
        except Exception as e:
            logger.error(f"生成信号灯控制指令失败: {e}")
            return {}
    
    def evaluate_decision_effect(self, simulation_id: str, decision_id: str,
                               before_data: Dict, after_data: Dict) -> Dict:
        """
        评估决策效果
        
        Args:
            simulation_id: 仿真ID
            decision_id: 决策ID
            before_data: 决策前数据
            after_data: 决策后数据
            
        Returns:
            Dict: 效果评估结果
        """
        try:
            logger.info(f"评估决策效果: simulation_id={simulation_id}, decision_id={decision_id}")
            
            # 计算关键指标变化
            metrics_comparison = self._compare_traffic_metrics(before_data, after_data)
            
            # 计算改善程度
            improvement_score = self._calculate_improvement_score(metrics_comparison)
            
            # 分析各方向效果
            direction_effects = self._analyze_direction_effects(before_data, after_data)
            
            # 计算整体效果评分
            overall_score = self._calculate_overall_effect_score(metrics_comparison, direction_effects)
            
            evaluation = {
                'simulation_id': simulation_id,
                'decision_id': decision_id,
                'evaluation_time': datetime.now().isoformat(),
                'metrics_comparison': metrics_comparison,
                'improvement_score': improvement_score,
                'direction_effects': direction_effects,
                'overall_score': overall_score,
                'effectiveness': self._determine_effectiveness(overall_score),
                'recommendations': self._generate_follow_up_recommendations(overall_score, metrics_comparison)
            }
            
            return evaluation
            
        except Exception as e:
            logger.error(f"评估决策效果失败: {e}")
            return {}
    
    def _calculate_confidence(self, suggestion: Dict, congestion_analysis: Dict) -> float:
        """计算建议的置信度"""
        base_confidence = 0.7
        
        # 基于拥堵等级调整置信度
        congestion_level = congestion_analysis.get('overall_level', 'low')
        if congestion_level == 'severe':
            base_confidence += 0.2
        elif congestion_level == 'high':
            base_confidence += 0.1
        
        # 基于建议类型调整
        suggestion_type = suggestion.get('type', '')
        if suggestion_type == 'emergency_control':
            base_confidence += 0.1
        
        return min(base_confidence, 1.0)
    
    def _calculate_impact_score(self, suggestion: Dict, optimization_result: Dict) -> float:
        """计算影响评分"""
        base_score = 5.0
        
        # 基于优化结果调整评分
        improvement = optimization_result.get('improvement_percentage', 0)
        if improvement > 20:
            base_score += 3.0
        elif improvement > 10:
            base_score += 2.0
        elif improvement > 5:
            base_score += 1.0
        
        return min(base_score, 10.0)
    
    def _get_applicable_conditions(self, suggestion: Dict, traffic_states: Dict) -> List[str]:
        """获取适用条件"""
        conditions = []
        
        # 基于交通状态确定适用条件
        for direction, state in traffic_states.items():
            if state.vehicle_count > 50:
                conditions.append(f"{direction}方向车流量大")
            if state.average_speed < 20:
                conditions.append(f"{direction}方向速度较慢")
        
        return conditions
    
    def _assess_risk(self, suggestion: Dict, traffic_states: Dict) -> Dict:
        """评估风险"""
        risk_level = "low"
        risk_factors = []
        
        # 基于建议类型评估风险
        suggestion_type = suggestion.get('type', '')
        if suggestion_type == 'emergency_control':
            risk_level = "medium"
            risk_factors.append("紧急控制可能影响正常通行")
        
        return {
            'level': risk_level,
            'factors': risk_factors,
            'mitigation': "建议在实施前进行仿真验证"
        }
    
    def _calculate_expiry_time(self, suggestion: Dict) -> str:
        """计算建议过期时间"""
        from datetime import timedelta
        
        # 基于建议类型设置过期时间
        suggestion_type = suggestion.get('type', '')
        if suggestion_type == 'emergency_control':
            expiry = datetime.now() + timedelta(minutes=5)
        else:
            expiry = datetime.now() + timedelta(minutes=30)
        
        return expiry.isoformat()

    def _generate_realtime_suggestions(self, traffic_states: Dict, congestion_analysis: Dict) -> List[Dict]:
        """生成实时建议"""
        suggestions = []

        overall_level = congestion_analysis.get('overall_level', 'low')

        if overall_level in ['high', 'severe']:
            suggestions.append({
                'type': 'immediate_action',
                'message': '检测到严重拥堵，建议立即调整信号配时',
                'priority': 'high',
                'action': 'optimize_signal_timing'
            })

        # 检查各方向状态
        for direction, level_info in congestion_analysis.get('direction_levels', {}).items():
            if level_info.get('level') == 'high':
                suggestions.append({
                    'type': 'direction_optimization',
                    'message': f'{direction}方向拥堵，建议增加绿灯时间',
                    'priority': 'medium',
                    'action': f'increase_green_time_{direction}'
                })

        return suggestions

    def _calculate_signal_optimization(self, traffic_states: Dict) -> Dict:
        """计算信号灯优化建议"""
        optimization = {
            'recommended_cycle': 120,
            'phase_adjustments': {},
            'priority_directions': []
        }

        # 找出需要优先处理的方向
        for direction, state in traffic_states.items():
            if state.vehicle_count > 30 or state.waiting_time > 60:
                optimization['priority_directions'].append(direction)
                optimization['phase_adjustments'][direction] = {
                    'increase_green_time': min(20, state.vehicle_count // 2)
                }

        return optimization

    def _predict_traffic_trend(self, traffic_states: Dict, current_state: Dict) -> Dict:
        """预测交通趋势"""
        trend = {
            'next_5_minutes': 'stable',
            'next_15_minutes': 'stable',
            'confidence': 0.7,
            'factors': []
        }

        # 简单的趋势预测逻辑
        total_vehicles = sum(state.vehicle_count for state in traffic_states.values())

        if total_vehicles > 100:
            trend['next_5_minutes'] = 'increasing'
            trend['next_15_minutes'] = 'peak'
            trend['factors'].append('高车流量')

        return trend

    def _get_recommended_actions(self, congestion_analysis: Dict) -> List[str]:
        """获取推荐行动"""
        actions = []

        overall_level = congestion_analysis.get('overall_level', 'low')

        if overall_level == 'severe':
            actions.extend([
                '启动应急交通控制',
                '通知交通管理部门',
                '考虑分流措施'
            ])
        elif overall_level == 'high':
            actions.extend([
                '优化信号配时',
                '监控交通状况',
                '准备应急预案'
            ])

        return actions

    def _determine_alert_level(self, congestion_analysis: Dict) -> str:
        """确定警报级别"""
        overall_level = congestion_analysis.get('overall_level', 'low')

        alert_mapping = {
            'low': 'green',
            'medium': 'yellow',
            'high': 'orange',
            'severe': 'red'
        }

        return alert_mapping.get(overall_level, 'green')

    def _generate_manual_control_commands(self, control_request: Dict) -> Dict:
        """生成手动控制指令"""
        lights = control_request.get('lights', {})
        commands = {}

        for direction, state in lights.items():
            if state.get('red'):
                commands[direction] = 'r'
            elif state.get('yellow'):
                commands[direction] = 'y'
            elif state.get('green'):
                commands[direction] = 'G'
            else:
                commands[direction] = 'r'  # 默认红灯

        return commands

    def _generate_emergency_control_commands(self) -> Dict:
        """生成紧急控制指令"""
        # 所有方向红灯
        return {
            'north': 'r',
            'south': 'r',
            'east': 'r',
            'west': 'r'
        }

    def _generate_auto_control_commands(self, traffic_data: Dict) -> Dict:
        """生成自动控制指令"""
        # 基于交通数据生成最优信号配时
        traffic_states = self.decision_engine.analyze_traffic_state(traffic_data)
        adaptive_timing = self.decision_engine.generate_adaptive_signal_timing(traffic_states)

        # 转换为控制指令
        commands = {}
        phases = adaptive_timing.get('phases', [])

        if phases:
            current_phase = phases[0]  # 使用第一个相位
            state = current_phase.get('state', 'rrrr')

            directions = ['north', 'south', 'east', 'west']
            for i, direction in enumerate(directions):
                if i < len(state):
                    commands[direction] = state[i]
                else:
                    commands[direction] = 'r'

        return commands

    def _validate_control_commands(self, commands: Dict) -> Dict:
        """验证控制指令的安全性"""
        validated = {}

        # 确保不会出现冲突的绿灯
        green_directions = [d for d, state in commands.items() if state == 'G']

        # 简单的冲突检查：东西和南北不能同时绿灯
        if 'east' in green_directions and 'west' in green_directions:
            if 'north' in green_directions or 'south' in green_directions:
                # 冲突：重置为安全状态
                for direction in commands:
                    validated[direction] = 'r'
                return validated

        return commands

    def _estimate_control_effect(self, commands: Dict) -> Dict:
        """估算控制效果"""
        green_count = sum(1 for state in commands.values() if state == 'G')

        return {
            'throughput_change': green_count * 10,  # 简化估算
            'delay_change': -green_count * 5,
            'safety_level': 'high' if green_count <= 2 else 'medium'
        }

    def _compare_traffic_metrics(self, before_data: Dict, after_data: Dict) -> Dict:
        """比较交通指标"""
        comparison = {}

        metrics = ['vehicle_count', 'average_speed', 'waiting_time', 'throughput']

        for metric in metrics:
            before_value = before_data.get(metric, 0)
            after_value = after_data.get(metric, 0)

            if before_value > 0:
                change_percentage = ((after_value - before_value) / before_value) * 100
            else:
                change_percentage = 0

            comparison[metric] = {
                'before': before_value,
                'after': after_value,
                'change': after_value - before_value,
                'change_percentage': change_percentage
            }

        return comparison

    def _calculate_improvement_score(self, metrics_comparison: Dict) -> float:
        """计算改善评分"""
        score = 0.0

        # 速度提升是正面的
        speed_change = metrics_comparison.get('average_speed', {}).get('change_percentage', 0)
        if speed_change > 0:
            score += min(speed_change / 10, 3.0)

        # 等待时间减少是正面的
        wait_change = metrics_comparison.get('waiting_time', {}).get('change_percentage', 0)
        if wait_change < 0:
            score += min(abs(wait_change) / 10, 3.0)

        # 通行能力提升是正面的
        throughput_change = metrics_comparison.get('throughput', {}).get('change_percentage', 0)
        if throughput_change > 0:
            score += min(throughput_change / 10, 4.0)

        return min(score, 10.0)

    def _analyze_direction_effects(self, before_data: Dict, after_data: Dict) -> Dict:
        """分析各方向效果"""
        direction_effects = {}

        directions = ['north', 'south', 'east', 'west']

        for direction in directions:
            before_dir = before_data.get('directions', {}).get(direction, {})
            after_dir = after_data.get('directions', {}).get(direction, {})

            if before_dir and after_dir:
                direction_effects[direction] = self._compare_traffic_metrics(before_dir, after_dir)

        return direction_effects

    def _calculate_overall_effect_score(self, metrics_comparison: Dict, direction_effects: Dict) -> float:
        """计算整体效果评分"""
        overall_score = self._calculate_improvement_score(metrics_comparison)

        # 考虑各方向的平衡性
        direction_scores = []
        for direction, effects in direction_effects.items():
            direction_score = self._calculate_improvement_score(effects)
            direction_scores.append(direction_score)

        if direction_scores:
            avg_direction_score = sum(direction_scores) / len(direction_scores)
            overall_score = (overall_score + avg_direction_score) / 2

        return overall_score

    def _determine_effectiveness(self, overall_score: float) -> str:
        """确定有效性等级"""
        if overall_score >= 8.0:
            return 'excellent'
        elif overall_score >= 6.0:
            return 'good'
        elif overall_score >= 4.0:
            return 'moderate'
        elif overall_score >= 2.0:
            return 'poor'
        else:
            return 'ineffective'

    def _generate_follow_up_recommendations(self, overall_score: float, metrics_comparison: Dict) -> List[str]:
        """生成后续建议"""
        recommendations = []

        if overall_score < 4.0:
            recommendations.append("建议重新评估决策策略")
            recommendations.append("考虑采用不同的优化方法")

        # 基于具体指标给出建议
        speed_change = metrics_comparison.get('average_speed', {}).get('change_percentage', 0)
        if speed_change < -5:
            recommendations.append("注意速度下降，可能需要调整信号配时")

        wait_change = metrics_comparison.get('waiting_time', {}).get('change_percentage', 0)
        if wait_change > 10:
            recommendations.append("等待时间增加，建议优化相位设计")

        return recommendations

# Flask应用实例
decision_api = DecisionSupportAPI()

def create_decision_api_app():
    """创建决策支持API Flask应用"""
    app = Flask(__name__)
    
    @app.route('/decision/suggestions/generate', methods=['POST'])
    def generate_suggestions():
        """生成决策建议API端点"""
        try:
            data = request.get_json()
            simulation_id = data.get('simulation_id')
            traffic_data = data.get('traffic_data')
            current_conditions = data.get('current_conditions')
            
            if not simulation_id or not traffic_data:
                return jsonify({'error': '缺少必要参数'}), 400
            
            suggestions = decision_api.generate_decision_suggestions(
                simulation_id, traffic_data, current_conditions
            )
            
            return jsonify({
                'status': 'success',
                'suggestions': suggestions,
                'count': len(suggestions)
            })
            
        except Exception as e:
            logger.error(f"生成决策建议API失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/decision/realtime', methods=['POST'])
    def get_realtime_support():
        """获取实时决策支持API端点"""
        try:
            data = request.get_json()
            simulation_id = data.get('simulation_id')
            current_state = data.get('current_state')
            
            if not simulation_id or not current_state:
                return jsonify({'error': '缺少必要参数'}), 400
            
            decision_support = decision_api.get_realtime_decision_support(
                simulation_id, current_state
            )
            
            return jsonify({
                'status': 'success',
                'decision_support': decision_support
            })
            
        except Exception as e:
            logger.error(f"获取实时决策支持API失败: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/decision/traffic-lights/control', methods=['POST'])
    def control_traffic_lights():
        """信号灯控制API端点"""
        try:
            data = request.get_json()
            simulation_id = data.get('simulation_id')
            control_request = data.get('control_request')

            if not simulation_id or not control_request:
                return jsonify({'error': '缺少必要参数'}), 400

            control_result = decision_api.generate_traffic_light_control_commands(
                simulation_id, control_request
            )

            return jsonify({
                'status': 'success',
                'control_result': control_result
            })

        except Exception as e:
            logger.error(f"信号灯控制API失败: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/decision/evaluation', methods=['POST'])
    def evaluate_effect():
        """决策效果评估API端点"""
        try:
            data = request.get_json()
            simulation_id = data.get('simulation_id')
            decision_id = data.get('decision_id')
            before_data = data.get('before_data')
            after_data = data.get('after_data')

            if not all([simulation_id, decision_id, before_data, after_data]):
                return jsonify({'error': '缺少必要参数'}), 400

            evaluation = decision_api.evaluate_decision_effect(
                simulation_id, decision_id, before_data, after_data
            )

            return jsonify({
                'status': 'success',
                'evaluation': evaluation
            })

        except Exception as e:
            logger.error(f"决策效果评估API失败: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/decision/status', methods=['GET'])
    def get_service_status():
        """获取服务状态API端点"""
        try:
            status = {
                'service': 'decision_support_api',
                'status': 'running',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0',
                'components': {
                    'decision_engine': 'active',
                    'traci_controller': 'active',
                    'data_converter': 'active',
                    'optimization_engine': 'active'
                }
            }

            return jsonify(status)

        except Exception as e:
            logger.error(f"获取服务状态失败: {e}")
            return jsonify({'error': str(e)}), 500

    return app

if __name__ == '__main__':
    app = create_decision_api_app()
    app.run(host='0.0.0.0', port=5002, debug=True)
