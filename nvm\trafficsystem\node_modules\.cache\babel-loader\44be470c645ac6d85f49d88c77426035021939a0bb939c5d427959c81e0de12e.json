{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { ref, reactive, onMounted, onUnmounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { BrainIcon, Refresh, LightbulbIcon, TrendCharts, DataAnalysis, Clock, Monitor } from '@element-plus/icons-vue';\nimport decisionApi from '@/api/decision';\nimport stompService from '@/utils/stomp-service';\nexport default {\n  name: 'DecisionSupportPanel',\n  components: {\n    BrainIcon,\n    Refresh,\n    LightbulbIcon,\n    TrendCharts,\n    DataAnalysis,\n    Clock,\n    Monitor\n  },\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['suggestion-applied', 'decision-made'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const loading = ref(false);\n    const suggestions = ref([]);\n    const realtimeSupport = ref(null);\n    const applyingIds = ref([]);\n    const showDetailDialog = ref(false);\n    const selectedSuggestion = ref(null);\n\n    // WebSocket订阅\n    let decisionSubscription = null;\n\n    // 计算属性\n    const alertTitle = computed(() => {\n      if (!realtimeSupport.value) return '等待决策数据...';\n      const level = realtimeSupport.value.alert_level;\n      const titles = {\n        'green': '交通状况良好',\n        'yellow': '交通状况一般',\n        'orange': '交通拥堵',\n        'red': '严重拥堵'\n      };\n      return titles[level] || '状态未知';\n    });\n    const alertType = computed(() => {\n      if (!realtimeSupport.value) return 'info';\n      const level = realtimeSupport.value.alert_level;\n      const types = {\n        'green': 'success',\n        'yellow': 'warning',\n        'orange': 'warning',\n        'red': 'error'\n      };\n      return types[level] || 'info';\n    });\n    const alertDescription = computed(() => {\n      if (!realtimeSupport.value) return '';\n      const congestion = realtimeSupport.value.current_congestion;\n      if (congestion) {\n        return `整体拥堵评分: ${(congestion.congestion_score * 100).toFixed(0)}%`;\n      }\n      return '';\n    });\n\n    // 方法\n    const getPriorityTagType = priority => {\n      const types = {\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info'\n      };\n      return types[priority] || 'info';\n    };\n    const getPriorityText = priority => {\n      const texts = {\n        'high': '高优先级',\n        'medium': '中优先级',\n        'low': '低优先级'\n      };\n      return texts[priority] || '未知';\n    };\n    const getCongestionText = level => {\n      const texts = {\n        'low': '畅通',\n        'medium': '一般',\n        'high': '拥堵',\n        'severe': '严重拥堵'\n      };\n      return texts[level] || '未知';\n    };\n    const getAlertText = level => {\n      const texts = {\n        'green': '正常',\n        'yellow': '注意',\n        'orange': '警告',\n        'red': '紧急'\n      };\n      return texts[level] || '未知';\n    };\n    const getRiskAlertType = level => {\n      const types = {\n        'low': 'success',\n        'medium': 'warning',\n        'high': 'error'\n      };\n      return types[level] || 'info';\n    };\n    const loadSuggestions = async () => {\n      try {\n        loading.value = true;\n\n        // 这里应该获取当前交通数据\n        const trafficData = {\n          // 模拟交通数据\n          directions: {\n            north: {\n              vehicleCount: 25,\n              averageSpeed: 30,\n              density: 0.6\n            },\n            south: {\n              vehicleCount: 30,\n              averageSpeed: 25,\n              density: 0.7\n            },\n            east: {\n              vehicleCount: 20,\n              averageSpeed: 35,\n              density: 0.5\n            },\n            west: {\n              vehicleCount: 28,\n              averageSpeed: 28,\n              density: 0.65\n            }\n          }\n        };\n        const response = await decisionApi.generateSuggestions({\n          simulation_id: props.simulationId,\n          traffic_data: trafficData,\n          current_conditions: {}\n        });\n        if (response.status === 'success') {\n          suggestions.value = response.suggestions || [];\n        }\n      } catch (error) {\n        console.error('加载决策建议失败:', error);\n        ElMessage.error('加载决策建议失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n    const loadRealtimeSupport = async () => {\n      try {\n        const currentState = {\n          // 模拟当前状态数据\n          directions: {\n            north: {\n              vehicleCount: 25,\n              averageSpeed: 30,\n              density: 0.6\n            },\n            south: {\n              vehicleCount: 30,\n              averageSpeed: 25,\n              density: 0.7\n            },\n            east: {\n              vehicleCount: 20,\n              averageSpeed: 35,\n              density: 0.5\n            },\n            west: {\n              vehicleCount: 28,\n              averageSpeed: 28,\n              density: 0.65\n            }\n          }\n        };\n        const response = await decisionApi.getRealtimeSupport({\n          simulation_id: props.simulationId,\n          current_state: currentState\n        });\n        if (response.status === 'success') {\n          realtimeSupport.value = response.decision_support;\n        }\n      } catch (error) {\n        console.error('加载实时决策支持失败:', error);\n      }\n    };\n    const refreshSuggestions = async () => {\n      await Promise.all([loadSuggestions(), loadRealtimeSupport()]);\n    };\n    const applySuggestion = async suggestion => {\n      try {\n        await ElMessageBox.confirm(`确定要应用建议\"${suggestion.title}\"吗？`, '确认应用', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        applyingIds.value.push(suggestion.id);\n        const userId = localStorage.getItem('userId');\n        const response = await decisionApi.applySuggestion(suggestion.id, {\n          simulation_id: props.simulationId,\n          user_id: userId\n        });\n        if (response.status === 'success') {\n          ElMessage.success('决策建议应用成功');\n          emit('suggestion-applied', suggestion);\n\n          // 刷新建议列表\n          await refreshSuggestions();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('应用决策建议失败:', error);\n          ElMessage.error('应用决策建议失败');\n        }\n      } finally {\n        applyingIds.value = applyingIds.value.filter(id => id !== suggestion.id);\n      }\n    };\n    const viewSuggestionDetail = suggestion => {\n      selectedSuggestion.value = suggestion;\n      showDetailDialog.value = true;\n    };\n    const applySuggestionFromDialog = async () => {\n      if (selectedSuggestion.value) {\n        await applySuggestion(selectedSuggestion.value);\n        showDetailDialog.value = false;\n      }\n    };\n    const setupWebSocketSubscription = async () => {\n      try {\n        await stompService.connect();\n        decisionSubscription = await stompService.subscribe(`/topic/decision-support/${props.simulationId}`, message => {\n          try {\n            const data = JSON.parse(message.body);\n            handleDecisionUpdate(data);\n          } catch (error) {\n            console.error('解析决策支持数据失败:', error);\n          }\n        });\n      } catch (error) {\n        console.error('设置决策支持WebSocket订阅失败:', error);\n      }\n    };\n    const handleDecisionUpdate = data => {\n      if (data.type === 'decision_suggestions') {\n        suggestions.value = data.suggestions || [];\n      } else if (data.type === 'realtime_decision_support') {\n        realtimeSupport.value = data.decisionSupport;\n      } else if (data.type === 'decision_application_result') {\n        ElMessage.success('决策应用结果已更新');\n        refreshSuggestions();\n      }\n    };\n    const cleanup = () => {\n      if (decisionSubscription) {\n        stompService.unsubscribe(decisionSubscription);\n        decisionSubscription = null;\n      }\n    };\n\n    // 生命周期\n    onMounted(async () => {\n      await refreshSuggestions();\n      await setupWebSocketSubscription();\n    });\n    onUnmounted(() => {\n      cleanup();\n    });\n    return {\n      loading,\n      suggestions,\n      realtimeSupport,\n      applyingIds,\n      showDetailDialog,\n      selectedSuggestion,\n      alertTitle,\n      alertType,\n      alertDescription,\n      getPriorityTagType,\n      getPriorityText,\n      getCongestionText,\n      getAlertText,\n      getRiskAlertType,\n      refreshSuggestions,\n      applySuggestion,\n      viewSuggestionDetail,\n      applySuggestionFromDialog\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "onUnmounted", "computed", "ElMessage", "ElMessageBox", "BrainIcon", "Refresh", "LightbulbIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataAnalysis", "Clock", "Monitor", "decisionApi", "stompService", "name", "components", "props", "simulationId", "type", "String", "required", "emits", "setup", "emit", "loading", "suggestions", "realtimeSupport", "applyingIds", "showDetailDialog", "selectedSuggestion", "decisionSubscription", "alertTitle", "value", "level", "alert_level", "titles", "alertType", "types", "alertDescription", "congestion", "current_congestion", "congestion_score", "toFixed", "getPriorityTagType", "priority", "getPriorityText", "texts", "getCongestionText", "getAlertText", "getRiskAlertType", "loadSuggestions", "trafficData", "directions", "north", "vehicleCount", "averageSpeed", "density", "south", "east", "west", "response", "generateSuggestions", "simulation_id", "traffic_data", "current_conditions", "status", "error", "console", "loadRealtimeSupport", "currentState", "getRealtimeSupport", "current_state", "decision_support", "refreshSuggestions", "Promise", "all", "applySuggestion", "suggestion", "confirm", "title", "confirmButtonText", "cancelButtonText", "push", "id", "userId", "localStorage", "getItem", "user_id", "success", "filter", "viewSuggestionDetail", "applySuggestionFromDialog", "setupWebSocketSubscription", "connect", "subscribe", "message", "data", "JSON", "parse", "body", "handleDecisionUpdate", "decisionSupport", "cleanup", "unsubscribe"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\DecisionSupportPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"decision-support-panel\">\n    <el-card class=\"panel-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <span class=\"title\">\n            <el-icon><BrainIcon /></el-icon>\n            智能决策支持\n          </span>\n          <div class=\"panel-actions\">\n            <el-button \n              size=\"small\" \n              @click=\"refreshSuggestions\"\n              :loading=\"loading\"\n            >\n              <el-icon><Refresh /></el-icon>\n              刷新建议\n            </el-button>\n          </div>\n        </div>\n      </template>\n\n      <!-- 实时决策状态 -->\n      <div class=\"decision-status\">\n        <el-alert\n          :title=\"alertTitle\"\n          :type=\"alertType\"\n          :description=\"alertDescription\"\n          show-icon\n          :closable=\"false\"\n        />\n      </div>\n\n      <!-- 决策建议列表 -->\n      <div class=\"suggestions-section\">\n        <h4>\n          <el-icon><LightbulbIcon /></el-icon>\n          智能建议 ({{ suggestions.length }})\n        </h4>\n        \n        <div v-if=\"suggestions.length === 0\" class=\"no-suggestions\">\n          <el-empty description=\"暂无决策建议\" />\n        </div>\n        \n        <div v-else class=\"suggestions-list\">\n          <div \n            v-for=\"suggestion in suggestions\" \n            :key=\"suggestion.id\"\n            class=\"suggestion-item\"\n            :class=\"[`priority-${suggestion.priority}`]\"\n          >\n            <div class=\"suggestion-header\">\n              <div class=\"suggestion-title\">\n                <el-tag \n                  :type=\"getPriorityTagType(suggestion.priority)\" \n                  size=\"small\"\n                >\n                  {{ getPriorityText(suggestion.priority) }}\n                </el-tag>\n                <span class=\"title-text\">{{ suggestion.title }}</span>\n              </div>\n              <div class=\"suggestion-actions\">\n                <el-button \n                  type=\"primary\" \n                  size=\"small\"\n                  @click=\"applySuggestion(suggestion)\"\n                  :loading=\"applyingIds.includes(suggestion.id)\"\n                >\n                  应用\n                </el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"viewSuggestionDetail(suggestion)\"\n                >\n                  详情\n                </el-button>\n              </div>\n            </div>\n            \n            <div class=\"suggestion-content\">\n              <p class=\"description\">{{ suggestion.description }}</p>\n              <div class=\"suggestion-meta\">\n                <span class=\"confidence\">\n                  <el-icon><TrendCharts /></el-icon>\n                  置信度: {{ (suggestion.confidence * 100).toFixed(0) }}%\n                </span>\n                <span class=\"impact\">\n                  <el-icon><DataAnalysis /></el-icon>\n                  影响评分: {{ suggestion.impact_score.toFixed(1) }}/10\n                </span>\n                <span class=\"time\">\n                  <el-icon><Clock /></el-icon>\n                  {{ suggestion.implementation_time }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 实时决策支持数据 -->\n      <div class=\"realtime-support\" v-if=\"realtimeSupport\">\n        <h4>\n          <el-icon><Monitor /></el-icon>\n          实时分析\n        </h4>\n        \n        <el-row :gutter=\"16\">\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">拥堵等级</div>\n              <div class=\"metric-value\" :class=\"`congestion-${realtimeSupport.current_congestion?.overall_level}`\">\n                {{ getCongestionText(realtimeSupport.current_congestion?.overall_level) }}\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">警报级别</div>\n              <div class=\"metric-value\" :class=\"`alert-${realtimeSupport.alert_level}`\">\n                {{ getAlertText(realtimeSupport.alert_level) }}\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">下次更新</div>\n              <div class=\"metric-value\">\n                {{ realtimeSupport.next_update_in }}秒\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n\n        <!-- 推荐行动 -->\n        <div class=\"recommended-actions\" v-if=\"realtimeSupport.recommended_actions?.length\">\n          <h5>推荐行动:</h5>\n          <ul>\n            <li v-for=\"action in realtimeSupport.recommended_actions\" :key=\"action\">\n              {{ action }}\n            </li>\n          </ul>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 建议详情对话框 -->\n    <el-dialog\n      v-model=\"showDetailDialog\"\n      title=\"决策建议详情\"\n      width=\"600px\"\n    >\n      <div v-if=\"selectedSuggestion\" class=\"suggestion-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"建议类型\">\n            {{ selectedSuggestion.type }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"优先级\">\n            <el-tag :type=\"getPriorityTagType(selectedSuggestion.priority)\">\n              {{ getPriorityText(selectedSuggestion.priority) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"置信度\">\n            {{ (selectedSuggestion.confidence * 100).toFixed(0) }}%\n          </el-descriptions-item>\n          <el-descriptions-item label=\"影响评分\">\n            {{ selectedSuggestion.impact_score.toFixed(1) }}/10\n          </el-descriptions-item>\n          <el-descriptions-item label=\"实施时间\" span=\"2\">\n            {{ selectedSuggestion.implementation_time }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"预期效果\" span=\"2\">\n            {{ selectedSuggestion.expected_effect }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"具体行动\" span=\"2\">\n            {{ selectedSuggestion.action }}\n          </el-descriptions-item>\n        </el-descriptions>\n\n        <!-- 风险评估 -->\n        <div v-if=\"selectedSuggestion.risk_assessment\" class=\"risk-assessment\">\n          <h4>风险评估</h4>\n          <el-alert\n            :title=\"`风险等级: ${selectedSuggestion.risk_assessment.level}`\"\n            :type=\"getRiskAlertType(selectedSuggestion.risk_assessment.level)\"\n            :description=\"selectedSuggestion.risk_assessment.mitigation\"\n            show-icon\n          />\n        </div>\n\n        <!-- 适用条件 -->\n        <div v-if=\"selectedSuggestion.applicable_conditions?.length\" class=\"applicable-conditions\">\n          <h4>适用条件</h4>\n          <ul>\n            <li v-for=\"condition in selectedSuggestion.applicable_conditions\" :key=\"condition\">\n              {{ condition }}\n            </li>\n          </ul>\n        </div>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n          <el-button \n            type=\"primary\" \n            @click=\"applySuggestionFromDialog\"\n            :loading=\"applyingIds.includes(selectedSuggestion?.id)\"\n          >\n            应用建议\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { \n  BrainIcon, \n  Refresh, \n  LightbulbIcon, \n  TrendCharts, \n  DataAnalysis, \n  Clock,\n  Monitor\n} from '@element-plus/icons-vue'\nimport decisionApi from '@/api/decision'\nimport stompService from '@/utils/stomp-service'\n\nexport default {\n  name: 'DecisionSupportPanel',\n  components: {\n    BrainIcon,\n    Refresh,\n    LightbulbIcon,\n    TrendCharts,\n    DataAnalysis,\n    Clock,\n    Monitor\n  },\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['suggestion-applied', 'decision-made'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const loading = ref(false)\n    const suggestions = ref([])\n    const realtimeSupport = ref(null)\n    const applyingIds = ref([])\n    const showDetailDialog = ref(false)\n    const selectedSuggestion = ref(null)\n    \n    // WebSocket订阅\n    let decisionSubscription = null\n    \n    // 计算属性\n    const alertTitle = computed(() => {\n      if (!realtimeSupport.value) return '等待决策数据...'\n      \n      const level = realtimeSupport.value.alert_level\n      const titles = {\n        'green': '交通状况良好',\n        'yellow': '交通状况一般',\n        'orange': '交通拥堵',\n        'red': '严重拥堵'\n      }\n      return titles[level] || '状态未知'\n    })\n    \n    const alertType = computed(() => {\n      if (!realtimeSupport.value) return 'info'\n      \n      const level = realtimeSupport.value.alert_level\n      const types = {\n        'green': 'success',\n        'yellow': 'warning',\n        'orange': 'warning',\n        'red': 'error'\n      }\n      return types[level] || 'info'\n    })\n    \n    const alertDescription = computed(() => {\n      if (!realtimeSupport.value) return ''\n      \n      const congestion = realtimeSupport.value.current_congestion\n      if (congestion) {\n        return `整体拥堵评分: ${(congestion.congestion_score * 100).toFixed(0)}%`\n      }\n      return ''\n    })\n    \n    // 方法\n    const getPriorityTagType = (priority) => {\n      const types = {\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info'\n      }\n      return types[priority] || 'info'\n    }\n\n    const getPriorityText = (priority) => {\n      const texts = {\n        'high': '高优先级',\n        'medium': '中优先级',\n        'low': '低优先级'\n      }\n      return texts[priority] || '未知'\n    }\n\n    const getCongestionText = (level) => {\n      const texts = {\n        'low': '畅通',\n        'medium': '一般',\n        'high': '拥堵',\n        'severe': '严重拥堵'\n      }\n      return texts[level] || '未知'\n    }\n\n    const getAlertText = (level) => {\n      const texts = {\n        'green': '正常',\n        'yellow': '注意',\n        'orange': '警告',\n        'red': '紧急'\n      }\n      return texts[level] || '未知'\n    }\n\n    const getRiskAlertType = (level) => {\n      const types = {\n        'low': 'success',\n        'medium': 'warning',\n        'high': 'error'\n      }\n      return types[level] || 'info'\n    }\n\n    const loadSuggestions = async () => {\n      try {\n        loading.value = true\n\n        // 这里应该获取当前交通数据\n        const trafficData = {\n          // 模拟交通数据\n          directions: {\n            north: { vehicleCount: 25, averageSpeed: 30, density: 0.6 },\n            south: { vehicleCount: 30, averageSpeed: 25, density: 0.7 },\n            east: { vehicleCount: 20, averageSpeed: 35, density: 0.5 },\n            west: { vehicleCount: 28, averageSpeed: 28, density: 0.65 }\n          }\n        }\n\n        const response = await decisionApi.generateSuggestions({\n          simulation_id: props.simulationId,\n          traffic_data: trafficData,\n          current_conditions: {}\n        })\n\n        if (response.status === 'success') {\n          suggestions.value = response.suggestions || []\n        }\n\n      } catch (error) {\n        console.error('加载决策建议失败:', error)\n        ElMessage.error('加载决策建议失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    const loadRealtimeSupport = async () => {\n      try {\n        const currentState = {\n          // 模拟当前状态数据\n          directions: {\n            north: { vehicleCount: 25, averageSpeed: 30, density: 0.6 },\n            south: { vehicleCount: 30, averageSpeed: 25, density: 0.7 },\n            east: { vehicleCount: 20, averageSpeed: 35, density: 0.5 },\n            west: { vehicleCount: 28, averageSpeed: 28, density: 0.65 }\n          }\n        }\n\n        const response = await decisionApi.getRealtimeSupport({\n          simulation_id: props.simulationId,\n          current_state: currentState\n        })\n\n        if (response.status === 'success') {\n          realtimeSupport.value = response.decision_support\n        }\n\n      } catch (error) {\n        console.error('加载实时决策支持失败:', error)\n      }\n    }\n\n    const refreshSuggestions = async () => {\n      await Promise.all([loadSuggestions(), loadRealtimeSupport()])\n    }\n\n    const applySuggestion = async (suggestion) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要应用建议\"${suggestion.title}\"吗？`,\n          '确认应用',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        applyingIds.value.push(suggestion.id)\n\n        const userId = localStorage.getItem('userId')\n        const response = await decisionApi.applySuggestion(suggestion.id, {\n          simulation_id: props.simulationId,\n          user_id: userId\n        })\n\n        if (response.status === 'success') {\n          ElMessage.success('决策建议应用成功')\n          emit('suggestion-applied', suggestion)\n\n          // 刷新建议列表\n          await refreshSuggestions()\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('应用决策建议失败:', error)\n          ElMessage.error('应用决策建议失败')\n        }\n      } finally {\n        applyingIds.value = applyingIds.value.filter(id => id !== suggestion.id)\n      }\n    }\n\n    const viewSuggestionDetail = (suggestion) => {\n      selectedSuggestion.value = suggestion\n      showDetailDialog.value = true\n    }\n\n    const applySuggestionFromDialog = async () => {\n      if (selectedSuggestion.value) {\n        await applySuggestion(selectedSuggestion.value)\n        showDetailDialog.value = false\n      }\n    }\n\n    const setupWebSocketSubscription = async () => {\n      try {\n        await stompService.connect()\n\n        decisionSubscription = await stompService.subscribe(\n          `/topic/decision-support/${props.simulationId}`,\n          (message) => {\n            try {\n              const data = JSON.parse(message.body)\n              handleDecisionUpdate(data)\n            } catch (error) {\n              console.error('解析决策支持数据失败:', error)\n            }\n          }\n        )\n\n      } catch (error) {\n        console.error('设置决策支持WebSocket订阅失败:', error)\n      }\n    }\n\n    const handleDecisionUpdate = (data) => {\n      if (data.type === 'decision_suggestions') {\n        suggestions.value = data.suggestions || []\n      } else if (data.type === 'realtime_decision_support') {\n        realtimeSupport.value = data.decisionSupport\n      } else if (data.type === 'decision_application_result') {\n        ElMessage.success('决策应用结果已更新')\n        refreshSuggestions()\n      }\n    }\n\n    const cleanup = () => {\n      if (decisionSubscription) {\n        stompService.unsubscribe(decisionSubscription)\n        decisionSubscription = null\n      }\n    }\n\n    // 生命周期\n    onMounted(async () => {\n      await refreshSuggestions()\n      await setupWebSocketSubscription()\n    })\n\n    onUnmounted(() => {\n      cleanup()\n    })\n\n    return {\n      loading,\n      suggestions,\n      realtimeSupport,\n      applyingIds,\n      showDetailDialog,\n      selectedSuggestion,\n      alertTitle,\n      alertType,\n      alertDescription,\n      getPriorityTagType,\n      getPriorityText,\n      getCongestionText,\n      getAlertText,\n      getRiskAlertType,\n      refreshSuggestions,\n      applySuggestion,\n      viewSuggestionDetail,\n      applySuggestionFromDialog\n    }\n  }\n}\n</script>\n"], "mappings": ";;;AA0NA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAO,QAAS,KAAI;AACpE,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SACEC,SAAS,EACTC,OAAO,EACPC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,KAAK,EACLC,OAAM,QACD,yBAAwB;AAC/B,OAAOC,WAAU,MAAO,gBAAe;AACvC,OAAOC,YAAW,MAAO,uBAAsB;AAE/C,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;IACVV,SAAS;IACTC,OAAO;IACPC,aAAa;IACbC,WAAW;IACXC,YAAY;IACZC,KAAK;IACLC;EACF,CAAC;EACDK,KAAK,EAAE;IACLC,YAAY,EAAE;MACZC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC;EAC9CC,KAAKA,CAACN,KAAK,EAAE;IAAEO;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,OAAM,GAAI1B,GAAG,CAAC,KAAK;IACzB,MAAM2B,WAAU,GAAI3B,GAAG,CAAC,EAAE;IAC1B,MAAM4B,eAAc,GAAI5B,GAAG,CAAC,IAAI;IAChC,MAAM6B,WAAU,GAAI7B,GAAG,CAAC,EAAE;IAC1B,MAAM8B,gBAAe,GAAI9B,GAAG,CAAC,KAAK;IAClC,MAAM+B,kBAAiB,GAAI/B,GAAG,CAAC,IAAI;;IAEnC;IACA,IAAIgC,oBAAmB,GAAI,IAAG;;IAE9B;IACA,MAAMC,UAAS,GAAI7B,QAAQ,CAAC,MAAM;MAChC,IAAI,CAACwB,eAAe,CAACM,KAAK,EAAE,OAAO,WAAU;MAE7C,MAAMC,KAAI,GAAIP,eAAe,CAACM,KAAK,CAACE,WAAU;MAC9C,MAAMC,MAAK,GAAI;QACb,OAAO,EAAE,QAAQ;QACjB,QAAQ,EAAE,QAAQ;QAClB,QAAQ,EAAE,MAAM;QAChB,KAAK,EAAE;MACT;MACA,OAAOA,MAAM,CAACF,KAAK,KAAK,MAAK;IAC/B,CAAC;IAED,MAAMG,SAAQ,GAAIlC,QAAQ,CAAC,MAAM;MAC/B,IAAI,CAACwB,eAAe,CAACM,KAAK,EAAE,OAAO,MAAK;MAExC,MAAMC,KAAI,GAAIP,eAAe,CAACM,KAAK,CAACE,WAAU;MAC9C,MAAMG,KAAI,GAAI;QACZ,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;QACnB,KAAK,EAAE;MACT;MACA,OAAOA,KAAK,CAACJ,KAAK,KAAK,MAAK;IAC9B,CAAC;IAED,MAAMK,gBAAe,GAAIpC,QAAQ,CAAC,MAAM;MACtC,IAAI,CAACwB,eAAe,CAACM,KAAK,EAAE,OAAO,EAAC;MAEpC,MAAMO,UAAS,GAAIb,eAAe,CAACM,KAAK,CAACQ,kBAAiB;MAC1D,IAAID,UAAU,EAAE;QACd,OAAO,WAAW,CAACA,UAAU,CAACE,gBAAe,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAE;MACpE;MACA,OAAO,EAAC;IACV,CAAC;;IAED;IACA,MAAMC,kBAAiB,GAAKC,QAAQ,IAAK;MACvC,MAAMP,KAAI,GAAI;QACZ,MAAM,EAAE,QAAQ;QAChB,QAAQ,EAAE,SAAS;QACnB,KAAK,EAAE;MACT;MACA,OAAOA,KAAK,CAACO,QAAQ,KAAK,MAAK;IACjC;IAEA,MAAMC,eAAc,GAAKD,QAAQ,IAAK;MACpC,MAAME,KAAI,GAAI;QACZ,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,MAAM;QAChB,KAAK,EAAE;MACT;MACA,OAAOA,KAAK,CAACF,QAAQ,KAAK,IAAG;IAC/B;IAEA,MAAMG,iBAAgB,GAAKd,KAAK,IAAK;MACnC,MAAMa,KAAI,GAAI;QACZ,KAAK,EAAE,IAAI;QACX,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE;MACZ;MACA,OAAOA,KAAK,CAACb,KAAK,KAAK,IAAG;IAC5B;IAEA,MAAMe,YAAW,GAAKf,KAAK,IAAK;MAC9B,MAAMa,KAAI,GAAI;QACZ,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE;MACT;MACA,OAAOA,KAAK,CAACb,KAAK,KAAK,IAAG;IAC5B;IAEA,MAAMgB,gBAAe,GAAKhB,KAAK,IAAK;MAClC,MAAMI,KAAI,GAAI;QACZ,KAAK,EAAE,SAAS;QAChB,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE;MACV;MACA,OAAOA,KAAK,CAACJ,KAAK,KAAK,MAAK;IAC9B;IAEA,MAAMiB,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF1B,OAAO,CAACQ,KAAI,GAAI,IAAG;;QAEnB;QACA,MAAMmB,WAAU,GAAI;UAClB;UACAC,UAAU,EAAE;YACVC,KAAK,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAC;YAC3DC,KAAK,EAAE;cAAEH,YAAY,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAC;YAC3DE,IAAI,EAAE;cAAEJ,YAAY,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAC;YAC1DG,IAAI,EAAE;cAAEL,YAAY,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAK;UAC5D;QACF;QAEA,MAAMI,QAAO,GAAI,MAAMhD,WAAW,CAACiD,mBAAmB,CAAC;UACrDC,aAAa,EAAE9C,KAAK,CAACC,YAAY;UACjC8C,YAAY,EAAEZ,WAAW;UACzBa,kBAAkB,EAAE,CAAC;QACvB,CAAC;QAED,IAAIJ,QAAQ,CAACK,MAAK,KAAM,SAAS,EAAE;UACjCxC,WAAW,CAACO,KAAI,GAAI4B,QAAQ,CAACnC,WAAU,IAAK,EAAC;QAC/C;MAEF,EAAE,OAAOyC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC/D,SAAS,CAAC+D,KAAK,CAAC,UAAU;MAC5B,UAAU;QACR1C,OAAO,CAACQ,KAAI,GAAI,KAAI;MACtB;IACF;IAEA,MAAMoC,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAI;QACF,MAAMC,YAAW,GAAI;UACnB;UACAjB,UAAU,EAAE;YACVC,KAAK,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAC;YAC3DC,KAAK,EAAE;cAAEH,YAAY,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAC;YAC3DE,IAAI,EAAE;cAAEJ,YAAY,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAI,CAAC;YAC1DG,IAAI,EAAE;cAAEL,YAAY,EAAE,EAAE;cAAEC,YAAY,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAK;UAC5D;QACF;QAEA,MAAMI,QAAO,GAAI,MAAMhD,WAAW,CAAC0D,kBAAkB,CAAC;UACpDR,aAAa,EAAE9C,KAAK,CAACC,YAAY;UACjCsD,aAAa,EAAEF;QACjB,CAAC;QAED,IAAIT,QAAQ,CAACK,MAAK,KAAM,SAAS,EAAE;UACjCvC,eAAe,CAACM,KAAI,GAAI4B,QAAQ,CAACY,gBAAe;QAClD;MAEF,EAAE,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;MACpC;IACF;IAEA,MAAMO,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC,MAAMC,OAAO,CAACC,GAAG,CAAC,CAACzB,eAAe,CAAC,CAAC,EAAEkB,mBAAmB,CAAC,CAAC,CAAC;IAC9D;IAEA,MAAMQ,eAAc,GAAI,MAAOC,UAAU,IAAK;MAC5C,IAAI;QACF,MAAMzE,YAAY,CAAC0E,OAAO,CACxB,WAAWD,UAAU,CAACE,KAAK,KAAK,EAChC,MAAM,EACN;UACEC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtB/D,IAAI,EAAE;QACR,CACF;QAEAS,WAAW,CAACK,KAAK,CAACkD,IAAI,CAACL,UAAU,CAACM,EAAE;QAEpC,MAAMC,MAAK,GAAIC,YAAY,CAACC,OAAO,CAAC,QAAQ;QAC5C,MAAM1B,QAAO,GAAI,MAAMhD,WAAW,CAACgE,eAAe,CAACC,UAAU,CAACM,EAAE,EAAE;UAChErB,aAAa,EAAE9C,KAAK,CAACC,YAAY;UACjCsE,OAAO,EAAEH;QACX,CAAC;QAED,IAAIxB,QAAQ,CAACK,MAAK,KAAM,SAAS,EAAE;UACjC9D,SAAS,CAACqF,OAAO,CAAC,UAAU;UAC5BjE,IAAI,CAAC,oBAAoB,EAAEsD,UAAU;;UAErC;UACA,MAAMJ,kBAAkB,CAAC;QAC3B;MAEF,EAAE,OAAOP,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;UAChC/D,SAAS,CAAC+D,KAAK,CAAC,UAAU;QAC5B;MACF,UAAU;QACRvC,WAAW,CAACK,KAAI,GAAIL,WAAW,CAACK,KAAK,CAACyD,MAAM,CAACN,EAAC,IAAKA,EAAC,KAAMN,UAAU,CAACM,EAAE;MACzE;IACF;IAEA,MAAMO,oBAAmB,GAAKb,UAAU,IAAK;MAC3ChD,kBAAkB,CAACG,KAAI,GAAI6C,UAAS;MACpCjD,gBAAgB,CAACI,KAAI,GAAI,IAAG;IAC9B;IAEA,MAAM2D,yBAAwB,GAAI,MAAAA,CAAA,KAAY;MAC5C,IAAI9D,kBAAkB,CAACG,KAAK,EAAE;QAC5B,MAAM4C,eAAe,CAAC/C,kBAAkB,CAACG,KAAK;QAC9CJ,gBAAgB,CAACI,KAAI,GAAI,KAAI;MAC/B;IACF;IAEA,MAAM4D,0BAAyB,GAAI,MAAAA,CAAA,KAAY;MAC7C,IAAI;QACF,MAAM/E,YAAY,CAACgF,OAAO,CAAC;QAE3B/D,oBAAmB,GAAI,MAAMjB,YAAY,CAACiF,SAAS,CACjD,2BAA2B9E,KAAK,CAACC,YAAY,EAAE,EAC9C8E,OAAO,IAAK;UACX,IAAI;YACF,MAAMC,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACH,OAAO,CAACI,IAAI;YACpCC,oBAAoB,CAACJ,IAAI;UAC3B,EAAE,OAAO9B,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;UACpC;QACF,CACF;MAEF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK;MAC7C;IACF;IAEA,MAAMkC,oBAAmB,GAAKJ,IAAI,IAAK;MACrC,IAAIA,IAAI,CAAC9E,IAAG,KAAM,sBAAsB,EAAE;QACxCO,WAAW,CAACO,KAAI,GAAIgE,IAAI,CAACvE,WAAU,IAAK,EAAC;MAC3C,OAAO,IAAIuE,IAAI,CAAC9E,IAAG,KAAM,2BAA2B,EAAE;QACpDQ,eAAe,CAACM,KAAI,GAAIgE,IAAI,CAACK,eAAc;MAC7C,OAAO,IAAIL,IAAI,CAAC9E,IAAG,KAAM,6BAA6B,EAAE;QACtDf,SAAS,CAACqF,OAAO,CAAC,WAAW;QAC7Bf,kBAAkB,CAAC;MACrB;IACF;IAEA,MAAM6B,OAAM,GAAIA,CAAA,KAAM;MACpB,IAAIxE,oBAAoB,EAAE;QACxBjB,YAAY,CAAC0F,WAAW,CAACzE,oBAAoB;QAC7CA,oBAAmB,GAAI,IAAG;MAC5B;IACF;;IAEA;IACA9B,SAAS,CAAC,YAAY;MACpB,MAAMyE,kBAAkB,CAAC;MACzB,MAAMmB,0BAA0B,CAAC;IACnC,CAAC;IAED3F,WAAW,CAAC,MAAM;MAChBqG,OAAO,CAAC;IACV,CAAC;IAED,OAAO;MACL9E,OAAO;MACPC,WAAW;MACXC,eAAe;MACfC,WAAW;MACXC,gBAAgB;MAChBC,kBAAkB;MAClBE,UAAU;MACVK,SAAS;MACTE,gBAAgB;MAChBK,kBAAkB;MAClBE,eAAe;MACfE,iBAAiB;MACjBC,YAAY;MACZC,gBAAgB;MAChBwB,kBAAkB;MAClBG,eAAe;MACfc,oBAAoB;MACpBC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}