package com.traffic.analysis.service;

import com.traffic.analysis.model.SimulationTask;
import com.traffic.analysis.model.OptimizationResult;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 仿真服务接口
 */
public interface SimulationService {
    
    /**
     * 创建仿真任务
     * @param userId 用户ID
     * @param username 用户名
     * @param taskName 任务名称
     * @param simulationType 仿真类型
     * @param trafficData 交通数据
     * @param simulationConfig 仿真配置
     * @return 仿真任务
     */
    SimulationTask createSimulationTask(String userId, String username, String taskName, 
                                      String simulationType, Map<String, Object> trafficData, 
                                      Map<String, Object> simulationConfig);
    
    /**
     * 启动仿真任务
     * @param simulationId 仿真ID
     * @param useGui 是否使用GUI
     * @return 启动结果
     */
    Map<String, Object> startSimulation(String simulationId, boolean useGui);
    
    /**
     * 停止仿真任务
     * @param simulationId 仿真ID
     * @return 停止结果
     */
    Map<String, Object> stopSimulation(String simulationId);
    
    /**
     * 获取仿真任务状态
     * @param simulationId 仿真ID
     * @return 仿真状态
     */
    Map<String, Object> getSimulationStatus(String simulationId);
    
    /**
     * 更新仿真任务进度
     * @param simulationId 仿真ID
     * @param progress 进度
     * @param status 状态
     * @param message 消息
     */
    void updateSimulationProgress(String simulationId, Integer progress, String status, String message);
    
    /**
     * 完成仿真任务
     * @param simulationId 仿真ID
     * @param simulationResults 仿真结果
     */
    void completeSimulation(String simulationId, Map<String, Object> simulationResults);
    
    /**
     * 仿真任务失败处理
     * @param simulationId 仿真ID
     * @param errorMessage 错误信息
     */
    void failSimulation(String simulationId, String errorMessage);
    
    /**
     * 获取用户的仿真任务列表
     * @param userId 用户ID
     * @return 仿真任务列表
     */
    List<SimulationTask> getUserSimulationTasks(String userId);
    
    /**
     * 获取仿真任务详情
     * @param simulationId 仿真ID
     * @return 仿真任务
     */
    Optional<SimulationTask> getSimulationTask(String simulationId);
    
    /**
     * 删除仿真任务
     * @param simulationId 仿真ID
     * @param userId 用户ID
     * @return 删除结果
     */
    Map<String, Object> deleteSimulationTask(String simulationId, String userId);
    
    /**
     * 执行信号灯配时优化
     * @param trafficData 交通数据
     * @param currentTiming 当前配时
     * @return 优化结果
     */
    Map<String, Object> optimizeSignalTiming(Map<String, Object> trafficData, Map<String, Object> currentTiming);
    
    /**
     * 执行流量平衡优化
     * @param trafficData 交通数据
     * @return 优化结果
     */
    Map<String, Object> optimizeFlowBalance(Map<String, Object> trafficData);
    
    /**
     * 执行综合优化分析
     * @param trafficData 交通数据
     * @return 优化结果
     */
    Map<String, Object> performComprehensiveOptimization(Map<String, Object> trafficData);
    
    /**
     * 保存优化结果
     * @param simulationTaskId 仿真任务ID
     * @param optimizationType 优化类型
     * @param optimizationData 优化数据
     * @return 优化结果
     */
    OptimizationResult saveOptimizationResult(String simulationTaskId, String optimizationType, 
                                            Map<String, Object> optimizationData);
    
    /**
     * 获取优化结果
     * @param simulationTaskId 仿真任务ID
     * @return 优化结果
     */
    Optional<OptimizationResult> getOptimizationResult(String simulationTaskId);
    
    /**
     * 获取仿真统计信息
     * @param userId 用户ID（可选，为null时获取全局统计）
     * @return 统计信息
     */
    Map<String, Object> getSimulationStatistics(String userId);
    
    /**
     * 清理历史仿真数据
     * @param daysToKeep 保留天数
     * @return 清理结果
     */
    Map<String, Object> cleanupHistoryData(int daysToKeep);
    
    /**
     * 检查SUMO服务状态
     * @return 服务状态
     */
    Map<String, Object> checkSumoServiceStatus();
    
    /**
     * 获取正在运行的仿真任务
     * @param userId 用户ID（可选）
     * @return 正在运行的任务列表
     */
    List<SimulationTask> getRunningSimulations(String userId);
    
    /**
     * 批量停止用户的仿真任务
     * @param userId 用户ID
     * @return 停止结果
     */
    Map<String, Object> stopUserSimulations(String userId);
    
    /**
     * 重启失败的仿真任务
     * @param simulationId 仿真ID
     * @return 重启结果
     */
    Map<String, Object> restartSimulation(String simulationId);
    
    /**
     * 导出仿真结果
     * @param simulationId 仿真ID
     * @param format 导出格式（json, excel, pdf）
     * @return 导出结果
     */
    Map<String, Object> exportSimulationResult(String simulationId, String format);
    
    /**
     * 比较多个仿真结果
     * @param simulationIds 仿真ID列表
     * @return 比较结果
     */
    Map<String, Object> compareSimulationResults(List<String> simulationIds);

    /**
     * 获取仿真性能报告
     * @param simulationId 仿真ID
     * @return 性能报告
     */
    Map<String, Object> getPerformanceReport(String simulationId);

    /**
     * 基于视频分析结果创建仿真任务
     * @param analysisTaskId 视频分析任务ID
     * @param userId 用户ID
     * @param username 用户名
     * @param simulationType 仿真类型
     * @return 仿真任务
     */
    SimulationTask createSimulationFromAnalysis(String analysisTaskId, String userId, String username, String simulationType);
}
