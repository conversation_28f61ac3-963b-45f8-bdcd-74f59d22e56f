package com.traffic.analysis.service;

import java.util.List;
import java.util.Map;

/**
 * 决策支持服务接口
 */
public interface DecisionSupportService {
    
    /**
     * 生成智能决策建议
     * @param trafficData 交通数据
     * @param analysisResult 分析结果
     * @return 决策建议
     */
    Map<String, Object> generateIntelligentDecisions(Map<String, Object> trafficData, Map<String, Object> analysisResult);
    
    /**
     * 评估交通状态
     * @param trafficData 交通数据
     * @return 交通状态评估
     */
    Map<String, Object> evaluateTrafficState(Map<String, Object> trafficData);
    
    /**
     * 生成自适应信号配时
     * @param trafficData 交通数据
     * @param currentTiming 当前配时
     * @return 自适应配时方案
     */
    Map<String, Object> generateAdaptiveSignalTiming(Map<String, Object> trafficData, Map<String, Object> currentTiming);
    
    /**
     * 计算拥堵等级
     * @param trafficData 交通数据
     * @return 拥堵等级分析
     */
    Map<String, Object> calculateCongestionLevel(Map<String, Object> trafficData);
    
    /**
     * 生成优化建议
     * @param trafficData 交通数据
     * @param optimizationResult 优化结果
     * @return 优化建议列表
     */
    List<Map<String, Object>> generateOptimizationRecommendations(Map<String, Object> trafficData, Map<String, Object> optimizationResult);
    
    /**
     * 预测交通趋势
     * @param historicalData 历史数据
     * @param currentData 当前数据
     * @return 趋势预测结果
     */
    Map<String, Object> predictTrafficTrends(List<Map<String, Object>> historicalData, Map<String, Object> currentData);
    
    /**
     * 评估优化效果
     * @param beforeData 优化前数据
     * @param afterData 优化后数据
     * @return 效果评估结果
     */
    Map<String, Object> evaluateOptimizationEffectiveness(Map<String, Object> beforeData, Map<String, Object> afterData);
    
    /**
     * 生成实时决策
     * @param realtimeData 实时数据
     * @param context 上下文信息
     * @return 实时决策建议
     */
    Map<String, Object> generateRealtimeDecisions(Map<String, Object> realtimeData, Map<String, Object> context);
    
    /**
     * 分析瓶颈路段
     * @param trafficData 交通数据
     * @return 瓶颈分析结果
     */
    Map<String, Object> analyzeBottlenecks(Map<String, Object> trafficData);
    
    /**
     * 生成应急响应方案
     * @param emergencyData 应急数据
     * @param trafficData 交通数据
     * @return 应急响应方案
     */
    Map<String, Object> generateEmergencyResponse(Map<String, Object> emergencyData, Map<String, Object> trafficData);
}
