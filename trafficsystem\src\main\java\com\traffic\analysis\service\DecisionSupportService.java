package com.traffic.analysis.service;

import com.traffic.analysis.model.DecisionSuggestion;
import com.traffic.analysis.model.DecisionHistory;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 决策支持服务接口
 */
public interface DecisionSupportService {
    
    /**
     * 生成智能决策建议
     * @param trafficData 交通数据
     * @param analysisResult 分析结果
     * @return 决策建议
     */
    Map<String, Object> generateIntelligentDecisions(Map<String, Object> trafficData, Map<String, Object> analysisResult);
    
    /**
     * 评估交通状态
     * @param trafficData 交通数据
     * @return 交通状态评估
     */
    Map<String, Object> evaluateTrafficState(Map<String, Object> trafficData);
    
    /**
     * 生成自适应信号配时
     * @param trafficData 交通数据
     * @param currentTiming 当前配时
     * @return 自适应配时方案
     */
    Map<String, Object> generateAdaptiveSignalTiming(Map<String, Object> trafficData, Map<String, Object> currentTiming);
    
    /**
     * 计算拥堵等级
     * @param trafficData 交通数据
     * @return 拥堵等级分析
     */
    Map<String, Object> calculateCongestionLevel(Map<String, Object> trafficData);
    
    /**
     * 生成优化建议
     * @param trafficData 交通数据
     * @param optimizationResult 优化结果
     * @return 优化建议列表
     */
    List<Map<String, Object>> generateOptimizationRecommendations(Map<String, Object> trafficData, Map<String, Object> optimizationResult);
    
    /**
     * 预测交通趋势
     * @param historicalData 历史数据
     * @param currentData 当前数据
     * @return 趋势预测结果
     */
    Map<String, Object> predictTrafficTrends(List<Map<String, Object>> historicalData, Map<String, Object> currentData);
    
    /**
     * 评估优化效果
     * @param beforeData 优化前数据
     * @param afterData 优化后数据
     * @return 效果评估结果
     */
    Map<String, Object> evaluateOptimizationEffectiveness(Map<String, Object> beforeData, Map<String, Object> afterData);
    
    /**
     * 生成实时决策
     * @param realtimeData 实时数据
     * @param context 上下文信息
     * @return 实时决策建议
     */
    Map<String, Object> generateRealtimeDecisions(Map<String, Object> realtimeData, Map<String, Object> context);
    
    /**
     * 分析瓶颈路段
     * @param trafficData 交通数据
     * @return 瓶颈分析结果
     */
    Map<String, Object> analyzeBottlenecks(Map<String, Object> trafficData);
    
    /**
     * 生成应急响应方案
     * @param emergencyData 应急数据
     * @param trafficData 交通数据
     * @return 应急响应方案
     */
    Map<String, Object> generateEmergencyResponse(Map<String, Object> emergencyData, Map<String, Object> trafficData);

    // ==================== 新增决策支持方法 ====================

    /**
     * 生成决策建议
     *
     * @param simulationId 仿真ID
     * @param trafficData 交通数据
     * @param currentConditions 当前条件
     * @return 决策建议列表
     */
    List<DecisionSuggestion> generateDecisionSuggestions(String simulationId,
                                                        Map<String, Object> trafficData,
                                                        Map<String, Object> currentConditions);

    /**
     * 获取实时决策支持
     *
     * @param simulationId 仿真ID
     * @param currentState 当前状态
     * @return 实时决策支持数据
     */
    Map<String, Object> getRealtimeDecisionSupport(String simulationId,
                                                  Map<String, Object> currentState);

    /**
     * 应用决策建议
     *
     * @param suggestionId 建议ID
     * @param simulationId 仿真ID
     * @param userId 用户ID
     * @return 应用结果
     */
    Map<String, Object> applyDecisionSuggestion(String suggestionId,
                                               String simulationId,
                                               String userId);

    /**
     * 获取决策历史
     *
     * @param simulationId 仿真ID（可选）
     * @param userId 用户ID（可选）
     * @param page 页码
     * @param size 页大小
     * @return 决策历史列表
     */
    List<DecisionHistory> getDecisionHistory(String simulationId,
                                           String userId,
                                           int page,
                                           int size);

    /**
     * 获取决策建议详情
     *
     * @param suggestionId 建议ID
     * @return 决策建议
     */
    Optional<DecisionSuggestion> getDecisionSuggestion(String suggestionId);

    /**
     * 评估决策效果
     *
     * @param simulationId 仿真ID
     * @param decisionId 决策ID
     * @param beforeData 决策前数据
     * @param afterData 决策后数据
     * @return 效果评估结果
     */
    Map<String, Object> evaluateDecisionEffect(String simulationId,
                                              String decisionId,
                                              Map<String, Object> beforeData,
                                              Map<String, Object> afterData);

    /**
     * 获取决策统计信息
     *
     * @param simulationId 仿真ID（可选）
     * @param userId 用户ID（可选）
     * @return 统计信息
     */
    Map<String, Object> getDecisionStatistics(String simulationId, String userId);

    /**
     * 检查服务状态
     *
     * @return 服务状态
     */
    Map<String, Object> checkServiceStatus();

    /**
     * 保存决策建议
     *
     * @param suggestion 决策建议
     * @return 保存的决策建议
     */
    DecisionSuggestion saveDecisionSuggestion(DecisionSuggestion suggestion);

    /**
     * 更新决策建议状态
     *
     * @param suggestionId 建议ID
     * @param status 新状态
     * @param appliedBy 应用者ID
     * @return 更新结果
     */
    boolean updateSuggestionStatus(String suggestionId, String status, String appliedBy);

    /**
     * 保存决策历史
     *
     * @param history 决策历史
     * @return 保存的决策历史
     */
    DecisionHistory saveDecisionHistory(DecisionHistory history);
}
