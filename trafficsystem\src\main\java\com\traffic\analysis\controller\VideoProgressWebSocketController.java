package com.traffic.analysis.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.time.LocalDateTime;

/**
 * WebSocket视频进度控制器
 * 用于处理视频分析进度的实时推送和实时帧数据推送
 */
@Controller
@RestController
@RequestMapping("/api/video-progress")
public class VideoProgressWebSocketController {

    private static final Logger logger = LoggerFactory.getLogger(VideoProgressWebSocketController.class);

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    // 帧数据缓存，存储每个任务的最近帧数据
    private final ConcurrentHashMap<String, ConcurrentLinkedQueue<Map<String, Object>>> frameCache = new ConcurrentHashMap<>();

    // 持久化帧数据存储，用于回放功能
    private final ConcurrentHashMap<String, List<Map<String, Object>>> persistentFrameStorage = new ConcurrentHashMap<>();

    // 缓存配置
    private static final int MAX_FRAMES_PER_TASK = 30; // 每个任务最多缓存30帧
    private static final int MAX_PERSISTENT_FRAMES = 200; // 每个任务最多持久化200帧用于回放
    private static final long FRAME_CACHE_TIMEOUT = 300000; // 5分钟缓存超时
    
    /**
     * 处理视频进度消息
     * @param taskId 任务ID
     * @param message 进度消息
     * @return 进度消息
     */
    @MessageMapping("/video-progress/{taskId}")
    @SendTo("/topic/video-progress/{taskId}")
    public Map<String, Object> sendProgress(@DestinationVariable String taskId, Map<String, Object> message) {
        logger.debug("收到WebSocket进度消息: taskId={}, message={}", taskId, message);
        return message;
    }
    
    /**
     * 推送视频进度更新
     * @param taskId 任务ID
     * @param progress 进度
     * @param status 状态
     * @param message 消息
     */
    public void sendProgressUpdate(String taskId, int progress, String status, String message) {
        try {
            // 构造进度消息
            Map<String, Object> progressData = Map.of(
                "taskId", taskId,
                "progress", progress,
                "status", status,
                "message", message
            );
            
            // 推送到特定任务的主题
            String destination = "/topic/video-progress/" + taskId;
            logger.debug("推送视频进度更新: destination={}, data={}", destination, progressData);
            messagingTemplate.convertAndSend(destination, progressData);
        } catch (Exception e) {
            logger.error("推送视频进度更新失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 发送包含结果ID的进度更新
     * @param taskId 任务ID
     * @param message 包含进度、状态等信息的消息对象
     */
    public void sendProgressUpdate(String taskId, Map<String, Object> message) {
        try {
            // 如果是完成状态消息且没有resultId，添加taskId作为resultId
            if ("completed".equals(message.get("status")) && !message.containsKey("resultId")) {
                // 创建可修改的Map
                Map<String, Object> updatedMessage = new HashMap<>(message);
                updatedMessage.put("resultId", taskId);
                message = updatedMessage;
            }
            
            // 添加统一的消息类型
            if (!message.containsKey("type")) {
                // 创建可修改的Map
                Map<String, Object> updatedMessage = new HashMap<>(message);
                updatedMessage.put("type", "processing_progress");
                message = updatedMessage;
            }
            
            // 推送到特定任务的主题
            String destination = "/topic/video-progress/" + taskId;
            logger.info("已发送进度更新到任务 {}: {}", taskId, message);
            messagingTemplate.convertAndSend(destination, message);
        } catch (Exception e) {
            logger.error("推送视频进度更新失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 推送实时帧数据更新
     * @param taskId 任务ID
     * @param frameNumber 帧号
     * @param totalFrames 总帧数
     * @param imageData Base64编码的图像数据
     * @param detectionCount 检测到的车辆数量
     */
    public void sendFrameUpdate(String taskId, int frameNumber, int totalFrames, String imageData, int detectionCount) {
        try {
            // 构造帧数据消息
            Map<String, Object> frameData = new HashMap<>();
            frameData.put("type", "frame_update");
            frameData.put("taskId", taskId);
            frameData.put("frameNumber", frameNumber);
            frameData.put("totalFrames", totalFrames);
            frameData.put("imageData", imageData);
            frameData.put("detectionCount", detectionCount);
            frameData.put("timestamp", LocalDateTime.now().toString());

            // 缓存帧数据
            cacheFrameData(taskId, frameData);

            // 推送到特定任务的帧更新主题
            String destination = "/topic/frame-updates/" + taskId;
            logger.debug("推送帧数据更新: destination={}, frame={}", destination, frameNumber);
            messagingTemplate.convertAndSend(destination, frameData);

            // 记录推送详情
            logger.info("已推送帧数据到主题: {}, 帧号: {}, 检测数量: {}", destination, frameNumber, detectionCount);

        } catch (Exception e) {
            logger.error("推送帧数据更新失败: {}", e.getMessage(), e);
        }
    }

    /**
     * REST API接口：接收Python后端推送的帧数据
     * @param frameUpdateRequest 帧更新请求
     * @return 响应结果
     */
    @PostMapping("/frame-update")
    public ResponseEntity<Map<String, Object>> receiveFrameUpdate(@RequestBody Map<String, Object> frameUpdateRequest) {
        try {
            String originalTaskId = (String) frameUpdateRequest.get("taskId");
            Integer frameNumber = (Integer) frameUpdateRequest.get("frameNumber");
            Integer totalFrames = (Integer) frameUpdateRequest.get("totalFrames");
            String imageData = (String) frameUpdateRequest.get("imageData");
            Integer detectionCount = (Integer) frameUpdateRequest.get("detectionCount");

            if (originalTaskId == null || frameNumber == null || totalFrames == null || imageData == null) {
                logger.warn("接收到不完整的帧更新请求: {}", frameUpdateRequest);
                return ResponseEntity.badRequest().body(Map.of("error", "缺少必要参数"));
            }

            logger.debug("接收到帧更新请求: taskId={}, frame={}/{}", originalTaskId, frameNumber, totalFrames);

            // 推送帧数据到带前缀的主题（保持原始taskId）
            String frameTaskId = originalTaskId; // 保持原始taskId，包含h_或v_前缀

            // 同时推送到清理后的主题（为了兼容性）
            String cleanTaskId = originalTaskId;
            if (originalTaskId.contains("_") && originalTaskId.split("_").length > 1) {
                String[] parts = originalTaskId.split("_", 2);
                if (parts[0].length() <= 2) { // h_ 或 v_ 前缀
                    cleanTaskId = parts[1];
                }
            }

            // 推送帧数据到带前缀的主题
            sendFrameUpdate(frameTaskId, frameNumber, totalFrames, imageData, detectionCount != null ? detectionCount : 0);

            // 如果有前缀，也推送到清理后的主题（为了兼容性）
            if (!frameTaskId.equals(cleanTaskId)) {
                sendFrameUpdate(cleanTaskId, frameNumber, totalFrames, imageData, detectionCount != null ? detectionCount : 0);
            }

            return ResponseEntity.ok(Map.of("status", "success", "message", "帧数据推送成功"));

        } catch (Exception e) {
            logger.error("处理帧更新请求失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "处理帧更新请求失败: " + e.getMessage()));
        }
    }

    /**
     * 缓存帧数据
     * @param taskId 任务ID
     * @param frameData 帧数据
     */
    private void cacheFrameData(String taskId, Map<String, Object> frameData) {
        try {
            // 获取或创建任务的帧缓存队列
            ConcurrentLinkedQueue<Map<String, Object>> taskFrames = frameCache.computeIfAbsent(taskId, k -> new ConcurrentLinkedQueue<>());

            // 添加新帧数据
            taskFrames.offer(frameData);

            // 限制缓存大小
            while (taskFrames.size() > MAX_FRAMES_PER_TASK) {
                taskFrames.poll(); // 移除最旧的帧
            }

            // 同时存储到持久化存储中用于回放
            storePersistentFrameData(taskId, frameData);

            logger.debug("缓存帧数据: taskId={}, 缓存大小={}", taskId, taskFrames.size());

        } catch (Exception e) {
            logger.error("缓存帧数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 存储持久化帧数据用于回放
     * @param taskId 任务ID
     * @param frameData 帧数据
     */
    private void storePersistentFrameData(String taskId, Map<String, Object> frameData) {
        try {
            // 获取或创建任务的持久化帧存储列表
            List<Map<String, Object>> persistentFrames = persistentFrameStorage.computeIfAbsent(taskId, k -> new ArrayList<>());

            // 添加新帧数据
            persistentFrames.add(new HashMap<>(frameData)); // 创建副本避免引用问题

            // 限制持久化存储大小
            if (persistentFrames.size() > MAX_PERSISTENT_FRAMES) {
                persistentFrames.remove(0); // 移除最旧的帧
            }

            logger.debug("持久化帧数据: taskId={}, 持久化存储大小={}", taskId, persistentFrames.size());

        } catch (Exception e) {
            logger.error("持久化帧数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取任务的缓存帧数据
     * @param taskId 任务ID
     * @return 缓存的帧数据列表
     */
    @GetMapping("/frames/{taskId}")
    public ResponseEntity<Map<String, Object>> getCachedFrames(@PathVariable String taskId) {
        try {
            ConcurrentLinkedQueue<Map<String, Object>> taskFrames = frameCache.get(taskId);

            if (taskFrames == null || taskFrames.isEmpty()) {
                return ResponseEntity.ok(Map.of("frames", new Object[0], "count", 0));
            }

            // 转换为数组返回
            Object[] framesArray = taskFrames.toArray();

            return ResponseEntity.ok(Map.of(
                "frames", framesArray,
                "count", framesArray.length,
                "taskId", taskId
            ));

        } catch (Exception e) {
            logger.error("获取缓存帧数据失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "获取缓存帧数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取任务的持久化帧数据用于回放
     * @param taskId 任务ID
     * @return 持久化的帧数据列表
     */
    @GetMapping("/replay-frames/{taskId}")
    public ResponseEntity<Map<String, Object>> getReplayFrames(@PathVariable String taskId) {
        try {
            List<Map<String, Object>> persistentFrames = persistentFrameStorage.get(taskId);

            if (persistentFrames == null || persistentFrames.isEmpty()) {
                return ResponseEntity.ok(Map.of("frames", new Object[0], "count", 0, "message", "没有找到回放帧数据"));
            }

            return ResponseEntity.ok(Map.of(
                "frames", persistentFrames,
                "count", persistentFrames.size(),
                "taskId", taskId,
                "message", "获取回放帧数据成功"
            ));

        } catch (Exception e) {
            logger.error("获取回放帧数据失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "获取回放帧数据失败: " + e.getMessage()));
        }
    }

    /**
     * 启动帧数据回放
     * @param taskId 任务ID
     * @param replaySpeed 回放速度（毫秒间隔，默认1000ms）
     * @return 回放启动结果
     */
    @PostMapping("/start-replay/{taskId}")
    public ResponseEntity<Map<String, Object>> startFrameReplay(@PathVariable String taskId,
                                                               @RequestParam(defaultValue = "1000") int replaySpeed) {
        try {
            List<Map<String, Object>> persistentFrames = persistentFrameStorage.get(taskId);

            if (persistentFrames == null || persistentFrames.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "没有找到任务的帧数据"));
            }

            // 启动异步回放任务
            startAsyncReplay(taskId, persistentFrames, replaySpeed);

            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "帧数据回放已启动",
                "taskId", taskId,
                "frameCount", persistentFrames.size(),
                "replaySpeed", replaySpeed
            ));

        } catch (Exception e) {
            logger.error("启动帧数据回放失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "启动帧数据回放失败: " + e.getMessage()));
        }
    }

    /**
     * 异步执行帧数据回放
     * @param taskId 任务ID
     * @param frames 帧数据列表
     * @param replaySpeed 回放速度（毫秒间隔）
     */
    private void startAsyncReplay(String taskId, List<Map<String, Object>> frames, int replaySpeed) {
        new Thread(() -> {
            try {
                logger.info("开始回放任务 {} 的帧数据，共 {} 帧，回放间隔 {}ms", taskId, frames.size(), replaySpeed);

                for (int i = 0; i < frames.size(); i++) {
                    Map<String, Object> frameData = frames.get(i);

                    // 创建回放帧数据
                    Map<String, Object> replayFrameData = new HashMap<>(frameData);
                    replayFrameData.put("type", "replay_frame_update");
                    replayFrameData.put("replayIndex", i);
                    replayFrameData.put("totalReplayFrames", frames.size());
                    replayFrameData.put("isReplay", true);

                    // 推送到帧更新主题
                    String destination = "/topic/frame-updates/" + taskId;
                    messagingTemplate.convertAndSend(destination, replayFrameData);

                    logger.debug("回放帧数据: taskId={}, 帧索引={}/{}", taskId, i + 1, frames.size());

                    // 等待指定的回放间隔
                    Thread.sleep(replaySpeed);
                }

                logger.info("任务 {} 的帧数据回放完成", taskId);

                // 发送回放完成消息
                Map<String, Object> completionMessage = Map.of(
                    "type", "replay_completed",
                    "taskId", taskId,
                    "message", "帧数据回放完成"
                );
                String destination = "/topic/frame-updates/" + taskId;
                messagingTemplate.convertAndSend(destination, completionMessage);

            } catch (InterruptedException e) {
                logger.warn("帧数据回放被中断: taskId={}", taskId);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                logger.error("帧数据回放失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }
        }, "FrameReplay-" + taskId).start();
    }

    // ==================== SUMO仿真WebSocket支持 ====================

    /**
     * 推送仿真状态更新
     * @param simulationId 仿真ID
     * @param status 仿真状态
     * @param progress 进度
     * @param message 消息
     */
    public void sendSimulationStatusUpdate(String simulationId, String status, int progress, String message) {
        try {
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("type", "simulation_status");
            statusData.put("simulationId", simulationId);
            statusData.put("status", status);
            statusData.put("progress", progress);
            statusData.put("message", message);
            statusData.put("timestamp", LocalDateTime.now().toString());

            String destination = "/topic/simulation-status/" + simulationId;
            logger.debug("推送仿真状态更新: destination={}, status={}, progress={}%",
                        destination, status, progress);
            messagingTemplate.convertAndSend(destination, statusData);

            logger.info("已推送仿真状态更新: simulationId={}, status={}, progress={}%",
                       simulationId, status, progress);

        } catch (Exception e) {
            logger.error("推送仿真状态更新失败: simulationId={}, error={}", simulationId, e.getMessage(), e);
        }
    }

    /**
     * 推送仿真实时数据
     * @param simulationId 仿真ID
     * @param simulationTime 仿真时间
     * @param vehicleCount 车辆数量
     * @param averageSpeed 平均速度
     * @param throughput 通行能力
     * @param waitingTime 等待时间
     */
    public void sendSimulationRealtimeData(String simulationId, double simulationTime, int vehicleCount,
                                          double averageSpeed, double throughput, double waitingTime) {
        try {
            Map<String, Object> realtimeData = new HashMap<>();
            realtimeData.put("type", "simulation_realtime");
            realtimeData.put("simulationId", simulationId);
            realtimeData.put("simulationTime", simulationTime);
            realtimeData.put("vehicleCount", vehicleCount);
            realtimeData.put("averageSpeed", averageSpeed);
            realtimeData.put("throughput", throughput);
            realtimeData.put("waitingTime", waitingTime);
            realtimeData.put("timestamp", LocalDateTime.now().toString());

            String destination = "/topic/simulation-realtime/" + simulationId;
            logger.debug("推送仿真实时数据: destination={}, time={}, vehicles={}",
                        destination, simulationTime, vehicleCount);
            messagingTemplate.convertAndSend(destination, realtimeData);

        } catch (Exception e) {
            logger.error("推送仿真实时数据失败: simulationId={}, error={}", simulationId, e.getMessage(), e);
        }
    }

    /**
     * 推送仿真方向流量数据
     * @param simulationId 仿真ID
     * @param directionFlows 各方向流量数据
     */
    public void sendSimulationDirectionFlows(String simulationId, Map<String, Map<String, Object>> directionFlows) {
        try {
            Map<String, Object> flowData = new HashMap<>();
            flowData.put("type", "simulation_direction_flows");
            flowData.put("simulationId", simulationId);
            flowData.put("directionFlows", directionFlows);
            flowData.put("timestamp", LocalDateTime.now().toString());

            String destination = "/topic/simulation-flows/" + simulationId;
            logger.debug("推送仿真方向流量数据: destination={}, directions={}",
                        destination, directionFlows.keySet());
            messagingTemplate.convertAndSend(destination, flowData);

        } catch (Exception e) {
            logger.error("推送仿真方向流量数据失败: simulationId={}, error={}", simulationId, e.getMessage(), e);
        }
    }

    /**
     * 推送信号灯状态更新
     * @param simulationId 仿真ID
     * @param trafficLightStates 信号灯状态
     * @param currentPhase 当前相位
     * @param phaseRemaining 相位剩余时间
     */
    public void sendTrafficLightUpdate(String simulationId, Map<String, String> trafficLightStates,
                                      String currentPhase, int phaseRemaining) {
        try {
            Map<String, Object> lightData = new HashMap<>();
            lightData.put("type", "traffic_light_update");
            lightData.put("simulationId", simulationId);
            lightData.put("trafficLightStates", trafficLightStates);
            lightData.put("currentPhase", currentPhase);
            lightData.put("phaseRemaining", phaseRemaining);
            lightData.put("timestamp", LocalDateTime.now().toString());

            String destination = "/topic/traffic-lights/" + simulationId;
            logger.debug("推送信号灯状态更新: destination={}, phase={}, remaining={}s",
                        destination, currentPhase, phaseRemaining);
            messagingTemplate.convertAndSend(destination, lightData);

        } catch (Exception e) {
            logger.error("推送信号灯状态更新失败: simulationId={}, error={}", simulationId, e.getMessage(), e);
        }
    }

    /**
     * 推送仿真优化结果
     * @param simulationId 仿真ID
     * @param optimizationResult 优化结果
     */
    public void sendOptimizationResult(String simulationId, Map<String, Object> optimizationResult) {
        try {
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("type", "optimization_result");
            resultData.put("simulationId", simulationId);
            resultData.put("optimizationResult", optimizationResult);
            resultData.put("timestamp", LocalDateTime.now().toString());

            String destination = "/topic/optimization-results/" + simulationId;
            logger.debug("推送仿真优化结果: destination={}", destination);
            messagingTemplate.convertAndSend(destination, resultData);

            logger.info("已推送仿真优化结果: simulationId={}", simulationId);

        } catch (Exception e) {
            logger.error("推送仿真优化结果失败: simulationId={}, error={}", simulationId, e.getMessage(), e);
        }
    }

    /**
     * 推送仿真错误信息
     * @param simulationId 仿真ID
     * @param errorMessage 错误信息
     * @param errorCode 错误代码
     */
    public void sendSimulationError(String simulationId, String errorMessage, String errorCode) {
        try {
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("type", "simulation_error");
            errorData.put("simulationId", simulationId);
            errorData.put("errorMessage", errorMessage);
            errorData.put("errorCode", errorCode);
            errorData.put("timestamp", LocalDateTime.now().toString());

            String destination = "/topic/simulation-status/" + simulationId;
            logger.debug("推送仿真错误信息: destination={}, error={}", destination, errorMessage);
            messagingTemplate.convertAndSend(destination, errorData);

            logger.warn("已推送仿真错误信息: simulationId={}, error={}", simulationId, errorMessage);

        } catch (Exception e) {
            logger.error("推送仿真错误信息失败: simulationId={}, error={}", simulationId, e.getMessage(), e);
        }
    }

    // ==================== 四方向交通分析WebSocket支持 ====================

    /**
     * 推送四方向帧数据更新
     * @param taskId 任务ID
     * @param direction 方向（east, south, west, north）
     * @param frameNumber 帧号
     * @param totalFrames 总帧数
     * @param imageData Base64编码的图像数据
     * @param detectionCount 检测到的车辆数量
     * @param vehicleTypes 车辆类型统计
     */
    public void sendFourWayFrameUpdate(String taskId, String direction, int frameNumber, int totalFrames,
                                      String imageData, int detectionCount, Map<String, Integer> vehicleTypes) {
        try {
            // 构造四方向帧数据消息
            Map<String, Object> frameData = new HashMap<>();
            frameData.put("type", "four_way_frame_update");
            frameData.put("taskId", taskId);
            frameData.put("direction", direction);
            frameData.put("frameNumber", frameNumber);
            frameData.put("totalFrames", totalFrames);
            frameData.put("imageData", imageData);
            frameData.put("detectionCount", detectionCount);
            frameData.put("vehicleTypes", vehicleTypes != null ? vehicleTypes : new HashMap<>());
            frameData.put("timestamp", LocalDateTime.now().toString());

            // 缓存帧数据（使用方向前缀的taskId）
            String directionTaskId = direction + "_" + taskId;
            cacheFrameData(directionTaskId, frameData);

            // 推送到四方向帧更新主题
            String destination = "/topic/four-way-frames/" + taskId;
            logger.debug("推送四方向帧数据更新: destination={}, direction={}, frame={}",
                        destination, direction, frameNumber);
            messagingTemplate.convertAndSend(destination, frameData);

            // 同时推送到特定方向的主题
            String directionDestination = "/topic/four-way-frames/" + taskId + "/" + direction;
            messagingTemplate.convertAndSend(directionDestination, frameData);

            logger.info("已推送四方向帧数据到主题: {}, 方向: {}, 帧号: {}, 检测数量: {}",
                       destination, direction, frameNumber, detectionCount);

        } catch (Exception e) {
            logger.error("推送四方向帧数据更新失败: direction={}, error={}", direction, e.getMessage(), e);
        }
    }

    /**
     * 推送四方向分析进度更新
     * @param taskId 任务ID
     * @param direction 方向
     * @param progress 进度
     * @param status 状态
     * @param message 消息
     */
    public void sendFourWayProgressUpdate(String taskId, String direction, int progress, String status, String message) {
        try {
            // 构造四方向进度消息
            Map<String, Object> progressData = new HashMap<>();
            progressData.put("type", "four_way_progress");
            progressData.put("taskId", taskId);
            progressData.put("direction", direction);
            progressData.put("progress", progress);
            progressData.put("status", status);
            progressData.put("message", message);
            progressData.put("timestamp", LocalDateTime.now().toString());

            // 推送到四方向进度主题
            String destination = "/topic/four-way-progress/" + taskId;
            logger.debug("推送四方向进度更新: destination={}, direction={}, progress={}%",
                        destination, direction, progress);
            messagingTemplate.convertAndSend(destination, progressData);

            // 同时推送到特定方向的进度主题
            String directionDestination = "/topic/four-way-progress/" + taskId + "/" + direction;
            messagingTemplate.convertAndSend(directionDestination, progressData);

            logger.info("已推送四方向进度更新: 任务={}, 方向={}, 进度={}%, 状态={}",
                       taskId, direction, progress, status);

        } catch (Exception e) {
            logger.error("推送四方向进度更新失败: direction={}, error={}", direction, e.getMessage(), e);
        }
    }

    /**
     * 推送四方向分析整体完成消息
     * @param taskId 任务ID
     * @param summary 分析结果摘要
     */
    public void sendFourWayAnalysisComplete(String taskId, Map<String, Object> summary) {
        try {
            // 构造四方向整体完成消息
            Map<String, Object> completeData = new HashMap<>();
            completeData.put("type", "four_way_analysis_complete");
            completeData.put("taskId", taskId);
            completeData.put("status", "completed");
            completeData.put("message", "所有四个方向的视频分析已完成");
            completeData.put("timestamp", LocalDateTime.now().toString());
            completeData.put("summary", summary);

            // 推送到四方向整体完成主题
            String destination = "/topic/four-way-analysis-complete/" + taskId;
            logger.debug("推送四方向整体完成消息: destination={}, taskId={}", destination, taskId);
            messagingTemplate.convertAndSend(destination, completeData);

            // 同时推送到通用四方向进度主题，便于现有监听器接收
            String progressDestination = "/topic/four-way-progress/" + taskId;
            messagingTemplate.convertAndSend(progressDestination, completeData);

            logger.info("已推送四方向整体完成消息: 任务={}, 总车辆数={}",
                       taskId, summary.get("totalVehicles"));

        } catch (Exception e) {
            logger.error("推送四方向整体完成消息失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * REST API接口：接收Python后端推送的四方向帧数据
     * @param frameUpdateRequest 四方向帧更新请求
     * @return 响应结果
     */
    @PostMapping("/four-way-frame-update")
    public ResponseEntity<Map<String, Object>> receiveFourWayFrameUpdate(@RequestBody Map<String, Object> frameUpdateRequest) {
        try {
            String taskId = (String) frameUpdateRequest.get("taskId");
            String direction = (String) frameUpdateRequest.get("direction");
            Integer frameNumber = (Integer) frameUpdateRequest.get("frameNumber");
            Integer totalFrames = (Integer) frameUpdateRequest.get("totalFrames");
            String imageData = (String) frameUpdateRequest.get("imageData");
            Integer detectionCount = (Integer) frameUpdateRequest.get("detectionCount");

            @SuppressWarnings("unchecked")
            Map<String, Integer> vehicleTypes = (Map<String, Integer>) frameUpdateRequest.get("vehicleTypes");

            if (taskId == null || direction == null || frameNumber == null || totalFrames == null || imageData == null) {
                logger.warn("接收到不完整的四方向帧更新请求: {}", frameUpdateRequest);
                return ResponseEntity.badRequest().body(Map.of("error", "缺少必要参数"));
            }

            logger.debug("接收到四方向帧更新请求: taskId={}, direction={}, frame={}/{}",
                        taskId, direction, frameNumber, totalFrames);

            // 推送四方向帧数据
            sendFourWayFrameUpdate(taskId, direction, frameNumber, totalFrames, imageData,
                                  detectionCount != null ? detectionCount : 0, vehicleTypes);

            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "四方向帧数据推送成功",
                "direction", direction
            ));

        } catch (Exception e) {
            logger.error("处理四方向帧更新请求失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "处理四方向帧更新请求失败: " + e.getMessage()));
        }
    }

    /**
     * 获取四方向任务的缓存帧数据
     * @param taskId 任务ID
     * @param direction 方向（可选）
     * @return 缓存的帧数据列表
     */
    @GetMapping("/four-way-frames/{taskId}")
    public ResponseEntity<Map<String, Object>> getFourWayCachedFrames(@PathVariable String taskId,
                                                                      @RequestParam(required = false) String direction) {
        try {
            Map<String, Object> result = new HashMap<>();

            if (direction != null) {
                // 获取特定方向的帧数据
                String directionTaskId = direction + "_" + taskId;
                ConcurrentLinkedQueue<Map<String, Object>> taskFrames = frameCache.get(directionTaskId);

                if (taskFrames == null || taskFrames.isEmpty()) {
                    result.put("frames", new Object[0]);
                    result.put("count", 0);
                    result.put("direction", direction);
                } else {
                    Object[] framesArray = taskFrames.toArray();
                    result.put("frames", framesArray);
                    result.put("count", framesArray.length);
                    result.put("direction", direction);
                }
            } else {
                // 获取所有方向的帧数据
                String[] directions = {"east", "south", "west", "north"};
                Map<String, Object> allDirections = new HashMap<>();
                int totalCount = 0;

                for (String dir : directions) {
                    String directionTaskId = dir + "_" + taskId;
                    ConcurrentLinkedQueue<Map<String, Object>> taskFrames = frameCache.get(directionTaskId);

                    if (taskFrames != null && !taskFrames.isEmpty()) {
                        Object[] framesArray = taskFrames.toArray();
                        allDirections.put(dir, Map.of("frames", framesArray, "count", framesArray.length));
                        totalCount += framesArray.length;
                    } else {
                        allDirections.put(dir, Map.of("frames", new Object[0], "count", 0));
                    }
                }

                result.put("directions", allDirections);
                result.put("totalCount", totalCount);
            }

            result.put("taskId", taskId);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("获取四方向缓存帧数据失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "获取四方向缓存帧数据失败: " + e.getMessage()));
        }
    }

    /**
     * 清理任务的缓存数据
     * @param taskId 任务ID
     */
    public void clearTaskCache(String taskId) {
        try {
            frameCache.remove(taskId);
            persistentFrameStorage.remove(taskId); // 同时清理持久化存储

            // 清理四方向相关的缓存
            String[] directions = {"east", "south", "west", "north"};
            for (String direction : directions) {
                String directionTaskId = direction + "_" + taskId;
                frameCache.remove(directionTaskId);
                persistentFrameStorage.remove(directionTaskId);
            }

            logger.debug("清理任务缓存: taskId={}", taskId);
        } catch (Exception e) {
            logger.error("清理任务缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 推送帧数据（通用方法）
     * @param taskId 任务ID
     * @param frameData 帧数据
     */
    public void pushFrameData(String taskId, Map<String, Object> frameData) {
        try {
            Integer frameNumber = (Integer) frameData.get("frameNumber");
            Integer totalFrames = (Integer) frameData.get("totalFrames");
            String imageData = (String) frameData.get("imageData");
            Integer detectionCount = (Integer) frameData.get("detectionCount");

            if (frameNumber != null && totalFrames != null && imageData != null) {
                sendFrameUpdate(taskId, frameNumber, totalFrames, imageData,
                              detectionCount != null ? detectionCount : 0);
            }
        } catch (Exception e) {
            logger.error("推送帧数据失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 推送四方向帧数据（通用方法）
     * @param taskId 任务ID
     * @param direction 方向
     * @param frameData 帧数据
     */
    public void pushFourWayFrameData(String taskId, String direction, Map<String, Object> frameData) {
        try {
            Integer frameNumber = (Integer) frameData.get("frameNumber");
            Integer totalFrames = (Integer) frameData.get("totalFrames");
            String imageData = (String) frameData.get("imageData");
            Integer detectionCount = (Integer) frameData.get("detectionCount");

            @SuppressWarnings("unchecked")
            Map<String, Integer> vehicleTypes = (Map<String, Integer>) frameData.get("vehicleTypes");

            if (frameNumber != null && totalFrames != null && imageData != null) {
                sendFourWayFrameUpdate(taskId, direction, frameNumber, totalFrames, imageData,
                                     detectionCount != null ? detectionCount : 0, vehicleTypes);
            }
        } catch (Exception e) {
            logger.error("推送四方向帧数据失败: taskId={}, direction={}, error={}", taskId, direction, e.getMessage(), e);
        }
    }
}