<template>
  <div class="simulation-monitor">
    <el-row :gutter="20">
      <!-- 实时数据卡片 -->
      <el-col :span="24">
        <el-card class="monitor-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3><i class="el-icon-monitor"></i> 实时仿真监控</h3>
              <el-tag :type="isConnected ? 'success' : 'danger'" size="small">
                {{ isConnected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
          </template>

          <!-- 关键指标展示 -->
          <div class="metrics-grid">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="metric-card">
                  <div class="metric-icon">
                    <i class="el-icon-time" style="color: #409eff"></i>
                  </div>
                  <div class="metric-content">
                    <div class="metric-value">{{ formatTime(simulationTime) }}</div>
                    <div class="metric-label">仿真时间</div>
                  </div>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="metric-card">
                  <div class="metric-icon">
                    <i class="el-icon-truck" style="color: #67c23a"></i>
                  </div>
                  <div class="metric-content">
                    <div class="metric-value">{{ vehicleCount }}</div>
                    <div class="metric-label">当前车辆</div>
                  </div>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="metric-card">
                  <div class="metric-icon">
                    <i class="el-icon-odometer" style="color: #e6a23c"></i>
                  </div>
                  <div class="metric-content">
                    <div class="metric-value">{{ formatSpeed(averageSpeed) }}</div>
                    <div class="metric-label">平均速度</div>
                  </div>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="metric-card">
                  <div class="metric-icon">
                    <i class="el-icon-warning" style="color: #f56c6c"></i>
                  </div>
                  <div class="metric-content">
                    <div class="metric-value">{{ trafficLightCount }}</div>
                    <div class="metric-label">信号灯数</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 实时图表 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <h4><i class="el-icon-data-line"></i> 车流量变化</h4>
          </template>
          <div ref="flowChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <h4><i class="el-icon-pie-chart"></i> 速度分布</h4>
          </template>
          <div ref="speedChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 方向流量监控 -->
      <el-col :span="24">
        <el-card class="direction-card" shadow="hover">
          <template #header>
            <h4><i class="el-icon-position"></i> 各方向流量监控</h4>
          </template>
          
          <div class="direction-grid">
            <el-row :gutter="20">
              <el-col :span="6" v-for="direction in directions" :key="direction.name">
                <div class="direction-item">
                  <div class="direction-header">
                    <i :class="direction.icon"></i>
                    <span>{{ direction.label }}</span>
                  </div>
                  
                  <div class="direction-metrics">
                    <div class="direction-metric">
                      <span class="metric-label">车辆数:</span>
                      <span class="metric-value">{{ direction.vehicleCount }}</span>
                    </div>
                    <div class="direction-metric">
                      <span class="metric-label">流量:</span>
                      <span class="metric-value">{{ direction.flowRate }} 辆/h</span>
                    </div>
                    <div class="direction-metric">
                      <span class="metric-label">速度:</span>
                      <span class="metric-value">{{ formatSpeed(direction.avgSpeed) }}</span>
                    </div>
                    <div class="direction-metric">
                      <span class="metric-label">密度:</span>
                      <span class="metric-value">{{ direction.density.toFixed(1) }} 辆/km</span>
                    </div>
                  </div>
                  
                  <div class="direction-status">
                    <el-tag :type="getFlowStatusType(direction.flowRate)" size="small">
                      {{ getFlowStatusText(direction.flowRate) }}
                    </el-tag>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 信号灯状态监控 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="traffic-light-card" shadow="hover">
          <template #header>
            <h4><i class="el-icon-warning"></i> 信号灯状态</h4>
          </template>
          
          <div class="traffic-light-monitor">
            <div class="intersection-view">
              <div class="intersection">
                <!-- 交叉口示意图 -->
                <div class="road road-horizontal"></div>
                <div class="road road-vertical"></div>
                
                <!-- 信号灯 -->
                <div class="traffic-light traffic-light-north" :class="getTrafficLightClass('north')">
                  <div class="light red" :class="{ active: trafficLights.north === 'red' }"></div>
                  <div class="light yellow" :class="{ active: trafficLights.north === 'yellow' }"></div>
                  <div class="light green" :class="{ active: trafficLights.north === 'green' }"></div>
                </div>
                
                <div class="traffic-light traffic-light-south" :class="getTrafficLightClass('south')">
                  <div class="light red" :class="{ active: trafficLights.south === 'red' }"></div>
                  <div class="light yellow" :class="{ active: trafficLights.south === 'yellow' }"></div>
                  <div class="light green" :class="{ active: trafficLights.south === 'green' }"></div>
                </div>
                
                <div class="traffic-light traffic-light-east" :class="getTrafficLightClass('east')">
                  <div class="light red" :class="{ active: trafficLights.east === 'red' }"></div>
                  <div class="light yellow" :class="{ active: trafficLights.east === 'yellow' }"></div>
                  <div class="light green" :class="{ active: trafficLights.east === 'green' }"></div>
                </div>
                
                <div class="traffic-light traffic-light-west" :class="getTrafficLightClass('west')">
                  <div class="light red" :class="{ active: trafficLights.west === 'red' }"></div>
                  <div class="light yellow" :class="{ active: trafficLights.west === 'yellow' }"></div>
                  <div class="light green" :class="{ active: trafficLights.west === 'green' }"></div>
                </div>
              </div>
            </div>
            
            <div class="signal-timing-info">
              <h5>当前配时方案</h5>
              <el-table :data="signalPhases" size="small" style="width: 100%">
                <el-table-column prop="phase" label="相位" width="80"></el-table-column>
                <el-table-column prop="duration" label="时长(秒)" width="100"></el-table-column>
                <el-table-column prop="state" label="状态" width="120">
                  <template #default="scope">
                    <el-tag size="small">{{ scope.row.state }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述"></el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'SimulationMonitor',
  props: {
    simulationId: {
      type: String,
      required: true
    },
    isRunning: {
      type: Boolean,
      default: false
    }
  },
  emits: ['data-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const isConnected = ref(false)
    const simulationTime = ref(0)
    const vehicleCount = ref(0)
    const averageSpeed = ref(0)
    const trafficLightCount = ref(4)
    
    // 图表引用
    const flowChartRef = ref(null)
    const speedChartRef = ref(null)
    let flowChart = null
    let speedChart = null
    
    // 方向数据
    const directions = reactive([
      {
        name: 'east',
        label: '东向',
        icon: 'el-icon-right',
        vehicleCount: 0,
        flowRate: 0,
        avgSpeed: 0,
        density: 0
      },
      {
        name: 'south',
        label: '南向',
        icon: 'el-icon-bottom',
        vehicleCount: 0,
        flowRate: 0,
        avgSpeed: 0,
        density: 0
      },
      {
        name: 'west',
        label: '西向',
        icon: 'el-icon-left',
        vehicleCount: 0,
        flowRate: 0,
        avgSpeed: 0,
        density: 0
      },
      {
        name: 'north',
        label: '北向',
        icon: 'el-icon-top',
        vehicleCount: 0,
        flowRate: 0,
        avgSpeed: 0,
        density: 0
      }
    ])
    
    // 信号灯状态
    const trafficLights = reactive({
      north: 'red',
      south: 'red',
      east: 'green',
      west: 'green'
    })
    
    // 信号配时方案
    const signalPhases = reactive([
      { phase: '相位1', duration: 30, state: 'GrGr', description: '东西绿灯' },
      { phase: '相位2', duration: 5, state: 'yryr', description: '东西黄灯' },
      { phase: '相位3', duration: 30, state: 'rGrG', description: '南北绿灯' },
      { phase: '相位4', duration: 5, state: 'ryry', description: '南北黄灯' }
    ])
    
    // 历史数据
    const flowData = reactive({
      times: [],
      values: []
    })
    
    const speedData = reactive({
      ranges: ['0-10', '10-20', '20-30', '30-40', '40-50', '50+'],
      values: [0, 0, 0, 0, 0, 0]
    })
    
    // 数据更新定时器
    let updateTimer = null
    
    // 方法
    const formatTime = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    
    const formatSpeed = (speed) => {
      return `${speed.toFixed(1)} m/s`
    }
    
    const getFlowStatusType = (flowRate) => {
      if (flowRate < 300) return 'success'
      if (flowRate < 600) return 'warning'
      return 'danger'
    }
    
    const getFlowStatusText = (flowRate) => {
      if (flowRate < 300) return '畅通'
      if (flowRate < 600) return '缓慢'
      return '拥堵'
    }
    
    const getTrafficLightClass = (direction) => {
      return `light-${trafficLights[direction]}`
    }
    
    const initCharts = () => {
      nextTick(() => {
        // 初始化车流量图表
        if (flowChartRef.value) {
          flowChart = echarts.init(flowChartRef.value)
          updateFlowChart()
        }
        
        // 初始化速度分布图表
        if (speedChartRef.value) {
          speedChart = echarts.init(speedChartRef.value)
          updateSpeedChart()
        }
      })
    }
    
    const updateFlowChart = () => {
      if (!flowChart) return
      
      const option = {
        title: {
          text: '实时车流量',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: flowData.times,
          axisLabel: {
            formatter: (value) => {
              const time = new Date(value)
              return `${time.getHours()}:${time.getMinutes().toString().padStart(2, '0')}`
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '车辆数'
        },
        series: [{
          data: flowData.values,
          type: 'line',
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          lineStyle: {
            color: '#409eff'
          }
        }]
      }
      
      flowChart.setOption(option)
    }
    
    const updateSpeedChart = () => {
      if (!speedChart) return
      
      const option = {
        title: {
          text: '速度分布',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'item'
        },
        series: [{
          type: 'pie',
          radius: '60%',
          data: speedData.ranges.map((range, index) => ({
            name: `${range} km/h`,
            value: speedData.values[index]
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      
      speedChart.setOption(option)
    }
    
    const updateMonitorData = () => {
      // 模拟实时数据更新
      if (props.isRunning) {
        simulationTime.value += 1
        vehicleCount.value = Math.floor(Math.random() * 50) + 20
        averageSpeed.value = Math.random() * 20 + 30
        
        // 更新方向数据
        directions.forEach(direction => {
          direction.vehicleCount = Math.floor(Math.random() * 15) + 5
          direction.flowRate = Math.floor(Math.random() * 400) + 200
          direction.avgSpeed = Math.random() * 15 + 25
          direction.density = Math.random() * 30 + 10
        })
        
        // 更新流量历史数据
        const now = new Date()
        flowData.times.push(now.toISOString())
        flowData.values.push(vehicleCount.value)
        
        // 保持最近50个数据点
        if (flowData.times.length > 50) {
          flowData.times.shift()
          flowData.values.shift()
        }
        
        // 更新速度分布数据
        speedData.values = speedData.values.map(() => Math.floor(Math.random() * 20))
        
        // 更新图表
        updateFlowChart()
        updateSpeedChart()
        
        // 模拟信号灯变化
        if (simulationTime.value % 30 === 0) {
          // 简单的信号灯切换逻辑
          if (trafficLights.east === 'green') {
            trafficLights.east = 'yellow'
            trafficLights.west = 'yellow'
          } else if (trafficLights.east === 'yellow') {
            trafficLights.east = 'red'
            trafficLights.west = 'red'
            trafficLights.north = 'green'
            trafficLights.south = 'green'
          } else if (trafficLights.north === 'green') {
            trafficLights.north = 'yellow'
            trafficLights.south = 'yellow'
          } else {
            trafficLights.north = 'red'
            trafficLights.south = 'red'
            trafficLights.east = 'green'
            trafficLights.west = 'green'
          }
        }
        
        isConnected.value = true
        emit('data-updated', {
          simulationTime: simulationTime.value,
          vehicleCount: vehicleCount.value,
          averageSpeed: averageSpeed.value,
          directions: directions
        })
      } else {
        isConnected.value = false
      }
    }
    
    const startMonitoring = () => {
      if (updateTimer) {
        clearInterval(updateTimer)
      }
      
      updateTimer = setInterval(updateMonitorData, 1000) // 每秒更新
    }
    
    const stopMonitoring = () => {
      if (updateTimer) {
        clearInterval(updateTimer)
        updateTimer = null
      }
      isConnected.value = false
    }
    
    // 生命周期
    onMounted(() => {
      initCharts()
      if (props.isRunning) {
        startMonitoring()
      }
    })
    
    onUnmounted(() => {
      stopMonitoring()
      if (flowChart) {
        flowChart.dispose()
      }
      if (speedChart) {
        speedChart.dispose()
      }
    })
    
    // 监听运行状态变化
    const { isRunning } = props
    if (isRunning) {
      startMonitoring()
    } else {
      stopMonitoring()
    }
    
    return {
      // 响应式数据
      isConnected,
      simulationTime,
      vehicleCount,
      averageSpeed,
      trafficLightCount,
      directions,
      trafficLights,
      signalPhases,
      
      // 图表引用
      flowChartRef,
      speedChartRef,
      
      // 方法
      formatTime,
      formatSpeed,
      getFlowStatusType,
      getFlowStatusText,
      getTrafficLightClass,
      startMonitoring,
      stopMonitoring
    }
  }
}
</script>

<style scoped>
.simulation-monitor {
  padding: 0;
}

.monitor-card,
.chart-card,
.direction-card,
.traffic-light-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3,
.card-header h4 {
  margin: 0;
  color: #303133;
}

.metrics-grid {
  margin-bottom: 20px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.metric-icon {
  font-size: 24px;
  margin-right: 15px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.direction-grid {
  margin-top: 15px;
}

.direction-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.direction-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-weight: bold;
  color: #303133;
}

.direction-header i {
  margin-right: 8px;
  font-size: 18px;
}

.direction-metrics {
  margin-bottom: 10px;
}

.direction-metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.direction-metric .metric-label {
  color: #909399;
}

.direction-metric .metric-value {
  color: #303133;
  font-weight: bold;
}

.traffic-light-monitor {
  display: flex;
  gap: 30px;
}

.intersection-view {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.intersection {
  position: relative;
  width: 200px;
  height: 200px;
}

.road {
  position: absolute;
  background: #ddd;
}

.road-horizontal {
  width: 100%;
  height: 40px;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.road-vertical {
  width: 40px;
  height: 100%;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.traffic-light {
  position: absolute;
  width: 20px;
  height: 60px;
  background: #333;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  padding: 5px 0;
}

.traffic-light-north {
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.traffic-light-south {
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.traffic-light-east {
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.traffic-light-west {
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  opacity: 0.3;
}

.light.red {
  background: #f56c6c;
}

.light.yellow {
  background: #e6a23c;
}

.light.green {
  background: #67c23a;
}

.light.active {
  opacity: 1;
  box-shadow: 0 0 10px currentColor;
}

.signal-timing-info {
  flex: 1;
}

.signal-timing-info h5 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
