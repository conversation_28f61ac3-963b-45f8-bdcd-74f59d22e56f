# 应用服务 WEB 访问端口
server.port=8080
server.servlet.context-path=/
server.tomcat.max-threads=200
server.tomcat.max-connections=10000
server.tomcat.accept-count=100
server.tomcat.max-http-form-post-size=500MB
server.tomcat.connection-timeout=120000
server.compression.enabled=true
server.compression.min-response-size=2048
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain

# Spring 核心配置
spring.main.allow-bean-definition-overriding=true

# 应用相关配置
spring.application.name=traffic-analysis

# 静态资源配置
spring.mvc.static-path-pattern=/static/**,/images/**,/videos/**
spring.web.resources.static-locations=classpath:/static/,file:./static/,file:./uploads/,file:D:/code/trafficsystem/trafficsystem/static/
spring.web.resources.chain.strategy.content.enabled=true
spring.web.resources.chain.strategy.content.paths=/**

# 添加MIME类型映射
spring.mvc.contentnegotiation.media-types.svg=image/svg+xml
spring.mvc.contentnegotiation.media-types.mp4=video/mp4
spring.mvc.contentnegotiation.media-types.avi=video/x-msvideo
spring.mvc.contentnegotiation.media-types.mkv=video/x-matroska
spring.web.resources.cache.cachecontrol.max-age=3600
# 设置路径匹配策略为ant风格，支持更灵活的URL模式
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

# 视频分析相关配置
traffic.analysis.ffmpeg.path=D:/ffmpeg/bin/ffmpeg.exe
traffic.analysis.video.upload-dir=D:/code/trafficsystem/trafficsystem/static/video/uploads
traffic.analysis.video.results-dir=D:/code/trafficsystem/trafficsystem/static/video/results
traffic.analysis.video.thumbnails-dir=D:/code/trafficsystem/trafficsystem/src/main/resources/static/images/thumbnails

# 模板引擎配置
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
spring.servlet.multipart.file-size-threshold=2MB
spring.servlet.multipart.location=${java.io.tmpdir}
app.upload.dir=${user.home}/trafficsystem/uploads

# 日志配置
logging.level.root=INFO
logging.level.com.traffic.analysis=INFO
logging.level.com.traffic.analysis.controller=INFO
logging.level.com.traffic.analysis.service=INFO
logging.level.com.traffic.analysis.repository=INFO
logging.level.com.traffic.analysis.utils=INFO
logging.level.org.springframework.web=WARN
logging.level.org.springframework.security=WARN
logging.level.org.springframework.data.mongodb=WARN
logging.level.org.springframework.web.client.RestTemplate=WARN
logging.file.name=logs/traffic-analysis.log

# 模型API配置
model.api.url=http://localhost:5001
model.api.health-check-url=${model.api.url}/health
model.api.analyze-url=${model.api.url}/analyze
model.api.timeout=30000

# Python模型配置
python.executable=D:\\Python3.12.4\\python.exe
python.script.path=./python_model/car_detection.py
model.timeout=60

# Python服务自动启动配置
python.service.auto-start=true
# 控制是否允许通过API独立控制服务（启动/停止）
python.service.independent-control=true

# 主模型API服务（处理east方向）
python.service.model-api.enabled=true
python.service.model-api.script=python_model/model_api.py
python.service.model-api.port=5001

# 四方向并行处理服务配置
python.service.four-way-parallel.enabled=true
python.service.model-api-south.enabled=true
python.service.model-api-south.script=python_model/model_api.py
python.service.model-api-south.port=5002
python.service.model-api-west.enabled=true
python.service.model-api-west.script=python_model/model_api.py
python.service.model-api-west.port=5003
python.service.model-api-north.enabled=true
python.service.model-api-north.script=python_model/model_api.py
python.service.model-api-north.port=5004

# API控制器服务
python.service.api-controller.enabled=true
python.service.api-controller.script=python_model/api_controller.py
python.service.api-controller.port=5000

# 服务启动配置
python.service.startup-delay=3000
python.service.health-check-timeout=30000
python.service.max-startup-attempts=3

# 数据库服务控制配置
database.service.independent-control=true
# 控制是否允许通过API断开/重连数据库（安全考虑，默认禁用）
# 注意：在生产环境中应保持为false，仅在开发/测试环境中设置为true
database.service.allow-disconnect=true

# 上传目录配置
upload.dir=uploads

# 历史记录配置
app.history.file=${user.home}/trafficsystem/history/analysis_history.json

# CORS 配置
spring.mvc.cors.allowed-origins=http://localhost:5173,http://localhost:5000,http://localhost:5001,http://localhost:8080
spring.mvc.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.mvc.cors.allowed-headers=*
spring.mvc.cors.exposed-headers=*
spring.mvc.cors.allow-credentials=true
spring.mvc.cors.max-age=3600

# 添加CORS相关日志以便调试
logging.level.org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping=TRACE
logging.level.org.springframework.web.cors=DEBUG

# Python API服务配置
python.api.url=http://localhost:5000
python.api.history.endpoint=/api/history
python.api.history-stats.endpoint=/api/history/stats
python.api.history-save.endpoint=/api/history/save

# HTTP客户端配置
spring.mvc.async.request-timeout=120000
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true

# MongoDB配置
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=traffic_analysis
spring.data.mongodb.username=adminUser
spring.data.mongodb.password=123456
spring.data.mongodb.authentication-database=admin
spring.data.mongodb.auto-index-creation=true

# 禁用JPA数据源自动配置
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# 会话配置
server.servlet.session.timeout=30m
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false
server.servlet.session.cookie.path=/
server.servlet.session.tracking-modes=cookie
spring.mvc.servlet.disable-url-rewriting=true

# JWT配置
jwt.secret=your-secret-key
jwt.expiration=86400000

# 打印认证相关的日志
logging.level.com.traffic.analysis.config=DEBUG
logging.level.com.traffic.analysis.security=DEBUG
logging.level.com.traffic.analysis.controller=DEBUG

# CORS配置
cors.allowedOrigins=http://localhost:5173,http://localhost:5000,http://localhost:5001,http://localhost:8080

