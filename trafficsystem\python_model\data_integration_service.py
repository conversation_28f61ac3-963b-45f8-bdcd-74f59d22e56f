#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据集成服务

负责在视频分析系统和SUMO仿真系统之间进行数据转换和集成
"""

import os
import sys
import logging
import json
import requests
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
from pymongo import MongoClient

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入配置
from config import MONGODB_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*", "supports_credentials": True}})

# MongoDB连接
mongodb_client = None
try:
    mongodb_client = MongoClient(MONGODB_CONFIG["uri"])
    # 测试连接
    mongodb_client.admin.command('ping')
    logger.info("MongoDB连接成功")
except Exception as e:
    logger.error(f"MongoDB连接失败: {e}")

class DataIntegrationService:
    """数据集成服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.java_backend_url = "http://localhost:8080"
        self.sumo_api_url = "http://localhost:5002"
    
    def get_analysis_result(self, analysis_task_id):
        """从数据库获取分析结果"""
        try:
            if mongodb_client is None:
                raise Exception("数据库连接失败")
            
            db = mongodb_client[MONGODB_CONFIG["database"]]
            collection = db["intersection_analysis_four_way"]
            
            result = collection.find_one({"task_id": analysis_task_id})
            if not result:
                # 尝试从其他集合查找
                collections_to_check = ["analysis_results", "video_analysis_results"]
                for coll_name in collections_to_check:
                    collection = db[coll_name]
                    result = collection.find_one({"task_id": analysis_task_id})
                    if result:
                        break
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取分析结果失败: {e}")
            return None
    
    def convert_analysis_to_sumo_format(self, analysis_result):
        """将分析结果转换为SUMO格式"""
        try:
            # 提取交通数据
            traffic_data = {}
            
            if 'traffic_analysis' in analysis_result:
                traffic_analysis = analysis_result['traffic_analysis']
                
                # 提取方向数据
                directions = {}
                if 'direction_analysis' in traffic_analysis:
                    direction_analysis = traffic_analysis['direction_analysis']
                    
                    for direction, data in direction_analysis.items():
                        directions[direction] = {
                            'vehicleCount': data.get('vehicle_count', 0),
                            'vehicleTypes': data.get('vehicle_types', {}),
                            'averageSpeed': data.get('average_speed', 30),
                            'density': data.get('density', 0),
                            'duration': 3600  # 假设1小时的观测时间
                        }
                
                traffic_data['directions'] = directions
                
                # 提取总体统计
                traffic_data['total_vehicles'] = traffic_analysis.get('total_vehicles', 0)
                traffic_data['congestion_level'] = traffic_analysis.get('congestion_level', '正常')
                traffic_data['traffic_flow_balance'] = traffic_analysis.get('traffic_flow_balance', 0.8)
                
                # 提取信号优化建议
                if 'signal_optimization' in traffic_analysis:
                    signal_opt = traffic_analysis['signal_optimization']
                    traffic_data['signal_optimization'] = {
                        'recommended_cycle': signal_opt.get('recommended_cycle', 120),
                        'green_time_allocation': signal_opt.get('green_time_allocation', {}),
                        'recommendations': traffic_analysis.get('recommendations', [])
                    }
            
            return traffic_data
            
        except Exception as e:
            self.logger.error(f"转换数据格式失败: {e}")
            return {}
    
    def create_simulation_task(self, user_id, username, analysis_task_id, simulation_type):
        """创建仿真任务"""
        try:
            # 获取分析结果
            analysis_result = self.get_analysis_result(analysis_task_id)
            if not analysis_result:
                raise Exception("未找到分析结果")
            
            # 转换数据格式
            traffic_data = self.convert_analysis_to_sumo_format(analysis_result)
            
            # 调用Java后端创建仿真任务
            create_url = f"{self.java_backend_url}/api/simulation/create"
            
            request_data = {
                "user_id": user_id,
                "username": username,
                "task_name": f"基于分析{analysis_task_id}的{simulation_type}仿真",
                "simulation_type": simulation_type,
                "traffic_data": traffic_data,
                "analysis_task_id": analysis_task_id
            }
            
            response = requests.post(create_url, json=request_data, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"创建仿真任务失败: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"创建仿真任务失败: {e}")
            raise
    
    def start_simulation_task(self, simulation_id, use_gui=False):
        """启动仿真任务"""
        try:
            start_url = f"{self.java_backend_url}/api/simulation/{simulation_id}/start"
            
            request_data = {"use_gui": use_gui}
            
            response = requests.post(start_url, json=request_data, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"启动仿真失败: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"启动仿真失败: {e}")
            raise
    
    def get_simulation_status(self, simulation_id):
        """获取仿真状态"""
        try:
            status_url = f"{self.java_backend_url}/api/simulation/{simulation_id}/status"
            
            response = requests.get(status_url, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"获取仿真状态失败: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"获取仿真状态失败: {e}")
            raise

# 创建服务实例
integration_service = DataIntegrationService()

# API端点
@app.route('/api/integration/convert-analysis-data', methods=['POST'])
def convert_analysis_data():
    """转换分析数据为SUMO格式"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "缺少请求数据"}), 400
        
        analysis_task_id = data.get('analysis_task_id')
        if not analysis_task_id:
            return jsonify({"status": "error", "message": "缺少分析任务ID"}), 400
        
        logger.info(f"转换分析数据: {analysis_task_id}")
        
        # 获取分析结果
        analysis_result = integration_service.get_analysis_result(analysis_task_id)
        if not analysis_result:
            return jsonify({"status": "error", "message": "未找到分析结果"}), 404
        
        # 转换数据格式
        sumo_data = integration_service.convert_analysis_to_sumo_format(analysis_result)
        
        return jsonify({
            "status": "success",
            "sumo_data": sumo_data,
            "analysis_task_id": analysis_task_id,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"转换分析数据失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/integration/create-simulation', methods=['POST'])
def create_simulation():
    """基于分析结果创建仿真任务"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "缺少请求数据"}), 400
        
        analysis_task_id = data.get('analysis_task_id')
        simulation_type = data.get('simulation_type', 'comprehensive')
        user_id = data.get('user_id')
        username = data.get('username')
        
        if not all([analysis_task_id, user_id, username]):
            return jsonify({"status": "error", "message": "缺少必要参数"}), 400
        
        logger.info(f"创建仿真任务: analysis_id={analysis_task_id}, type={simulation_type}")
        
        # 创建仿真任务
        result = integration_service.create_simulation_task(
            user_id, username, analysis_task_id, simulation_type
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"创建仿真任务失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/integration/start-simulation', methods=['POST'])
def start_simulation():
    """启动仿真任务"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "缺少请求数据"}), 400
        
        simulation_id = data.get('simulation_id')
        use_gui = data.get('use_gui', False)
        
        if not simulation_id:
            return jsonify({"status": "error", "message": "缺少仿真ID"}), 400
        
        logger.info(f"启动仿真: {simulation_id}")
        
        # 启动仿真
        result = integration_service.start_simulation_task(simulation_id, use_gui)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"启动仿真失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/integration/simulation-status/<simulation_id>', methods=['GET'])
def get_simulation_status(simulation_id):
    """获取仿真状态"""
    try:
        logger.info(f"获取仿真状态: {simulation_id}")
        
        # 获取仿真状态
        result = integration_service.get_simulation_status(simulation_id)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"获取仿真状态失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/integration/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "mongodb_connected": mongodb_client is not None,
            "services": {
                "java_backend": integration_service.java_backend_url,
                "sumo_api": integration_service.sumo_api_url
            }
        }
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    logger.info("启动数据集成服务...")
    app.run(host='0.0.0.0', port=5003, debug=True)
