{"ast": null, "code": "import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport simulationApi from '@/api/simulation';\nexport default {\n  name: 'SimulationControlPanel',\n  props: {\n    analysisTaskId: {\n      type: String,\n      required: true\n    },\n    trafficData: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['simulation-started', 'simulation-stopped', 'simulation-completed', 'status-updated'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const simulationId = ref('');\n    const simulationStatus = ref('idle'); // idle, running, completed, failed, stopped\n    const simulationProgress = ref(0);\n    const isStarting = ref(false);\n    const isRestarting = ref(false);\n    const errorMessage = ref('');\n\n    // 仿真配置\n    const simulationConfig = reactive({\n      simulationType: 'comprehensive',\n      duration: 3600,\n      useGui: false,\n      randomSeed: 42\n    });\n\n    // 实时监控数据\n    const currentSimulationTime = ref(0);\n    const currentVehicleCount = ref(0);\n    const averageSpeed = ref(0);\n    const totalVehicles = ref(0);\n    const throughput = ref(0);\n    const waitingTime = ref(0);\n    const improvementRate = ref(0);\n\n    // 状态轮询定时器\n    let statusTimer = null;\n\n    // 计算属性\n    const isSimulationRunning = computed(() => simulationStatus.value === 'running');\n    const canStartSimulation = computed(() => props.analysisTaskId && !isStarting.value && simulationStatus.value !== 'running');\n    const hasResults = computed(() => simulationStatus.value === 'completed' && simulationId.value);\n\n    // 方法\n    const getStatusTagType = status => {\n      const statusMap = {\n        'idle': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      };\n      return statusMap[status] || 'info';\n    };\n    const getStatusText = status => {\n      const statusMap = {\n        'idle': '待启动',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      };\n      return statusMap[status] || '未知';\n    };\n    const getProgressStatus = () => {\n      if (simulationStatus.value === 'failed') return 'exception';\n      if (simulationStatus.value === 'completed') return 'success';\n      return null;\n    };\n    const formatTime = seconds => {\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor(seconds % 3600 / 60);\n      const secs = seconds % 60;\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    };\n    const formatSpeed = speed => {\n      return `${speed.toFixed(1)} m/s`;\n    };\n    const toggleSimulation = async () => {\n      if (isSimulationRunning.value) {\n        await stopSimulation();\n      } else {\n        await startSimulation();\n      }\n    };\n    const startSimulation = async () => {\n      try {\n        isStarting.value = true;\n        errorMessage.value = '';\n\n        // 创建仿真任务\n        const createResponse = await simulationApi.createSimulation({\n          analysis_task_id: props.analysisTaskId,\n          simulation_type: simulationConfig.simulationType,\n          user_id: localStorage.getItem('userId'),\n          username: localStorage.getItem('username')\n        });\n        if (createResponse.status === 'success') {\n          simulationId.value = createResponse.simulation_task.simulationId;\n\n          // 启动仿真\n          const startResponse = await simulationApi.startSimulation(simulationId.value, {\n            use_gui: simulationConfig.useGui\n          });\n          if (startResponse.status === 'success') {\n            simulationStatus.value = 'running';\n            startStatusPolling();\n            emit('simulation-started', simulationId.value);\n            ElMessage.success('仿真启动成功');\n          } else {\n            throw new Error(startResponse.message || '启动仿真失败');\n          }\n        } else {\n          throw new Error(createResponse.message || '创建仿真任务失败');\n        }\n      } catch (error) {\n        console.error('启动仿真失败:', error);\n        errorMessage.value = error.message || '启动仿真失败';\n        ElMessage.error(errorMessage.value);\n      } finally {\n        isStarting.value = false;\n      }\n    };\n    const stopSimulation = async () => {\n      try {\n        const response = await simulationApi.stopSimulation(simulationId.value);\n        if (response.status === 'success') {\n          simulationStatus.value = 'stopped';\n          stopStatusPolling();\n          emit('simulation-stopped', simulationId.value);\n          ElMessage.success('仿真已停止');\n        } else {\n          throw new Error(response.message || '停止仿真失败');\n        }\n      } catch (error) {\n        console.error('停止仿真失败:', error);\n        ElMessage.error(error.message || '停止仿真失败');\n      }\n    };\n    const restartSimulation = async () => {\n      try {\n        isRestarting.value = true;\n        await ElMessageBox.confirm('确定要重启仿真吗？', '确认重启', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        await stopSimulation();\n        setTimeout(async () => {\n          await startSimulation();\n          isRestarting.value = false;\n        }, 2000);\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('重启仿真失败:', error);\n          ElMessage.error('重启仿真失败');\n        }\n        isRestarting.value = false;\n      }\n    };\n    const exportResults = async () => {\n      try {\n        const response = await simulationApi.exportResults(simulationId.value, 'json');\n        if (response.status === 'success') {\n          ElMessage.success('结果导出成功');\n        } else {\n          ElMessage.warning('导出功能暂未实现');\n        }\n      } catch (error) {\n        console.error('导出结果失败:', error);\n        ElMessage.error('导出结果失败');\n      }\n    };\n    const startStatusPolling = () => {\n      if (statusTimer) {\n        clearInterval(statusTimer);\n      }\n      statusTimer = setInterval(async () => {\n        try {\n          const response = await simulationApi.getSimulationStatus(simulationId.value);\n          if (response.status) {\n            simulationStatus.value = response.status;\n            simulationProgress.value = response.progress || 0;\n\n            // 更新实时数据\n            if (response.sumo_status) {\n              const sumoStatus = response.sumo_status;\n              currentSimulationTime.value = sumoStatus.current_time || 0;\n              currentVehicleCount.value = sumoStatus.vehicle_count || 0;\n              totalVehicles.value = sumoStatus.total_vehicles || 0;\n            }\n\n            // 检查仿真是否完成\n            if (simulationStatus.value === 'completed') {\n              stopStatusPolling();\n              emit('simulation-completed', simulationId.value);\n              ElMessage.success('仿真已完成');\n            } else if (simulationStatus.value === 'failed') {\n              stopStatusPolling();\n              errorMessage.value = response.error_message || '仿真执行失败';\n              ElMessage.error(errorMessage.value);\n            }\n            emit('status-updated', response);\n          }\n        } catch (error) {\n          console.error('获取仿真状态失败:', error);\n        }\n      }, 2000); // 每2秒轮询一次\n    };\n    const stopStatusPolling = () => {\n      if (statusTimer) {\n        clearInterval(statusTimer);\n        statusTimer = null;\n      }\n    };\n\n    // 生命周期\n    onMounted(() => {\n      // 组件挂载时的初始化逻辑\n    });\n    onUnmounted(() => {\n      stopStatusPolling();\n    });\n    return {\n      // 响应式数据\n      simulationId,\n      simulationStatus,\n      simulationProgress,\n      isStarting,\n      isRestarting,\n      errorMessage,\n      simulationConfig,\n      currentSimulationTime,\n      currentVehicleCount,\n      averageSpeed,\n      totalVehicles,\n      throughput,\n      waitingTime,\n      improvementRate,\n      // 计算属性\n      isSimulationRunning,\n      canStartSimulation,\n      hasResults,\n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getProgressStatus,\n      formatTime,\n      formatSpeed,\n      toggleSimulation,\n      restartSimulation,\n      exportResults\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "ElMessage", "ElMessageBox", "simulationApi", "name", "props", "analysisTaskId", "type", "String", "required", "trafficData", "Object", "default", "emits", "setup", "emit", "simulationId", "simulationStatus", "simulationProgress", "isStarting", "isRestarting", "errorMessage", "simulationConfig", "simulationType", "duration", "useGui", "randomSeed", "currentSimulationTime", "currentVehicleCount", "averageSpeed", "totalVehicles", "throughput", "waitingTime", "improvementRate", "statusTimer", "isSimulationRunning", "value", "canStartSimulation", "hasResults", "getStatusTagType", "status", "statusMap", "getStatusText", "getProgressStatus", "formatTime", "seconds", "hours", "Math", "floor", "minutes", "secs", "toString", "padStart", "formatSpeed", "speed", "toFixed", "toggleSimulation", "stopSimulation", "startSimulation", "createResponse", "createSimulation", "analysis_task_id", "simulation_type", "user_id", "localStorage", "getItem", "username", "simulation_task", "startResponse", "use_gui", "startStatusPolling", "success", "Error", "message", "error", "console", "response", "stopStatusPolling", "restartSimulation", "confirm", "confirmButtonText", "cancelButtonText", "setTimeout", "exportResults", "warning", "clearInterval", "setInterval", "getSimulationStatus", "progress", "sumo_status", "sumoStatus", "current_time", "vehicle_count", "total_vehicles", "error_message"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\SimulationControlPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"simulation-control-panel\">\n    <el-card class=\"control-card\" shadow=\"hover\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3><i class=\"el-icon-setting\"></i> 仿真控制面板</h3>\n          <el-tag :type=\"getStatusTagType(simulationStatus)\" size=\"large\">\n            {{ getStatusText(simulationStatus) }}\n          </el-tag>\n        </div>\n      </template>\n\n      <!-- 仿真配置区域 -->\n      <div class=\"config-section\" v-if=\"!isSimulationRunning\">\n        <h4><i class=\"el-icon-tools\"></i> 仿真配置</h4>\n        <el-form :model=\"simulationConfig\" label-width=\"120px\" size=\"default\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"仿真类型\">\n                <el-select v-model=\"simulationConfig.simulationType\" placeholder=\"选择仿真类型\">\n                  <el-option label=\"信号灯配时优化\" value=\"signal_timing\"></el-option>\n                  <el-option label=\"流量平衡优化\" value=\"flow_balance\"></el-option>\n                  <el-option label=\"综合优化分析\" value=\"comprehensive\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"仿真时长\">\n                <el-input-number \n                  v-model=\"simulationConfig.duration\" \n                  :min=\"300\" \n                  :max=\"7200\" \n                  :step=\"300\"\n                  controls-position=\"right\">\n                </el-input-number>\n                <span class=\"unit-text\">秒</span>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          \n          <el-row :gutter=\"20\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"使用GUI\">\n                <el-switch v-model=\"simulationConfig.useGui\"></el-switch>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"随机种子\">\n                <el-input-number \n                  v-model=\"simulationConfig.randomSeed\" \n                  :min=\"1\" \n                  :max=\"9999\"\n                  controls-position=\"right\">\n                </el-input-number>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <!-- 控制按钮区域 -->\n      <div class=\"control-buttons\">\n        <el-button-group>\n          <el-button \n            type=\"primary\" \n            :icon=\"isSimulationRunning ? 'el-icon-video-pause' : 'el-icon-video-play'\"\n            :loading=\"isStarting\"\n            @click=\"toggleSimulation\"\n            :disabled=\"!canStartSimulation\">\n            {{ isSimulationRunning ? '停止仿真' : '启动仿真' }}\n          </el-button>\n          \n          <el-button \n            type=\"warning\" \n            icon=\"el-icon-refresh\"\n            :loading=\"isRestarting\"\n            @click=\"restartSimulation\"\n            :disabled=\"!simulationId || isStarting\">\n            重启仿真\n          </el-button>\n          \n          <el-button \n            type=\"info\" \n            icon=\"el-icon-download\"\n            @click=\"exportResults\"\n            :disabled=\"!hasResults\">\n            导出结果\n          </el-button>\n        </el-button-group>\n      </div>\n\n      <!-- 仿真进度区域 -->\n      <div class=\"progress-section\" v-if=\"simulationId\">\n        <h4><i class=\"el-icon-time\"></i> 仿真进度</h4>\n        <el-progress \n          :percentage=\"simulationProgress\" \n          :status=\"getProgressStatus()\"\n          :stroke-width=\"8\">\n        </el-progress>\n        \n        <div class=\"progress-info\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">仿真时间:</span>\n                <span class=\"value\">{{ formatTime(currentSimulationTime) }}</span>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">车辆数量:</span>\n                <span class=\"value\">{{ currentVehicleCount }}</span>\n              </div>\n            </el-col>\n            <el-col :span=\"8\">\n              <div class=\"info-item\">\n                <span class=\"label\">平均速度:</span>\n                <span class=\"value\">{{ formatSpeed(averageSpeed) }}</span>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n      </div>\n\n      <!-- 实时状态监控 -->\n      <div class=\"status-monitor\" v-if=\"isSimulationRunning\">\n        <h4><i class=\"el-icon-monitor\"></i> 实时监控</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <el-statistic title=\"总车辆数\" :value=\"totalVehicles\" suffix=\"辆\">\n              <template #prefix>\n                <i class=\"el-icon-truck\" style=\"color: #409eff\"></i>\n              </template>\n            </el-statistic>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"通行能力\" :value=\"throughput\" suffix=\"辆/小时\">\n              <template #prefix>\n                <i class=\"el-icon-right\" style=\"color: #67c23a\"></i>\n              </template>\n            </el-statistic>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"等待时间\" :value=\"waitingTime\" suffix=\"秒\">\n              <template #prefix>\n                <i class=\"el-icon-timer\" style=\"color: #e6a23c\"></i>\n              </template>\n            </el-statistic>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"改善效果\" :value=\"improvementRate\" suffix=\"%\">\n              <template #prefix>\n                <i class=\"el-icon-trend-charts\" style=\"color: #f56c6c\"></i>\n              </template>\n            </el-statistic>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 错误信息显示 -->\n      <div class=\"error-section\" v-if=\"errorMessage\">\n        <el-alert\n          :title=\"errorMessage\"\n          type=\"error\"\n          :closable=\"false\"\n          show-icon>\n        </el-alert>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport simulationApi from '@/api/simulation'\n\nexport default {\n  name: 'SimulationControlPanel',\n  props: {\n    analysisTaskId: {\n      type: String,\n      required: true\n    },\n    trafficData: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['simulation-started', 'simulation-stopped', 'simulation-completed', 'status-updated'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const simulationId = ref('')\n    const simulationStatus = ref('idle') // idle, running, completed, failed, stopped\n    const simulationProgress = ref(0)\n    const isStarting = ref(false)\n    const isRestarting = ref(false)\n    const errorMessage = ref('')\n    \n    // 仿真配置\n    const simulationConfig = reactive({\n      simulationType: 'comprehensive',\n      duration: 3600,\n      useGui: false,\n      randomSeed: 42\n    })\n    \n    // 实时监控数据\n    const currentSimulationTime = ref(0)\n    const currentVehicleCount = ref(0)\n    const averageSpeed = ref(0)\n    const totalVehicles = ref(0)\n    const throughput = ref(0)\n    const waitingTime = ref(0)\n    const improvementRate = ref(0)\n    \n    // 状态轮询定时器\n    let statusTimer = null\n    \n    // 计算属性\n    const isSimulationRunning = computed(() => simulationStatus.value === 'running')\n    const canStartSimulation = computed(() => \n      props.analysisTaskId && !isStarting.value && simulationStatus.value !== 'running'\n    )\n    const hasResults = computed(() => \n      simulationStatus.value === 'completed' && simulationId.value\n    )\n    \n    // 方法\n    const getStatusTagType = (status) => {\n      const statusMap = {\n        'idle': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      }\n      return statusMap[status] || 'info'\n    }\n    \n    const getStatusText = (status) => {\n      const statusMap = {\n        'idle': '待启动',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      }\n      return statusMap[status] || '未知'\n    }\n    \n    const getProgressStatus = () => {\n      if (simulationStatus.value === 'failed') return 'exception'\n      if (simulationStatus.value === 'completed') return 'success'\n      return null\n    }\n    \n    const formatTime = (seconds) => {\n      const hours = Math.floor(seconds / 3600)\n      const minutes = Math.floor((seconds % 3600) / 60)\n      const secs = seconds % 60\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n    }\n    \n    const formatSpeed = (speed) => {\n      return `${speed.toFixed(1)} m/s`\n    }\n    \n    const toggleSimulation = async () => {\n      if (isSimulationRunning.value) {\n        await stopSimulation()\n      } else {\n        await startSimulation()\n      }\n    }\n    \n    const startSimulation = async () => {\n      try {\n        isStarting.value = true\n        errorMessage.value = ''\n        \n        // 创建仿真任务\n        const createResponse = await simulationApi.createSimulation({\n          analysis_task_id: props.analysisTaskId,\n          simulation_type: simulationConfig.simulationType,\n          user_id: localStorage.getItem('userId'),\n          username: localStorage.getItem('username')\n        })\n        \n        if (createResponse.status === 'success') {\n          simulationId.value = createResponse.simulation_task.simulationId\n          \n          // 启动仿真\n          const startResponse = await simulationApi.startSimulation(simulationId.value, {\n            use_gui: simulationConfig.useGui\n          })\n          \n          if (startResponse.status === 'success') {\n            simulationStatus.value = 'running'\n            startStatusPolling()\n            emit('simulation-started', simulationId.value)\n            ElMessage.success('仿真启动成功')\n          } else {\n            throw new Error(startResponse.message || '启动仿真失败')\n          }\n        } else {\n          throw new Error(createResponse.message || '创建仿真任务失败')\n        }\n        \n      } catch (error) {\n        console.error('启动仿真失败:', error)\n        errorMessage.value = error.message || '启动仿真失败'\n        ElMessage.error(errorMessage.value)\n      } finally {\n        isStarting.value = false\n      }\n    }\n    \n    const stopSimulation = async () => {\n      try {\n        const response = await simulationApi.stopSimulation(simulationId.value)\n        \n        if (response.status === 'success') {\n          simulationStatus.value = 'stopped'\n          stopStatusPolling()\n          emit('simulation-stopped', simulationId.value)\n          ElMessage.success('仿真已停止')\n        } else {\n          throw new Error(response.message || '停止仿真失败')\n        }\n        \n      } catch (error) {\n        console.error('停止仿真失败:', error)\n        ElMessage.error(error.message || '停止仿真失败')\n      }\n    }\n    \n    const restartSimulation = async () => {\n      try {\n        isRestarting.value = true\n        \n        await ElMessageBox.confirm('确定要重启仿真吗？', '确认重启', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        await stopSimulation()\n        setTimeout(async () => {\n          await startSimulation()\n          isRestarting.value = false\n        }, 2000)\n        \n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('重启仿真失败:', error)\n          ElMessage.error('重启仿真失败')\n        }\n        isRestarting.value = false\n      }\n    }\n    \n    const exportResults = async () => {\n      try {\n        const response = await simulationApi.exportResults(simulationId.value, 'json')\n        \n        if (response.status === 'success') {\n          ElMessage.success('结果导出成功')\n        } else {\n          ElMessage.warning('导出功能暂未实现')\n        }\n        \n      } catch (error) {\n        console.error('导出结果失败:', error)\n        ElMessage.error('导出结果失败')\n      }\n    }\n    \n    const startStatusPolling = () => {\n      if (statusTimer) {\n        clearInterval(statusTimer)\n      }\n      \n      statusTimer = setInterval(async () => {\n        try {\n          const response = await simulationApi.getSimulationStatus(simulationId.value)\n          \n          if (response.status) {\n            simulationStatus.value = response.status\n            simulationProgress.value = response.progress || 0\n            \n            // 更新实时数据\n            if (response.sumo_status) {\n              const sumoStatus = response.sumo_status\n              currentSimulationTime.value = sumoStatus.current_time || 0\n              currentVehicleCount.value = sumoStatus.vehicle_count || 0\n              totalVehicles.value = sumoStatus.total_vehicles || 0\n            }\n            \n            // 检查仿真是否完成\n            if (simulationStatus.value === 'completed') {\n              stopStatusPolling()\n              emit('simulation-completed', simulationId.value)\n              ElMessage.success('仿真已完成')\n            } else if (simulationStatus.value === 'failed') {\n              stopStatusPolling()\n              errorMessage.value = response.error_message || '仿真执行失败'\n              ElMessage.error(errorMessage.value)\n            }\n            \n            emit('status-updated', response)\n          }\n          \n        } catch (error) {\n          console.error('获取仿真状态失败:', error)\n        }\n      }, 2000) // 每2秒轮询一次\n    }\n    \n    const stopStatusPolling = () => {\n      if (statusTimer) {\n        clearInterval(statusTimer)\n        statusTimer = null\n      }\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      // 组件挂载时的初始化逻辑\n    })\n    \n    onUnmounted(() => {\n      stopStatusPolling()\n    })\n    \n    return {\n      // 响应式数据\n      simulationId,\n      simulationStatus,\n      simulationProgress,\n      isStarting,\n      isRestarting,\n      errorMessage,\n      simulationConfig,\n      currentSimulationTime,\n      currentVehicleCount,\n      averageSpeed,\n      totalVehicles,\n      throughput,\n      waitingTime,\n      improvementRate,\n      \n      // 计算属性\n      isSimulationRunning,\n      canStartSimulation,\n      hasResults,\n      \n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getProgressStatus,\n      formatTime,\n      formatSpeed,\n      toggleSimulation,\n      restartSimulation,\n      exportResults\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simulation-control-panel {\n  margin-bottom: 20px;\n}\n\n.control-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  color: #303133;\n}\n\n.config-section,\n.progress-section,\n.status-monitor {\n  margin-bottom: 20px;\n}\n\n.config-section h4,\n.progress-section h4,\n.status-monitor h4 {\n  margin-bottom: 15px;\n  color: #606266;\n  font-size: 16px;\n}\n\n.control-buttons {\n  text-align: center;\n  margin: 20px 0;\n}\n\n.unit-text {\n  margin-left: 8px;\n  color: #909399;\n}\n\n.progress-info {\n  margin-top: 15px;\n}\n\n.info-item {\n  text-align: center;\n}\n\n.info-item .label {\n  display: block;\n  color: #909399;\n  font-size: 12px;\n  margin-bottom: 4px;\n}\n\n.info-item .value {\n  display: block;\n  color: #303133;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.error-section {\n  margin-top: 15px;\n}\n\n.el-statistic {\n  text-align: center;\n}\n</style>\n"], "mappings": "AA6KA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AACpE,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,OAAOC,aAAY,MAAO,kBAAiB;AAE3C,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,KAAK,EAAE;IACLC,cAAc,EAAE;MACdC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,WAAW,EAAE;MACXH,IAAI,EAAEI,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC;EAC7FC,KAAKA,CAACT,KAAK,EAAE;IAAEU;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,YAAW,GAAIpB,GAAG,CAAC,EAAE;IAC3B,MAAMqB,gBAAe,GAAIrB,GAAG,CAAC,MAAM,GAAE;IACrC,MAAMsB,kBAAiB,GAAItB,GAAG,CAAC,CAAC;IAChC,MAAMuB,UAAS,GAAIvB,GAAG,CAAC,KAAK;IAC5B,MAAMwB,YAAW,GAAIxB,GAAG,CAAC,KAAK;IAC9B,MAAMyB,YAAW,GAAIzB,GAAG,CAAC,EAAE;;IAE3B;IACA,MAAM0B,gBAAe,GAAIzB,QAAQ,CAAC;MAChC0B,cAAc,EAAE,eAAe;MAC/BC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,KAAK;MACbC,UAAU,EAAE;IACd,CAAC;;IAED;IACA,MAAMC,qBAAoB,GAAI/B,GAAG,CAAC,CAAC;IACnC,MAAMgC,mBAAkB,GAAIhC,GAAG,CAAC,CAAC;IACjC,MAAMiC,YAAW,GAAIjC,GAAG,CAAC,CAAC;IAC1B,MAAMkC,aAAY,GAAIlC,GAAG,CAAC,CAAC;IAC3B,MAAMmC,UAAS,GAAInC,GAAG,CAAC,CAAC;IACxB,MAAMoC,WAAU,GAAIpC,GAAG,CAAC,CAAC;IACzB,MAAMqC,eAAc,GAAIrC,GAAG,CAAC,CAAC;;IAE7B;IACA,IAAIsC,WAAU,GAAI,IAAG;;IAErB;IACA,MAAMC,mBAAkB,GAAIrC,QAAQ,CAAC,MAAMmB,gBAAgB,CAACmB,KAAI,KAAM,SAAS;IAC/E,MAAMC,kBAAiB,GAAIvC,QAAQ,CAAC,MAClCO,KAAK,CAACC,cAAa,IAAK,CAACa,UAAU,CAACiB,KAAI,IAAKnB,gBAAgB,CAACmB,KAAI,KAAM,SAC1E;IACA,MAAME,UAAS,GAAIxC,QAAQ,CAAC,MAC1BmB,gBAAgB,CAACmB,KAAI,KAAM,WAAU,IAAKpB,YAAY,CAACoB,KACzD;;IAEA;IACA,MAAMG,gBAAe,GAAKC,MAAM,IAAK;MACnC,MAAMC,SAAQ,GAAI;QAChB,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,SAAS;QACpB,WAAW,EAAE,SAAS;QACtB,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE;MACb;MACA,OAAOA,SAAS,CAACD,MAAM,KAAK,MAAK;IACnC;IAEA,MAAME,aAAY,GAAKF,MAAM,IAAK;MAChC,MAAMC,SAAQ,GAAI;QAChB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,KAAK;QAChB,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE;MACb;MACA,OAAOA,SAAS,CAACD,MAAM,KAAK,IAAG;IACjC;IAEA,MAAMG,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,IAAI1B,gBAAgB,CAACmB,KAAI,KAAM,QAAQ,EAAE,OAAO,WAAU;MAC1D,IAAInB,gBAAgB,CAACmB,KAAI,KAAM,WAAW,EAAE,OAAO,SAAQ;MAC3D,OAAO,IAAG;IACZ;IAEA,MAAMQ,UAAS,GAAKC,OAAO,IAAK;MAC9B,MAAMC,KAAI,GAAIC,IAAI,CAACC,KAAK,CAACH,OAAM,GAAI,IAAI;MACvC,MAAMI,OAAM,GAAIF,IAAI,CAACC,KAAK,CAAEH,OAAM,GAAI,IAAI,GAAI,EAAE;MAChD,MAAMK,IAAG,GAAIL,OAAM,GAAI,EAAC;MACxB,OAAO,GAAGC,KAAK,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IACzH;IAEA,MAAMC,WAAU,GAAKC,KAAK,IAAK;MAC7B,OAAO,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,MAAK;IACjC;IAEA,MAAMC,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAIrB,mBAAmB,CAACC,KAAK,EAAE;QAC7B,MAAMqB,cAAc,CAAC;MACvB,OAAO;QACL,MAAMC,eAAe,CAAC;MACxB;IACF;IAEA,MAAMA,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFvC,UAAU,CAACiB,KAAI,GAAI,IAAG;QACtBf,YAAY,CAACe,KAAI,GAAI,EAAC;;QAEtB;QACA,MAAMuB,cAAa,GAAI,MAAMxD,aAAa,CAACyD,gBAAgB,CAAC;UAC1DC,gBAAgB,EAAExD,KAAK,CAACC,cAAc;UACtCwD,eAAe,EAAExC,gBAAgB,CAACC,cAAc;UAChDwC,OAAO,EAAEC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;UACvCC,QAAQ,EAAEF,YAAY,CAACC,OAAO,CAAC,UAAU;QAC3C,CAAC;QAED,IAAIN,cAAc,CAACnB,MAAK,KAAM,SAAS,EAAE;UACvCxB,YAAY,CAACoB,KAAI,GAAIuB,cAAc,CAACQ,eAAe,CAACnD,YAAW;;UAE/D;UACA,MAAMoD,aAAY,GAAI,MAAMjE,aAAa,CAACuD,eAAe,CAAC1C,YAAY,CAACoB,KAAK,EAAE;YAC5EiC,OAAO,EAAE/C,gBAAgB,CAACG;UAC5B,CAAC;UAED,IAAI2C,aAAa,CAAC5B,MAAK,KAAM,SAAS,EAAE;YACtCvB,gBAAgB,CAACmB,KAAI,GAAI,SAAQ;YACjCkC,kBAAkB,CAAC;YACnBvD,IAAI,CAAC,oBAAoB,EAAEC,YAAY,CAACoB,KAAK;YAC7CnC,SAAS,CAACsE,OAAO,CAAC,QAAQ;UAC5B,OAAO;YACL,MAAM,IAAIC,KAAK,CAACJ,aAAa,CAACK,OAAM,IAAK,QAAQ;UACnD;QACF,OAAO;UACL,MAAM,IAAID,KAAK,CAACb,cAAc,CAACc,OAAM,IAAK,UAAU;QACtD;MAEF,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BrD,YAAY,CAACe,KAAI,GAAIsC,KAAK,CAACD,OAAM,IAAK,QAAO;QAC7CxE,SAAS,CAACyE,KAAK,CAACrD,YAAY,CAACe,KAAK;MACpC,UAAU;QACRjB,UAAU,CAACiB,KAAI,GAAI,KAAI;MACzB;IACF;IAEA,MAAMqB,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF,MAAMmB,QAAO,GAAI,MAAMzE,aAAa,CAACsD,cAAc,CAACzC,YAAY,CAACoB,KAAK;QAEtE,IAAIwC,QAAQ,CAACpC,MAAK,KAAM,SAAS,EAAE;UACjCvB,gBAAgB,CAACmB,KAAI,GAAI,SAAQ;UACjCyC,iBAAiB,CAAC;UAClB9D,IAAI,CAAC,oBAAoB,EAAEC,YAAY,CAACoB,KAAK;UAC7CnC,SAAS,CAACsE,OAAO,CAAC,OAAO;QAC3B,OAAO;UACL,MAAM,IAAIC,KAAK,CAACI,QAAQ,CAACH,OAAM,IAAK,QAAQ;QAC9C;MAEF,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BzE,SAAS,CAACyE,KAAK,CAACA,KAAK,CAACD,OAAM,IAAK,QAAQ;MAC3C;IACF;IAEA,MAAMK,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF1D,YAAY,CAACgB,KAAI,GAAI,IAAG;QAExB,MAAMlC,YAAY,CAAC6E,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE;UAC9CC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtB1E,IAAI,EAAE;QACR,CAAC;QAED,MAAMkD,cAAc,CAAC;QACrByB,UAAU,CAAC,YAAY;UACrB,MAAMxB,eAAe,CAAC;UACtBtC,YAAY,CAACgB,KAAI,GAAI,KAAI;QAC3B,CAAC,EAAE,IAAI;MAET,EAAE,OAAOsC,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BzE,SAAS,CAACyE,KAAK,CAAC,QAAQ;QAC1B;QACAtD,YAAY,CAACgB,KAAI,GAAI,KAAI;MAC3B;IACF;IAEA,MAAM+C,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMP,QAAO,GAAI,MAAMzE,aAAa,CAACgF,aAAa,CAACnE,YAAY,CAACoB,KAAK,EAAE,MAAM;QAE7E,IAAIwC,QAAQ,CAACpC,MAAK,KAAM,SAAS,EAAE;UACjCvC,SAAS,CAACsE,OAAO,CAAC,QAAQ;QAC5B,OAAO;UACLtE,SAAS,CAACmF,OAAO,CAAC,UAAU;QAC9B;MAEF,EAAE,OAAOV,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BzE,SAAS,CAACyE,KAAK,CAAC,QAAQ;MAC1B;IACF;IAEA,MAAMJ,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,IAAIpC,WAAW,EAAE;QACfmD,aAAa,CAACnD,WAAW;MAC3B;MAEAA,WAAU,GAAIoD,WAAW,CAAC,YAAY;QACpC,IAAI;UACF,MAAMV,QAAO,GAAI,MAAMzE,aAAa,CAACoF,mBAAmB,CAACvE,YAAY,CAACoB,KAAK;UAE3E,IAAIwC,QAAQ,CAACpC,MAAM,EAAE;YACnBvB,gBAAgB,CAACmB,KAAI,GAAIwC,QAAQ,CAACpC,MAAK;YACvCtB,kBAAkB,CAACkB,KAAI,GAAIwC,QAAQ,CAACY,QAAO,IAAK;;YAEhD;YACA,IAAIZ,QAAQ,CAACa,WAAW,EAAE;cACxB,MAAMC,UAAS,GAAId,QAAQ,CAACa,WAAU;cACtC9D,qBAAqB,CAACS,KAAI,GAAIsD,UAAU,CAACC,YAAW,IAAK;cACzD/D,mBAAmB,CAACQ,KAAI,GAAIsD,UAAU,CAACE,aAAY,IAAK;cACxD9D,aAAa,CAACM,KAAI,GAAIsD,UAAU,CAACG,cAAa,IAAK;YACrD;;YAEA;YACA,IAAI5E,gBAAgB,CAACmB,KAAI,KAAM,WAAW,EAAE;cAC1CyC,iBAAiB,CAAC;cAClB9D,IAAI,CAAC,sBAAsB,EAAEC,YAAY,CAACoB,KAAK;cAC/CnC,SAAS,CAACsE,OAAO,CAAC,OAAO;YAC3B,OAAO,IAAItD,gBAAgB,CAACmB,KAAI,KAAM,QAAQ,EAAE;cAC9CyC,iBAAiB,CAAC;cAClBxD,YAAY,CAACe,KAAI,GAAIwC,QAAQ,CAACkB,aAAY,IAAK,QAAO;cACtD7F,SAAS,CAACyE,KAAK,CAACrD,YAAY,CAACe,KAAK;YACpC;YAEArB,IAAI,CAAC,gBAAgB,EAAE6D,QAAQ;UACjC;QAEF,EAAE,OAAOF,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAClC;MACF,CAAC,EAAE,IAAI,GAAE;IACX;IAEA,MAAMG,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,IAAI3C,WAAW,EAAE;QACfmD,aAAa,CAACnD,WAAW;QACzBA,WAAU,GAAI,IAAG;MACnB;IACF;;IAEA;IACAnC,SAAS,CAAC,MAAM;MACd;IAAA,CACD;IAEDC,WAAW,CAAC,MAAM;MAChB6E,iBAAiB,CAAC;IACpB,CAAC;IAED,OAAO;MACL;MACA7D,YAAY;MACZC,gBAAgB;MAChBC,kBAAkB;MAClBC,UAAU;MACVC,YAAY;MACZC,YAAY;MACZC,gBAAgB;MAChBK,qBAAqB;MACrBC,mBAAmB;MACnBC,YAAY;MACZC,aAAa;MACbC,UAAU;MACVC,WAAW;MACXC,eAAe;MAEf;MACAE,mBAAmB;MACnBE,kBAAkB;MAClBC,UAAU;MAEV;MACAC,gBAAgB;MAChBG,aAAa;MACbC,iBAAiB;MACjBC,UAAU;MACVS,WAAW;MACXG,gBAAgB;MAChBsB,iBAAiB;MACjBK;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}