package com.traffic.analysis.service.impl;

import com.traffic.analysis.model.SimulationTask;
import com.traffic.analysis.model.OptimizationResult;
import com.traffic.analysis.repository.SimulationTaskRepository;
import com.traffic.analysis.repository.OptimizationResultRepository;
import com.traffic.analysis.service.SimulationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仿真服务实现类
 */
@Slf4j
@Service
public class SimulationServiceImpl implements SimulationService {
    
    @Autowired
    private SimulationTaskRepository simulationTaskRepository;
    
    @Autowired
    private OptimizationResultRepository optimizationResultRepository;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${sumo.api.url:http://localhost:5002}")
    private String sumoApiUrl;
    
    @Override
    public SimulationTask createSimulationTask(String userId, String username, String taskName, 
                                             String simulationType, Map<String, Object> trafficData, 
                                             Map<String, Object> simulationConfig) {
        try {
            log.info("创建仿真任务: userId={}, type={}, name={}", userId, simulationType, taskName);
            
            // 创建仿真任务对象
            SimulationTask task = new SimulationTask();
            task.setSimulationId(UUID.randomUUID().toString());
            task.setUserId(userId);
            task.setUsername(username);
            task.setTaskName(taskName);
            task.setSimulationType(simulationType);
            task.setTrafficData(trafficData);
            task.setSimulationConfig(simulationConfig);
            
            // 设置默认仿真参数
            if (simulationConfig == null || simulationConfig.isEmpty()) {
                simulationConfig = createDefaultSimulationConfig();
                task.setSimulationConfig(simulationConfig);
            }
            
            // 保存到数据库
            SimulationTask savedTask = simulationTaskRepository.save(task);
            
            log.info("仿真任务创建成功: simulationId={}", savedTask.getSimulationId());
            return savedTask;
            
        } catch (Exception e) {
            log.error("创建仿真任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建仿真任务失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> startSimulation(String simulationId, boolean useGui) {
        try {
            log.info("启动仿真: simulationId={}, useGui={}", simulationId, useGui);
            
            // 获取仿真任务
            Optional<SimulationTask> taskOpt = simulationTaskRepository.findBySimulationId(simulationId);
            if (!taskOpt.isPresent()) {
                return createErrorResponse("仿真任务不存在: " + simulationId);
            }
            
            SimulationTask task = taskOpt.get();
            
            // 检查任务状态
            if (task.isRunning()) {
                return createErrorResponse("仿真任务已在运行中");
            }
            
            // 调用SUMO API创建仿真
            Map<String, Object> createRequest = new HashMap<>();
            createRequest.put("traffic_data", task.getTrafficData());
            createRequest.put("simulation_config", task.getSimulationConfig());
            
            ResponseEntity<Map> createResponse = callSumoApi("/api/sumo/simulation/create", createRequest);
            if (!createResponse.getStatusCode().is2xxSuccessful()) {
                return createErrorResponse("创建SUMO仿真失败");
            }
            
            // 启动仿真
            Map<String, Object> startRequest = new HashMap<>();
            startRequest.put("use_gui", useGui);
            
            ResponseEntity<Map> startResponse = callSumoApi("/api/sumo/simulation/" + simulationId + "/start", startRequest);
            if (!startResponse.getStatusCode().is2xxSuccessful()) {
                return createErrorResponse("启动SUMO仿真失败");
            }
            
            // 更新任务状态
            task.updateStatus("running");
            task.updateProgress(0);
            simulationTaskRepository.save(task);
            
            Map<String, Object> result = new HashMap<>();
            result.put("status", "success");
            result.put("message", "仿真启动成功");
            result.put("simulation_id", simulationId);
            result.put("timestamp", LocalDateTime.now());
            
            return result;
            
        } catch (Exception e) {
            log.error("启动仿真失败: {}", e.getMessage(), e);
            return createErrorResponse("启动仿真失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> stopSimulation(String simulationId) {
        try {
            log.info("停止仿真: simulationId={}", simulationId);
            
            // 获取仿真任务
            Optional<SimulationTask> taskOpt = simulationTaskRepository.findBySimulationId(simulationId);
            if (!taskOpt.isPresent()) {
                return createErrorResponse("仿真任务不存在: " + simulationId);
            }
            
            SimulationTask task = taskOpt.get();
            
            // 调用SUMO API停止仿真
            ResponseEntity<Map> response = callSumoApi("/api/sumo/simulation/stop", new HashMap<>());
            
            // 更新任务状态
            task.updateStatus("stopped");
            simulationTaskRepository.save(task);
            
            Map<String, Object> result = new HashMap<>();
            result.put("status", "success");
            result.put("message", "仿真停止成功");
            result.put("simulation_id", simulationId);
            result.put("timestamp", LocalDateTime.now());
            
            return result;
            
        } catch (Exception e) {
            log.error("停止仿真失败: {}", e.getMessage(), e);
            return createErrorResponse("停止仿真失败: " + e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> getSimulationStatus(String simulationId) {
        try {
            // 获取仿真任务
            Optional<SimulationTask> taskOpt = simulationTaskRepository.findBySimulationId(simulationId);
            if (!taskOpt.isPresent()) {
                return createErrorResponse("仿真任务不存在: " + simulationId);
            }
            
            SimulationTask task = taskOpt.get();
            
            Map<String, Object> status = new HashMap<>();
            status.put("simulation_id", simulationId);
            status.put("status", task.getStatus());
            status.put("progress", task.getProgress());
            status.put("start_time", task.getStartTime());
            status.put("end_time", task.getEndTime());
            status.put("duration", task.getDuration());
            status.put("error_message", task.getErrorMessage());
            
            // 如果正在运行，尝试获取SUMO实时状态
            if (task.isRunning()) {
                try {
                    ResponseEntity<Map> sumoStatus = callSumoApiGet("/api/sumo/status");
                    if (sumoStatus.getStatusCode().is2xxSuccessful()) {
                        Map<String, Object> sumoData = sumoStatus.getBody();
                        status.put("sumo_status", sumoData);
                    }
                } catch (Exception e) {
                    log.warn("获取SUMO状态失败: {}", e.getMessage());
                }
            }
            
            return status;
            
        } catch (Exception e) {
            log.error("获取仿真状态失败: {}", e.getMessage(), e);
            return createErrorResponse("获取仿真状态失败: " + e.getMessage());
        }
    }
    
    @Override
    public void updateSimulationProgress(String simulationId, Integer progress, String status, String message) {
        try {
            Optional<SimulationTask> taskOpt = simulationTaskRepository.findBySimulationId(simulationId);
            if (taskOpt.isPresent()) {
                SimulationTask task = taskOpt.get();
                task.updateProgress(progress);
                if (status != null) {
                    task.updateStatus(status);
                }
                simulationTaskRepository.save(task);
                
                log.debug("更新仿真进度: simulationId={}, progress={}, status={}", simulationId, progress, status);
            }
        } catch (Exception e) {
            log.error("更新仿真进度失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void completeSimulation(String simulationId, Map<String, Object> simulationResults) {
        try {
            Optional<SimulationTask> taskOpt = simulationTaskRepository.findBySimulationId(simulationId);
            if (taskOpt.isPresent()) {
                SimulationTask task = taskOpt.get();
                task.updateStatus("completed");
                task.updateProgress(100);
                task.setSimulationResults(simulationResults);
                
                // 提取性能指标
                if (simulationResults.containsKey("performance_metrics")) {
                    Map<String, Object> metrics = (Map<String, Object>) simulationResults.get("performance_metrics");
                    updatePerformanceMetrics(task, metrics);
                }
                
                simulationTaskRepository.save(task);
                
                log.info("仿真任务完成: simulationId={}", simulationId);
            }
        } catch (Exception e) {
            log.error("完成仿真任务失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public void failSimulation(String simulationId, String errorMessage) {
        try {
            Optional<SimulationTask> taskOpt = simulationTaskRepository.findBySimulationId(simulationId);
            if (taskOpt.isPresent()) {
                SimulationTask task = taskOpt.get();
                task.setError(errorMessage);
                simulationTaskRepository.save(task);
                
                log.warn("仿真任务失败: simulationId={}, error={}", simulationId, errorMessage);
            }
        } catch (Exception e) {
            log.error("处理仿真失败失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public List<SimulationTask> getUserSimulationTasks(String userId) {
        try {
            return simulationTaskRepository.findByUserIdOrderByCreateTimeDesc(userId);
        } catch (Exception e) {
            log.error("获取用户仿真任务失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public Optional<SimulationTask> getSimulationTask(String simulationId) {
        try {
            return simulationTaskRepository.findBySimulationId(simulationId);
        } catch (Exception e) {
            log.error("获取仿真任务失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }
    
    @Override
    public Map<String, Object> deleteSimulationTask(String simulationId, String userId) {
        try {
            Optional<SimulationTask> taskOpt = simulationTaskRepository.findBySimulationId(simulationId);
            if (!taskOpt.isPresent()) {
                return createErrorResponse("仿真任务不存在");
            }
            
            SimulationTask task = taskOpt.get();
            
            // 检查权限
            if (!task.getUserId().equals(userId)) {
                return createErrorResponse("无权限删除此仿真任务");
            }
            
            // 如果正在运行，先停止
            if (task.isRunning()) {
                stopSimulation(simulationId);
            }
            
            // 删除相关的优化结果
            optimizationResultRepository.findBySimulationTaskId(task.getId())
                    .ifPresent(result -> optimizationResultRepository.delete(result));
            
            // 删除仿真任务
            simulationTaskRepository.delete(task);
            
            Map<String, Object> result = new HashMap<>();
            result.put("status", "success");
            result.put("message", "仿真任务删除成功");
            
            return result;
            
        } catch (Exception e) {
            log.error("删除仿真任务失败: {}", e.getMessage(), e);
            return createErrorResponse("删除仿真任务失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> optimizeSignalTiming(Map<String, Object> trafficData, Map<String, Object> currentTiming) {
        try {
            log.info("执行信号灯配时优化");

            Map<String, Object> request = new HashMap<>();
            request.put("traffic_data", trafficData);
            request.put("current_timing", currentTiming);

            ResponseEntity<Map> response = callSumoApi("/api/sumo/optimization/signal", request);

            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            } else {
                return createErrorResponse("信号灯配时优化失败");
            }

        } catch (Exception e) {
            log.error("信号灯配时优化失败: {}", e.getMessage(), e);
            return createErrorResponse("信号灯配时优化失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> optimizeFlowBalance(Map<String, Object> trafficData) {
        try {
            log.info("执行流量平衡优化");

            Map<String, Object> request = new HashMap<>();
            request.put("traffic_data", trafficData);

            ResponseEntity<Map> response = callSumoApi("/api/sumo/optimization/flow", request);

            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            } else {
                return createErrorResponse("流量平衡优化失败");
            }

        } catch (Exception e) {
            log.error("流量平衡优化失败: {}", e.getMessage(), e);
            return createErrorResponse("流量平衡优化失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> performComprehensiveOptimization(Map<String, Object> trafficData) {
        try {
            log.info("执行综合优化分析");

            Map<String, Object> request = new HashMap<>();
            request.put("traffic_data", trafficData);

            ResponseEntity<Map> response = callSumoApi("/api/sumo/optimization/comprehensive", request);

            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            } else {
                return createErrorResponse("综合优化分析失败");
            }

        } catch (Exception e) {
            log.error("综合优化分析失败: {}", e.getMessage(), e);
            return createErrorResponse("综合优化分析失败: " + e.getMessage());
        }
    }

    @Override
    public OptimizationResult saveOptimizationResult(String simulationTaskId, String optimizationType,
                                                    Map<String, Object> optimizationData) {
        try {
            log.info("保存优化结果: taskId={}, type={}", simulationTaskId, optimizationType);

            OptimizationResult result = new OptimizationResult();
            result.setSimulationTaskId(simulationTaskId);
            result.setOptimizationType(optimizationType);

            // 解析优化数据
            if (optimizationData.containsKey("optimization_result")) {
                Map<String, Object> optResult = (Map<String, Object>) optimizationData.get("optimization_result");

                // 设置优化方法
                if (optResult.containsKey("optimization_method")) {
                    result.setOptimizationMethod((String) optResult.get("optimization_method"));
                }

                // 设置改善指标
                if (optResult.containsKey("improvement_percentage")) {
                    OptimizationResult.ImprovementMetrics metrics = new OptimizationResult.ImprovementMetrics();
                    metrics.setOverallImprovement((Double) optResult.get("improvement_percentage"));
                    result.setImprovementMetrics(metrics);
                }

                // 设置其他数据
                result.setOptimizedConfig(optResult);
            }

            return optimizationResultRepository.save(result);

        } catch (Exception e) {
            log.error("保存优化结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存优化结果失败: " + e.getMessage());
        }
    }

    @Override
    public Optional<OptimizationResult> getOptimizationResult(String simulationTaskId) {
        try {
            return optimizationResultRepository.findBySimulationTaskId(simulationTaskId);
        } catch (Exception e) {
            log.error("获取优化结果失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Map<String, Object> getSimulationStatistics(String userId) {
        try {
            Map<String, Object> stats = new HashMap<>();

            if (userId != null) {
                // 用户统计
                long totalTasks = simulationTaskRepository.countByUserId(userId);
                long runningTasks = simulationTaskRepository.countByUserIdAndStatus(userId, "running");
                long completedTasks = simulationTaskRepository.countByUserIdAndStatus(userId, "completed");
                long failedTasks = simulationTaskRepository.countByUserIdAndStatus(userId, "failed");

                stats.put("total_tasks", totalTasks);
                stats.put("running_tasks", runningTasks);
                stats.put("completed_tasks", completedTasks);
                stats.put("failed_tasks", failedTasks);

                // 最近任务
                List<SimulationTask> recentTasks = simulationTaskRepository.findRecentTasksByUserId(userId, 10);
                stats.put("recent_tasks", recentTasks);

            } else {
                // 全局统计
                long totalTasks = simulationTaskRepository.count();
                long runningTasks = simulationTaskRepository.countByStatus("running");
                long completedTasks = simulationTaskRepository.countByStatus("completed");
                long failedTasks = simulationTaskRepository.countByStatus("failed");

                stats.put("total_tasks", totalTasks);
                stats.put("running_tasks", runningTasks);
                stats.put("completed_tasks", completedTasks);
                stats.put("failed_tasks", failedTasks);
            }

            return stats;

        } catch (Exception e) {
            log.error("获取仿真统计失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    // 辅助方法
    private Map<String, Object> createDefaultSimulationConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("simulation_duration", 3600); // 1小时
        config.put("step_length", 1.0);
        config.put("use_gui", false);
        config.put("random_seed", 42);
        config.put("network_type", "four_way_intersection");
        config.put("lane_count", 2);
        config.put("speed_limit", 50);
        return config;
    }

    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "error");
        response.put("message", message);
        response.put("timestamp", LocalDateTime.now());
        return response;
    }

    private ResponseEntity<Map> callSumoApi(String endpoint, Map<String, Object> request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);

            String url = sumoApiUrl + endpoint;
            return restTemplate.exchange(url, HttpMethod.POST, entity, Map.class);

        } catch (Exception e) {
            log.error("调用SUMO API失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用SUMO API失败: " + e.getMessage());
        }
    }

    private ResponseEntity<Map> callSumoApiGet(String endpoint) {
        try {
            String url = sumoApiUrl + endpoint;
            return restTemplate.getForEntity(url, Map.class);

        } catch (Exception e) {
            log.error("调用SUMO API失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用SUMO API失败: " + e.getMessage());
        }
    }

    private void updatePerformanceMetrics(SimulationTask task, Map<String, Object> metrics) {
        try {
            SimulationTask.PerformanceMetrics perfMetrics = new SimulationTask.PerformanceMetrics();

            if (metrics.containsKey("total_vehicles")) {
                perfMetrics.setTotalVehicles((Integer) metrics.get("total_vehicles"));
            }
            if (metrics.containsKey("average_speed")) {
                perfMetrics.setAverageSpeed((Double) metrics.get("average_speed"));
            }
            if (metrics.containsKey("total_waiting_time")) {
                perfMetrics.setTotalWaitingTime((Double) metrics.get("total_waiting_time"));
            }
            if (metrics.containsKey("throughput")) {
                perfMetrics.setThroughput((Double) metrics.get("throughput"));
            }
            if (metrics.containsKey("improvement_percentage")) {
                perfMetrics.setImprovementPercentage((Double) metrics.get("improvement_percentage"));
            }

            task.setPerformanceMetrics(perfMetrics);

        } catch (Exception e) {
            log.warn("更新性能指标失败: {}", e.getMessage());
        }
    }

    // 实现剩余的接口方法
    @Override
    public Map<String, Object> cleanupHistoryData(int daysToKeep) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);

            long deletedTasks = simulationTaskRepository.deleteOldCompletedTasks(cutoffTime);
            long deletedResults = optimizationResultRepository.deleteOldResults(cutoffTime);

            Map<String, Object> result = new HashMap<>();
            result.put("status", "success");
            result.put("deleted_tasks", deletedTasks);
            result.put("deleted_results", deletedResults);
            result.put("cutoff_time", cutoffTime);

            return result;

        } catch (Exception e) {
            log.error("清理历史数据失败: {}", e.getMessage(), e);
            return createErrorResponse("清理历史数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> checkSumoServiceStatus() {
        try {
            ResponseEntity<Map> response = callSumoApiGet("/api/sumo/health");

            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> result = new HashMap<>();
                result.put("status", "healthy");
                result.put("sumo_service", response.getBody());
                return result;
            } else {
                return createErrorResponse("SUMO服务不可用");
            }

        } catch (Exception e) {
            log.error("检查SUMO服务状态失败: {}", e.getMessage(), e);
            return createErrorResponse("SUMO服务连接失败: " + e.getMessage());
        }
    }

    @Override
    public List<SimulationTask> getRunningSimulations(String userId) {
        try {
            if (userId != null) {
                return simulationTaskRepository.findRunningTasksByUserId(userId);
            } else {
                return simulationTaskRepository.findRunningTasks();
            }
        } catch (Exception e) {
            log.error("获取运行中的仿真失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> stopUserSimulations(String userId) {
        try {
            List<SimulationTask> runningTasks = simulationTaskRepository.findRunningTasksByUserId(userId);

            int stoppedCount = 0;
            for (SimulationTask task : runningTasks) {
                try {
                    stopSimulation(task.getSimulationId());
                    stoppedCount++;
                } catch (Exception e) {
                    log.warn("停止仿真失败: {}", task.getSimulationId());
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("status", "success");
            result.put("stopped_count", stoppedCount);
            result.put("total_running", runningTasks.size());

            return result;

        } catch (Exception e) {
            log.error("批量停止仿真失败: {}", e.getMessage(), e);
            return createErrorResponse("批量停止仿真失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> restartSimulation(String simulationId) {
        try {
            // 先停止，再启动
            stopSimulation(simulationId);
            Thread.sleep(2000); // 等待2秒
            return startSimulation(simulationId, false);

        } catch (Exception e) {
            log.error("重启仿真失败: {}", e.getMessage(), e);
            return createErrorResponse("重启仿真失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> exportSimulationResult(String simulationId, String format) {
        // TODO: 实现导出功能
        return createErrorResponse("导出功能暂未实现");
    }

    @Override
    public Map<String, Object> compareSimulationResults(List<String> simulationIds) {
        // TODO: 实现比较功能
        return createErrorResponse("比较功能暂未实现");
    }

    @Override
    public Map<String, Object> getPerformanceReport(String simulationId) {
        try {
            Optional<SimulationTask> taskOpt = getSimulationTask(simulationId);
            if (!taskOpt.isPresent()) {
                return createErrorResponse("仿真任务不存在");
            }

            SimulationTask task = taskOpt.get();
            Map<String, Object> report = new HashMap<>();

            report.put("simulation_id", simulationId);
            report.put("task_name", task.getTaskName());
            report.put("simulation_type", task.getSimulationType());
            report.put("status", task.getStatus());
            report.put("duration", task.getDuration());
            report.put("performance_metrics", task.getPerformanceMetrics());

            // 添加优化结果
            Optional<OptimizationResult> optResult = getOptimizationResult(task.getId());
            if (optResult.isPresent()) {
                report.put("optimization_result", optResult.get());
            }

            return report;

        } catch (Exception e) {
            log.error("获取性能报告失败: {}", e.getMessage(), e);
            return createErrorResponse("获取性能报告失败: " + e.getMessage());
        }
    }

    @Override
    public SimulationTask createSimulationFromAnalysis(String analysisTaskId, String userId, String username, String simulationType) {
        try {
            // TODO: 从视频分析结果创建仿真任务
            // 这里需要获取视频分析的结果数据，然后转换为仿真输入数据

            Map<String, Object> trafficData = new HashMap<>();
            // 从分析结果中提取交通数据...

            return createSimulationTask(userId, username, "基于分析结果的仿真", simulationType, trafficData, null);

        } catch (Exception e) {
            log.error("从分析结果创建仿真失败: {}", e.getMessage(), e);
            throw new RuntimeException("从分析结果创建仿真失败: " + e.getMessage());
        }
    }
}
