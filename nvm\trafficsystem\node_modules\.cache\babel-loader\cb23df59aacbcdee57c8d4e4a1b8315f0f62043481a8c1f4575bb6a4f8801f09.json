{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"simulation-monitor\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"metrics-grid\"\n};\nconst _hoisted_4 = {\n  class: \"metric-card\"\n};\nconst _hoisted_5 = {\n  class: \"metric-content\"\n};\nconst _hoisted_6 = {\n  class: \"metric-value\"\n};\nconst _hoisted_7 = {\n  class: \"metric-card\"\n};\nconst _hoisted_8 = {\n  class: \"metric-content\"\n};\nconst _hoisted_9 = {\n  class: \"metric-value\"\n};\nconst _hoisted_10 = {\n  class: \"metric-card\"\n};\nconst _hoisted_11 = {\n  class: \"metric-content\"\n};\nconst _hoisted_12 = {\n  class: \"metric-value\"\n};\nconst _hoisted_13 = {\n  class: \"metric-card\"\n};\nconst _hoisted_14 = {\n  class: \"metric-content\"\n};\nconst _hoisted_15 = {\n  class: \"metric-value\"\n};\nconst _hoisted_16 = {\n  ref: \"flowChartRef\",\n  class: \"chart-container\"\n};\nconst _hoisted_17 = {\n  ref: \"speedChartRef\",\n  class: \"chart-container\"\n};\nconst _hoisted_18 = {\n  class: \"direction-grid\"\n};\nconst _hoisted_19 = {\n  class: \"direction-item\"\n};\nconst _hoisted_20 = {\n  class: \"direction-header\"\n};\nconst _hoisted_21 = {\n  class: \"direction-metrics\"\n};\nconst _hoisted_22 = {\n  class: \"direction-metric\"\n};\nconst _hoisted_23 = {\n  class: \"metric-value\"\n};\nconst _hoisted_24 = {\n  class: \"direction-metric\"\n};\nconst _hoisted_25 = {\n  class: \"metric-value\"\n};\nconst _hoisted_26 = {\n  class: \"direction-metric\"\n};\nconst _hoisted_27 = {\n  class: \"metric-value\"\n};\nconst _hoisted_28 = {\n  class: \"direction-metric\"\n};\nconst _hoisted_29 = {\n  class: \"metric-value\"\n};\nconst _hoisted_30 = {\n  class: \"direction-status\"\n};\nconst _hoisted_31 = {\n  class: \"traffic-light-monitor\"\n};\nconst _hoisted_32 = {\n  class: \"intersection-view\"\n};\nconst _hoisted_33 = {\n  class: \"intersection\"\n};\nconst _hoisted_34 = {\n  class: \"signal-timing-info\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_row, {\n    gutter: 20\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 实时数据卡片 \"), _createVNode(_component_el_col, {\n      span: 24\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"monitor-card\",\n        shadow: \"hover\"\n      }, {\n        header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"h3\", null, [_createElementVNode(\"i\", {\n          class: \"el-icon-monitor\"\n        }), _createTextVNode(\" 实时仿真监控\")], -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n          type: $setup.isConnected ? 'success' : 'danger',\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.isConnected ? '已连接' : '未连接'), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])])]),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_row, {\n          gutter: 20\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n              class: \"metric-icon\"\n            }, [_createElementVNode(\"i\", {\n              class: \"el-icon-time\",\n              style: {\n                \"color\": \"#409eff\"\n              }\n            })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.formatTime($setup.simulationTime)), 1 /* TEXT */), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n              class: \"metric-label\"\n            }, \"仿真时间\", -1 /* HOISTED */))])])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n              class: \"metric-icon\"\n            }, [_createElementVNode(\"i\", {\n              class: \"el-icon-truck\",\n              style: {\n                \"color\": \"#67c23a\"\n              }\n            })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.vehicleCount), 1 /* TEXT */), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n              class: \"metric-label\"\n            }, \"当前车辆\", -1 /* HOISTED */))])])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n              class: \"metric-icon\"\n            }, [_createElementVNode(\"i\", {\n              class: \"el-icon-odometer\",\n              style: {\n                \"color\": \"#e6a23c\"\n              }\n            })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString($setup.formatSpeed($setup.averageSpeed)), 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n              class: \"metric-label\"\n            }, \"平均速度\", -1 /* HOISTED */))])])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n              class: \"metric-icon\"\n            }, [_createElementVNode(\"i\", {\n              class: \"el-icon-warning\",\n              style: {\n                \"color\": \"#f56c6c\"\n              }\n            })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.trafficLightCount), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n              class: \"metric-label\"\n            }, \"信号灯数\", -1 /* HOISTED */))])])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_row, {\n    gutter: 20,\n    style: {\n      \"margin-top\": \"20px\"\n    }\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 实时图表 \"), _createVNode(_component_el_col, {\n      span: 12\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"chart-card\",\n        shadow: \"hover\"\n      }, {\n        header: _withCtx(() => _cache[9] || (_cache[9] = [_createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n          class: \"el-icon-data-line\"\n        }), _createTextVNode(\" 车流量变化\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_16, null, 512 /* NEED_PATCH */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 12\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"chart-card\",\n        shadow: \"hover\"\n      }, {\n        header: _withCtx(() => _cache[10] || (_cache[10] = [_createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n          class: \"el-icon-pie-chart\"\n        }), _createTextVNode(\" 速度分布\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, null, 512 /* NEED_PATCH */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_row, {\n    gutter: 20,\n    style: {\n      \"margin-top\": \"20px\"\n    }\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 方向流量监控 \"), _createVNode(_component_el_col, {\n      span: 24\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"direction-card\",\n        shadow: \"hover\"\n      }, {\n        header: _withCtx(() => _cache[11] || (_cache[11] = [_createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n          class: \"el-icon-position\"\n        }), _createTextVNode(\" 各方向流量监控\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_row, {\n          gutter: 20\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.directions, direction => {\n            return _openBlock(), _createBlock(_component_el_col, {\n              span: 6,\n              key: direction.name\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"i\", {\n                class: _normalizeClass(direction.icon)\n              }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(direction.label), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[12] || (_cache[12] = _createElementVNode(\"span\", {\n                class: \"metric-label\"\n              }, \"车辆数:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_23, _toDisplayString(direction.vehicleCount), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_24, [_cache[13] || (_cache[13] = _createElementVNode(\"span\", {\n                class: \"metric-label\"\n              }, \"流量:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_25, _toDisplayString(direction.flowRate) + \" 辆/h\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_26, [_cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n                class: \"metric-label\"\n              }, \"速度:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_27, _toDisplayString($setup.formatSpeed(direction.avgSpeed)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_28, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n                class: \"metric-label\"\n              }, \"密度:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_29, _toDisplayString(direction.density.toFixed(1)) + \" 辆/km\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_30, [_createVNode(_component_el_tag, {\n                type: $setup.getFlowStatusType(direction.flowRate),\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getFlowStatusText(direction.flowRate)), 1 /* TEXT */)]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])])])]),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        })])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 信号灯状态监控 \"), _createVNode(_component_el_row, {\n    gutter: 20,\n    style: {\n      \"margin-top\": \"20px\"\n    }\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 24\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"traffic-light-card\",\n        shadow: \"hover\"\n      }, {\n        header: _withCtx(() => _cache[16] || (_cache[16] = [_createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n          class: \"el-icon-warning\"\n        }), _createTextVNode(\" 信号灯状态\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createCommentVNode(\" 交叉口示意图 \"), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n          class: \"road road-horizontal\"\n        }, null, -1 /* HOISTED */)), _cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n          class: \"road road-vertical\"\n        }, null, -1 /* HOISTED */)), _createCommentVNode(\" 信号灯 \"), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"traffic-light traffic-light-north\", $setup.getTrafficLightClass('north')])\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"light red\", {\n            active: $setup.trafficLights.north === 'red'\n          }])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"light yellow\", {\n            active: $setup.trafficLights.north === 'yellow'\n          }])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"light green\", {\n            active: $setup.trafficLights.north === 'green'\n          }])\n        }, null, 2 /* CLASS */)], 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"traffic-light traffic-light-south\", $setup.getTrafficLightClass('south')])\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"light red\", {\n            active: $setup.trafficLights.south === 'red'\n          }])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"light yellow\", {\n            active: $setup.trafficLights.south === 'yellow'\n          }])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"light green\", {\n            active: $setup.trafficLights.south === 'green'\n          }])\n        }, null, 2 /* CLASS */)], 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"traffic-light traffic-light-east\", $setup.getTrafficLightClass('east')])\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"light red\", {\n            active: $setup.trafficLights.east === 'red'\n          }])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"light yellow\", {\n            active: $setup.trafficLights.east === 'yellow'\n          }])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"light green\", {\n            active: $setup.trafficLights.east === 'green'\n          }])\n        }, null, 2 /* CLASS */)], 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"traffic-light traffic-light-west\", $setup.getTrafficLightClass('west')])\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"light red\", {\n            active: $setup.trafficLights.west === 'red'\n          }])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"light yellow\", {\n            active: $setup.trafficLights.west === 'yellow'\n          }])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"light green\", {\n            active: $setup.trafficLights.west === 'green'\n          }])\n        }, null, 2 /* CLASS */)], 2 /* CLASS */)])]), _createElementVNode(\"div\", _hoisted_34, [_cache[19] || (_cache[19] = _createElementVNode(\"h5\", null, \"当前配时方案\", -1 /* HOISTED */)), _createVNode(_component_el_table, {\n          data: $setup.signalPhases,\n          size: \"small\",\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_table_column, {\n            prop: \"phase\",\n            label: \"相位\",\n            width: \"80\"\n          }), _createVNode(_component_el_table_column, {\n            prop: \"duration\",\n            label: \"时长(秒)\",\n            width: \"100\"\n          }), _createVNode(_component_el_table_column, {\n            prop: \"state\",\n            label: \"状态\",\n            width: \"120\"\n          }, {\n            default: _withCtx(scope => [_createVNode(_component_el_tag, {\n              size: \"small\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.state), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_table_column, {\n            prop: \"description\",\n            label: \"描述\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"data\"])])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_row", "gutter", "default", "_withCtx", "_createCommentVNode", "_component_el_col", "span", "_component_el_card", "shadow", "header", "_createElementVNode", "_hoisted_2", "_createTextVNode", "_component_el_tag", "type", "$setup", "isConnected", "size", "_toDisplayString", "_", "_hoisted_3", "_hoisted_4", "style", "_hoisted_5", "_hoisted_6", "formatTime", "simulationTime", "_hoisted_7", "_hoisted_8", "_hoisted_9", "vehicleCount", "_hoisted_10", "_hoisted_11", "_hoisted_12", "formatSpeed", "averageSpeed", "_hoisted_13", "_hoisted_14", "_hoisted_15", "trafficLightCount", "_cache", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_Fragment", "_renderList", "directions", "direction", "_createBlock", "key", "name", "_hoisted_19", "_hoisted_20", "_normalizeClass", "icon", "label", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "flowRate", "_hoisted_26", "_hoisted_27", "avgSpeed", "_hoisted_28", "_hoisted_29", "density", "toFixed", "_hoisted_30", "getFlowStatusType", "getFlowStatusText", "_hoisted_31", "_hoisted_32", "_hoisted_33", "getTrafficLightClass", "active", "trafficLights", "north", "south", "east", "west", "_hoisted_34", "_component_el_table", "data", "signalPhases", "_component_el_table_column", "prop", "width", "scope", "row", "state"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\SimulationMonitor.vue"], "sourcesContent": ["<template>\n  <div class=\"simulation-monitor\">\n    <el-row :gutter=\"20\">\n      <!-- 实时数据卡片 -->\n      <el-col :span=\"24\">\n        <el-card class=\"monitor-card\" shadow=\"hover\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3><i class=\"el-icon-monitor\"></i> 实时仿真监控</h3>\n              <el-tag :type=\"isConnected ? 'success' : 'danger'\" size=\"small\">\n                {{ isConnected ? '已连接' : '未连接' }}\n              </el-tag>\n            </div>\n          </template>\n\n          <!-- 关键指标展示 -->\n          <div class=\"metrics-grid\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"6\">\n                <div class=\"metric-card\">\n                  <div class=\"metric-icon\">\n                    <i class=\"el-icon-time\" style=\"color: #409eff\"></i>\n                  </div>\n                  <div class=\"metric-content\">\n                    <div class=\"metric-value\">{{ formatTime(simulationTime) }}</div>\n                    <div class=\"metric-label\">仿真时间</div>\n                  </div>\n                </div>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <div class=\"metric-card\">\n                  <div class=\"metric-icon\">\n                    <i class=\"el-icon-truck\" style=\"color: #67c23a\"></i>\n                  </div>\n                  <div class=\"metric-content\">\n                    <div class=\"metric-value\">{{ vehicleCount }}</div>\n                    <div class=\"metric-label\">当前车辆</div>\n                  </div>\n                </div>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <div class=\"metric-card\">\n                  <div class=\"metric-icon\">\n                    <i class=\"el-icon-odometer\" style=\"color: #e6a23c\"></i>\n                  </div>\n                  <div class=\"metric-content\">\n                    <div class=\"metric-value\">{{ formatSpeed(averageSpeed) }}</div>\n                    <div class=\"metric-label\">平均速度</div>\n                  </div>\n                </div>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <div class=\"metric-card\">\n                  <div class=\"metric-icon\">\n                    <i class=\"el-icon-warning\" style=\"color: #f56c6c\"></i>\n                  </div>\n                  <div class=\"metric-content\">\n                    <div class=\"metric-value\">{{ trafficLightCount }}</div>\n                    <div class=\"metric-label\">信号灯数</div>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 实时图表 -->\n      <el-col :span=\"12\">\n        <el-card class=\"chart-card\" shadow=\"hover\">\n          <template #header>\n            <h4><i class=\"el-icon-data-line\"></i> 车流量变化</h4>\n          </template>\n          <div ref=\"flowChartRef\" class=\"chart-container\"></div>\n        </el-card>\n      </el-col>\n      \n      <el-col :span=\"12\">\n        <el-card class=\"chart-card\" shadow=\"hover\">\n          <template #header>\n            <h4><i class=\"el-icon-pie-chart\"></i> 速度分布</h4>\n          </template>\n          <div ref=\"speedChartRef\" class=\"chart-container\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 方向流量监控 -->\n      <el-col :span=\"24\">\n        <el-card class=\"direction-card\" shadow=\"hover\">\n          <template #header>\n            <h4><i class=\"el-icon-position\"></i> 各方向流量监控</h4>\n          </template>\n          \n          <div class=\"direction-grid\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"6\" v-for=\"direction in directions\" :key=\"direction.name\">\n                <div class=\"direction-item\">\n                  <div class=\"direction-header\">\n                    <i :class=\"direction.icon\"></i>\n                    <span>{{ direction.label }}</span>\n                  </div>\n                  \n                  <div class=\"direction-metrics\">\n                    <div class=\"direction-metric\">\n                      <span class=\"metric-label\">车辆数:</span>\n                      <span class=\"metric-value\">{{ direction.vehicleCount }}</span>\n                    </div>\n                    <div class=\"direction-metric\">\n                      <span class=\"metric-label\">流量:</span>\n                      <span class=\"metric-value\">{{ direction.flowRate }} 辆/h</span>\n                    </div>\n                    <div class=\"direction-metric\">\n                      <span class=\"metric-label\">速度:</span>\n                      <span class=\"metric-value\">{{ formatSpeed(direction.avgSpeed) }}</span>\n                    </div>\n                    <div class=\"direction-metric\">\n                      <span class=\"metric-label\">密度:</span>\n                      <span class=\"metric-value\">{{ direction.density.toFixed(1) }} 辆/km</span>\n                    </div>\n                  </div>\n                  \n                  <div class=\"direction-status\">\n                    <el-tag :type=\"getFlowStatusType(direction.flowRate)\" size=\"small\">\n                      {{ getFlowStatusText(direction.flowRate) }}\n                    </el-tag>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 信号灯状态监控 -->\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <el-col :span=\"24\">\n        <el-card class=\"traffic-light-card\" shadow=\"hover\">\n          <template #header>\n            <h4><i class=\"el-icon-warning\"></i> 信号灯状态</h4>\n          </template>\n          \n          <div class=\"traffic-light-monitor\">\n            <div class=\"intersection-view\">\n              <div class=\"intersection\">\n                <!-- 交叉口示意图 -->\n                <div class=\"road road-horizontal\"></div>\n                <div class=\"road road-vertical\"></div>\n                \n                <!-- 信号灯 -->\n                <div class=\"traffic-light traffic-light-north\" :class=\"getTrafficLightClass('north')\">\n                  <div class=\"light red\" :class=\"{ active: trafficLights.north === 'red' }\"></div>\n                  <div class=\"light yellow\" :class=\"{ active: trafficLights.north === 'yellow' }\"></div>\n                  <div class=\"light green\" :class=\"{ active: trafficLights.north === 'green' }\"></div>\n                </div>\n                \n                <div class=\"traffic-light traffic-light-south\" :class=\"getTrafficLightClass('south')\">\n                  <div class=\"light red\" :class=\"{ active: trafficLights.south === 'red' }\"></div>\n                  <div class=\"light yellow\" :class=\"{ active: trafficLights.south === 'yellow' }\"></div>\n                  <div class=\"light green\" :class=\"{ active: trafficLights.south === 'green' }\"></div>\n                </div>\n                \n                <div class=\"traffic-light traffic-light-east\" :class=\"getTrafficLightClass('east')\">\n                  <div class=\"light red\" :class=\"{ active: trafficLights.east === 'red' }\"></div>\n                  <div class=\"light yellow\" :class=\"{ active: trafficLights.east === 'yellow' }\"></div>\n                  <div class=\"light green\" :class=\"{ active: trafficLights.east === 'green' }\"></div>\n                </div>\n                \n                <div class=\"traffic-light traffic-light-west\" :class=\"getTrafficLightClass('west')\">\n                  <div class=\"light red\" :class=\"{ active: trafficLights.west === 'red' }\"></div>\n                  <div class=\"light yellow\" :class=\"{ active: trafficLights.west === 'yellow' }\"></div>\n                  <div class=\"light green\" :class=\"{ active: trafficLights.west === 'green' }\"></div>\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"signal-timing-info\">\n              <h5>当前配时方案</h5>\n              <el-table :data=\"signalPhases\" size=\"small\" style=\"width: 100%\">\n                <el-table-column prop=\"phase\" label=\"相位\" width=\"80\"></el-table-column>\n                <el-table-column prop=\"duration\" label=\"时长(秒)\" width=\"100\"></el-table-column>\n                <el-table-column prop=\"state\" label=\"状态\" width=\"120\">\n                  <template #default=\"scope\">\n                    <el-tag size=\"small\">{{ scope.row.state }}</el-tag>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"description\" label=\"描述\"></el-table-column>\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'SimulationMonitor',\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    },\n    isRunning: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['data-updated'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isConnected = ref(false)\n    const simulationTime = ref(0)\n    const vehicleCount = ref(0)\n    const averageSpeed = ref(0)\n    const trafficLightCount = ref(4)\n    \n    // 图表引用\n    const flowChartRef = ref(null)\n    const speedChartRef = ref(null)\n    let flowChart = null\n    let speedChart = null\n    \n    // 方向数据\n    const directions = reactive([\n      {\n        name: 'east',\n        label: '东向',\n        icon: 'el-icon-right',\n        vehicleCount: 0,\n        flowRate: 0,\n        avgSpeed: 0,\n        density: 0\n      },\n      {\n        name: 'south',\n        label: '南向',\n        icon: 'el-icon-bottom',\n        vehicleCount: 0,\n        flowRate: 0,\n        avgSpeed: 0,\n        density: 0\n      },\n      {\n        name: 'west',\n        label: '西向',\n        icon: 'el-icon-left',\n        vehicleCount: 0,\n        flowRate: 0,\n        avgSpeed: 0,\n        density: 0\n      },\n      {\n        name: 'north',\n        label: '北向',\n        icon: 'el-icon-top',\n        vehicleCount: 0,\n        flowRate: 0,\n        avgSpeed: 0,\n        density: 0\n      }\n    ])\n    \n    // 信号灯状态\n    const trafficLights = reactive({\n      north: 'red',\n      south: 'red',\n      east: 'green',\n      west: 'green'\n    })\n    \n    // 信号配时方案\n    const signalPhases = reactive([\n      { phase: '相位1', duration: 30, state: 'GrGr', description: '东西绿灯' },\n      { phase: '相位2', duration: 5, state: 'yryr', description: '东西黄灯' },\n      { phase: '相位3', duration: 30, state: 'rGrG', description: '南北绿灯' },\n      { phase: '相位4', duration: 5, state: 'ryry', description: '南北黄灯' }\n    ])\n    \n    // 历史数据\n    const flowData = reactive({\n      times: [],\n      values: []\n    })\n    \n    const speedData = reactive({\n      ranges: ['0-10', '10-20', '20-30', '30-40', '40-50', '50+'],\n      values: [0, 0, 0, 0, 0, 0]\n    })\n    \n    // 数据更新定时器\n    let updateTimer = null\n    \n    // 方法\n    const formatTime = (seconds) => {\n      const hours = Math.floor(seconds / 3600)\n      const minutes = Math.floor((seconds % 3600) / 60)\n      const secs = seconds % 60\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n    }\n    \n    const formatSpeed = (speed) => {\n      return `${speed.toFixed(1)} m/s`\n    }\n    \n    const getFlowStatusType = (flowRate) => {\n      if (flowRate < 300) return 'success'\n      if (flowRate < 600) return 'warning'\n      return 'danger'\n    }\n    \n    const getFlowStatusText = (flowRate) => {\n      if (flowRate < 300) return '畅通'\n      if (flowRate < 600) return '缓慢'\n      return '拥堵'\n    }\n    \n    const getTrafficLightClass = (direction) => {\n      return `light-${trafficLights[direction]}`\n    }\n    \n    const initCharts = () => {\n      nextTick(() => {\n        // 初始化车流量图表\n        if (flowChartRef.value) {\n          flowChart = echarts.init(flowChartRef.value)\n          updateFlowChart()\n        }\n        \n        // 初始化速度分布图表\n        if (speedChartRef.value) {\n          speedChart = echarts.init(speedChartRef.value)\n          updateSpeedChart()\n        }\n      })\n    }\n    \n    const updateFlowChart = () => {\n      if (!flowChart) return\n      \n      const option = {\n        title: {\n          text: '实时车流量',\n          textStyle: { fontSize: 14 }\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: flowData.times,\n          axisLabel: {\n            formatter: (value) => {\n              const time = new Date(value)\n              return `${time.getHours()}:${time.getMinutes().toString().padStart(2, '0')}`\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数'\n        },\n        series: [{\n          data: flowData.values,\n          type: 'line',\n          smooth: true,\n          areaStyle: {\n            opacity: 0.3\n          },\n          lineStyle: {\n            color: '#409eff'\n          }\n        }]\n      }\n      \n      flowChart.setOption(option)\n    }\n    \n    const updateSpeedChart = () => {\n      if (!speedChart) return\n      \n      const option = {\n        title: {\n          text: '速度分布',\n          textStyle: { fontSize: 14 }\n        },\n        tooltip: {\n          trigger: 'item'\n        },\n        series: [{\n          type: 'pie',\n          radius: '60%',\n          data: speedData.ranges.map((range, index) => ({\n            name: `${range} km/h`,\n            value: speedData.values[index]\n          })),\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }]\n      }\n      \n      speedChart.setOption(option)\n    }\n    \n    const updateMonitorData = () => {\n      // 模拟实时数据更新\n      if (props.isRunning) {\n        simulationTime.value += 1\n        vehicleCount.value = Math.floor(Math.random() * 50) + 20\n        averageSpeed.value = Math.random() * 20 + 30\n        \n        // 更新方向数据\n        directions.forEach(direction => {\n          direction.vehicleCount = Math.floor(Math.random() * 15) + 5\n          direction.flowRate = Math.floor(Math.random() * 400) + 200\n          direction.avgSpeed = Math.random() * 15 + 25\n          direction.density = Math.random() * 30 + 10\n        })\n        \n        // 更新流量历史数据\n        const now = new Date()\n        flowData.times.push(now.toISOString())\n        flowData.values.push(vehicleCount.value)\n        \n        // 保持最近50个数据点\n        if (flowData.times.length > 50) {\n          flowData.times.shift()\n          flowData.values.shift()\n        }\n        \n        // 更新速度分布数据\n        speedData.values = speedData.values.map(() => Math.floor(Math.random() * 20))\n        \n        // 更新图表\n        updateFlowChart()\n        updateSpeedChart()\n        \n        // 模拟信号灯变化\n        if (simulationTime.value % 30 === 0) {\n          // 简单的信号灯切换逻辑\n          if (trafficLights.east === 'green') {\n            trafficLights.east = 'yellow'\n            trafficLights.west = 'yellow'\n          } else if (trafficLights.east === 'yellow') {\n            trafficLights.east = 'red'\n            trafficLights.west = 'red'\n            trafficLights.north = 'green'\n            trafficLights.south = 'green'\n          } else if (trafficLights.north === 'green') {\n            trafficLights.north = 'yellow'\n            trafficLights.south = 'yellow'\n          } else {\n            trafficLights.north = 'red'\n            trafficLights.south = 'red'\n            trafficLights.east = 'green'\n            trafficLights.west = 'green'\n          }\n        }\n        \n        isConnected.value = true\n        emit('data-updated', {\n          simulationTime: simulationTime.value,\n          vehicleCount: vehicleCount.value,\n          averageSpeed: averageSpeed.value,\n          directions: directions\n        })\n      } else {\n        isConnected.value = false\n      }\n    }\n    \n    const startMonitoring = () => {\n      if (updateTimer) {\n        clearInterval(updateTimer)\n      }\n      \n      updateTimer = setInterval(updateMonitorData, 1000) // 每秒更新\n    }\n    \n    const stopMonitoring = () => {\n      if (updateTimer) {\n        clearInterval(updateTimer)\n        updateTimer = null\n      }\n      isConnected.value = false\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      initCharts()\n      if (props.isRunning) {\n        startMonitoring()\n      }\n    })\n    \n    onUnmounted(() => {\n      stopMonitoring()\n      if (flowChart) {\n        flowChart.dispose()\n      }\n      if (speedChart) {\n        speedChart.dispose()\n      }\n    })\n    \n    // 监听运行状态变化\n    const { isRunning } = props\n    if (isRunning) {\n      startMonitoring()\n    } else {\n      stopMonitoring()\n    }\n    \n    return {\n      // 响应式数据\n      isConnected,\n      simulationTime,\n      vehicleCount,\n      averageSpeed,\n      trafficLightCount,\n      directions,\n      trafficLights,\n      signalPhases,\n      \n      // 图表引用\n      flowChartRef,\n      speedChartRef,\n      \n      // 方法\n      formatTime,\n      formatSpeed,\n      getFlowStatusType,\n      getFlowStatusText,\n      getTrafficLightClass,\n      startMonitoring,\n      stopMonitoring\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simulation-monitor {\n  padding: 0;\n}\n\n.monitor-card,\n.chart-card,\n.direction-card,\n.traffic-light-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3,\n.card-header h4 {\n  margin: 0;\n  color: #303133;\n}\n\n.metrics-grid {\n  margin-bottom: 20px;\n}\n\n.metric-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border-left: 4px solid #409eff;\n}\n\n.metric-icon {\n  font-size: 24px;\n  margin-right: 15px;\n}\n\n.metric-content {\n  flex: 1;\n}\n\n.metric-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.metric-label {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.chart-container {\n  height: 300px;\n  width: 100%;\n}\n\n.direction-grid {\n  margin-top: 15px;\n}\n\n.direction-item {\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.direction-header i {\n  margin-right: 8px;\n  font-size: 18px;\n}\n\n.direction-metrics {\n  margin-bottom: 10px;\n}\n\n.direction-metric {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n\n.direction-metric .metric-label {\n  color: #909399;\n}\n\n.direction-metric .metric-value {\n  color: #303133;\n  font-weight: bold;\n}\n\n.traffic-light-monitor {\n  display: flex;\n  gap: 30px;\n}\n\n.intersection-view {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 300px;\n}\n\n.intersection {\n  position: relative;\n  width: 200px;\n  height: 200px;\n}\n\n.road {\n  position: absolute;\n  background: #ddd;\n}\n\n.road-horizontal {\n  width: 100%;\n  height: 40px;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n}\n\n.road-vertical {\n  width: 40px;\n  height: 100%;\n  left: 50%;\n  top: 0;\n  transform: translateX(-50%);\n}\n\n.traffic-light {\n  position: absolute;\n  width: 20px;\n  height: 60px;\n  background: #333;\n  border-radius: 10px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-around;\n  align-items: center;\n  padding: 5px 0;\n}\n\n.traffic-light-north {\n  top: 10px;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.traffic-light-south {\n  bottom: 10px;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.traffic-light-east {\n  right: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.traffic-light-west {\n  left: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.light {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  opacity: 0.3;\n}\n\n.light.red {\n  background: #f56c6c;\n}\n\n.light.yellow {\n  background: #e6a23c;\n}\n\n.light.green {\n  background: #67c23a;\n}\n\n.light.active {\n  opacity: 1;\n  box-shadow: 0 0 10px currentColor;\n}\n\n.signal-timing-info {\n  flex: 1;\n}\n\n.signal-timing-info h5 {\n  margin-bottom: 15px;\n  color: #303133;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAMhBA,KAAK,EAAC;AAAa;;EASrBA,KAAK,EAAC;AAAc;;EAGdA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAOxBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAOxBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAOxBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAkB9BC,GAAG,EAAC,cAAc;EAACD,KAAK,EAAC;;;EASzBC,GAAG,EAAC,eAAe;EAACD,KAAK,EAAC;;;EAa1BA,KAAK,EAAC;AAAgB;;EAGhBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAkB;;EAKxBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAkB;;EAErBA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAkB;;EAErBA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAkB;;EAErBA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAkB;;EAErBA,KAAK,EAAC;AAAc;;EAIzBA,KAAK,EAAC;AAAkB;;EAqBhCA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAc;;EAgCtBA,KAAK,EAAC;AAAoB;;;;;;;;uBAtLzCE,mBAAA,CAuMM,OAvMNC,UAuMM,GAtMJC,YAAA,CAmESC,iBAAA;IAnEAC,MAAM,EAAE;EAAE;IAFvBC,OAAA,EAAAC,QAAA,CAGM,MAAe,CAAfC,mBAAA,YAAe,EACfL,YAAA,CAgESM,iBAAA;MAhEAC,IAAI,EAAE;IAAE;MAJvBJ,OAAA,EAAAC,QAAA,CAKQ,MA8DU,CA9DVJ,YAAA,CA8DUQ,kBAAA;QA9DDZ,KAAK,EAAC,cAAc;QAACa,MAAM,EAAC;;QACxBC,MAAM,EAAAN,QAAA,CACf,MAKM,CALNO,mBAAA,CAKM,OALNC,UAKM,G,0BAJJD,mBAAA,CAA+C,aAA3CA,mBAAA,CAA+B;UAA5Bf,KAAK,EAAC;QAAiB,IAR5CiB,gBAAA,CAQiD,SAAO,E,sBAC1Cb,YAAA,CAESc,iBAAA;UAFAC,IAAI,EAAEC,MAAA,CAAAC,WAAW;UAAyBC,IAAI,EAAC;;UATtEf,OAAA,EAAAC,QAAA,CAUgB,MAAiC,CAVjDS,gBAAA,CAAAM,gBAAA,CAUmBH,MAAA,CAAAC,WAAW,iC;UAV9BG,CAAA;;QAAAjB,OAAA,EAAAC,QAAA,CAgBU,MAkDM,CAlDNO,mBAAA,CAkDM,OAlDNU,UAkDM,GAjDJrB,YAAA,CAgDSC,iBAAA;UAhDAC,MAAM,EAAE;QAAE;UAjB/BC,OAAA,EAAAC,QAAA,CAkBc,MAUS,CAVTJ,YAAA,CAUSM,iBAAA;YAVAC,IAAI,EAAE;UAAC;YAlB9BJ,OAAA,EAAAC,QAAA,CAmBgB,MAQM,CARNO,mBAAA,CAQM,OARNW,UAQM,G,0BAPJX,mBAAA,CAEM;cAFDf,KAAK,EAAC;YAAa,IACtBe,mBAAA,CAAmD;cAAhDf,KAAK,EAAC,cAAc;cAAC2B,KAAsB,EAAtB;gBAAA;cAAA;qCAE1BZ,mBAAA,CAGM,OAHNa,UAGM,GAFJb,mBAAA,CAAgE,OAAhEc,UAAgE,EAAAN,gBAAA,CAAnCH,MAAA,CAAAU,UAAU,CAACV,MAAA,CAAAW,cAAc,mB,0BACtDhB,mBAAA,CAAoC;cAA/Bf,KAAK,EAAC;YAAc,GAAC,MAAI,qB;YAzBlDwB,CAAA;cA8BcpB,YAAA,CAUSM,iBAAA;YAVAC,IAAI,EAAE;UAAC;YA9B9BJ,OAAA,EAAAC,QAAA,CA+BgB,MAQM,CARNO,mBAAA,CAQM,OARNiB,UAQM,G,0BAPJjB,mBAAA,CAEM;cAFDf,KAAK,EAAC;YAAa,IACtBe,mBAAA,CAAoD;cAAjDf,KAAK,EAAC,eAAe;cAAC2B,KAAsB,EAAtB;gBAAA;cAAA;qCAE3BZ,mBAAA,CAGM,OAHNkB,UAGM,GAFJlB,mBAAA,CAAkD,OAAlDmB,UAAkD,EAAAX,gBAAA,CAArBH,MAAA,CAAAe,YAAY,kB,0BACzCpB,mBAAA,CAAoC;cAA/Bf,KAAK,EAAC;YAAc,GAAC,MAAI,qB;YArClDwB,CAAA;cA0CcpB,YAAA,CAUSM,iBAAA;YAVAC,IAAI,EAAE;UAAC;YA1C9BJ,OAAA,EAAAC,QAAA,CA2CgB,MAQM,CARNO,mBAAA,CAQM,OARNqB,WAQM,G,0BAPJrB,mBAAA,CAEM;cAFDf,KAAK,EAAC;YAAa,IACtBe,mBAAA,CAAuD;cAApDf,KAAK,EAAC,kBAAkB;cAAC2B,KAAsB,EAAtB;gBAAA;cAAA;qCAE9BZ,mBAAA,CAGM,OAHNsB,WAGM,GAFJtB,mBAAA,CAA+D,OAA/DuB,WAA+D,EAAAf,gBAAA,CAAlCH,MAAA,CAAAmB,WAAW,CAACnB,MAAA,CAAAoB,YAAY,mB,0BACrDzB,mBAAA,CAAoC;cAA/Bf,KAAK,EAAC;YAAc,GAAC,MAAI,qB;YAjDlDwB,CAAA;cAsDcpB,YAAA,CAUSM,iBAAA;YAVAC,IAAI,EAAE;UAAC;YAtD9BJ,OAAA,EAAAC,QAAA,CAuDgB,MAQM,CARNO,mBAAA,CAQM,OARN0B,WAQM,G,0BAPJ1B,mBAAA,CAEM;cAFDf,KAAK,EAAC;YAAa,IACtBe,mBAAA,CAAsD;cAAnDf,KAAK,EAAC,iBAAiB;cAAC2B,KAAsB,EAAtB;gBAAA;cAAA;qCAE7BZ,mBAAA,CAGM,OAHN2B,WAGM,GAFJ3B,mBAAA,CAAuD,OAAvD4B,WAAuD,EAAApB,gBAAA,CAA1BH,MAAA,CAAAwB,iBAAiB,kB,0BAC9C7B,mBAAA,CAAoC;cAA/Bf,KAAK,EAAC;YAAc,GAAC,MAAI,qB;YA7DlDwB,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MAuEIpB,YAAA,CAmBSC,iBAAA;IAnBAC,MAAM,EAAE,EAAE;IAAEqB,KAAyB,EAAzB;MAAA;IAAA;;IAvEzBpB,OAAA,EAAAC,QAAA,CAwEM,MAAa,CAAbC,mBAAA,UAAa,EACbL,YAAA,CAOSM,iBAAA;MAPAC,IAAI,EAAE;IAAE;MAzEvBJ,OAAA,EAAAC,QAAA,CA0EQ,MAKU,CALVJ,YAAA,CAKUQ,kBAAA;QALDZ,KAAK,EAAC,YAAY;QAACa,MAAM,EAAC;;QACtBC,MAAM,EAAAN,QAAA,CACf,MAAgDqC,MAAA,QAAAA,MAAA,OAAhD9B,mBAAA,CAAgD,aAA5CA,mBAAA,CAAiC;UAA9Bf,KAAK,EAAC;QAAmB,IA5E5CiB,gBAAA,CA4EiD,QAAM,E;QA5EvDV,OAAA,EAAAC,QAAA,CA8EU,MAAsD,CAAtDO,mBAAA,CAAsD,OAAtD+B,WAAsD,8B;QA9EhEtB,CAAA;;MAAAA,CAAA;QAkFMpB,YAAA,CAOSM,iBAAA;MAPAC,IAAI,EAAE;IAAE;MAlFvBJ,OAAA,EAAAC,QAAA,CAmFQ,MAKU,CALVJ,YAAA,CAKUQ,kBAAA;QALDZ,KAAK,EAAC,YAAY;QAACa,MAAM,EAAC;;QACtBC,MAAM,EAAAN,QAAA,CACf,MAA+CqC,MAAA,SAAAA,MAAA,QAA/C9B,mBAAA,CAA+C,aAA3CA,mBAAA,CAAiC;UAA9Bf,KAAK,EAAC;QAAmB,IArF5CiB,gBAAA,CAqFiD,OAAK,E;QArFtDV,OAAA,EAAAC,QAAA,CAuFU,MAAuD,CAAvDO,mBAAA,CAAuD,OAAvDgC,WAAuD,8B;QAvFjEvB,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA4FIpB,YAAA,CA+CSC,iBAAA;IA/CAC,MAAM,EAAE,EAAE;IAAEqB,KAAyB,EAAzB;MAAA;IAAA;;IA5FzBpB,OAAA,EAAAC,QAAA,CA6FM,MAAe,CAAfC,mBAAA,YAAe,EACfL,YAAA,CA4CSM,iBAAA;MA5CAC,IAAI,EAAE;IAAE;MA9FvBJ,OAAA,EAAAC,QAAA,CA+FQ,MA0CU,CA1CVJ,YAAA,CA0CUQ,kBAAA;QA1CDZ,KAAK,EAAC,gBAAgB;QAACa,MAAM,EAAC;;QAC1BC,MAAM,EAAAN,QAAA,CACf,MAAiDqC,MAAA,SAAAA,MAAA,QAAjD9B,mBAAA,CAAiD,aAA7CA,mBAAA,CAAgC;UAA7Bf,KAAK,EAAC;QAAkB,IAjG3CiB,gBAAA,CAiGgD,UAAQ,E;QAjGxDV,OAAA,EAAAC,QAAA,CAoGU,MAoCM,CApCNO,mBAAA,CAoCM,OApCNiC,WAoCM,GAnCJ5C,YAAA,CAkCSC,iBAAA;UAlCAC,MAAM,EAAE;QAAE;UArG/BC,OAAA,EAAAC,QAAA,CAsGgC,MAA+B,E,kBAAjDN,mBAAA,CAgCS+C,SAAA,QAtIvBC,WAAA,CAsGoD9B,MAAA,CAAA+B,UAAU,EAAvBC,SAAS;iCAAlCC,YAAA,CAgCS3C,iBAAA;cAhCAC,IAAI,EAAE,CAAC;cAAmC2C,GAAG,EAAEF,SAAS,CAACG;;cAtGhFhD,OAAA,EAAAC,QAAA,CAuGgB,MA8BM,CA9BNO,mBAAA,CA8BM,OA9BNyC,WA8BM,GA7BJzC,mBAAA,CAGM,OAHN0C,WAGM,GAFJ1C,mBAAA,CAA+B;gBAA3Bf,KAAK,EAzG7B0D,eAAA,CAyG+BN,SAAS,CAACO,IAAI;uCACzB5C,mBAAA,CAAkC,cAAAQ,gBAAA,CAAzB6B,SAAS,CAACQ,KAAK,iB,GAG1B7C,mBAAA,CAiBM,OAjBN8C,WAiBM,GAhBJ9C,mBAAA,CAGM,OAHN+C,WAGM,G,4BAFJ/C,mBAAA,CAAsC;gBAAhCf,KAAK,EAAC;cAAc,GAAC,MAAI,sBAC/Be,mBAAA,CAA8D,QAA9DgD,WAA8D,EAAAxC,gBAAA,CAAhC6B,SAAS,CAACjB,YAAY,iB,GAEtDpB,mBAAA,CAGM,OAHNiD,WAGM,G,4BAFJjD,mBAAA,CAAqC;gBAA/Bf,KAAK,EAAC;cAAc,GAAC,KAAG,sBAC9Be,mBAAA,CAA8D,QAA9DkD,WAA8D,EAAA1C,gBAAA,CAAhC6B,SAAS,CAACc,QAAQ,IAAG,MAAI,gB,GAEzDnD,mBAAA,CAGM,OAHNoD,WAGM,G,4BAFJpD,mBAAA,CAAqC;gBAA/Bf,KAAK,EAAC;cAAc,GAAC,KAAG,sBAC9Be,mBAAA,CAAuE,QAAvEqD,WAAuE,EAAA7C,gBAAA,CAAzCH,MAAA,CAAAmB,WAAW,CAACa,SAAS,CAACiB,QAAQ,kB,GAE9DtD,mBAAA,CAGM,OAHNuD,WAGM,G,4BAFJvD,mBAAA,CAAqC;gBAA/Bf,KAAK,EAAC;cAAc,GAAC,KAAG,sBAC9Be,mBAAA,CAAyE,QAAzEwD,WAAyE,EAAAhD,gBAAA,CAA3C6B,SAAS,CAACoB,OAAO,CAACC,OAAO,OAAM,OAAK,gB,KAItE1D,mBAAA,CAIM,OAJN2D,WAIM,GAHJtE,YAAA,CAESc,iBAAA;gBAFAC,IAAI,EAAEC,MAAA,CAAAuD,iBAAiB,CAACvB,SAAS,CAACc,QAAQ;gBAAG5C,IAAI,EAAC;;gBAjI/Ef,OAAA,EAAAC,QAAA,CAkIsB,MAA2C,CAlIjES,gBAAA,CAAAM,gBAAA,CAkIyBH,MAAA,CAAAwD,iBAAiB,CAACxB,SAAS,CAACc,QAAQ,kB;gBAlI7D1C,CAAA;;cAAAA,CAAA;;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA6IIf,mBAAA,aAAgB,EAChBL,YAAA,CAyDSC,iBAAA;IAzDAC,MAAM,EAAE,EAAE;IAAEqB,KAAyB,EAAzB;MAAA;IAAA;;IA9IzBpB,OAAA,EAAAC,QAAA,CA+IM,MAuDS,CAvDTJ,YAAA,CAuDSM,iBAAA;MAvDAC,IAAI,EAAE;IAAE;MA/IvBJ,OAAA,EAAAC,QAAA,CAgJQ,MAqDU,CArDVJ,YAAA,CAqDUQ,kBAAA;QArDDZ,KAAK,EAAC,oBAAoB;QAACa,MAAM,EAAC;;QAC9BC,MAAM,EAAAN,QAAA,CACf,MAA8CqC,MAAA,SAAAA,MAAA,QAA9C9B,mBAAA,CAA8C,aAA1CA,mBAAA,CAA+B;UAA5Bf,KAAK,EAAC;QAAiB,IAlJ1CiB,gBAAA,CAkJ+C,QAAM,E;QAlJrDV,OAAA,EAAAC,QAAA,CAqJU,MA+CM,CA/CNO,mBAAA,CA+CM,OA/CN8D,WA+CM,GA9CJ9D,mBAAA,CA+BM,OA/BN+D,WA+BM,GA9BJ/D,mBAAA,CA6BM,OA7BNgE,WA6BM,GA5BJtE,mBAAA,YAAe,E,4BACfM,mBAAA,CAAwC;UAAnCf,KAAK,EAAC;QAAsB,6B,4BACjCe,mBAAA,CAAsC;UAAjCf,KAAK,EAAC;QAAoB,6BAE/BS,mBAAA,SAAY,EACZM,mBAAA,CAIM;UAJDf,KAAK,EA7J1B0D,eAAA,EA6J2B,mCAAmC,EAAStC,MAAA,CAAA4D,oBAAoB;YACzEjE,mBAAA,CAAgF;UAA3Ef,KAAK,EA9J5B0D,eAAA,EA8J6B,WAAW;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACC,KAAK;UAAA;iCAC5DpE,mBAAA,CAAsF;UAAjFf,KAAK,EA/J5B0D,eAAA,EA+J6B,cAAc;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACC,KAAK;UAAA;iCAC/DpE,mBAAA,CAAoF;UAA/Ef,KAAK,EAhK5B0D,eAAA,EAgK6B,aAAa;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACC,KAAK;UAAA;kDAGhEpE,mBAAA,CAIM;UAJDf,KAAK,EAnK1B0D,eAAA,EAmK2B,mCAAmC,EAAStC,MAAA,CAAA4D,oBAAoB;YACzEjE,mBAAA,CAAgF;UAA3Ef,KAAK,EApK5B0D,eAAA,EAoK6B,WAAW;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACE,KAAK;UAAA;iCAC5DrE,mBAAA,CAAsF;UAAjFf,KAAK,EArK5B0D,eAAA,EAqK6B,cAAc;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACE,KAAK;UAAA;iCAC/DrE,mBAAA,CAAoF;UAA/Ef,KAAK,EAtK5B0D,eAAA,EAsK6B,aAAa;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACE,KAAK;UAAA;kDAGhErE,mBAAA,CAIM;UAJDf,KAAK,EAzK1B0D,eAAA,EAyK2B,kCAAkC,EAAStC,MAAA,CAAA4D,oBAAoB;YACxEjE,mBAAA,CAA+E;UAA1Ef,KAAK,EA1K5B0D,eAAA,EA0K6B,WAAW;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACG,IAAI;UAAA;iCAC3DtE,mBAAA,CAAqF;UAAhFf,KAAK,EA3K5B0D,eAAA,EA2K6B,cAAc;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACG,IAAI;UAAA;iCAC9DtE,mBAAA,CAAmF;UAA9Ef,KAAK,EA5K5B0D,eAAA,EA4K6B,aAAa;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACG,IAAI;UAAA;kDAG/DtE,mBAAA,CAIM;UAJDf,KAAK,EA/K1B0D,eAAA,EA+K2B,kCAAkC,EAAStC,MAAA,CAAA4D,oBAAoB;YACxEjE,mBAAA,CAA+E;UAA1Ef,KAAK,EAhL5B0D,eAAA,EAgL6B,WAAW;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACI,IAAI;UAAA;iCAC3DvE,mBAAA,CAAqF;UAAhFf,KAAK,EAjL5B0D,eAAA,EAiL6B,cAAc;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACI,IAAI;UAAA;iCAC9DvE,mBAAA,CAAmF;UAA9Ef,KAAK,EAlL5B0D,eAAA,EAkL6B,aAAa;YAAAuB,MAAA,EAAmB7D,MAAA,CAAA8D,aAAa,CAACI,IAAI;UAAA;sDAKnEvE,mBAAA,CAYM,OAZNwE,WAYM,G,4BAXJxE,mBAAA,CAAe,YAAX,QAAM,sBACVX,YAAA,CASWoF,mBAAA;UATAC,IAAI,EAAErE,MAAA,CAAAsE,YAAY;UAAEpE,IAAI,EAAC,OAAO;UAACK,KAAmB,EAAnB;YAAA;UAAA;;UAzL1DpB,OAAA,EAAAC,QAAA,CA0LgB,MAAsE,CAAtEJ,YAAA,CAAsEuF,0BAAA;YAArDC,IAAI,EAAC,OAAO;YAAChC,KAAK,EAAC,IAAI;YAACiC,KAAK,EAAC;cAC/CzF,YAAA,CAA6EuF,0BAAA;YAA5DC,IAAI,EAAC,UAAU;YAAChC,KAAK,EAAC,OAAO;YAACiC,KAAK,EAAC;cACrDzF,YAAA,CAIkBuF,0BAAA;YAJDC,IAAI,EAAC,OAAO;YAAChC,KAAK,EAAC,IAAI;YAACiC,KAAK,EAAC;;YAClCtF,OAAO,EAAAC,QAAA,CACmCsF,KAD5B,KACvB1F,YAAA,CAAmDc,iBAAA;cAA3CI,IAAI,EAAC;YAAO;cA9LxCf,OAAA,EAAAC,QAAA,CA8LyC,MAAqB,CA9L9DS,gBAAA,CAAAM,gBAAA,CA8L4CuE,KAAK,CAACC,GAAG,CAACC,KAAK,iB;cA9L3DxE,CAAA;;YAAAA,CAAA;cAiMgBpB,YAAA,CAAiEuF,0BAAA;YAAhDC,IAAI,EAAC,aAAa;YAAChC,KAAK,EAAC;;UAjM1DpC,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}