import request from '@/utils/request'

/**
 * 仿真相关API
 */
const simulationApi = {
  /**
   * 创建仿真任务
   * @param {Object} data 仿真任务数据
   * @returns {Promise}
   */
  createSimulation(data) {
    return request({
      url: '/api/simulation/create',
      method: 'post',
      data
    })
  },

  /**
   * 启动仿真
   * @param {string} simulationId 仿真ID
   * @param {Object} config 启动配置
   * @returns {Promise}
   */
  startSimulation(simulationId, config = {}) {
    return request({
      url: `/api/simulation/${simulationId}/start`,
      method: 'post',
      data: config
    })
  },

  /**
   * 停止仿真
   * @param {string} simulationId 仿真ID
   * @returns {Promise}
   */
  stopSimulation(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}/stop`,
      method: 'post'
    })
  },

  /**
   * 获取仿真状态
   * @param {string} simulationId 仿真ID
   * @returns {Promise}
   */
  getSimulationStatus(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}/status`,
      method: 'get'
    })
  },

  /**
   * 获取用户的仿真任务列表
   * @param {string} userId 用户ID
   * @returns {Promise}
   */
  getUserSimulations(userId) {
    return request({
      url: `/api/simulation/user/${userId}`,
      method: 'get'
    })
  },

  /**
   * 获取仿真任务详情
   * @param {string} simulationId 仿真ID
   * @returns {Promise}
   */
  getSimulationTask(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}`,
      method: 'get'
    })
  },

  /**
   * 删除仿真任务
   * @param {string} simulationId 仿真ID
   * @param {string} userId 用户ID
   * @returns {Promise}
   */
  deleteSimulation(simulationId, userId) {
    return request({
      url: `/api/simulation/${simulationId}`,
      method: 'delete',
      params: { userId }
    })
  },

  /**
   * 信号灯配时优化
   * @param {Object} data 优化数据
   * @returns {Promise}
   */
  optimizeSignalTiming(data) {
    return request({
      url: '/api/simulation/optimization/signal',
      method: 'post',
      data
    })
  },

  /**
   * 流量平衡优化
   * @param {Object} data 优化数据
   * @returns {Promise}
   */
  optimizeFlowBalance(data) {
    return request({
      url: '/api/simulation/optimization/flow',
      method: 'post',
      data
    })
  },

  /**
   * 综合优化分析
   * @param {Object} data 优化数据
   * @returns {Promise}
   */
  comprehensiveOptimization(data) {
    return request({
      url: '/api/simulation/optimization/comprehensive',
      method: 'post',
      data
    })
  },

  /**
   * 获取优化结果
   * @param {string} simulationId 仿真ID
   * @returns {Promise}
   */
  getOptimizationResult(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}/optimization`,
      method: 'get'
    })
  },

  /**
   * 获取仿真统计信息
   * @param {string} userId 用户ID（可选）
   * @returns {Promise}
   */
  getStatistics(userId = null) {
    return request({
      url: '/api/simulation/statistics',
      method: 'get',
      params: userId ? { userId } : {}
    })
  },

  /**
   * 检查SUMO服务状态
   * @returns {Promise}
   */
  checkServiceStatus() {
    return request({
      url: '/api/simulation/service/status',
      method: 'get'
    })
  },

  /**
   * 导出仿真结果
   * @param {string} simulationId 仿真ID
   * @param {string} format 导出格式
   * @returns {Promise}
   */
  exportResults(simulationId, format = 'json') {
    return request({
      url: `/api/simulation/${simulationId}/export`,
      method: 'get',
      params: { format },
      responseType: format === 'json' ? 'json' : 'blob'
    })
  },

  /**
   * 重启仿真
   * @param {string} simulationId 仿真ID
   * @returns {Promise}
   */
  restartSimulation(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}/restart`,
      method: 'post'
    })
  },

  /**
   * 比较多个仿真结果
   * @param {Array} simulationIds 仿真ID列表
   * @returns {Promise}
   */
  compareResults(simulationIds) {
    return request({
      url: '/api/simulation/compare',
      method: 'post',
      data: { simulation_ids: simulationIds }
    })
  },

  /**
   * 获取性能报告
   * @param {string} simulationId 仿真ID
   * @returns {Promise}
   */
  getPerformanceReport(simulationId) {
    return request({
      url: `/api/simulation/${simulationId}/performance`,
      method: 'get'
    })
  }
}

/**
 * 数据集成相关API
 */
const integrationApi = {
  /**
   * 转换分析数据为SUMO格式
   * @param {string} analysisTaskId 分析任务ID
   * @returns {Promise}
   */
  convertAnalysisData(analysisTaskId) {
    return request({
      url: '/api/integration/convert-analysis-data',
      method: 'post',
      data: { analysis_task_id: analysisTaskId },
      baseURL: 'http://localhost:5003'
    })
  },

  /**
   * 基于分析结果创建仿真任务
   * @param {Object} data 创建数据
   * @returns {Promise}
   */
  createSimulationFromAnalysis(data) {
    return request({
      url: '/api/integration/create-simulation',
      method: 'post',
      data,
      baseURL: 'http://localhost:5003'
    })
  },

  /**
   * 启动仿真任务
   * @param {string} simulationId 仿真ID
   * @param {boolean} useGui 是否使用GUI
   * @returns {Promise}
   */
  startSimulationTask(simulationId, useGui = false) {
    return request({
      url: '/api/integration/start-simulation',
      method: 'post',
      data: { simulation_id: simulationId, use_gui: useGui },
      baseURL: 'http://localhost:5003'
    })
  },

  /**
   * 获取仿真状态
   * @param {string} simulationId 仿真ID
   * @returns {Promise}
   */
  getSimulationStatus(simulationId) {
    return request({
      url: `/api/integration/simulation-status/${simulationId}`,
      method: 'get',
      baseURL: 'http://localhost:5003'
    })
  },

  /**
   * 健康检查
   * @returns {Promise}
   */
  healthCheck() {
    return request({
      url: '/api/integration/health',
      method: 'get',
      baseURL: 'http://localhost:5003'
    })
  }
}

/**
 * SUMO API相关
 */
const sumoApi = {
  /**
   * 获取SUMO服务状态
   * @returns {Promise}
   */
  getStatus() {
    return request({
      url: '/api/sumo/status',
      method: 'get',
      baseURL: 'http://localhost:5002'
    })
  },

  /**
   * 创建SUMO仿真
   * @param {Object} data 仿真数据
   * @returns {Promise}
   */
  createSimulation(data) {
    return request({
      url: '/api/sumo/simulation/create',
      method: 'post',
      data,
      baseURL: 'http://localhost:5002'
    })
  },

  /**
   * 启动SUMO仿真
   * @param {string} simulationId 仿真ID
   * @param {Object} config 配置
   * @returns {Promise}
   */
  startSimulation(simulationId, config) {
    return request({
      url: `/api/sumo/simulation/${simulationId}/start`,
      method: 'post',
      data: config,
      baseURL: 'http://localhost:5002'
    })
  },

  /**
   * 停止SUMO仿真
   * @returns {Promise}
   */
  stopSimulation() {
    return request({
      url: '/api/sumo/simulation/stop',
      method: 'post',
      baseURL: 'http://localhost:5002'
    })
  },

  /**
   * 数据转换
   * @param {Object} data 转换数据
   * @returns {Promise}
   */
  convertData(data) {
    return request({
      url: '/api/sumo/data/convert',
      method: 'post',
      data,
      baseURL: 'http://localhost:5002'
    })
  },

  /**
   * 健康检查
   * @returns {Promise}
   */
  healthCheck() {
    return request({
      url: '/api/sumo/health',
      method: 'get',
      baseURL: 'http://localhost:5002'
    })
  }
}

export default simulationApi
export { integrationApi, sumoApi }
