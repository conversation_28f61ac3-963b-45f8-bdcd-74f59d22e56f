<template>
  <div class="simulation-detail">
    <!-- 页面头部 -->
    <div class="detail-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/simulation' }">仿真分析</el-breadcrumb-item>
        <el-breadcrumb-item>仿真详情</el-breadcrumb-item>
      </el-breadcrumb>
      
      <div class="header-actions">
        <el-button @click="$router.go(-1)">
          <i class="el-icon-back"></i> 返回
        </el-button>
      </div>
    </div>

    <!-- 仿真基本信息 -->
    <el-card class="info-card" v-if="simulationTask">
      <template #header>
        <div class="card-header">
          <h3>{{ simulationTask.taskName }}</h3>
          <el-tag :type="getStatusTagType(simulationTask.status)" size="large">
            {{ getStatusText(simulationTask.status) }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="3" border>
        <el-descriptions-item label="仿真ID">{{ simulationTask.simulationId }}</el-descriptions-item>
        <el-descriptions-item label="仿真类型">
          <el-tag :type="getTypeTagType(simulationTask.simulationType)" size="small">
            {{ getTypeText(simulationTask.simulationType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(simulationTask.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ formatTime(simulationTask.startTime) }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ formatTime(simulationTask.endTime) }}</el-descriptions-item>
        <el-descriptions-item label="运行时长">{{ formatDuration(simulationTask.duration) }}</el-descriptions-item>
        <el-descriptions-item label="创建用户">{{ simulationTask.username }}</el-descriptions-item>
        <el-descriptions-item label="关联分析">{{ simulationTask.analysisTaskId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="进度">
          <el-progress 
            :percentage="simulationTask.progress || 0" 
            :status="getProgressStatus(simulationTask.status)"
            :stroke-width="8">
          </el-progress>
        </el-descriptions-item>
      </el-descriptions>

      <div v-if="simulationTask.errorMessage" class="error-message">
        <el-alert
          :title="simulationTask.errorMessage"
          type="error"
          :closable="false"
          show-icon>
        </el-alert>
      </div>
    </el-card>

    <!-- 仿真控制面板 -->
    <SimulationControlPanel
      v-if="simulationId"
      :analysisTaskId="simulationTask?.analysisTaskId || ''"
      :trafficData="simulationTask?.trafficData || {}"
      @simulation-started="handleSimulationStarted"
      @simulation-stopped="handleSimulationStopped"
      @simulation-completed="handleSimulationCompleted"
      @status-updated="handleStatusUpdated"
    />

    <!-- 仿真监控 -->
    <SimulationMonitor
      v-if="simulationId && isSimulationRunning"
      :simulationId="simulationId"
      :isRunning="isSimulationRunning"
      @data-updated="handleMonitorDataUpdated"
    />

    <!-- 优化结果对比 -->
    <OptimizationComparison
      v-if="simulationId && hasOptimizationResult"
      :optimizationResult="optimizationResult"
      @refresh-requested="loadOptimizationResult"
    />

    <!-- 性能指标 -->
    <el-card class="performance-card" v-if="simulationTask?.performanceMetrics">
      <template #header>
        <h4><i class="el-icon-data-analysis"></i> 性能指标</h4>
      </template>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总车辆数" :value="simulationTask.performanceMetrics.totalVehicles || 0" suffix="辆">
            <template #prefix>
              <i class="el-icon-truck" style="color: #409eff"></i>
            </template>
          </el-statistic>
        </el-col>
        
        <el-col :span="6">
          <el-statistic title="平均速度" :value="(simulationTask.performanceMetrics.averageSpeed || 0).toFixed(1)" suffix="m/s">
            <template #prefix>
              <i class="el-icon-odometer" style="color: #67c23a"></i>
            </template>
          </el-statistic>
        </el-col>
        
        <el-col :span="6">
          <el-statistic title="通行能力" :value="(simulationTask.performanceMetrics.throughput || 0).toFixed(0)" suffix="辆/h">
            <template #prefix>
              <i class="el-icon-right" style="color: #e6a23c"></i>
            </template>
          </el-statistic>
        </el-col>
        
        <el-col :span="6">
          <el-statistic title="改善效果" :value="(simulationTask.performanceMetrics.improvementPercentage || 0).toFixed(1)" suffix="%">
            <template #prefix>
              <i class="el-icon-trend-charts" style="color: #f56c6c"></i>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </el-card>

    <!-- 仿真日志 -->
    <el-card class="log-card">
      <template #header>
        <div class="card-header">
          <h4><i class="el-icon-document"></i> 仿真日志</h4>
          <el-button size="small" @click="refreshLogs">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
      </template>

      <div class="log-container">
        <div v-for="(log, index) in simulationLogs" :key="index" class="log-entry">
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        
        <div v-if="simulationLogs.length === 0" class="no-logs">
          暂无日志记录
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import simulationApi from '@/api/simulation'
import stompService from '@/utils/stomp-service'
import SimulationControlPanel from '@/components/simulation/SimulationControlPanel.vue'
import SimulationMonitor from '@/components/simulation/SimulationMonitor.vue'
import OptimizationComparison from '@/components/simulation/OptimizationComparison.vue'

export default {
  name: 'SimulationDetail',
  components: {
    SimulationControlPanel,
    SimulationMonitor,
    OptimizationComparison
  },
  setup() {
    const route = useRoute()
    const simulationId = ref(route.params.id)
    
    // 响应式数据
    const simulationTask = ref(null)
    const optimizationResult = ref(null)
    const simulationLogs = ref([])
    const loading = ref(false)
    
    // WebSocket订阅
    let simulationSubscriptions = null
    
    // 计算属性
    const isSimulationRunning = computed(() => 
      simulationTask.value?.status === 'running'
    )
    
    const hasOptimizationResult = computed(() => 
      optimizationResult.value && Object.keys(optimizationResult.value).length > 0
    )
    
    // 方法
    const getStatusTagType = (status) => {
      const statusMap = {
        'created': 'info',
        'running': 'success',
        'completed': 'success',
        'failed': 'danger',
        'stopped': 'warning'
      }
      return statusMap[status] || 'info'
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'created': '已创建',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'stopped': '已停止'
      }
      return statusMap[status] || '未知'
    }
    
    const getTypeTagType = (type) => {
      const typeMap = {
        'signal_timing': 'primary',
        'flow_balance': 'success',
        'comprehensive': 'warning'
      }
      return typeMap[type] || 'info'
    }
    
    const getTypeText = (type) => {
      const typeMap = {
        'signal_timing': '信号配时优化',
        'flow_balance': '流量平衡优化',
        'comprehensive': '综合优化分析'
      }
      return typeMap[type] || '未知'
    }
    
    const getProgressStatus = (status) => {
      if (status === 'failed') return 'exception'
      if (status === 'completed') return 'success'
      return null
    }
    
    const formatTime = (timeStr) => {
      if (!timeStr) return '-'
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    }
    
    const formatDuration = (duration) => {
      if (!duration) return '-'
      const hours = Math.floor(duration / 3600)
      const minutes = Math.floor((duration % 3600) / 60)
      const seconds = duration % 60
      
      if (hours > 0) {
        return `${hours}h ${minutes}m ${seconds}s`
      } else if (minutes > 0) {
        return `${minutes}m ${seconds}s`
      } else {
        return `${seconds}s`
      }
    }
    
    const loadSimulationTask = async () => {
      try {
        loading.value = true
        const response = await simulationApi.getSimulationTask(simulationId.value)
        
        if (response.status === 'success') {
          simulationTask.value = response.simulation_task
        } else {
          ElMessage.error('仿真任务不存在')
        }
      } catch (error) {
        console.error('加载仿真任务失败:', error)
        ElMessage.error('加载仿真任务失败')
      } finally {
        loading.value = false
      }
    }
    
    const loadOptimizationResult = async () => {
      try {
        const response = await simulationApi.getOptimizationResult(simulationId.value)
        
        if (response.status === 'success') {
          optimizationResult.value = response.optimization_result
        }
      } catch (error) {
        console.error('加载优化结果失败:', error)
      }
    }
    
    const setupWebSocketSubscriptions = async () => {
      try {
        // 确保WebSocket连接
        await stompService.connect()
        
        // 订阅仿真相关主题
        simulationSubscriptions = await stompService.subscribeSimulationAll(simulationId.value, {
          onStatusUpdate: handleWebSocketStatusUpdate,
          onRealtimeData: handleWebSocketRealtimeData,
          onFlowData: handleWebSocketFlowData,
          onTrafficLights: handleWebSocketTrafficLights,
          onOptimizationResult: handleWebSocketOptimizationResult
        })
        
        console.log('WebSocket订阅设置完成')
        
      } catch (error) {
        console.error('设置WebSocket订阅失败:', error)
      }
    }
    
    const cleanupWebSocketSubscriptions = () => {
      if (simulationSubscriptions) {
        stompService.unsubscribeSimulation(simulationSubscriptions)
        simulationSubscriptions = null
      }
    }
    
    // WebSocket事件处理
    const handleWebSocketStatusUpdate = (data) => {
      if (data.simulationId === simulationId.value) {
        // 更新仿真状态
        if (simulationTask.value) {
          simulationTask.value.status = data.status
          simulationTask.value.progress = data.progress
        }
        
        // 添加日志
        addLog('info', data.message || `状态更新: ${data.status}`)
      }
    }
    
    const handleWebSocketRealtimeData = (data) => {
      if (data.simulationId === simulationId.value) {
        // 处理实时数据
        addLog('debug', `实时数据: 时间${data.simulationTime}s, 车辆${data.vehicleCount}辆`)
      }
    }
    
    const handleWebSocketFlowData = (data) => {
      if (data.simulationId === simulationId.value) {
        // 处理流量数据
        addLog('debug', '收到方向流量数据更新')
      }
    }
    
    const handleWebSocketTrafficLights = (data) => {
      if (data.simulationId === simulationId.value) {
        // 处理信号灯数据
        addLog('debug', `信号灯更新: 相位${data.currentPhase}, 剩余${data.phaseRemaining}s`)
      }
    }
    
    const handleWebSocketOptimizationResult = (data) => {
      if (data.simulationId === simulationId.value) {
        // 处理优化结果
        optimizationResult.value = data.optimizationResult
        addLog('info', '收到优化结果')
      }
    }
    
    // 组件事件处理
    const handleSimulationStarted = (simId) => {
      addLog('info', '仿真已启动')
      loadSimulationTask()
    }
    
    const handleSimulationStopped = (simId) => {
      addLog('info', '仿真已停止')
      loadSimulationTask()
    }
    
    const handleSimulationCompleted = (simId) => {
      addLog('info', '仿真已完成')
      loadSimulationTask()
      loadOptimizationResult()
    }
    
    const handleStatusUpdated = (status) => {
      // 状态更新已通过WebSocket处理
    }
    
    const handleMonitorDataUpdated = (data) => {
      // 监控数据更新
    }
    
    const addLog = (level, message) => {
      simulationLogs.value.unshift({
        timestamp: new Date().toISOString(),
        level,
        message
      })
      
      // 保持最近100条日志
      if (simulationLogs.value.length > 100) {
        simulationLogs.value = simulationLogs.value.slice(0, 100)
      }
    }
    
    const refreshLogs = () => {
      // 这里可以从服务器获取日志
      addLog('info', '日志已刷新')
    }
    
    // 生命周期
    onMounted(async () => {
      await loadSimulationTask()
      await loadOptimizationResult()
      await setupWebSocketSubscriptions()
      
      addLog('info', '页面加载完成')
    })
    
    onUnmounted(() => {
      cleanupWebSocketSubscriptions()
    })
    
    return {
      // 响应式数据
      simulationId,
      simulationTask,
      optimizationResult,
      simulationLogs,
      loading,
      
      // 计算属性
      isSimulationRunning,
      hasOptimizationResult,
      
      // 方法
      getStatusTagType,
      getStatusText,
      getTypeTagType,
      getTypeText,
      getProgressStatus,
      formatTime,
      formatDuration,
      loadOptimizationResult,
      refreshLogs,
      
      // 事件处理
      handleSimulationStarted,
      handleSimulationStopped,
      handleSimulationCompleted,
      handleStatusUpdated,
      handleMonitorDataUpdated
    }
  }
}
</script>

<style scoped>
.simulation-detail {
  padding: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.info-card,
.performance-card,
.log-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3,
.card-header h4 {
  margin: 0;
  color: #303133;
}

.error-message {
  margin-top: 15px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 10px;
}

.log-entry {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 150px;
}

.log-level {
  margin-right: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  min-width: 50px;
  text-align: center;
}

.log-info {
  background: #e1f3d8;
  color: #67c23a;
}

.log-debug {
  background: #e6f7ff;
  color: #409eff;
}

.log-warn {
  background: #fdf6ec;
  color: #e6a23c;
}

.log-error {
  background: #fef0f0;
  color: #f56c6c;
}

.log-message {
  flex: 1;
  color: #303133;
}

.no-logs {
  text-align: center;
  color: #909399;
  padding: 20px;
}
</style>
