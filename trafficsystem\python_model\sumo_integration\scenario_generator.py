#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SUMO仿真场景生成器

根据交通数据生成SUMO仿真所需的配置文件
"""

import os
import logging
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import xml.etree.ElementTree as ET

class ScenarioGenerator:
    """SUMO场景生成器类"""
    
    def __init__(self):
        """初始化场景生成器"""
        self.logger = logging.getLogger(__name__)
        self.temp_dir = None
    
    def generate_scenario(self, simulation_id: str, sumo_data: Dict, config: Optional[Dict] = None) -> Dict:
        """
        生成完整的SUMO仿真场景
        
        Args:
            simulation_id: 仿真ID
            sumo_data: SUMO格式的交通数据
            config: 额外配置参数
            
        Returns:
            Dict: 生成的文件路径字典
        """
        try:
            self.logger.info(f"生成SUMO场景: {simulation_id}")
            
            # 创建临时目录
            self.temp_dir = Path(tempfile.gettempdir()) / 'sumo_simulations' / simulation_id
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成各种配置文件
            files = {}
            
            # 1. 生成网络文件
            files['network_file'] = self._generate_network_file(sumo_data)
            
            # 2. 生成路线文件
            files['route_file'] = self._generate_route_file(sumo_data)
            
            # 3. 生成交通信号灯文件
            files['traffic_lights_file'] = self._generate_traffic_lights_file(sumo_data)
            
            # 4. 生成车辆类型文件
            files['vehicle_types_file'] = self._generate_vehicle_types_file(sumo_data)
            
            # 5. 生成主配置文件
            files['config_file'] = self._generate_config_file(files, sumo_data)
            
            self.logger.info(f"SUMO场景生成完成: {len(files)} 个文件")
            return files
            
        except Exception as e:
            self.logger.error(f"生成SUMO场景失败: {e}")
            raise
    
    def _generate_network_file(self, sumo_data: Dict) -> str:
        """生成网络文件 (.net.xml)"""
        network_file = self.temp_dir / "intersection.net.xml"
        
        # 创建网络XML结构
        root = ET.Element("net", version="1.16.0", junctionCornerDetail="5", limitTurnSpeed="5.50", xmlns="http://sumo.dlr.de/xsd/net_file.xsd")
        
        # 添加位置信息
        location = ET.SubElement(root, "location", netOffset="0.00,0.00", convBoundary="-500.00,-500.00,500.00,500.00", origBoundary="-500.00,-500.00,500.00,500.00", projParameter="!")
        
        # 添加边（道路）
        edges = ET.SubElement(root, "edges")
        
        # 定义四个方向的道路
        directions = [
            {"id": "E_in", "from": "E_start", "to": "center", "priority": "1"},
            {"id": "E_out", "from": "center", "to": "E_end", "priority": "1"},
            {"id": "S_in", "from": "S_start", "to": "center", "priority": "1"},
            {"id": "S_out", "from": "center", "to": "S_end", "priority": "1"},
            {"id": "W_in", "from": "W_start", "to": "center", "priority": "1"},
            {"id": "W_out", "from": "center", "to": "W_end", "priority": "1"},
            {"id": "N_in", "from": "N_start", "to": "center", "priority": "1"},
            {"id": "N_out", "from": "center", "to": "N_end", "priority": "1"}
        ]
        
        for edge_info in directions:
            edge = ET.SubElement(root, "edge", id=edge_info["id"], from_=edge_info["from"], to=edge_info["to"], priority=edge_info["priority"])
            # 添加车道
            lane_count = sumo_data.get('network_info', {}).get('lane_count', 2)
            speed_limit = sumo_data.get('network_info', {}).get('speed_limit', 50) / 3.6  # 转换为m/s
            
            for i in range(lane_count):
                lane_id = f"{edge_info['id']}_{i}"
                lane = ET.SubElement(edge, "lane", id=lane_id, index=str(i), speed=f"{speed_limit:.2f}", length="500.00", shape=self._get_lane_shape(edge_info["id"], i))
        
        # 添加交叉口
        junctions = ET.SubElement(root, "junctions")
        
        # 中心交叉口
        center_junction = ET.SubElement(root, "junction", id="center", type="traffic_light", x="0.00", y="0.00", incLanes="E_in_0 E_in_1 S_in_0 S_in_1 W_in_0 W_in_1 N_in_0 N_in_1", intLanes="", shape="-10.00,-10.00 10.00,-10.00 10.00,10.00 -10.00,10.00")
        
        # 端点交叉口
        endpoints = [
            {"id": "E_start", "x": "-500.00", "y": "0.00"},
            {"id": "E_end", "x": "500.00", "y": "0.00"},
            {"id": "S_start", "x": "0.00", "y": "-500.00"},
            {"id": "S_end", "x": "0.00", "y": "500.00"},
            {"id": "W_start", "x": "-500.00", "y": "0.00"},
            {"id": "W_end", "x": "500.00", "y": "0.00"},
            {"id": "N_start", "x": "0.00", "y": "-500.00"},
            {"id": "N_end", "x": "0.00", "y": "500.00"}
        ]
        
        for endpoint in endpoints:
            ET.SubElement(root, "junction", id=endpoint["id"], type="dead_end", x=endpoint["x"], y=endpoint["y"], incLanes="", intLanes="", shape="")
        
        # 写入文件
        tree = ET.ElementTree(root)
        tree.write(network_file, encoding='utf-8', xml_declaration=True)
        
        self.logger.debug(f"网络文件生成: {network_file}")
        return str(network_file)
    
    def _get_lane_shape(self, edge_id: str, lane_index: int) -> str:
        """获取车道形状坐标"""
        offset = (lane_index - 0.5) * 3.2  # 车道宽度3.2米
        
        if edge_id.startswith("E"):
            if "in" in edge_id:
                return f"-500.00,{offset} 0.00,{offset}"
            else:
                return f"0.00,{offset} 500.00,{offset}"
        elif edge_id.startswith("S"):
            if "in" in edge_id:
                return f"{offset},-500.00 {offset},0.00"
            else:
                return f"{offset},0.00 {offset},500.00"
        elif edge_id.startswith("W"):
            if "in" in edge_id:
                return f"500.00,{-offset} 0.00,{-offset}"
            else:
                return f"0.00,{-offset} -500.00,{-offset}"
        elif edge_id.startswith("N"):
            if "in" in edge_id:
                return f"{-offset},500.00 {-offset},0.00"
            else:
                return f"{-offset},0.00 {-offset},-500.00"
        
        return "0.00,0.00 100.00,0.00"
    
    def _generate_route_file(self, sumo_data: Dict) -> str:
        """生成路线文件 (.rou.xml)"""
        route_file = self.temp_dir / "routes.rou.xml"
        
        root = ET.Element("routes", xmlns="http://sumo.dlr.de/xsd/routes_file.xsd")
        
        # 定义路线
        routes = [
            {"id": "route_east", "edges": "E_in E_out"},
            {"id": "route_south", "edges": "S_in S_out"},
            {"id": "route_west", "edges": "W_in W_out"},
            {"id": "route_north", "edges": "N_in N_out"},
            {"id": "route_E_to_S", "edges": "E_in S_out"},
            {"id": "route_E_to_W", "edges": "E_in W_out"},
            {"id": "route_E_to_N", "edges": "E_in N_out"},
            {"id": "route_S_to_W", "edges": "S_in W_out"},
            {"id": "route_S_to_N", "edges": "S_in N_out"},
            {"id": "route_W_to_N", "edges": "W_in N_out"}
        ]
        
        for route_info in routes:
            ET.SubElement(root, "route", id=route_info["id"], edges=route_info["edges"])
        
        # 添加车辆
        vehicles = sumo_data.get('vehicles', [])
        for vehicle in vehicles:
            veh_elem = ET.SubElement(root, "vehicle", 
                                   id=vehicle['id'],
                                   type=vehicle['type'],
                                   route=vehicle['route'],
                                   depart=f"{vehicle['depart']:.1f}")
        
        # 写入文件
        tree = ET.ElementTree(root)
        tree.write(route_file, encoding='utf-8', xml_declaration=True)
        
        self.logger.debug(f"路线文件生成: {route_file}")
        return str(route_file)
    
    def _generate_traffic_lights_file(self, sumo_data: Dict) -> str:
        """生成交通信号灯文件 (.add.xml)"""
        tls_file = self.temp_dir / "traffic_lights.add.xml"
        
        root = ET.Element("additionalFile", xmlns="http://sumo.dlr.de/xsd/additional_file.xsd")
        
        # 获取信号灯配时
        signal_timing = sumo_data.get('signal_timing', {})
        cycle_time = signal_timing.get('cycle_time', 120)
        phases = signal_timing.get('phases', [
            {'duration': 30, 'state': 'GrGr'},
            {'duration': 5, 'state': 'yryr'},
            {'duration': 30, 'state': 'rGrG'},
            {'duration': 5, 'state': 'ryry'}
        ])
        
        # 创建交通信号灯逻辑
        tls_logic = ET.SubElement(root, "tlLogic", id="center", type="static", programID="0", offset="0")
        
        for phase in phases:
            ET.SubElement(tls_logic, "phase", 
                         duration=str(phase['duration']),
                         state=phase['state'])
        
        # 写入文件
        tree = ET.ElementTree(root)
        tree.write(tls_file, encoding='utf-8', xml_declaration=True)
        
        self.logger.debug(f"信号灯文件生成: {tls_file}")
        return str(tls_file)
    
    def _generate_vehicle_types_file(self, sumo_data: Dict) -> str:
        """生成车辆类型文件"""
        vtype_file = self.temp_dir / "vehicle_types.add.xml"
        
        root = ET.Element("additionalFile", xmlns="http://sumo.dlr.de/xsd/additional_file.xsd")
        
        # 定义车辆类型
        vehicle_types = [
            {"id": "passenger", "accel": "2.6", "decel": "4.5", "sigma": "0.5", "length": "4.5", "maxSpeed": "55.56"},
            {"id": "truck", "accel": "1.8", "decel": "4.0", "sigma": "0.5", "length": "12.0", "maxSpeed": "44.44"},
            {"id": "bus", "accel": "1.5", "decel": "4.0", "sigma": "0.5", "length": "15.0", "maxSpeed": "41.67"},
            {"id": "motorcycle", "accel": "3.5", "decel": "5.0", "sigma": "0.3", "length": "2.5", "maxSpeed": "61.11"},
            {"id": "bicycle", "accel": "1.2", "decel": "3.0", "sigma": "0.2", "length": "1.8", "maxSpeed": "8.33"}
        ]
        
        for vtype in vehicle_types:
            ET.SubElement(root, "vType", **vtype)
        
        # 写入文件
        tree = ET.ElementTree(root)
        tree.write(vtype_file, encoding='utf-8', xml_declaration=True)
        
        self.logger.debug(f"车辆类型文件生成: {vtype_file}")
        return str(vtype_file)
    
    def _generate_config_file(self, files: Dict, sumo_data: Dict) -> str:
        """生成主配置文件 (.sumocfg)"""
        config_file = self.temp_dir / "simulation.sumocfg"
        
        root = ET.Element("configuration", xmlns="http://sumo.dlr.de/xsd/sumoConfiguration.xsd")
        
        # 输入文件
        input_elem = ET.SubElement(root, "input")
        ET.SubElement(input_elem, "net-file", value=os.path.basename(files['network_file']))
        ET.SubElement(input_elem, "route-files", value=os.path.basename(files['route_file']))
        ET.SubElement(input_elem, "additional-files", value=f"{os.path.basename(files['traffic_lights_file'])},{os.path.basename(files['vehicle_types_file'])}")
        
        # 时间设置
        time_elem = ET.SubElement(root, "time")
        sim_params = sumo_data.get('simulation_params', {})
        ET.SubElement(time_elem, "begin", value=str(sim_params.get('begin_time', 0)))
        ET.SubElement(time_elem, "end", value=str(sim_params.get('end_time', 3600)))
        ET.SubElement(time_elem, "step-length", value=str(sim_params.get('step_length', 1.0)))
        
        # 处理设置
        processing_elem = ET.SubElement(root, "processing")
        ET.SubElement(processing_elem, "time-to-teleport", value="300")
        
        # 报告设置
        report_elem = ET.SubElement(root, "report")
        ET.SubElement(report_elem, "verbose", value="true")
        ET.SubElement(report_elem, "no-step-log", value="true")
        
        # 随机种子
        random_elem = ET.SubElement(root, "random_number")
        ET.SubElement(random_elem, "seed", value=str(sim_params.get('random_seed', 42)))
        
        # 写入文件
        tree = ET.ElementTree(root)
        tree.write(config_file, encoding='utf-8', xml_declaration=True)
        
        self.logger.debug(f"配置文件生成: {config_file}")
        return str(config_file)
