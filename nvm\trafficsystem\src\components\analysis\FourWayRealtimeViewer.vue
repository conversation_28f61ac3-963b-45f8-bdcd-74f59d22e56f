<template>
  <div class="four-way-realtime-viewer">
    <!-- 连接状态指示器 -->
    <div class="connection-status">
      <el-tag :type="connectionStatusType" size="small">
        <el-icon><Connection /></el-icon>
        {{ connectionStatusText }}
      </el-tag>
      <span class="last-update">
        最后更新: {{ lastUpdateTime || '未连接' }}
      </span>
    </div>

    <!-- 四方向检测网格 - 2x2布局 -->
    <div class="detection-grid">
      <!-- 上排：北向和东向 -->
      <!-- 北向检测 - 左上 -->
      <div class="detection-item north">
        <div class="direction-header">
          <el-icon><Top /></el-icon>
          <span class="direction-label">北向 (North)</span>
          <el-tag :type="getDirectionStatusType('north')" size="small">
            {{ getDirectionStatusText('north') }}
          </el-tag>
        </div>
        <div class="detection-content">
          <RealTimeFrameViewer
            ref="northViewer"
            direction="north"
            :frame-data="directionFrameData.north"
            :status="directionStats.north.status"
            :progress="0"
            :show-controls="false"
            @pause-toggled="(data) => console.log('North pause toggled:', data)"
            @frame-saved="(data) => console.log('North frame saved:', data)"
            class="direction-viewer"
          />
          <div class="direction-stats">
            <div class="stat-item">
              <span class="stat-value">{{ directionStats.north.vehicleCount }}</span>
              <span class="stat-label">车辆数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ directionStats.north.frameRate }}</span>
              <span class="stat-label">帧率</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 下排：西向和南向 -->
      <!-- 西向检测 - 左下 -->
      <div class="detection-item west">
        <div class="direction-header">
          <el-icon><Back /></el-icon>
          <span class="direction-label">西向 (West)</span>
          <el-tag :type="getDirectionStatusType('west')" size="small">
            {{ getDirectionStatusText('west') }}
          </el-tag>
        </div>
        <div class="detection-content">
          <RealTimeFrameViewer
            ref="westViewer"
            direction="west"
            :frame-data="directionFrameData.west"
            :status="directionStats.west.status"
            :progress="0"
            :show-controls="false"
            @pause-toggled="(data) => console.log('West pause toggled:', data)"
            @frame-saved="(data) => console.log('West frame saved:', data)"
            class="direction-viewer"
          />
          <div class="direction-stats">
            <div class="stat-item">
              <span class="stat-value">{{ directionStats.west.vehicleCount }}</span>
              <span class="stat-label">车辆数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ directionStats.west.frameRate }}</span>
              <span class="stat-label">帧率</span>
            </div>
          </div>
        </div>
      </div>



      <!-- 东向检测 - 右上 -->
      <div class="detection-item east">
        <div class="direction-header">
          <el-icon><Right /></el-icon>
          <span class="direction-label">东向 (East)</span>
          <el-tag :type="getDirectionStatusType('east')" size="small">
            {{ getDirectionStatusText('east') }}
          </el-tag>
        </div>
        <div class="detection-content">
          <RealTimeFrameViewer
            ref="eastViewer"
            direction="east"
            :frame-data="directionFrameData.east"
            :status="directionStats.east.status"
            :progress="0"
            :show-controls="false"
            @pause-toggled="(data) => console.log('East pause toggled:', data)"
            @frame-saved="(data) => console.log('East frame saved:', data)"
            class="direction-viewer"
          />
          <div class="direction-stats">
            <div class="stat-item">
              <span class="stat-value">{{ directionStats.east.vehicleCount }}</span>
              <span class="stat-label">车辆数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ directionStats.east.frameRate }}</span>
              <span class="stat-label">帧率</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 南向检测 - 右下 -->
      <div class="detection-item south">
        <div class="direction-header">
          <el-icon><Bottom /></el-icon>
          <span class="direction-label">南向 (South)</span>
          <el-tag :type="getDirectionStatusType('south')" size="small">
            {{ getDirectionStatusText('south') }}
          </el-tag>
        </div>
        <div class="detection-content">
          <RealTimeFrameViewer
            ref="southViewer"
            direction="south"
            :frame-data="directionFrameData.south"
            :status="directionStats.south.status"
            :progress="0"
            :show-controls="false"
            @pause-toggled="(data) => console.log('South pause toggled:', data)"
            @frame-saved="(data) => console.log('South frame saved:', data)"
            class="direction-viewer"
          />
          <div class="direction-stats">
            <div class="stat-item">
              <span class="stat-value">{{ directionStats.south.vehicleCount }}</span>
              <span class="stat-label">车辆数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ directionStats.south.frameRate }}</span>
              <span class="stat-label">帧率</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 总览统计 -->
    <div class="overview-stats">
      <div class="total-vehicles-summary">
        <el-icon><Grid /></el-icon>
        <span class="total-count">{{ totalVehicleCount }}</span>
        <span class="total-label">总车辆数</span>
      </div>
      <div class="peak-direction-info">
        <span class="peak-text">高峰方向: {{ getPeakDirection() }}</span>
      </div>
    </div>

    <!-- 检测进度条 -->
    <div v-if="showProgress" class="detection-progress">
      <div class="progress-header">
        <span class="progress-title">检测进度</span>
        <span class="progress-percentage">{{ overallProgress }}%</span>
      </div>
      <el-progress
        :percentage="overallProgress"
        :status="progressStatus"
        :stroke-width="12"
        :show-text="false"
        class="animated-progress"
      />
      <div class="progress-details">
        <span class="progress-info">
          正在处理 {{ getActiveDirectionsCount() }} 个方向的视频
        </span>
      </div>
    </div>

    <!-- 智能交通状态面板 -->
    <IntelligentTrafficStatusPanel
      :direction-stats="directionStats"
      :last-update-time="lastUpdateTime"
      @apply-suggestion="handleApplySuggestion"
    />

    <!-- 增强的全局统计信息 -->
    <div class="enhanced-global-stats">
      <div class="stats-header">
        <h3>实时交通分析</h3>
        <div class="connection-status-info">
          <el-icon :class="{ 'connected': isConnected, 'disconnected': !isConnected }">
            <Connection />
          </el-icon>
          <span>{{ isConnected ? '已连接' : '未连接' }}</span>
          <el-tag v-if="lastUpdateTime" size="small" type="info">
            {{ lastUpdateTime }}
          </el-tag>
        </div>
      </div>

      <div class="stats-grid">
        <div class="stat-card primary">
          <div class="stat-icon">🚗</div>
          <div class="stat-content">
            <div class="stat-value">{{ totalVehicleCount }}</div>
            <div class="stat-label">总车辆数</div>
            <div class="stat-trend positive">
              +{{ getRecentIncrease() }}
            </div>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">📍</div>
          <div class="stat-content">
            <div class="stat-value">{{ getPeakDirection() }}</div>
            <div class="stat-label">最繁忙方向</div>
            <div class="stat-trend">
              {{ getPeakDirectionPercentage() }}%
            </div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">⚡</div>
          <div class="stat-content">
            <div class="stat-value">{{ getAverageFrameRate() }}</div>
            <div class="stat-label">平均帧率</div>
            <div class="stat-trend">实时</div>
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">⏱️</div>
          <div class="stat-content">
            <div class="stat-value">{{ getProcessingTime() }}s</div>
            <div class="stat-label">处理时间</div>
            <div class="stat-trend">{{ getEfficiencyLevel() }}</div>
          </div>
        </div>

        <div class="stat-card danger">
          <div class="stat-icon">🚨</div>
          <div class="stat-content">
            <div class="stat-value">{{ getCongestionLevel() }}</div>
            <div class="stat-label">拥堵等级</div>
            <div class="stat-trend">{{ getCongestionTrend() }}</div>
          </div>
        </div>

        <div class="stat-card purple">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-value">{{ getTrafficFlowBalance() }}%</div>
            <div class="stat-label">流量平衡度</div>
            <div class="stat-trend">{{ getBalanceTrend() }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Connection, Grid, Top, Bottom, ArrowLeft as Back, Right
} from '@element-plus/icons-vue'
import RealTimeFrameViewer from '@/components/analysis/RealTimeFrameViewer.vue'
import IntelligentTrafficStatusPanel from '@/components/analysis/IntelligentTrafficStatusPanel.vue'
import stompService from '@/utils/stomp-service'

export default {
  name: 'FourWayRealtimeViewer',
  components: {
    Connection, Grid, Top, Bottom, Back, Right,
    RealTimeFrameViewer,
    IntelligentTrafficStatusPanel
  },
  props: {
    taskId: {
      type: String,
      required: true
    },
    autoStart: {
      type: Boolean,
      default: true
    }
  },
  emits: ['detection-update', 'status-change', 'analysis-complete'],
  setup(props, { emit }) {
    // 响应式数据
    const isConnected = ref(false)
    const lastUpdateTime = ref('')
    const frameSubscription = ref(null)
    const showProgress = ref(true)
    
    // 方向查看器引用
    const northViewer = ref(null)
    const southViewer = ref(null)
    const eastViewer = ref(null)
    const westViewer = ref(null)

    // 方向帧数据状态
    const directionFrameData = reactive({
      north: null,
      south: null,
      east: null,
      west: null
    })

    // 方向统计数据
    const directionStats = reactive({
      north: {
        vehicleCount: 0,
        frameRate: '0 fps',
        status: 'waiting',
        lastUpdate: null,
        lastFrameTime: null,
        currentFrame: 0,
        totalFrames: 0,
        recentVehicles: [], // 最近N帧的车辆数，用于移动平均
        maxRecentFrames: 10 // 保留最近10帧数据
      },
      south: {
        vehicleCount: 0,
        frameRate: '0 fps',
        status: 'waiting',
        lastUpdate: null,
        lastFrameTime: null,
        currentFrame: 0,
        totalFrames: 0,
        recentVehicles: [],
        maxRecentFrames: 10
      },
      east: {
        vehicleCount: 0,
        frameRate: '0 fps',
        status: 'waiting',
        lastUpdate: null,
        lastFrameTime: null,
        currentFrame: 0,
        totalFrames: 0,
        recentVehicles: [],
        maxRecentFrames: 10
      },
      west: {
        vehicleCount: 0,
        frameRate: '0 fps',
        status: 'waiting',
        lastUpdate: null,
        lastFrameTime: null,
        currentFrame: 0,
        totalFrames: 0,
        recentVehicles: [],
        maxRecentFrames: 10
      }
    })
    
    // 计算属性
    const connectionStatusType = computed(() => {
      return isConnected.value ? 'success' : 'danger'
    })
    
    const connectionStatusText = computed(() => {
      return isConnected.value ? '已连接' : '未连接'
    })
    
    const totalVehicleCount = computed(() => {
      return Object.values(directionStats).reduce((total, stats) => total + stats.vehicleCount, 0)
    })
    
    const overallProgress = computed(() => {
      const directions = Object.values(directionStats)
      let totalProgress = 0
      let activeDirections = 0

      directions.forEach(direction => {
        if (direction.status !== 'waiting') {
          activeDirections++
          // 基于帧数计算进度
          if (direction.totalFrames > 0) {
            const frameProgress = Math.min((direction.currentFrame || 0) / direction.totalFrames * 100, 100)
            totalProgress += frameProgress
          } else if (direction.status === 'completed') {
            totalProgress += 100
          }
        }
      })

      if (activeDirections === 0) return 0
      return Math.round(totalProgress / 4) // 总是除以4个方向
    })
    
    const progressStatus = computed(() => {
      if (overallProgress.value === 100) return 'success'
      if (overallProgress.value > 0) return ''
      return 'exception'
    })
    
    // 方法
    const getDirectionStatusType = (direction) => {
      const status = directionStats[direction].status
      const typeMap = {
        'waiting': 'info',
        'processing': 'warning',
        'completed': 'success',
        'error': 'danger'
      }
      return typeMap[status] || 'info'
    }

    const getDirectionStatusText = (direction) => {
      const status = directionStats[direction].status
      const textMap = {
        'waiting': '等待中',
        'processing': '检测中',
        'completed': '已完成',
        'error': '检测失败'
      }

      // 如果是等待状态，检查是否为长视频处理
      if (status === 'waiting') {
        const processingTime = getProcessingTime()
        if (processingTime > 30) {
          return '长视频处理中...'
        } else if (processingTime > 10) {
          return '正在处理...'
        }
      }

      return textMap[status] || '未知'
    }
    
    const formatImageData = (imageData) => {
      if (!imageData) {
        console.warn('🖼️ 图像数据为空')
        return null
      }

      // 如果已经是完整的 data URL，直接返回
      if (imageData.startsWith('data:image/')) {
        return imageData
      }

      // 如果是 Base64 字符串，添加 data URL 前缀
      if (typeof imageData === 'string' && imageData.length > 0) {
        const formattedData = `data:image/jpeg;base64,${imageData}`
        console.log('🖼️ 格式化图像数据:', {
          originalLength: imageData.length,
          formattedLength: formattedData.length,
          prefix: formattedData.substring(0, 50) + '...'
        })
        return formattedData
      }

      console.warn('🖼️ 无效的图像数据格式:', typeof imageData, imageData ? imageData.substring(0, 50) : 'null')
      return null
    }

    const handleFrameReceived = (direction, frameData) => {
      try {
        console.log(`🎬 处理${direction}方向帧数据:`, {
          frameNumber: frameData.frameNumber,
          detectionCount: frameData.detectionCount,
          hasImageData: !!frameData.imageData,
          imageDataLength: frameData.imageData ? frameData.imageData.length : 0
        })

        // 格式化图像数据
        const formattedImageData = formatImageData(frameData.imageData)
        if (!formattedImageData) {
          console.error(`❌ ${direction}方向图像数据格式化失败`)
          return
        }

        // 创建格式化的帧数据
        const formattedFrameData = {
          ...frameData,
          imageData: formattedImageData,
          direction: direction,
          timestamp: new Date().toISOString()
        }

        // 更新方向帧数据
        directionFrameData[direction] = formattedFrameData
        console.log(`✅ ${direction}方向帧数据已更新`)

        // 更新帧数信息
        if (frameData.frameNumber !== undefined) {
          directionStats[direction].currentFrame = frameData.frameNumber
        }
        if (frameData.totalFrames !== undefined) {
          directionStats[direction].totalFrames = frameData.totalFrames
        }

        // 更新方向统计 - 使用当前帧的车辆数而不是累加
        const currentVehicleCount = frameData.detectionCount || 0

        // 更新移动平均数据
        const recentVehicles = directionStats[direction].recentVehicles
        recentVehicles.push(currentVehicleCount)

        // 保持最近N帧的数据
        if (recentVehicles.length > directionStats[direction].maxRecentFrames) {
          recentVehicles.shift()
        }

        // 计算移动平均
        const movingAverage = recentVehicles.length > 0
          ? Math.round(recentVehicles.reduce((sum, count) => sum + count, 0) / recentVehicles.length * 10) / 10
          : 0

        // 更新车辆计数为当前帧的检测数量
        directionStats[direction].vehicleCount = currentVehicleCount
        directionStats[direction].movingAverage = movingAverage
        directionStats[direction].status = 'processing'
        directionStats[direction].lastUpdate = new Date()

        // 更新最后更新时间
        lastUpdateTime.value = new Date().toLocaleTimeString()

        // 计算帧率（简化版本）
        const now = Date.now()
        if (directionStats[direction].lastFrameTime) {
          const interval = now - directionStats[direction].lastFrameTime
          const fps = Math.round(1000 / interval * 10) / 10
          directionStats[direction].frameRate = `${fps} fps`
        }
        directionStats[direction].lastFrameTime = now

        // 发出检测更新事件
        emit('detection-update', {
          direction,
          frameData: formattedFrameData,
          directionStats: directionStats[direction],
          globalStats: {
            totalVehicles: totalVehicleCount.value,
            peakDirection: getPeakDirection(),
            averageSpeed: getAverageFrameRate(),
            processingTime: getProcessingTime()
          }
        })

      } catch (error) {
        console.error(`❌ 处理${direction}方向帧数据失败:`, error)
        directionStats[direction].status = 'error'
      }
    }
    
    const getPeakDirection = () => {
      let maxCount = 0
      let peakDir = '-'
      
      Object.entries(directionStats).forEach(([dir, stats]) => {
        if (stats.vehicleCount > maxCount) {
          maxCount = stats.vehicleCount
          peakDir = getDirectionName(dir)
        }
      })
      
      return peakDir
    }
    
    const getDirectionName = (direction) => {
      const names = {
        north: '北向',
        south: '南向',
        east: '东向',
        west: '西向'
      }
      return names[direction] || direction
    }
    
    const getAverageFrameRate = () => {
      const rates = Object.values(directionStats)
        .map(stats => parseFloat(stats.frameRate))
        .filter(rate => !isNaN(rate))

      if (rates.length === 0) return 0

      const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length
      return Math.round(average * 10) / 10
    }

    // 新增的智能分析方法
    const getRecentIncrease = () => {
      // 模拟最近增长数据
      return Math.floor(Math.random() * 5) + 1
    }

    const getPeakDirectionPercentage = () => {
      const total = totalVehicleCount.value
      if (total === 0) return 0

      const maxCount = Math.max(...Object.values(directionStats).map(s => s.vehicleCount))
      return Math.round((maxCount / total) * 100)
    }

    const getEfficiencyLevel = () => {
      const avgRate = getAverageFrameRate()
      if (avgRate >= 25) return '高效'
      if (avgRate >= 15) return '正常'
      if (avgRate >= 10) return '较慢'
      return '低效'
    }

    const getCongestionLevel = () => {
      const total = totalVehicleCount.value
      if (total >= 50) return '严重拥堵'
      if (total >= 30) return '中度拥堵'
      if (total >= 15) return '轻度拥堵'
      return '畅通'
    }

    const getCongestionTrend = () => {
      const level = getCongestionLevel()
      if (level === '严重拥堵') return '↗️ 恶化'
      if (level === '中度拥堵') return '→ 稳定'
      if (level === '轻度拥堵') return '↘️ 改善'
      return '✅ 良好'
    }

    const getTrafficFlowBalance = () => {
      const counts = Object.values(directionStats).map(s => s.vehicleCount)
      const max = Math.max(...counts)
      const min = Math.min(...counts)

      if (max === 0) return 100

      const balance = ((max - min) / max) * 100
      return Math.round(100 - balance)
    }

    const getBalanceTrend = () => {
      const balance = getTrafficFlowBalance()
      if (balance >= 80) return '均衡'
      if (balance >= 60) return '较均衡'
      if (balance >= 40) return '不均衡'
      return '严重不均衡'
    }
    
    const getProcessingTime = () => {
      const startTimes = Object.values(directionStats)
        .map(stats => stats.lastUpdate)
        .filter(time => time)

      if (startTimes.length === 0) return 0

      const earliest = Math.min(...startTimes.map(time => time.getTime()))
      return Math.round((Date.now() - earliest) / 1000)
    }

    const getActiveDirectionsCount = () => {
      return Object.values(directionStats).filter(stats => stats.status !== 'waiting').length
    }

    // 处理四方向整体分析完成
    const handleAnalysisComplete = (completeData) => {
      try {
        console.log('🎉 四方向分析完成，准备跳转到智能分析模块')

        // 更新所有方向状态为完成
        Object.keys(directionStats).forEach(direction => {
          directionStats[direction].status = 'completed'
        })

        // 显示完成提示
        ElMessage({
          message: '所有四个方向的视频分析已完成，即将跳转到智能分析模块',
          type: 'success',
          duration: 3000
        })

        // 发出分析完成事件给父组件
        emit('analysis-complete', {
          taskId: props.taskId,
          summary: completeData.summary,
          message: completeData.message,
          timestamp: completeData.timestamp
        })

      } catch (error) {
        console.error('处理分析完成消息失败:', error)
        ElMessage.error('处理完成消息失败: ' + error.message)
      }
    }

    const initializeWebSocketConnection = async () => {
      try {
        console.log(`初始化四方向WebSocket连接: ${props.taskId}`)

        // 订阅四方向帧数据、进度和完成消息
        frameSubscription.value = await stompService.subscribeFourWayFrameUpdates(
          props.taskId,
          // 帧数据回调
          (frameData) => {
            const direction = frameData.direction
            if (direction && directionStats[direction] !== undefined) {
              handleFrameReceived(direction, frameData)
            }
          },
          // 进度数据回调
          (progressData) => {
            console.log('收到四方向进度数据:', progressData)
            // 处理进度更新
            if (progressData.direction && directionStats[progressData.direction]) {
              const direction = progressData.direction
              const dirStats = directionStats[direction]

              // 更新状态
              if (progressData.status) {
                dirStats.status = progressData.status
              }

              // 更新帧数信息
              if (progressData.currentFrame !== undefined) {
                dirStats.currentFrame = progressData.currentFrame
              }
              if (progressData.totalFrames !== undefined) {
                dirStats.totalFrames = progressData.totalFrames
              }

              // 更新进度百分比
              if (progressData.progress !== undefined) {
                dirStats.progress = progressData.progress
              }

              console.log(`✓ 更新${direction}方向进度:`, {
                status: dirStats.status,
                currentFrame: dirStats.currentFrame,
                totalFrames: dirStats.totalFrames,
                progress: dirStats.progress
              })
            }
          },
          // 整体完成回调
          (completeData) => {
            console.log('✓ 收到四方向整体分析完成消息:', completeData)
            handleAnalysisComplete(completeData)
          }
        )

        isConnected.value = true
        ElMessage.success('四方向实时检测连接成功')

      } catch (error) {
        console.error('WebSocket连接失败:', error)
        ElMessage.error('连接失败: ' + error.message)
        isConnected.value = false
      }
    }
    
    const cleanup = () => {
      // 清理WebSocket订阅
      if (frameSubscription.value) {
        stompService.unsubscribe(frameSubscription.value)
        frameSubscription.value = null
      }
      
      // 清理各方向的帧缓冲
      const directions = ['north', 'south', 'east', 'west']
      directions.forEach(direction => {
        const taskId = getDirectionTaskId(direction)
        stompService.clearFrameBuffer(taskId)
      })
      
      isConnected.value = false
    }
    
    // 监听taskId变化
    watch(() => props.taskId, (newTaskId) => {
      if (newTaskId) {
        cleanup()
        initializeWebSocketConnection()
      }
    })
    
    // 生命周期
    onMounted(() => {
      if (props.taskId && props.autoStart) {
        initializeWebSocketConnection()
      }
    })
    
    onUnmounted(() => {
      cleanup()
    })
    
    // 处理建议应用
    const handleApplySuggestion = (suggestion) => {
      console.log('应用交通管理建议:', suggestion)

      ElMessage({
        message: `正在应用建议: ${suggestion.title}`,
        type: 'info',
        duration: 2000
      })

      // 这里可以添加实际的建议应用逻辑
      // 例如调用API来调整信号灯配时等

      // 发出事件给父组件
      emit('apply-suggestion', suggestion)
    }

    // 暴露方法给父组件
    const startDetection = () => {
      initializeWebSocketConnection()
    }

    const stopDetection = () => {
      cleanup()
    }
    
    return {
      // 响应式数据
      isConnected,
      lastUpdateTime,
      showProgress,
      northViewer,
      southViewer,
      eastViewer,
      westViewer,
      directionStats,
      directionFrameData,

      // 计算属性
      connectionStatusType,
      connectionStatusText,
      totalVehicleCount,
      overallProgress,
      progressStatus,

      // 方法
      getDirectionStatusType,
      getDirectionStatusText,
      handleFrameReceived,
      handleAnalysisComplete,
      getPeakDirection,
      getDirectionName,
      getAverageFrameRate,
      getProcessingTime,
      getActiveDirectionsCount,
      getRecentIncrease,
      getPeakDirectionPercentage,
      getEfficiencyLevel,
      getCongestionLevel,
      getCongestionTrend,
      getTrafficFlowBalance,
      getBalanceTrend,
      handleApplySuggestion,
      cleanup,
      startDetection,
      stopDetection
    }
  }
}
</script>

<style scoped>
.four-way-realtime-viewer {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.connection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.last-update {
  font-size: 14px;
  color: #6b7280;
}

.detection-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  grid-template-rows: 1fr 1fr !important;
  gap: 24px !important;
  margin-bottom: 24px;
  min-height: 600px;
}

.detection-item {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.detection-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.detection-item.north {
  grid-column: 1 !important;
  grid-row: 1 !important;
}

.detection-item.east {
  grid-column: 2 !important;
  grid-row: 1 !important;
}

.detection-item.west {
  grid-column: 1 !important;
  grid-row: 2 !important;
}

.detection-item.south {
  grid-column: 2 !important;
  grid-row: 2 !important;
}



.direction-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.direction-label {
  font-weight: 500;
  color: #374151;
}

.detection-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.direction-viewer {
  flex: 1;
  min-height: 300px;
  margin-bottom: 12px;
}

/* 总览统计样式 */
.overview-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.total-vehicles-summary {
  display: flex;
  align-items: center;
  gap: 12px;
}

.total-vehicles-summary .el-icon {
  font-size: 24px;
}

.total-count {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
}

.total-label {
  font-size: 14px;
  opacity: 0.9;
}

.peak-direction-info {
  font-size: 14px;
  font-weight: 500;
}

.peak-text {
  opacity: 0.95;
}

.direction-stats {
  display: flex;
  justify-content: space-around;
  padding: 8px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.detection-progress {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.progress-percentage {
  font-size: 20px;
  font-weight: 700;
  color: #3b82f6;
  transition: all 0.3s ease;
}

.animated-progress {
  margin-bottom: 8px;
}

.animated-progress :deep(.el-progress-bar__outer) {
  background-color: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.animated-progress :deep(.el-progress-bar__inner) {
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #3b82f6 100%);
  background-size: 200% 100%;
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.progress-details {
  text-align: center;
}

.progress-info {
  font-size: 14px;
  color: #6b7280;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 增强统计信息样式 */
.enhanced-global-stats {
  margin-top: 24px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.connection-status-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.connection-status-info .el-icon {
  font-size: 16px;
}

.connection-status-info .el-icon.connected {
  color: #10b981;
}

.connection-status-info .el-icon.disconnected {
  color: #ef4444;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
  border-left-color: #3b82f6;
}

.stat-card.success {
  border-left-color: #10b981;
}

.stat-card.warning {
  border-left-color: #f59e0b;
}

.stat-card.info {
  border-left-color: #06b6d4;
}

.stat-card.danger {
  border-left-color: #ef4444;
}

.stat-card.purple {
  border-left-color: #8b5cf6;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.stat-trend {
  font-size: 11px;
  font-weight: 500;
  margin-top: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  background: #f3f4f6;
  color: #6b7280;
}

.stat-trend.positive {
  background: #dcfce7;
  color: #16a34a;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detection-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
  }
  
  .detection-item {
    grid-column: 1 !important;
    grid-row: auto !important;
  }
}

@media (max-width: 768px) {
  .four-way-realtime-viewer {
    padding: 16px;
  }
  
  .detection-grid {
    gap: 16px;
  }
  
  .direction-viewer {
    min-height: 150px;
  }
}
</style>
