{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"decision-support-panel\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"title\"\n};\nconst _hoisted_4 = {\n  class: \"panel-actions\"\n};\nconst _hoisted_5 = {\n  class: \"decision-status\"\n};\nconst _hoisted_6 = {\n  class: \"suggestions-section\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"no-suggestions\"\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"suggestions-list\"\n};\nconst _hoisted_9 = {\n  class: \"suggestion-header\"\n};\nconst _hoisted_10 = {\n  class: \"suggestion-title\"\n};\nconst _hoisted_11 = {\n  class: \"title-text\"\n};\nconst _hoisted_12 = {\n  class: \"suggestion-actions\"\n};\nconst _hoisted_13 = {\n  class: \"suggestion-content\"\n};\nconst _hoisted_14 = {\n  class: \"description\"\n};\nconst _hoisted_15 = {\n  class: \"suggestion-meta\"\n};\nconst _hoisted_16 = {\n  class: \"confidence\"\n};\nconst _hoisted_17 = {\n  class: \"impact\"\n};\nconst _hoisted_18 = {\n  class: \"time\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"realtime-support\"\n};\nconst _hoisted_20 = {\n  class: \"support-metric\"\n};\nconst _hoisted_21 = {\n  class: \"support-metric\"\n};\nconst _hoisted_22 = {\n  class: \"support-metric\"\n};\nconst _hoisted_23 = {\n  class: \"metric-value\"\n};\nconst _hoisted_24 = {\n  key: 0,\n  class: \"recommended-actions\"\n};\nconst _hoisted_25 = {\n  key: 0,\n  class: \"suggestion-detail\"\n};\nconst _hoisted_26 = {\n  key: 0,\n  class: \"risk-assessment\"\n};\nconst _hoisted_27 = {\n  key: 1,\n  class: \"applicable-conditions\"\n};\nconst _hoisted_28 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BrainIcon = _resolveComponent(\"BrainIcon\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_LightbulbIcon = _resolveComponent(\"LightbulbIcon\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_TrendCharts = _resolveComponent(\"TrendCharts\");\n  const _component_DataAnalysis = _resolveComponent(\"DataAnalysis\");\n  const _component_Clock = _resolveComponent(\"Clock\");\n  const _component_Monitor = _resolveComponent(\"Monitor\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"panel-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"span\", _hoisted_3, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_BrainIcon)]),\n      _: 1 /* STABLE */\n    }), _cache[2] || (_cache[2] = _createTextVNode(\" 智能决策支持 \"))]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: _ctx.refreshSuggestions,\n      loading: $setup.loading\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Refresh)]),\n        _: 1 /* STABLE */\n      }), _cache[3] || (_cache[3] = _createTextVNode(\" 刷新建议 \"))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_alert, {\n      title: $setup.alertTitle,\n      type: $setup.alertType,\n      description: $setup.alertDescription,\n      \"show-icon\": \"\",\n      closable: false\n    }, null, 8 /* PROPS */, [\"title\", \"type\", \"description\"])]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"h4\", null, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_LightbulbIcon)]),\n      _: 1 /* STABLE */\n    }), _createTextVNode(\" 智能建议 (\" + _toDisplayString($setup.suggestions.length) + \") \", 1 /* TEXT */)]), $setup.suggestions.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createVNode(_component_el_empty, {\n      description: \"暂无决策建议\"\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.suggestions, suggestion => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: suggestion.id,\n        class: _normalizeClass([\"suggestion-item\", [`priority-${suggestion.priority}`]])\n      }, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_tag, {\n        type: _ctx.getPriorityTagType(suggestion.priority),\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.getPriorityText(suggestion.priority)), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"]), _createElementVNode(\"span\", _hoisted_11, _toDisplayString(suggestion.title), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"small\",\n        onClick: $event => _ctx.applySuggestion(suggestion),\n        loading: $setup.applyingIds.includes(suggestion.id)\n      }, {\n        default: _withCtx(() => [...(_cache[4] || (_cache[4] = [_createTextVNode(\" 应用 \")]))]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: $event => _ctx.viewSuggestionDetail(suggestion)\n      }, {\n        default: _withCtx(() => [...(_cache[5] || (_cache[5] = [_createTextVNode(\" 详情 \")]))]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"p\", _hoisted_14, _toDisplayString(suggestion.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"span\", _hoisted_16, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_TrendCharts)]),\n        _: 1 /* STABLE */\n      }), _createTextVNode(\" 置信度: \" + _toDisplayString((suggestion.confidence * 100).toFixed(0)) + \"% \", 1 /* TEXT */)]), _createElementVNode(\"span\", _hoisted_17, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_DataAnalysis)]),\n        _: 1 /* STABLE */\n      }), _createTextVNode(\" 影响评分: \" + _toDisplayString(suggestion.impact_score.toFixed(1)) + \"/10 \", 1 /* TEXT */)]), _createElementVNode(\"span\", _hoisted_18, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Clock)]),\n        _: 1 /* STABLE */\n      }), _createTextVNode(\" \" + _toDisplayString(suggestion.implementation_time), 1 /* TEXT */)])])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]))]), $setup.realtimeSupport ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"h4\", null, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Monitor)]),\n      _: 1 /* STABLE */\n    }), _cache[6] || (_cache[6] = _createTextVNode(\" 实时分析 \"))]), _createVNode(_component_el_row, {\n      gutter: 16\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_20, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n          class: \"metric-label\"\n        }, \"拥堵等级\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"metric-value\", `congestion-${$setup.realtimeSupport.current_congestion?.overall_level}`])\n        }, _toDisplayString(_ctx.getCongestionText($setup.realtimeSupport.current_congestion?.overall_level)), 3 /* TEXT, CLASS */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_21, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n          class: \"metric-label\"\n        }, \"警报级别\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"metric-value\", `alert-${$setup.realtimeSupport.alert_level}`])\n        }, _toDisplayString(_ctx.getAlertText($setup.realtimeSupport.alert_level)), 3 /* TEXT, CLASS */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_22, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n          class: \"metric-label\"\n        }, \"下次更新\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_23, _toDisplayString($setup.realtimeSupport.next_update_in) + \"秒 \", 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 推荐行动 \"), $setup.realtimeSupport.recommended_actions?.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_cache[10] || (_cache[10] = _createElementVNode(\"h5\", null, \"推荐行动:\", -1 /* HOISTED */)), _createElementVNode(\"ul\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.realtimeSupport.recommended_actions, action => {\n      return _openBlock(), _createElementBlock(\"li\", {\n        key: action\n      }, _toDisplayString(action), 1 /* TEXT */);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 建议详情对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.showDetailDialog = $event),\n    title: \"决策建议详情\",\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_28, [_createVNode(_component_el_button, {\n      onClick: _cache[0] || (_cache[0] = $event => $setup.showDetailDialog = false)\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"关闭\")])),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _ctx.applySuggestionFromDialog,\n      loading: $setup.applyingIds.includes($setup.selectedSuggestion?.id)\n    }, {\n      default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\" 应用建议 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [$setup.selectedSuggestion ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [_createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"建议类型\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSuggestion.type), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"优先级\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: _ctx.getPriorityTagType($setup.selectedSuggestion.priority)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.getPriorityText($setup.selectedSuggestion.priority)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"置信度\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(($setup.selectedSuggestion.confidence * 100).toFixed(0)) + \"% \", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"影响评分\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSuggestion.impact_score.toFixed(1)) + \"/10 \", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"实施时间\",\n        span: \"2\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSuggestion.implementation_time), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"预期效果\",\n        span: \"2\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSuggestion.expected_effect), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"具体行动\",\n        span: \"2\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSuggestion.action), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 风险评估 \"), $setup.selectedSuggestion.risk_assessment ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [_cache[11] || (_cache[11] = _createElementVNode(\"h4\", null, \"风险评估\", -1 /* HOISTED */)), _createVNode(_component_el_alert, {\n      title: `风险等级: ${$setup.selectedSuggestion.risk_assessment.level}`,\n      type: _ctx.getRiskAlertType($setup.selectedSuggestion.risk_assessment.level),\n      description: $setup.selectedSuggestion.risk_assessment.mitigation,\n      \"show-icon\": \"\"\n    }, null, 8 /* PROPS */, [\"title\", \"type\", \"description\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 适用条件 \"), $setup.selectedSuggestion.applicable_conditions?.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [_cache[12] || (_cache[12] = _createElementVNode(\"h4\", null, \"适用条件\", -1 /* HOISTED */)), _createElementVNode(\"ul\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.selectedSuggestion.applicable_conditions, condition => {\n      return _openBlock(), _createElementBlock(\"li\", {\n        key: condition\n      }, _toDisplayString(condition), 1 /* TEXT */);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_component_el_icon", "default", "_component_BrainIcon", "_", "_createTextVNode", "_hoisted_4", "_component_el_button", "size", "onClick", "_ctx", "refreshSuggestions", "loading", "$setup", "_component_Refresh", "_hoisted_5", "_component_el_alert", "title", "alertTitle", "type", "alertType", "description", "alertDescription", "closable", "_hoisted_6", "_component_LightbulbIcon", "_toDisplayString", "suggestions", "length", "_hoisted_7", "_component_el_empty", "_hoisted_8", "_Fragment", "_renderList", "suggestion", "id", "_normalizeClass", "priority", "_hoisted_9", "_hoisted_10", "_component_el_tag", "getPriorityTagType", "getPriorityText", "_hoisted_11", "_hoisted_12", "$event", "applySuggestion", "applyingIds", "includes", "_cache", "viewSuggestionDetail", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_component_Trend<PERSON><PERSON>s", "confidence", "toFixed", "_hoisted_17", "_component_DataAnalysis", "impact_score", "_hoisted_18", "_component_Clock", "implementation_time", "realtimeSupport", "_hoisted_19", "_component_Monitor", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_20", "current_congestion", "overall_level", "getCongestionText", "_hoisted_21", "alert_level", "getAlertText", "_hoisted_22", "_hoisted_23", "next_update_in", "_createCommentVNode", "recommended_actions", "_hoisted_24", "action", "_component_el_dialog", "modelValue", "showDetailDialog", "width", "footer", "_hoisted_28", "applySuggestionFromDialog", "selectedSuggestion", "_hoisted_25", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "label", "expected_effect", "risk_assessment", "_hoisted_26", "level", "getRiskAlertType", "mitigation", "applicable_conditions", "_hoisted_27", "condition"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\DecisionSupportPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"decision-support-panel\">\n    <el-card class=\"panel-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <span class=\"title\">\n            <el-icon><BrainIcon /></el-icon>\n            智能决策支持\n          </span>\n          <div class=\"panel-actions\">\n            <el-button \n              size=\"small\" \n              @click=\"refreshSuggestions\"\n              :loading=\"loading\"\n            >\n              <el-icon><Refresh /></el-icon>\n              刷新建议\n            </el-button>\n          </div>\n        </div>\n      </template>\n\n      <!-- 实时决策状态 -->\n      <div class=\"decision-status\">\n        <el-alert\n          :title=\"alertTitle\"\n          :type=\"alertType\"\n          :description=\"alertDescription\"\n          show-icon\n          :closable=\"false\"\n        />\n      </div>\n\n      <!-- 决策建议列表 -->\n      <div class=\"suggestions-section\">\n        <h4>\n          <el-icon><LightbulbIcon /></el-icon>\n          智能建议 ({{ suggestions.length }})\n        </h4>\n        \n        <div v-if=\"suggestions.length === 0\" class=\"no-suggestions\">\n          <el-empty description=\"暂无决策建议\" />\n        </div>\n        \n        <div v-else class=\"suggestions-list\">\n          <div \n            v-for=\"suggestion in suggestions\" \n            :key=\"suggestion.id\"\n            class=\"suggestion-item\"\n            :class=\"[`priority-${suggestion.priority}`]\"\n          >\n            <div class=\"suggestion-header\">\n              <div class=\"suggestion-title\">\n                <el-tag \n                  :type=\"getPriorityTagType(suggestion.priority)\" \n                  size=\"small\"\n                >\n                  {{ getPriorityText(suggestion.priority) }}\n                </el-tag>\n                <span class=\"title-text\">{{ suggestion.title }}</span>\n              </div>\n              <div class=\"suggestion-actions\">\n                <el-button \n                  type=\"primary\" \n                  size=\"small\"\n                  @click=\"applySuggestion(suggestion)\"\n                  :loading=\"applyingIds.includes(suggestion.id)\"\n                >\n                  应用\n                </el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"viewSuggestionDetail(suggestion)\"\n                >\n                  详情\n                </el-button>\n              </div>\n            </div>\n            \n            <div class=\"suggestion-content\">\n              <p class=\"description\">{{ suggestion.description }}</p>\n              <div class=\"suggestion-meta\">\n                <span class=\"confidence\">\n                  <el-icon><TrendCharts /></el-icon>\n                  置信度: {{ (suggestion.confidence * 100).toFixed(0) }}%\n                </span>\n                <span class=\"impact\">\n                  <el-icon><DataAnalysis /></el-icon>\n                  影响评分: {{ suggestion.impact_score.toFixed(1) }}/10\n                </span>\n                <span class=\"time\">\n                  <el-icon><Clock /></el-icon>\n                  {{ suggestion.implementation_time }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 实时决策支持数据 -->\n      <div class=\"realtime-support\" v-if=\"realtimeSupport\">\n        <h4>\n          <el-icon><Monitor /></el-icon>\n          实时分析\n        </h4>\n        \n        <el-row :gutter=\"16\">\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">拥堵等级</div>\n              <div class=\"metric-value\" :class=\"`congestion-${realtimeSupport.current_congestion?.overall_level}`\">\n                {{ getCongestionText(realtimeSupport.current_congestion?.overall_level) }}\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">警报级别</div>\n              <div class=\"metric-value\" :class=\"`alert-${realtimeSupport.alert_level}`\">\n                {{ getAlertText(realtimeSupport.alert_level) }}\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">下次更新</div>\n              <div class=\"metric-value\">\n                {{ realtimeSupport.next_update_in }}秒\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n\n        <!-- 推荐行动 -->\n        <div class=\"recommended-actions\" v-if=\"realtimeSupport.recommended_actions?.length\">\n          <h5>推荐行动:</h5>\n          <ul>\n            <li v-for=\"action in realtimeSupport.recommended_actions\" :key=\"action\">\n              {{ action }}\n            </li>\n          </ul>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 建议详情对话框 -->\n    <el-dialog\n      v-model=\"showDetailDialog\"\n      title=\"决策建议详情\"\n      width=\"600px\"\n    >\n      <div v-if=\"selectedSuggestion\" class=\"suggestion-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"建议类型\">\n            {{ selectedSuggestion.type }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"优先级\">\n            <el-tag :type=\"getPriorityTagType(selectedSuggestion.priority)\">\n              {{ getPriorityText(selectedSuggestion.priority) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"置信度\">\n            {{ (selectedSuggestion.confidence * 100).toFixed(0) }}%\n          </el-descriptions-item>\n          <el-descriptions-item label=\"影响评分\">\n            {{ selectedSuggestion.impact_score.toFixed(1) }}/10\n          </el-descriptions-item>\n          <el-descriptions-item label=\"实施时间\" span=\"2\">\n            {{ selectedSuggestion.implementation_time }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"预期效果\" span=\"2\">\n            {{ selectedSuggestion.expected_effect }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"具体行动\" span=\"2\">\n            {{ selectedSuggestion.action }}\n          </el-descriptions-item>\n        </el-descriptions>\n\n        <!-- 风险评估 -->\n        <div v-if=\"selectedSuggestion.risk_assessment\" class=\"risk-assessment\">\n          <h4>风险评估</h4>\n          <el-alert\n            :title=\"`风险等级: ${selectedSuggestion.risk_assessment.level}`\"\n            :type=\"getRiskAlertType(selectedSuggestion.risk_assessment.level)\"\n            :description=\"selectedSuggestion.risk_assessment.mitigation\"\n            show-icon\n          />\n        </div>\n\n        <!-- 适用条件 -->\n        <div v-if=\"selectedSuggestion.applicable_conditions?.length\" class=\"applicable-conditions\">\n          <h4>适用条件</h4>\n          <ul>\n            <li v-for=\"condition in selectedSuggestion.applicable_conditions\" :key=\"condition\">\n              {{ condition }}\n            </li>\n          </ul>\n        </div>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n          <el-button \n            type=\"primary\" \n            @click=\"applySuggestionFromDialog\"\n            :loading=\"applyingIds.includes(selectedSuggestion?.id)\"\n          >\n            应用建议\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { \n  BrainIcon, \n  Refresh, \n  LightbulbIcon, \n  TrendCharts, \n  DataAnalysis, \n  Clock,\n  Monitor\n} from '@element-plus/icons-vue'\nimport decisionApi from '@/api/decision'\nimport stompService from '@/utils/stomp-service'\n\nexport default {\n  name: 'DecisionSupportPanel',\n  components: {\n    BrainIcon,\n    Refresh,\n    LightbulbIcon,\n    TrendCharts,\n    DataAnalysis,\n    Clock,\n    Monitor\n  },\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['suggestion-applied', 'decision-made'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const loading = ref(false)\n    const suggestions = ref([])\n    const realtimeSupport = ref(null)\n    const applyingIds = ref([])\n    const showDetailDialog = ref(false)\n    const selectedSuggestion = ref(null)\n    \n    // WebSocket订阅\n    let decisionSubscription = null\n    \n    // 计算属性\n    const alertTitle = computed(() => {\n      if (!realtimeSupport.value) return '等待决策数据...'\n      \n      const level = realtimeSupport.value.alert_level\n      const titles = {\n        'green': '交通状况良好',\n        'yellow': '交通状况一般',\n        'orange': '交通拥堵',\n        'red': '严重拥堵'\n      }\n      return titles[level] || '状态未知'\n    })\n    \n    const alertType = computed(() => {\n      if (!realtimeSupport.value) return 'info'\n      \n      const level = realtimeSupport.value.alert_level\n      const types = {\n        'green': 'success',\n        'yellow': 'warning',\n        'orange': 'warning',\n        'red': 'error'\n      }\n      return types[level] || 'info'\n    })\n    \n    const alertDescription = computed(() => {\n      if (!realtimeSupport.value) return ''\n      \n      const congestion = realtimeSupport.value.current_congestion\n      if (congestion) {\n        return `整体拥堵评分: ${(congestion.congestion_score * 100).toFixed(0)}%`\n      }\n      return ''\n    })\n    \n    return {\n      loading,\n      suggestions,\n      realtimeSupport,\n      applyingIds,\n      showDetailDialog,\n      selectedSuggestion,\n      alertTitle,\n      alertType,\n      alertDescription\n    }\n  }\n}\n</script>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAGxBA,KAAK,EAAC;AAAa;;EAChBA,KAAK,EAAC;AAAO;;EAIdA,KAAK,EAAC;AAAe;;EAczBA,KAAK,EAAC;AAAiB;;EAWvBA,KAAK,EAAC;AAAqB;;EAlCtCC,GAAA;EAwC6CD,KAAK,EAAC;;;EAxCnDC,GAAA;EA4CoBD,KAAK,EAAC;;;EAOTA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAkB;;EAOrBA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAoB;;EAkB5BA,KAAK,EAAC;AAAoB;;EAC1BA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAiB;;EACpBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAQ;;EAIdA,KAAK,EAAC;AAAM;;EA1FlCC,GAAA;EAqGWD,KAAK,EAAC;;;EAQAA,KAAK,EAAC;AAAgB;;EAQtBA,KAAK,EAAC;AAAgB;;EAQtBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAc;;EA/HvCC,GAAA;EAuIaD,KAAK,EAAC;;;EAvInBC,GAAA;EAwJqCD,KAAK,EAAC;;;EAxJ3CC,GAAA;EAoLuDD,KAAK,EAAC;;;EApL7DC,GAAA;EA+LqED,KAAK,EAAC;;;EAW7DA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;uBAzMjCE,mBAAA,CAqNM,OArNNC,UAqNM,GApNJC,YAAA,CA8IUC,kBAAA;IA9IDL,KAAK,EAAC;EAAY;IACdM,MAAM,EAAAC,QAAA,CACf,MAeM,CAfNC,mBAAA,CAeM,OAfNC,UAeM,GAdJD,mBAAA,CAGO,QAHPE,UAGO,GAFLN,YAAA,CAAgCO,kBAAA;MAN5CC,OAAA,EAAAL,QAAA,CAMqB,MAAa,CAAbH,YAAA,CAAaS,oBAAA,E;MANlCC,CAAA;kCAAAC,gBAAA,CAM4C,UAElC,G,GACAP,mBAAA,CASM,OATNQ,UASM,GARJZ,YAAA,CAOYa,oBAAA;MANVC,IAAI,EAAC,OAAO;MACXC,OAAK,EAAEC,IAAA,CAAAC,kBAAkB;MACzBC,OAAO,EAAEC,MAAA,CAAAD;;MAbxBV,OAAA,EAAAL,QAAA,CAec,MAA8B,CAA9BH,YAAA,CAA8BO,kBAAA;QAf5CC,OAAA,EAAAL,QAAA,CAeuB,MAAW,CAAXH,YAAA,CAAWoB,kBAAA,E;QAflCV,CAAA;oCAAAC,gBAAA,CAe4C,QAEhC,G;MAjBZD,CAAA;;IAAAF,OAAA,EAAAL,QAAA,CAuBM,MAQM,CARNC,mBAAA,CAQM,OARNiB,UAQM,GAPJrB,YAAA,CAMEsB,mBAAA;MALCC,KAAK,EAAEJ,MAAA,CAAAK,UAAU;MACjBC,IAAI,EAAEN,MAAA,CAAAO,SAAS;MACfC,WAAW,EAAER,MAAA,CAAAS,gBAAgB;MAC9B,WAAS,EAAT,EAAS;MACRC,QAAQ,EAAE;iEAKfzB,mBAAA,CAgEM,OAhEN0B,UAgEM,GA/DJ1B,mBAAA,CAGK,aAFHJ,YAAA,CAAoCO,kBAAA;MApC9CC,OAAA,EAAAL,QAAA,CAoCmB,MAAiB,CAAjBH,YAAA,CAAiB+B,wBAAA,E;MApCpCrB,CAAA;QAAAC,gBAAA,CAoC8C,SAC9B,GAAAqB,gBAAA,CAAGb,MAAA,CAAAc,WAAW,CAACC,MAAM,IAAG,IAChC,gB,GAEWf,MAAA,CAAAc,WAAW,CAACC,MAAM,U,cAA7BpC,mBAAA,CAEM,OAFNqC,UAEM,GADJnC,YAAA,CAAiCoC,mBAAA;MAAvBT,WAAW,EAAC;IAAQ,G,oBAGhC7B,mBAAA,CAqDM,OArDNuC,UAqDM,I,kBApDJvC,mBAAA,CAmDMwC,SAAA,QAhGhBC,WAAA,CA8CiCpB,MAAA,CAAAc,WAAW,EAAzBO,UAAU;2BADnB1C,mBAAA,CAmDM;QAjDHD,GAAG,EAAE2C,UAAU,CAACC,EAAE;QACnB7C,KAAK,EAhDjB8C,eAAA,EAgDkB,iBAAiB,eACFF,UAAU,CAACG,QAAQ;UAExCvC,mBAAA,CA0BM,OA1BNwC,UA0BM,GAzBJxC,mBAAA,CAQM,OARNyC,WAQM,GAPJ7C,YAAA,CAKS8C,iBAAA;QAJNrB,IAAI,EAAET,IAAA,CAAA+B,kBAAkB,CAACP,UAAU,CAACG,QAAQ;QAC7C7B,IAAI,EAAC;;QAvDvBN,OAAA,EAAAL,QAAA,CAyDkB,MAA0C,CAzD5DQ,gBAAA,CAAAqB,gBAAA,CAyDqBhB,IAAA,CAAAgC,eAAe,CAACR,UAAU,CAACG,QAAQ,kB;QAzDxDjC,CAAA;qDA2DgBN,mBAAA,CAAsD,QAAtD6C,WAAsD,EAAAjB,gBAAA,CAA1BQ,UAAU,CAACjB,KAAK,iB,GAE9CnB,mBAAA,CAeM,OAfN8C,WAeM,GAdJlD,YAAA,CAOYa,oBAAA;QANVY,IAAI,EAAC,SAAS;QACdX,IAAI,EAAC,OAAO;QACXC,OAAK,EAAAoC,MAAA,IAAEnC,IAAA,CAAAoC,eAAe,CAACZ,UAAU;QACjCtB,OAAO,EAAEC,MAAA,CAAAkC,WAAW,CAACC,QAAQ,CAACd,UAAU,CAACC,EAAE;;QAlE9DjC,OAAA,EAAAL,QAAA,CAmEiB,MAED,KAAAoD,MAAA,QAAAA,MAAA,OArEhB5C,gBAAA,CAmEiB,MAED,E;QArEhBD,CAAA;mEAsEgBV,YAAA,CAKYa,oBAAA;QAJVC,IAAI,EAAC,OAAO;QACXC,OAAK,EAAAoC,MAAA,IAAEnC,IAAA,CAAAwC,oBAAoB,CAAChB,UAAU;;QAxEzDhC,OAAA,EAAAL,QAAA,CAyEiB,MAED,KAAAoD,MAAA,QAAAA,MAAA,OA3EhB5C,gBAAA,CAyEiB,MAED,E;QA3EhBD,CAAA;4DA+EYN,mBAAA,CAgBM,OAhBNqD,WAgBM,GAfJrD,mBAAA,CAAuD,KAAvDsD,WAAuD,EAAA1B,gBAAA,CAA7BQ,UAAU,CAACb,WAAW,kBAChDvB,mBAAA,CAaM,OAbNuD,WAaM,GAZJvD,mBAAA,CAGO,QAHPwD,WAGO,GAFL5D,YAAA,CAAkCO,kBAAA;QAnFpDC,OAAA,EAAAL,QAAA,CAmF2B,MAAe,CAAfH,YAAA,CAAe6D,sBAAA,E;QAnF1CnD,CAAA;UAAAC,gBAAA,CAmFoD,QAC7B,GAAAqB,gBAAA,EAAIQ,UAAU,CAACsB,UAAU,QAAQC,OAAO,OAAM,IACrD,gB,GACA3D,mBAAA,CAGO,QAHP4D,WAGO,GAFLhE,YAAA,CAAmCO,kBAAA;QAvFrDC,OAAA,EAAAL,QAAA,CAuF2B,MAAgB,CAAhBH,YAAA,CAAgBiE,uBAAA,E;QAvF3CvD,CAAA;UAAAC,gBAAA,CAuFqD,SAC7B,GAAAqB,gBAAA,CAAGQ,UAAU,CAAC0B,YAAY,CAACH,OAAO,OAAM,MAChD,gB,GACA3D,mBAAA,CAGO,QAHP+D,WAGO,GAFLnE,YAAA,CAA4BO,kBAAA;QA3F9CC,OAAA,EAAAL,QAAA,CA2F2B,MAAS,CAATH,YAAA,CAASoE,gBAAA,E;QA3FpC1D,CAAA;UAAAC,gBAAA,CA2F8C,GAC5B,GAAAqB,gBAAA,CAAGQ,UAAU,CAAC6B,mBAAmB,iB;yCASTlD,MAAA,CAAAmD,eAAe,I,cAAnDxE,mBAAA,CA0CM,OA1CNyE,WA0CM,GAzCJnE,mBAAA,CAGK,aAFHJ,YAAA,CAA8BO,kBAAA;MAvGxCC,OAAA,EAAAL,QAAA,CAuGmB,MAAW,CAAXH,YAAA,CAAWwE,kBAAA,E;MAvG9B9D,CAAA;kCAAAC,gBAAA,CAuGwC,QAEhC,G,GAEAX,YAAA,CAyBSyE,iBAAA;MAzBAC,MAAM,EAAE;IAAE;MA3G3BlE,OAAA,EAAAL,QAAA,CA4GU,MAOS,CAPTH,YAAA,CAOS2E,iBAAA;QAPAC,IAAI,EAAE;MAAC;QA5G1BpE,OAAA,EAAAL,QAAA,CA6GY,MAKM,CALNC,mBAAA,CAKM,OALNyE,WAKM,G,0BAJJzE,mBAAA,CAAoC;UAA/BR,KAAK,EAAC;QAAc,GAAC,MAAI,sBAC9BQ,mBAAA,CAEM;UAFDR,KAAK,EA/GxB8C,eAAA,EA+GyB,cAAc,gBAAuBvB,MAAA,CAAAmD,eAAe,CAACQ,kBAAkB,EAAEC,aAAa;4BAC5F/D,IAAA,CAAAgE,iBAAiB,CAAC7D,MAAA,CAAAmD,eAAe,CAACQ,kBAAkB,EAAEC,aAAa,yB;QAhHtFrE,CAAA;UAoHUV,YAAA,CAOS2E,iBAAA;QAPAC,IAAI,EAAE;MAAC;QApH1BpE,OAAA,EAAAL,QAAA,CAqHY,MAKM,CALNC,mBAAA,CAKM,OALN6E,WAKM,G,0BAJJ7E,mBAAA,CAAoC;UAA/BR,KAAK,EAAC;QAAc,GAAC,MAAI,sBAC9BQ,mBAAA,CAEM;UAFDR,KAAK,EAvHxB8C,eAAA,EAuHyB,cAAc,WAAkBvB,MAAA,CAAAmD,eAAe,CAACY,WAAW;4BACjElE,IAAA,CAAAmE,YAAY,CAAChE,MAAA,CAAAmD,eAAe,CAACY,WAAW,yB;QAxH3DxE,CAAA;UA4HUV,YAAA,CAOS2E,iBAAA;QAPAC,IAAI,EAAE;MAAC;QA5H1BpE,OAAA,EAAAL,QAAA,CA6HY,MAKM,CALNC,mBAAA,CAKM,OALNgF,WAKM,G,0BAJJhF,mBAAA,CAAoC;UAA/BR,KAAK,EAAC;QAAc,GAAC,MAAI,sBAC9BQ,mBAAA,CAEM,OAFNiF,WAEM,EAAArD,gBAAA,CADDb,MAAA,CAAAmD,eAAe,CAACgB,cAAc,IAAG,IACtC,gB;QAjId5E,CAAA;;MAAAA,CAAA;QAsIQ6E,mBAAA,UAAa,EAC0BpE,MAAA,CAAAmD,eAAe,CAACkB,mBAAmB,EAAEtD,MAAM,I,cAAlFpC,mBAAA,CAOM,OAPN2F,WAOM,G,4BANJrF,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAIK,c,kBAHHN,mBAAA,CAEKwC,SAAA,QA5IjBC,WAAA,CA0IiCpB,MAAA,CAAAmD,eAAe,CAACkB,mBAAmB,EAA7CE,MAAM;2BAAjB5F,mBAAA,CAEK;QAFsDD,GAAG,EAAE6F;MAAM,GAAA1D,gBAAA,CACjE0D,MAAM;0CA3IvBH,mBAAA,e,KAAAA,mBAAA,e;IAAA7E,CAAA;MAkJI6E,mBAAA,aAAgB,EAChBvF,YAAA,CAkEY2F,oBAAA;IArNhBC,UAAA,EAoJezE,MAAA,CAAA0E,gBAAgB;IApJ/B,uBAAAtC,MAAA,QAAAA,MAAA,MAAAJ,MAAA,IAoJehC,MAAA,CAAA0E,gBAAgB,GAAA1C,MAAA;IACzB5B,KAAK,EAAC,QAAQ;IACduE,KAAK,EAAC;;IAmDKC,MAAM,EAAA5F,QAAA,CACf,MASO,CATPC,mBAAA,CASO,QATP4F,WASO,GARLhG,YAAA,CAA2Da,oBAAA;MAA/CE,OAAK,EAAAwC,MAAA,QAAAA,MAAA,MAAAJ,MAAA,IAAEhC,MAAA,CAAA0E,gBAAgB;;MA3M7CrF,OAAA,EAAAL,QAAA,CA2MuD,MAAEoD,MAAA,SAAAA,MAAA,QA3MzD5C,gBAAA,CA2MuD,IAAE,E;MA3MzDD,CAAA;QA4MUV,YAAA,CAMYa,oBAAA;MALVY,IAAI,EAAC,SAAS;MACbV,OAAK,EAAEC,IAAA,CAAAiF,yBAAyB;MAChC/E,OAAO,EAAEC,MAAA,CAAAkC,WAAW,CAACC,QAAQ,CAACnC,MAAA,CAAA+E,kBAAkB,EAAEzD,EAAE;;MA/MjEjC,OAAA,EAAAL,QAAA,CAgNW,MAEDoD,MAAA,SAAAA,MAAA,QAlNV5C,gBAAA,CAgNW,QAED,E;MAlNVD,CAAA;;IAAAF,OAAA,EAAAL,QAAA,CAwJM,MA+CM,CA/CKgB,MAAA,CAAA+E,kBAAkB,I,cAA7BpG,mBAAA,CA+CM,OA/CNqG,WA+CM,GA9CJnG,YAAA,CAwBkBoG,0BAAA;MAxBAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MAzJrC9F,OAAA,EAAAL,QAAA,CA0JU,MAEuB,CAFvBH,YAAA,CAEuBuG,+BAAA;QAFDC,KAAK,EAAC;MAAM;QA1J5ChG,OAAA,EAAAL,QAAA,CA2JY,MAA6B,CA3JzCQ,gBAAA,CAAAqB,gBAAA,CA2Jeb,MAAA,CAAA+E,kBAAkB,CAACzE,IAAI,iB;QA3JtCf,CAAA;UA6JUV,YAAA,CAIuBuG,+BAAA;QAJDC,KAAK,EAAC;MAAK;QA7J3ChG,OAAA,EAAAL,QAAA,CA8JY,MAES,CAFTH,YAAA,CAES8C,iBAAA;UAFArB,IAAI,EAAET,IAAA,CAAA+B,kBAAkB,CAAC5B,MAAA,CAAA+E,kBAAkB,CAACvD,QAAQ;;UA9JzEnC,OAAA,EAAAL,QAAA,CA+Jc,MAAkD,CA/JhEQ,gBAAA,CAAAqB,gBAAA,CA+JiBhB,IAAA,CAAAgC,eAAe,CAAC7B,MAAA,CAAA+E,kBAAkB,CAACvD,QAAQ,kB;UA/J5DjC,CAAA;;QAAAA,CAAA;UAkKUV,YAAA,CAEuBuG,+BAAA;QAFDC,KAAK,EAAC;MAAK;QAlK3ChG,OAAA,EAAAL,QAAA,CAmKY,MAAsD,CAnKlEQ,gBAAA,CAAAqB,gBAAA,EAmKgBb,MAAA,CAAA+E,kBAAkB,CAACpC,UAAU,QAAQC,OAAO,OAAM,IACxD,gB;QApKVrD,CAAA;UAqKUV,YAAA,CAEuBuG,+BAAA;QAFDC,KAAK,EAAC;MAAM;QArK5ChG,OAAA,EAAAL,QAAA,CAsKY,MAAgD,CAtK5DQ,gBAAA,CAAAqB,gBAAA,CAsKeb,MAAA,CAAA+E,kBAAkB,CAAChC,YAAY,CAACH,OAAO,OAAM,MAClD,gB;QAvKVrD,CAAA;UAwKUV,YAAA,CAEuBuG,+BAAA;QAFDC,KAAK,EAAC,MAAM;QAAC5B,IAAI,EAAC;;QAxKlDpE,OAAA,EAAAL,QAAA,CAyKY,MAA4C,CAzKxDQ,gBAAA,CAAAqB,gBAAA,CAyKeb,MAAA,CAAA+E,kBAAkB,CAAC7B,mBAAmB,iB;QAzKrD3D,CAAA;UA2KUV,YAAA,CAEuBuG,+BAAA;QAFDC,KAAK,EAAC,MAAM;QAAC5B,IAAI,EAAC;;QA3KlDpE,OAAA,EAAAL,QAAA,CA4KY,MAAwC,CA5KpDQ,gBAAA,CAAAqB,gBAAA,CA4Keb,MAAA,CAAA+E,kBAAkB,CAACO,eAAe,iB;QA5KjD/F,CAAA;UA8KUV,YAAA,CAEuBuG,+BAAA;QAFDC,KAAK,EAAC,MAAM;QAAC5B,IAAI,EAAC;;QA9KlDpE,OAAA,EAAAL,QAAA,CA+KY,MAA+B,CA/K3CQ,gBAAA,CAAAqB,gBAAA,CA+Keb,MAAA,CAAA+E,kBAAkB,CAACR,MAAM,iB;QA/KxChF,CAAA;;MAAAA,CAAA;QAmLQ6E,mBAAA,UAAa,EACFpE,MAAA,CAAA+E,kBAAkB,CAACQ,eAAe,I,cAA7C5G,mBAAA,CAQM,OARN6G,WAQM,G,4BAPJvG,mBAAA,CAAa,YAAT,MAAI,sBACRJ,YAAA,CAKEsB,mBAAA;MAJCC,KAAK,WAAWJ,MAAA,CAAA+E,kBAAkB,CAACQ,eAAe,CAACE,KAAK;MACxDnF,IAAI,EAAET,IAAA,CAAA6F,gBAAgB,CAAC1F,MAAA,CAAA+E,kBAAkB,CAACQ,eAAe,CAACE,KAAK;MAC/DjF,WAAW,EAAER,MAAA,CAAA+E,kBAAkB,CAACQ,eAAe,CAACI,UAAU;MAC3D,WAAS,EAAT;mEA1LZvB,mBAAA,gBA8LQA,mBAAA,UAAa,EACFpE,MAAA,CAAA+E,kBAAkB,CAACa,qBAAqB,EAAE7E,MAAM,I,cAA3DpC,mBAAA,CAOM,OAPNkH,WAOM,G,4BANJ5G,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAIK,c,kBAHHN,mBAAA,CAEKwC,SAAA,QApMjBC,WAAA,CAkMoCpB,MAAA,CAAA+E,kBAAkB,CAACa,qBAAqB,EAArDE,SAAS;2BAApBnH,mBAAA,CAEK;QAF8DD,GAAG,EAAEoH;MAAS,GAAAjF,gBAAA,CAC5EiF,SAAS;0CAnM1B1B,mBAAA,e,KAAAA,mBAAA,e;IAAA7E,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}