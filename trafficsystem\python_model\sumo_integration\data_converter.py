#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据转换器模块

负责在视频分析数据和SUMO仿真数据之间进行转换
"""

import logging
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import xml.etree.ElementTree as ET

class DataConverter:
    """数据转换器类"""
    
    def __init__(self):
        """初始化数据转换器"""
        self.logger = logging.getLogger(__name__)
        
        # 车辆类型映射
        self.vehicle_type_mapping = {
            'car': 'passenger',
            'truck': 'truck', 
            'bus': 'bus',
            'motorcycle': 'motorcycle',
            'bicycle': 'bicycle'
        }
        
        # 方向映射
        self.direction_mapping = {
            'east': 'E',
            'south': 'S', 
            'west': 'W',
            'north': 'N'
        }
    
    def convert_traffic_data_to_sumo(self, traffic_data: Dict) -> Dict:
        """
        将视频分析的交通数据转换为SUMO格式
        
        Args:
            traffic_data: 视频分析结果数据
            
        Returns:
            Dict: SUMO格式的交通数据
        """
        try:
            self.logger.info("开始转换交通数据到SUMO格式")
            
            sumo_data = {
                'vehicles': [],
                'traffic_flows': {},
                'signal_timing': {},
                'network_info': {},
                'simulation_params': {}
            }
            
            # 转换车辆数据
            if 'directions' in traffic_data:
                sumo_data['vehicles'] = self._convert_vehicle_data(traffic_data['directions'])
                sumo_data['traffic_flows'] = self._convert_traffic_flows(traffic_data['directions'])
            
            # 转换信号灯配时数据
            if 'signal_optimization' in traffic_data:
                sumo_data['signal_timing'] = self._convert_signal_timing(traffic_data['signal_optimization'])
            
            # 设置网络信息
            sumo_data['network_info'] = self._generate_network_info(traffic_data)
            
            # 设置仿真参数
            sumo_data['simulation_params'] = self._generate_simulation_params(traffic_data)
            
            self.logger.info("交通数据转换完成")
            return sumo_data
            
        except Exception as e:
            self.logger.error(f"转换交通数据失败: {e}")
            raise
    
    def _convert_vehicle_data(self, directions_data: Dict) -> List[Dict]:
        """转换车辆数据"""
        vehicles = []
        
        for direction, data in directions_data.items():
            if 'vehicleTypes' in data:
                vehicle_types = data['vehicleTypes']
                total_count = data.get('vehicleCount', 0)
                
                # 为每种车辆类型生成车辆
                for vehicle_type, count in vehicle_types.items():
                    sumo_type = self.vehicle_type_mapping.get(vehicle_type.lower(), 'passenger')
                    
                    for i in range(count):
                        vehicle = {
                            'id': f"{direction}_{vehicle_type}_{i}",
                            'type': sumo_type,
                            'route': f"route_{direction}",
                            'depart': self._calculate_depart_time(i, count),
                            'direction': direction
                        }
                        vehicles.append(vehicle)
        
        return vehicles
    
    def _convert_traffic_flows(self, directions_data: Dict) -> Dict:
        """转换交通流量数据"""
        flows = {}
        
        for direction, data in directions_data.items():
            vehicle_count = data.get('vehicleCount', 0)
            duration = data.get('duration', 3600)  # 默认1小时
            
            # 计算流量 (vehicles/hour)
            flow_rate = (vehicle_count / duration) * 3600 if duration > 0 else 0
            
            flows[direction] = {
                'vehicle_count': vehicle_count,
                'flow_rate': flow_rate,
                'duration': duration,
                'density': self._calculate_density(vehicle_count, direction)
            }
        
        return flows
    
    def _convert_signal_timing(self, signal_data: Dict) -> Dict:
        """转换信号灯配时数据"""
        timing = {}
        
        if 'recommendations' in signal_data:
            recommendations = signal_data['recommendations']
            
            # 提取配时建议
            for rec in recommendations:
                if 'signal_timing' in rec:
                    timing.update(rec['signal_timing'])
        
        # 如果没有配时数据，使用默认值
        if not timing:
            timing = {
                'cycle_time': 120,  # 周期时长（秒）
                'phases': [
                    {'duration': 30, 'state': 'GrGr'},  # 东西绿灯
                    {'duration': 5, 'state': 'yryr'},   # 东西黄灯
                    {'duration': 30, 'state': 'rGrG'},  # 南北绿灯
                    {'duration': 5, 'state': 'ryry'}    # 南北黄灯
                ]
            }
        
        return timing
    
    def _generate_network_info(self, traffic_data: Dict) -> Dict:
        """生成网络信息"""
        return {
            'intersection_type': 'four_way',
            'lane_count': 2,  # 每个方向的车道数
            'lane_length': 500,  # 车道长度（米）
            'speed_limit': 50,  # 速度限制（km/h）
            'junction_id': 'main_junction'
        }
    
    def _generate_simulation_params(self, traffic_data: Dict) -> Dict:
        """生成仿真参数"""
        return {
            'begin_time': 0,
            'end_time': 3600,  # 1小时仿真
            'step_length': 1.0,  # 仿真步长（秒）
            'random_seed': 42,
            'gui': False
        }
    
    def _calculate_depart_time(self, index: int, total_count: int) -> float:
        """计算车辆出发时间"""
        if total_count <= 1:
            return 0.0
        
        # 在仿真时间内均匀分布
        simulation_duration = 3600  # 1小时
        interval = simulation_duration / total_count
        return index * interval
    
    def _calculate_density(self, vehicle_count: int, direction: str) -> float:
        """计算交通密度"""
        # 假设每个方向500米长度，2车道
        lane_length = 500
        lane_count = 2
        total_length = lane_length * lane_count
        
        # 密度 = 车辆数 / 总长度 (vehicles/km)
        return (vehicle_count / total_length) * 1000 if total_length > 0 else 0
    
    def convert_sumo_results_to_frontend(self, sumo_results: Dict) -> Dict:
        """
        将SUMO仿真结果转换为前端显示格式
        
        Args:
            sumo_results: SUMO仿真结果
            
        Returns:
            Dict: 前端格式的数据
        """
        try:
            self.logger.info("开始转换SUMO结果到前端格式")
            
            frontend_data = {
                'simulation_summary': {},
                'optimization_results': {},
                'traffic_metrics': {},
                'recommendations': [],
                'charts_data': {}
            }
            
            # 转换仿真摘要
            if 'summary' in sumo_results:
                frontend_data['simulation_summary'] = self._convert_simulation_summary(sumo_results['summary'])
            
            # 转换优化结果
            if 'optimization' in sumo_results:
                frontend_data['optimization_results'] = self._convert_optimization_results(sumo_results['optimization'])
            
            # 转换交通指标
            if 'metrics' in sumo_results:
                frontend_data['traffic_metrics'] = self._convert_traffic_metrics(sumo_results['metrics'])
            
            # 生成建议
            frontend_data['recommendations'] = self._generate_recommendations(sumo_results)
            
            # 生成图表数据
            frontend_data['charts_data'] = self._generate_charts_data(sumo_results)
            
            self.logger.info("SUMO结果转换完成")
            return frontend_data
            
        except Exception as e:
            self.logger.error(f"转换SUMO结果失败: {e}")
            raise
    
    def _convert_simulation_summary(self, summary: Dict) -> Dict:
        """转换仿真摘要"""
        return {
            'total_vehicles': summary.get('total_vehicles', 0),
            'simulation_time': summary.get('simulation_time', 0),
            'average_speed': summary.get('average_speed', 0),
            'total_waiting_time': summary.get('total_waiting_time', 0),
            'throughput': summary.get('throughput', 0)
        }
    
    def _convert_optimization_results(self, optimization: Dict) -> Dict:
        """转换优化结果"""
        return {
            'improvement_percentage': optimization.get('improvement', 0),
            'reduced_waiting_time': optimization.get('waiting_time_reduction', 0),
            'increased_throughput': optimization.get('throughput_increase', 0),
            'optimized_signal_timing': optimization.get('signal_timing', {})
        }
    
    def _convert_traffic_metrics(self, metrics: Dict) -> Dict:
        """转换交通指标"""
        return {
            'flow_rates': metrics.get('flow_rates', {}),
            'density_levels': metrics.get('density', {}),
            'speed_distribution': metrics.get('speeds', {}),
            'queue_lengths': metrics.get('queues', {})
        }
    
    def _generate_recommendations(self, sumo_results: Dict) -> List[Dict]:
        """生成优化建议"""
        recommendations = []
        
        # 基于仿真结果生成建议
        if 'optimization' in sumo_results:
            opt_data = sumo_results['optimization']
            
            if opt_data.get('improvement', 0) > 10:
                recommendations.append({
                    'type': 'signal_timing',
                    'priority': 'high',
                    'title': '信号灯配时优化',
                    'description': f"建议调整信号灯配时，可提升{opt_data.get('improvement', 0):.1f}%的通行效率",
                    'action': '应用优化配时方案'
                })
        
        return recommendations
    
    def _generate_charts_data(self, sumo_results: Dict) -> Dict:
        """生成图表数据"""
        charts = {}
        
        # 生成流量对比图数据
        if 'metrics' in sumo_results and 'flow_rates' in sumo_results['metrics']:
            flow_rates = sumo_results['metrics']['flow_rates']
            charts['flow_comparison'] = {
                'labels': list(flow_rates.keys()),
                'data': list(flow_rates.values())
            }
        
        # 生成速度分布图数据
        if 'metrics' in sumo_results and 'speeds' in sumo_results['metrics']:
            speeds = sumo_results['metrics']['speeds']
            charts['speed_distribution'] = {
                'labels': list(speeds.keys()),
                'data': list(speeds.values())
            }
        
        return charts
