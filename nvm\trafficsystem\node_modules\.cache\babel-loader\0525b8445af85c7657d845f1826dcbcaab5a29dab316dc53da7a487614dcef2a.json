{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createBlock as _createBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"simulation-dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  class: \"stats-cards\"\n};\nconst _hoisted_5 = {\n  class: \"stat-content\"\n};\nconst _hoisted_6 = {\n  class: \"stat-info\"\n};\nconst _hoisted_7 = {\n  class: \"stat-value\"\n};\nconst _hoisted_8 = {\n  class: \"stat-content\"\n};\nconst _hoisted_9 = {\n  class: \"stat-info\"\n};\nconst _hoisted_10 = {\n  class: \"stat-value\"\n};\nconst _hoisted_11 = {\n  class: \"stat-content\"\n};\nconst _hoisted_12 = {\n  class: \"stat-info\"\n};\nconst _hoisted_13 = {\n  class: \"stat-value\"\n};\nconst _hoisted_14 = {\n  class: \"stat-content\"\n};\nconst _hoisted_15 = {\n  class: \"stat-info\"\n};\nconst _hoisted_16 = {\n  class: \"stat-value\"\n};\nconst _hoisted_17 = {\n  class: \"card-header\"\n};\nconst _hoisted_18 = {\n  class: \"header-filters\"\n};\nconst _hoisted_19 = {\n  class: \"task-name\"\n};\nconst _hoisted_20 = {\n  class: \"task-id\"\n};\nconst _hoisted_21 = {\n  class: \"pagination-wrapper\"\n};\nconst _hoisted_22 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  const _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_button_group = _resolveComponent(\"el-button-group\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面头部 \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_breadcrumb, {\n    separator: \"/\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_breadcrumb_item, {\n      to: {\n        path: '/'\n      }\n    }, {\n      default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"首页\")])),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_breadcrumb_item, null, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"仿真分析\")])),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_breadcrumb_item, null, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"仿真控制台\")])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[0] || (_cache[0] = $event => $setup.showCreateDialog = true)\n  }, {\n    default: _withCtx(() => _cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n      class: \"el-icon-plus\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 创建仿真 \")])),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    onClick: $setup.refreshData\n  }, {\n    default: _withCtx(() => _cache[15] || (_cache[15] = [_createElementVNode(\"i\", {\n      class: \"el-icon-refresh\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 刷新 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createCommentVNode(\" 统计卡片 \"), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_row, {\n    gutter: 20\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 6\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stat-card\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n          class: \"stat-icon\"\n        }, [_createElementVNode(\"i\", {\n          class: \"el-icon-cpu\",\n          style: {\n            \"color\": \"#409eff\"\n          }\n        })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.statistics.total_tasks || 0), 1 /* TEXT */), _cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n          class: \"stat-label\"\n        }, \"总仿真任务\", -1 /* HOISTED */))])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 6\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stat-card\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n          class: \"stat-icon\"\n        }, [_createElementVNode(\"i\", {\n          class: \"el-icon-video-play\",\n          style: {\n            \"color\": \"#67c23a\"\n          }\n        })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.statistics.running_tasks || 0), 1 /* TEXT */), _cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n          class: \"stat-label\"\n        }, \"运行中\", -1 /* HOISTED */))])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 6\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stat-card\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_11, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n          class: \"stat-icon\"\n        }, [_createElementVNode(\"i\", {\n          class: \"el-icon-check\",\n          style: {\n            \"color\": \"#e6a23c\"\n          }\n        })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.statistics.completed_tasks || 0), 1 /* TEXT */), _cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n          class: \"stat-label\"\n        }, \"已完成\", -1 /* HOISTED */))])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 6\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stat-card\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_cache[23] || (_cache[23] = _createElementVNode(\"div\", {\n          class: \"stat-icon\"\n        }, [_createElementVNode(\"i\", {\n          class: \"el-icon-warning\",\n          style: {\n            \"color\": \"#f56c6c\"\n          }\n        })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString($setup.statistics.failed_tasks || 0), 1 /* TEXT */), _cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n          class: \"stat-label\"\n        }, \"失败\", -1 /* HOISTED */))])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" 仿真任务列表 \"), _createVNode(_component_el_card, {\n    class: \"task-list-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, [_cache[24] || (_cache[24] = _createElementVNode(\"h3\", null, \"仿真任务列表\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_select, {\n      modelValue: $setup.filterStatus,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.filterStatus = $event),\n      placeholder: \"状态筛选\",\n      clearable: \"\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部\",\n        value: \"\"\n      }), _createVNode(_component_el_option, {\n        label: \"运行中\",\n        value: \"running\"\n      }), _createVNode(_component_el_option, {\n        label: \"已完成\",\n        value: \"completed\"\n      }), _createVNode(_component_el_option, {\n        label: \"失败\",\n        value: \"failed\"\n      }), _createVNode(_component_el_option, {\n        label: \"已停止\",\n        value: \"stopped\"\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_select, {\n      modelValue: $setup.filterType,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.filterType = $event),\n      placeholder: \"类型筛选\",\n      clearable: \"\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部\",\n        value: \"\"\n      }), _createVNode(_component_el_option, {\n        label: \"信号配时优化\",\n        value: \"signal_timing\"\n      }), _createVNode(_component_el_option, {\n        label: \"流量平衡优化\",\n        value: \"flow_balance\"\n      }), _createVNode(_component_el_option, {\n        label: \"综合优化\",\n        value: \"comprehensive\"\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.filteredTasks,\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        prop: \"taskName\",\n        label: \"任务名称\",\n        \"min-width\": \"200\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"strong\", null, _toDisplayString(scope.row.taskName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_20, \"ID: \" + _toDisplayString(scope.row.simulationId), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"simulationType\",\n        label: \"仿真类型\",\n        width: \"150\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: $setup.getTypeTagType(scope.row.simulationType),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getTypeText(scope.row.simulationType)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"status\",\n        label: \"状态\",\n        width: \"120\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusTagType(scope.row.status),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(scope.row.status)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"progress\",\n        label: \"进度\",\n        width: \"150\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_progress, {\n          percentage: scope.row.progress || 0,\n          status: $setup.getProgressStatus(scope.row.status),\n          \"stroke-width\": 6\n        }, null, 8 /* PROPS */, [\"percentage\", \"status\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"createTime\",\n        label: \"创建时间\",\n        width: \"180\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatTime(scope.row.createTime)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"duration\",\n        label: \"耗时\",\n        width: \"100\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDuration(scope.row.duration)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"200\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_button_group, {\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: $event => $setup.viewTask(scope.row),\n            icon: 'el-icon-view'\n          }, {\n            default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\" 查看 \")])),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), scope.row.status === 'running' ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 0,\n            type: \"warning\",\n            onClick: $event => $setup.stopTask(scope.row),\n            icon: 'el-icon-video-pause'\n          }, {\n            default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\" 停止 \")])),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), scope.row.status === 'failed' || scope.row.status === 'stopped' ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 1,\n            type: \"success\",\n            onClick: $event => $setup.restartTask(scope.row),\n            icon: 'el-icon-refresh'\n          }, {\n            default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\" 重启 \")])),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n            type: \"danger\",\n            onClick: $event => $setup.deleteTask(scope.row),\n            icon: 'el-icon-delete'\n          }, {\n            default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\" 删除 \")])),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[3] || (_cache[3] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[4] || (_cache[4] = $event => $setup.pageSize = $event),\n      \"page-sizes\": [10, 20, 50, 100],\n      total: $setup.totalTasks,\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 创建仿真对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showCreateDialog,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.showCreateDialog = $event),\n    title: \"创建仿真任务\",\n    width: \"600px\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_22, [_createVNode(_component_el_button, {\n      onClick: _cache[9] || (_cache[9] = $event => $setup.showCreateDialog = false)\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.createSimulation,\n      loading: $setup.creating\n    }, {\n      default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\" 创建并启动 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.createForm,\n      rules: $setup.createRules,\n      ref: \"createFormRef\",\n      \"label-width\": \"120px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"任务名称\",\n        prop: \"taskName\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.createForm.taskName,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.createForm.taskName = $event),\n          placeholder: \"请输入任务名称\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"仿真类型\",\n        prop: \"simulationType\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.createForm.simulationType,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.createForm.simulationType = $event),\n          placeholder: \"选择仿真类型\",\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"信号灯配时优化\",\n            value: \"signal_timing\"\n          }), _createVNode(_component_el_option, {\n            label: \"流量平衡优化\",\n            value: \"flow_balance\"\n          }), _createVNode(_component_el_option, {\n            label: \"综合优化分析\",\n            value: \"comprehensive\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"分析任务ID\",\n        prop: \"analysisTaskId\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.createForm.analysisTaskId,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.createForm.analysisTaskId = $event),\n          placeholder: \"请输入视频分析任务ID\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"使用GUI\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_switch, {\n          modelValue: $setup.createForm.useGui,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.createForm.useGui = $event)\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_breadcrumb", "separator", "default", "_withCtx", "_component_el_breadcrumb_item", "to", "path", "_cache", "_createTextVNode", "_", "_hoisted_3", "_component_el_button", "type", "onClick", "$event", "$setup", "showCreateDialog", "refreshData", "_hoisted_4", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_card", "_hoisted_5", "style", "_hoisted_6", "_hoisted_7", "_toDisplayString", "statistics", "total_tasks", "_hoisted_8", "_hoisted_9", "_hoisted_10", "running_tasks", "_hoisted_11", "_hoisted_12", "_hoisted_13", "completed_tasks", "_hoisted_14", "_hoisted_15", "_hoisted_16", "failed_tasks", "header", "_hoisted_17", "_hoisted_18", "_component_el_select", "modelValue", "filterStatus", "placeholder", "clearable", "size", "_component_el_option", "label", "value", "filterType", "_createBlock", "_component_el_table", "data", "filteredTasks", "_component_el_table_column", "prop", "scope", "_hoisted_19", "row", "taskName", "_hoisted_20", "simulationId", "width", "_component_el_tag", "getTypeTagType", "simulationType", "getTypeText", "getStatusTagType", "status", "getStatusText", "_component_el_progress", "percentage", "progress", "getProgressStatus", "formatTime", "createTime", "formatDuration", "duration", "fixed", "_component_el_button_group", "viewTask", "icon", "key", "stopTask", "restartTask", "deleteTask", "loading", "_hoisted_21", "_component_el_pagination", "currentPage", "pageSize", "total", "totalTasks", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "title", "footer", "_hoisted_22", "createSimulation", "creating", "_component_el_form", "model", "createForm", "rules", "createRules", "ref", "_component_el_form_item", "_component_el_input", "analysisTaskId", "_component_el_switch", "useGui"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\simulation\\SimulationDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"simulation-dashboard\">\n    <!-- 页面头部 -->\n    <div class=\"dashboard-header\">\n      <el-breadcrumb separator=\"/\">\n        <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\n        <el-breadcrumb-item>仿真分析</el-breadcrumb-item>\n        <el-breadcrumb-item>仿真控制台</el-breadcrumb-item>\n      </el-breadcrumb>\n      \n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"showCreateDialog = true\">\n          <i class=\"el-icon-plus\"></i> 创建仿真\n        </el-button>\n        <el-button @click=\"refreshData\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon\">\n                <i class=\"el-icon-cpu\" style=\"color: #409eff\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.total_tasks || 0 }}</div>\n                <div class=\"stat-label\">总仿真任务</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon\">\n                <i class=\"el-icon-video-play\" style=\"color: #67c23a\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.running_tasks || 0 }}</div>\n                <div class=\"stat-label\">运行中</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon\">\n                <i class=\"el-icon-check\" style=\"color: #e6a23c\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.completed_tasks || 0 }}</div>\n                <div class=\"stat-label\">已完成</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon\">\n                <i class=\"el-icon-warning\" style=\"color: #f56c6c\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.failed_tasks || 0 }}</div>\n                <div class=\"stat-label\">失败</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 仿真任务列表 -->\n    <el-card class=\"task-list-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>仿真任务列表</h3>\n          <div class=\"header-filters\">\n            <el-select v-model=\"filterStatus\" placeholder=\"状态筛选\" clearable size=\"small\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option label=\"运行中\" value=\"running\"></el-option>\n              <el-option label=\"已完成\" value=\"completed\"></el-option>\n              <el-option label=\"失败\" value=\"failed\"></el-option>\n              <el-option label=\"已停止\" value=\"stopped\"></el-option>\n            </el-select>\n            \n            <el-select v-model=\"filterType\" placeholder=\"类型筛选\" clearable size=\"small\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option label=\"信号配时优化\" value=\"signal_timing\"></el-option>\n              <el-option label=\"流量平衡优化\" value=\"flow_balance\"></el-option>\n              <el-option label=\"综合优化\" value=\"comprehensive\"></el-option>\n            </el-select>\n          </div>\n        </div>\n      </template>\n\n      <el-table :data=\"filteredTasks\" v-loading=\"loading\" style=\"width: 100%\">\n        <el-table-column prop=\"taskName\" label=\"任务名称\" min-width=\"200\">\n          <template #default=\"scope\">\n            <div class=\"task-name\">\n              <strong>{{ scope.row.taskName }}</strong>\n              <div class=\"task-id\">ID: {{ scope.row.simulationId }}</div>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"simulationType\" label=\"仿真类型\" width=\"150\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getTypeTagType(scope.row.simulationType)\" size=\"small\">\n              {{ getTypeText(scope.row.simulationType) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"status\" label=\"状态\" width=\"120\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">\n              {{ getStatusText(scope.row.status) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"progress\" label=\"进度\" width=\"150\">\n          <template #default=\"scope\">\n            <el-progress \n              :percentage=\"scope.row.progress || 0\" \n              :status=\"getProgressStatus(scope.row.status)\"\n              :stroke-width=\"6\">\n            </el-progress>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"180\">\n          <template #default=\"scope\">\n            {{ formatTime(scope.row.createTime) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"duration\" label=\"耗时\" width=\"100\">\n          <template #default=\"scope\">\n            {{ formatDuration(scope.row.duration) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\n          <template #default=\"scope\">\n            <el-button-group size=\"small\">\n              <el-button \n                type=\"primary\" \n                @click=\"viewTask(scope.row)\"\n                :icon=\"'el-icon-view'\">\n                查看\n              </el-button>\n              \n              <el-button \n                v-if=\"scope.row.status === 'running'\"\n                type=\"warning\" \n                @click=\"stopTask(scope.row)\"\n                :icon=\"'el-icon-video-pause'\">\n                停止\n              </el-button>\n              \n              <el-button \n                v-if=\"scope.row.status === 'failed' || scope.row.status === 'stopped'\"\n                type=\"success\" \n                @click=\"restartTask(scope.row)\"\n                :icon=\"'el-icon-refresh'\">\n                重启\n              </el-button>\n              \n              <el-button \n                type=\"danger\" \n                @click=\"deleteTask(scope.row)\"\n                :icon=\"'el-icon-delete'\">\n                删除\n              </el-button>\n            </el-button-group>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :total=\"totalTasks\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 创建仿真对话框 -->\n    <el-dialog\n      v-model=\"showCreateDialog\"\n      title=\"创建仿真任务\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\">\n      \n      <el-form :model=\"createForm\" :rules=\"createRules\" ref=\"createFormRef\" label-width=\"120px\">\n        <el-form-item label=\"任务名称\" prop=\"taskName\">\n          <el-input v-model=\"createForm.taskName\" placeholder=\"请输入任务名称\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"仿真类型\" prop=\"simulationType\">\n          <el-select v-model=\"createForm.simulationType\" placeholder=\"选择仿真类型\" style=\"width: 100%\">\n            <el-option label=\"信号灯配时优化\" value=\"signal_timing\"></el-option>\n            <el-option label=\"流量平衡优化\" value=\"flow_balance\"></el-option>\n            <el-option label=\"综合优化分析\" value=\"comprehensive\"></el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"分析任务ID\" prop=\"analysisTaskId\">\n          <el-input v-model=\"createForm.analysisTaskId\" placeholder=\"请输入视频分析任务ID\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"使用GUI\">\n          <el-switch v-model=\"createForm.useGui\"></el-switch>\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showCreateDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"createSimulation\" :loading=\"creating\">\n            创建并启动\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport simulationApi from '@/api/simulation'\n\nexport default {\n  name: 'SimulationDashboard',\n  setup() {\n    const router = useRouter()\n    \n    // 响应式数据\n    const loading = ref(false)\n    const creating = ref(false)\n    const showCreateDialog = ref(false)\n    const tasks = ref([])\n    const statistics = ref({})\n    \n    // 筛选和分页\n    const filterStatus = ref('')\n    const filterType = ref('')\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const totalTasks = ref(0)\n    \n    // 创建表单\n    const createForm = reactive({\n      taskName: '',\n      simulationType: 'comprehensive',\n      analysisTaskId: '',\n      useGui: false\n    })\n    \n    const createRules = {\n      taskName: [\n        { required: true, message: '请输入任务名称', trigger: 'blur' }\n      ],\n      simulationType: [\n        { required: true, message: '请选择仿真类型', trigger: 'change' }\n      ],\n      analysisTaskId: [\n        { required: true, message: '请输入分析任务ID', trigger: 'blur' }\n      ]\n    }\n    \n    const createFormRef = ref(null)\n    \n    // 计算属性\n    const filteredTasks = computed(() => {\n      let filtered = tasks.value\n      \n      if (filterStatus.value) {\n        filtered = filtered.filter(task => task.status === filterStatus.value)\n      }\n      \n      if (filterType.value) {\n        filtered = filtered.filter(task => task.simulationType === filterType.value)\n      }\n      \n      return filtered\n    })\n    \n    // 方法\n    const getStatusTagType = (status) => {\n      const statusMap = {\n        'created': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      }\n      return statusMap[status] || 'info'\n    }\n    \n    const getStatusText = (status) => {\n      const statusMap = {\n        'created': '已创建',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      }\n      return statusMap[status] || '未知'\n    }\n    \n    const getTypeTagType = (type) => {\n      const typeMap = {\n        'signal_timing': 'primary',\n        'flow_balance': 'success',\n        'comprehensive': 'warning'\n      }\n      return typeMap[type] || 'info'\n    }\n    \n    const getTypeText = (type) => {\n      const typeMap = {\n        'signal_timing': '信号配时',\n        'flow_balance': '流量平衡',\n        'comprehensive': '综合优化'\n      }\n      return typeMap[type] || '未知'\n    }\n    \n    const getProgressStatus = (status) => {\n      if (status === 'failed') return 'exception'\n      if (status === 'completed') return 'success'\n      return null\n    }\n    \n    const formatTime = (timeStr) => {\n      if (!timeStr) return '-'\n      const date = new Date(timeStr)\n      return date.toLocaleString('zh-CN')\n    }\n    \n    const formatDuration = (duration) => {\n      if (!duration) return '-'\n      const hours = Math.floor(duration / 3600)\n      const minutes = Math.floor((duration % 3600) / 60)\n      const seconds = duration % 60\n      \n      if (hours > 0) {\n        return `${hours}h ${minutes}m ${seconds}s`\n      } else if (minutes > 0) {\n        return `${minutes}m ${seconds}s`\n      } else {\n        return `${seconds}s`\n      }\n    }\n    \n    const loadTasks = async () => {\n      try {\n        loading.value = true\n        const userId = localStorage.getItem('userId')\n        const response = await simulationApi.getUserSimulations(userId)\n        \n        if (response.status === 'success') {\n          tasks.value = response.simulation_tasks || []\n          totalTasks.value = response.total_count || 0\n        }\n      } catch (error) {\n        console.error('加载仿真任务失败:', error)\n        ElMessage.error('加载仿真任务失败')\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    const loadStatistics = async () => {\n      try {\n        const userId = localStorage.getItem('userId')\n        const response = await simulationApi.getStatistics(userId)\n        statistics.value = response\n      } catch (error) {\n        console.error('加载统计信息失败:', error)\n      }\n    }\n    \n    const refreshData = async () => {\n      await Promise.all([loadTasks(), loadStatistics()])\n    }\n    \n    const createSimulation = async () => {\n      try {\n        await createFormRef.value.validate()\n        creating.value = true\n        \n        const userId = localStorage.getItem('userId')\n        const username = localStorage.getItem('username')\n        \n        const response = await simulationApi.createSimulation({\n          user_id: userId,\n          username: username,\n          task_name: createForm.taskName,\n          simulation_type: createForm.simulationType,\n          analysis_task_id: createForm.analysisTaskId,\n          traffic_data: {} // 这里应该从分析结果获取\n        })\n        \n        if (response.status === 'success') {\n          ElMessage.success('仿真任务创建成功')\n          showCreateDialog.value = false\n          \n          // 如果需要立即启动\n          const simulationId = response.simulation_task.simulationId\n          await simulationApi.startSimulation(simulationId, {\n            use_gui: createForm.useGui\n          })\n          \n          // 跳转到仿真详情页\n          router.push(`/simulation/${simulationId}`)\n        }\n      } catch (error) {\n        console.error('创建仿真失败:', error)\n        ElMessage.error('创建仿真失败')\n      } finally {\n        creating.value = false\n      }\n    }\n    \n    const viewTask = (task) => {\n      router.push(`/simulation/${task.simulationId}`)\n    }\n    \n    const stopTask = async (task) => {\n      try {\n        await ElMessageBox.confirm('确定要停止此仿真任务吗？', '确认停止', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await simulationApi.stopSimulation(task.simulationId)\n        if (response.status === 'success') {\n          ElMessage.success('仿真已停止')\n          await refreshData()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('停止仿真失败:', error)\n          ElMessage.error('停止仿真失败')\n        }\n      }\n    }\n    \n    const restartTask = async (task) => {\n      try {\n        await ElMessageBox.confirm('确定要重启此仿真任务吗？', '确认重启', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await simulationApi.restartSimulation(task.simulationId)\n        if (response.status === 'success') {\n          ElMessage.success('仿真重启成功')\n          await refreshData()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('重启仿真失败:', error)\n          ElMessage.error('重启仿真失败')\n        }\n      }\n    }\n    \n    const deleteTask = async (task) => {\n      try {\n        await ElMessageBox.confirm('确定要删除此仿真任务吗？删除后无法恢复。', '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const userId = localStorage.getItem('userId')\n        const response = await simulationApi.deleteSimulation(task.simulationId, userId)\n        if (response.status === 'success') {\n          ElMessage.success('仿真任务已删除')\n          await refreshData()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除仿真失败:', error)\n          ElMessage.error('删除仿真失败')\n        }\n      }\n    }\n    \n    const handleSizeChange = (newSize) => {\n      pageSize.value = newSize\n      currentPage.value = 1\n      loadTasks()\n    }\n    \n    const handleCurrentChange = (newPage) => {\n      currentPage.value = newPage\n      loadTasks()\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      refreshData()\n    })\n    \n    return {\n      // 响应式数据\n      loading,\n      creating,\n      showCreateDialog,\n      tasks,\n      statistics,\n      filterStatus,\n      filterType,\n      currentPage,\n      pageSize,\n      totalTasks,\n      createForm,\n      createRules,\n      createFormRef,\n      \n      // 计算属性\n      filteredTasks,\n      \n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getTypeTagType,\n      getTypeText,\n      getProgressStatus,\n      formatTime,\n      formatDuration,\n      refreshData,\n      createSimulation,\n      viewTask,\n      stopTask,\n      restartTask,\n      deleteTask,\n      handleSizeChange,\n      handleCurrentChange\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simulation-dashboard {\n  padding: 20px;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.stats-cards {\n  margin-bottom: 20px;\n}\n\n.stat-card {\n  border-radius: 8px;\n}\n\n.stat-content {\n  display: flex;\n  align-items: center;\n}\n\n.stat-icon {\n  font-size: 32px;\n  margin-right: 15px;\n}\n\n.stat-info {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.task-list-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  color: #303133;\n}\n\n.header-filters {\n  display: flex;\n  gap: 10px;\n}\n\n.task-name {\n  line-height: 1.4;\n}\n\n.task-id {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: right;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAkB;;EAOtBA,KAAK,EAAC;AAAgB;;EAWxBA,KAAK,EAAC;AAAa;;EAIXA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAStBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAStBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAStBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAY1BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;EAsBpBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAS;;EAiFvBA,KAAK,EAAC;AAAoB;;EA2CvBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;uBAzOjCC,mBAAA,CAiPM,OAjPNC,UAiPM,GAhPJC,mBAAA,UAAa,EACbC,mBAAA,CAeM,OAfNC,UAeM,GAdJC,YAAA,CAIgBC,wBAAA;IAJDC,SAAS,EAAC;EAAG;IAJlCC,OAAA,EAAAC,QAAA,CAKQ,MAA+D,CAA/DJ,YAAA,CAA+DK,6BAAA;MAA1CC,EAAE,EAAE;QAAAC,IAAA;MAAA;IAAa;MAL9CJ,OAAA,EAAAC,QAAA,CAKgD,MAAEI,MAAA,SAAAA,MAAA,QALlDC,gBAAA,CAKgD,IAAE,E;MALlDC,CAAA;QAMQV,YAAA,CAA6CK,6BAAA;MANrDF,OAAA,EAAAC,QAAA,CAM4B,MAAII,MAAA,SAAAA,MAAA,QANhCC,gBAAA,CAM4B,MAAI,E;MANhCC,CAAA;QAOQV,YAAA,CAA8CK,6BAAA;MAPtDF,OAAA,EAAAC,QAAA,CAO4B,MAAKI,MAAA,SAAAA,MAAA,QAPjCC,gBAAA,CAO4B,OAAK,E;MAPjCC,CAAA;;IAAAA,CAAA;MAUMZ,mBAAA,CAOM,OAPNa,UAOM,GANJX,YAAA,CAEYY,oBAAA;IAFDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;;IAX1Dd,OAAA,EAAAC,QAAA,CAYU,MAA4BI,MAAA,SAAAA,MAAA,QAA5BV,mBAAA,CAA4B;MAAzBJ,KAAK,EAAC;IAAc,4BAZjCe,gBAAA,CAYsC,QAC9B,E;IAbRC,CAAA;MAcQV,YAAA,CAEYY,oBAAA;IAFAE,OAAK,EAAEE,MAAA,CAAAE;EAAW;IAdtCf,OAAA,EAAAC,QAAA,CAeU,MAA+BI,MAAA,SAAAA,MAAA,QAA/BV,mBAAA,CAA+B;MAA5BJ,KAAK,EAAC;IAAiB,4BAfpCe,gBAAA,CAeyC,MACjC,E;IAhBRC,CAAA;sCAoBIb,mBAAA,UAAa,EACbC,mBAAA,CA0DM,OA1DNqB,UA0DM,GAzDJnB,YAAA,CAwDSoB,iBAAA;IAxDAC,MAAM,EAAE;EAAE;IAtBzBlB,OAAA,EAAAC,QAAA,CAuBQ,MAYS,CAZTJ,YAAA,CAYSsB,iBAAA;MAZAC,IAAI,EAAE;IAAC;MAvBxBpB,OAAA,EAAAC,QAAA,CAwBU,MAUU,CAVVJ,YAAA,CAUUwB,kBAAA;QAVD9B,KAAK,EAAC;MAAW;QAxBpCS,OAAA,EAAAC,QAAA,CAyBY,MAQM,CARNN,mBAAA,CAQM,OARN2B,UAQM,G,4BAPJ3B,mBAAA,CAEM;UAFDJ,KAAK,EAAC;QAAW,IACpBI,mBAAA,CAAkD;UAA/CJ,KAAK,EAAC,aAAa;UAACgC,KAAsB,EAAtB;YAAA;UAAA;iCAEzB5B,mBAAA,CAGM,OAHN6B,UAGM,GAFJ7B,mBAAA,CAA+D,OAA/D8B,UAA+D,EAAAC,gBAAA,CAApCb,MAAA,CAAAc,UAAU,CAACC,WAAW,uB,4BACjDjC,mBAAA,CAAmC;UAA9BJ,KAAK,EAAC;QAAY,GAAC,OAAK,qB;QA/B7CgB,CAAA;;MAAAA,CAAA;QAqCQV,YAAA,CAYSsB,iBAAA;MAZAC,IAAI,EAAE;IAAC;MArCxBpB,OAAA,EAAAC,QAAA,CAsCU,MAUU,CAVVJ,YAAA,CAUUwB,kBAAA;QAVD9B,KAAK,EAAC;MAAW;QAtCpCS,OAAA,EAAAC,QAAA,CAuCY,MAQM,CARNN,mBAAA,CAQM,OARNkC,UAQM,G,4BAPJlC,mBAAA,CAEM;UAFDJ,KAAK,EAAC;QAAW,IACpBI,mBAAA,CAAyD;UAAtDJ,KAAK,EAAC,oBAAoB;UAACgC,KAAsB,EAAtB;YAAA;UAAA;iCAEhC5B,mBAAA,CAGM,OAHNmC,UAGM,GAFJnC,mBAAA,CAAiE,OAAjEoC,WAAiE,EAAAL,gBAAA,CAAtCb,MAAA,CAAAc,UAAU,CAACK,aAAa,uB,4BACnDrC,mBAAA,CAAiC;UAA5BJ,KAAK,EAAC;QAAY,GAAC,KAAG,qB;QA7C3CgB,CAAA;;MAAAA,CAAA;QAmDQV,YAAA,CAYSsB,iBAAA;MAZAC,IAAI,EAAE;IAAC;MAnDxBpB,OAAA,EAAAC,QAAA,CAoDU,MAUU,CAVVJ,YAAA,CAUUwB,kBAAA;QAVD9B,KAAK,EAAC;MAAW;QApDpCS,OAAA,EAAAC,QAAA,CAqDY,MAQM,CARNN,mBAAA,CAQM,OARNsC,WAQM,G,4BAPJtC,mBAAA,CAEM;UAFDJ,KAAK,EAAC;QAAW,IACpBI,mBAAA,CAAoD;UAAjDJ,KAAK,EAAC,eAAe;UAACgC,KAAsB,EAAtB;YAAA;UAAA;iCAE3B5B,mBAAA,CAGM,OAHNuC,WAGM,GAFJvC,mBAAA,CAAmE,OAAnEwC,WAAmE,EAAAT,gBAAA,CAAxCb,MAAA,CAAAc,UAAU,CAACS,eAAe,uB,4BACrDzC,mBAAA,CAAiC;UAA5BJ,KAAK,EAAC;QAAY,GAAC,KAAG,qB;QA3D3CgB,CAAA;;MAAAA,CAAA;QAiEQV,YAAA,CAYSsB,iBAAA;MAZAC,IAAI,EAAE;IAAC;MAjExBpB,OAAA,EAAAC,QAAA,CAkEU,MAUU,CAVVJ,YAAA,CAUUwB,kBAAA;QAVD9B,KAAK,EAAC;MAAW;QAlEpCS,OAAA,EAAAC,QAAA,CAmEY,MAQM,CARNN,mBAAA,CAQM,OARN0C,WAQM,G,4BAPJ1C,mBAAA,CAEM;UAFDJ,KAAK,EAAC;QAAW,IACpBI,mBAAA,CAAsD;UAAnDJ,KAAK,EAAC,iBAAiB;UAACgC,KAAsB,EAAtB;YAAA;UAAA;iCAE7B5B,mBAAA,CAGM,OAHN2C,WAGM,GAFJ3C,mBAAA,CAAgE,OAAhE4C,WAAgE,EAAAb,gBAAA,CAArCb,MAAA,CAAAc,UAAU,CAACa,YAAY,uB,4BAClD7C,mBAAA,CAAgC;UAA3BJ,KAAK,EAAC;QAAY,GAAC,IAAE,qB;QAzE1CgB,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;QAiFIb,mBAAA,YAAe,EACfG,YAAA,CAwHUwB,kBAAA;IAxHD9B,KAAK,EAAC;EAAgB;IAClBkD,MAAM,EAAAxC,QAAA,CACf,MAkBM,CAlBNN,mBAAA,CAkBM,OAlBN+C,WAkBM,G,4BAjBJ/C,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAeM,OAfNgD,WAeM,GAdJ9C,YAAA,CAMY+C,oBAAA;MA7FxBC,UAAA,EAuFgChC,MAAA,CAAAiC,YAAY;MAvF5C,uBAAAzC,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAuFgCC,MAAA,CAAAiC,YAAY,GAAAlC,MAAA;MAAEmC,WAAW,EAAC,MAAM;MAACC,SAAS,EAAT,EAAS;MAACC,IAAI,EAAC;;MAvFhFjD,OAAA,EAAAC,QAAA,CAwFc,MAA2C,CAA3CJ,YAAA,CAA2CqD,oBAAA;QAAhCC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UAC5BvD,YAAA,CAAmDqD,oBAAA;QAAxCC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7BvD,YAAA,CAAqDqD,oBAAA;QAA1CC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7BvD,YAAA,CAAiDqD,oBAAA;QAAtCC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UAC5BvD,YAAA,CAAmDqD,oBAAA;QAAxCC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;MA5F3C7C,CAAA;uCA+FYV,YAAA,CAKY+C,oBAAA;MApGxBC,UAAA,EA+FgChC,MAAA,CAAAwC,UAAU;MA/F1C,uBAAAhD,MAAA,QAAAA,MAAA,MAAAO,MAAA,IA+FgCC,MAAA,CAAAwC,UAAU,GAAAzC,MAAA;MAAEmC,WAAW,EAAC,MAAM;MAACC,SAAS,EAAT,EAAS;MAACC,IAAI,EAAC;;MA/F9EjD,OAAA,EAAAC,QAAA,CAgGc,MAA2C,CAA3CJ,YAAA,CAA2CqD,oBAAA;QAAhCC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UAC5BvD,YAAA,CAA4DqD,oBAAA;QAAjDC,KAAK,EAAC,QAAQ;QAACC,KAAK,EAAC;UAChCvD,YAAA,CAA2DqD,oBAAA;QAAhDC,KAAK,EAAC,QAAQ;QAACC,KAAK,EAAC;UAChCvD,YAAA,CAA0DqD,oBAAA;QAA/CC,KAAK,EAAC,MAAM;QAACC,KAAK,EAAC;;MAnG5C7C,CAAA;;IAAAP,OAAA,EAAAC,QAAA,CAyGM,MAmFW,C,+BAnFXqD,YAAA,CAmFWC,mBAAA;MAnFAC,IAAI,EAAE3C,MAAA,CAAA4C,aAAa;MAAsBlC,KAAmB,EAAnB;QAAA;MAAA;;MAzG1DvB,OAAA,EAAAC,QAAA,CA0GQ,MAOkB,CAPlBJ,YAAA,CAOkB6D,0BAAA;QAPDC,IAAI,EAAC,UAAU;QAACR,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3CnD,OAAO,EAAAC,QAAA,CAIV2D,KAJiB,KACvBjE,mBAAA,CAGM,OAHNkE,WAGM,GAFJlE,mBAAA,CAAyC,gBAAA+B,gBAAA,CAA9BkC,KAAK,CAACE,GAAG,CAACC,QAAQ,kBAC7BpE,mBAAA,CAA2D,OAA3DqE,WAA2D,EAAtC,MAAI,GAAAtC,gBAAA,CAAGkC,KAAK,CAACE,GAAG,CAACG,YAAY,iB;QA9GhE1D,CAAA;UAmHQV,YAAA,CAMkB6D,0BAAA;QANDC,IAAI,EAAC,gBAAgB;QAACR,KAAK,EAAC,MAAM;QAACe,KAAK,EAAC;;QAC7ClE,OAAO,EAAAC,QAAA,CAGP2D,KAHc,KACvB/D,YAAA,CAESsE,iBAAA;UAFAzD,IAAI,EAAEG,MAAA,CAAAuD,cAAc,CAACR,KAAK,CAACE,GAAG,CAACO,cAAc;UAAGpB,IAAI,EAAC;;UArH1EjD,OAAA,EAAAC,QAAA,CAsHc,MAA2C,CAtHzDK,gBAAA,CAAAoB,gBAAA,CAsHiBb,MAAA,CAAAyD,WAAW,CAACV,KAAK,CAACE,GAAG,CAACO,cAAc,kB;UAtHrD9D,CAAA;;QAAAA,CAAA;UA2HQV,YAAA,CAMkB6D,0BAAA;QANDC,IAAI,EAAC,QAAQ;QAACR,KAAK,EAAC,IAAI;QAACe,KAAK,EAAC;;QACnClE,OAAO,EAAAC,QAAA,CAGP2D,KAHc,KACvB/D,YAAA,CAESsE,iBAAA;UAFAzD,IAAI,EAAEG,MAAA,CAAA0D,gBAAgB,CAACX,KAAK,CAACE,GAAG,CAACU,MAAM;UAAGvB,IAAI,EAAC;;UA7HpEjD,OAAA,EAAAC,QAAA,CA8Hc,MAAqC,CA9HnDK,gBAAA,CAAAoB,gBAAA,CA8HiBb,MAAA,CAAA4D,aAAa,CAACb,KAAK,CAACE,GAAG,CAACU,MAAM,kB;UA9H/CjE,CAAA;;QAAAA,CAAA;UAmIQV,YAAA,CAQkB6D,0BAAA;QARDC,IAAI,EAAC,UAAU;QAACR,KAAK,EAAC,IAAI;QAACe,KAAK,EAAC;;QACrClE,OAAO,EAAAC,QAAA,CAKF2D,KALS,KACvB/D,YAAA,CAIc6E,sBAAA;UAHXC,UAAU,EAAEf,KAAK,CAACE,GAAG,CAACc,QAAQ;UAC9BJ,MAAM,EAAE3D,MAAA,CAAAgE,iBAAiB,CAACjB,KAAK,CAACE,GAAG,CAACU,MAAM;UAC1C,cAAY,EAAE;;QAxI7BjE,CAAA;UA6IQV,YAAA,CAIkB6D,0BAAA;QAJDC,IAAI,EAAC,YAAY;QAACR,KAAK,EAAC,MAAM;QAACe,KAAK,EAAC;;QACzClE,OAAO,EAAAC,QAAA,CACsB2D,KADf,KA9InCtD,gBAAA,CAAAoB,gBAAA,CA+Ieb,MAAA,CAAAiE,UAAU,CAAClB,KAAK,CAACE,GAAG,CAACiB,UAAU,kB;QA/I9CxE,CAAA;UAmJQV,YAAA,CAIkB6D,0BAAA;QAJDC,IAAI,EAAC,UAAU;QAACR,KAAK,EAAC,IAAI;QAACe,KAAK,EAAC;;QACrClE,OAAO,EAAAC,QAAA,CACwB2D,KADjB,KApJnCtD,gBAAA,CAAAoB,gBAAA,CAqJeb,MAAA,CAAAmE,cAAc,CAACpB,KAAK,CAACE,GAAG,CAACmB,QAAQ,kB;QArJhD1E,CAAA;UAyJQV,YAAA,CAkCkB6D,0BAAA;QAlCDP,KAAK,EAAC,IAAI;QAACe,KAAK,EAAC,KAAK;QAACgB,KAAK,EAAC;;QACjClF,OAAO,EAAAC,QAAA,CA+BE2D,KA/BK,KACvB/D,YAAA,CA8BkBsF,0BAAA;UA9BDlC,IAAI,EAAC;QAAO;UA3JzCjD,OAAA,EAAAC,QAAA,CA4Jc,MAKY,CALZJ,YAAA,CAKYY,oBAAA;YAJVC,IAAI,EAAC,SAAS;YACbC,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAuE,QAAQ,CAACxB,KAAK,CAACE,GAAG;YACzBuB,IAAI,EAAE;;YA/JvBrF,OAAA,EAAAC,QAAA,CA+JuC,MAEzBI,MAAA,SAAAA,MAAA,QAjKdC,gBAAA,CA+JuC,MAEzB,E;YAjKdC,CAAA;4DAoKsBqD,KAAK,CAACE,GAAG,CAACU,MAAM,kB,cADxBlB,YAAA,CAMY7C,oBAAA;YAzK1B6E,GAAA;YAqKgB5E,IAAI,EAAC,SAAS;YACbC,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAA0E,QAAQ,CAAC3B,KAAK,CAACE,GAAG;YACzBuB,IAAI,EAAE;;YAvKvBrF,OAAA,EAAAC,QAAA,CAuK8C,MAEhCI,MAAA,SAAAA,MAAA,QAzKdC,gBAAA,CAuK8C,MAEhC,E;YAzKdC,CAAA;8DAAAb,mBAAA,gBA4KsBkE,KAAK,CAACE,GAAG,CAACU,MAAM,iBAAiBZ,KAAK,CAACE,GAAG,CAACU,MAAM,kB,cADzDlB,YAAA,CAMY7C,oBAAA;YAjL1B6E,GAAA;YA6KgB5E,IAAI,EAAC,SAAS;YACbC,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAA2E,WAAW,CAAC5B,KAAK,CAACE,GAAG;YAC5BuB,IAAI,EAAE;;YA/KvBrF,OAAA,EAAAC,QAAA,CA+K0C,MAE5BI,MAAA,SAAAA,MAAA,QAjLdC,gBAAA,CA+K0C,MAE5B,E;YAjLdC,CAAA;8DAAAb,mBAAA,gBAmLcG,YAAA,CAKYY,oBAAA;YAJVC,IAAI,EAAC,QAAQ;YACZC,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAA4E,UAAU,CAAC7B,KAAK,CAACE,GAAG;YAC3BuB,IAAI,EAAE;;YAtLvBrF,OAAA,EAAAC,QAAA,CAsLyC,MAE3BI,MAAA,SAAAA,MAAA,QAxLdC,gBAAA,CAsLyC,MAE3B,E;YAxLdC,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;wDAyGiDM,MAAA,CAAA6E,OAAO,E,GAsFlD/F,mBAAA,CAUM,OAVNgG,WAUM,GATJ9F,YAAA,CAQgB+F,wBAAA;MAPN,cAAY,EAAE/E,MAAA,CAAAgF,WAAW;MAjM3C,wBAAAxF,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAiMgCC,MAAA,CAAAgF,WAAW,GAAAjF,MAAA;MACzB,WAAS,EAAEC,MAAA,CAAAiF,QAAQ;MAlMrC,qBAAAzF,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAkM6BC,MAAA,CAAAiF,QAAQ,GAAAlF,MAAA;MAC1B,YAAU,EAAE,iBAAiB;MAC7BmF,KAAK,EAAElF,MAAA,CAAAmF,UAAU;MAClBC,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAErF,MAAA,CAAAsF,gBAAgB;MAC7BC,eAAc,EAAEvF,MAAA,CAAAwF;;IAvM3B9F,CAAA;MA4MIb,mBAAA,aAAgB,EAChBG,YAAA,CAoCYyG,oBAAA;IAjPhBzD,UAAA,EA8MehC,MAAA,CAAAC,gBAAgB;IA9M/B,uBAAAT,MAAA,SAAAA,MAAA,OAAAO,MAAA,IA8MeC,MAAA,CAAAC,gBAAgB,GAAAF,MAAA;IACzB2F,KAAK,EAAC,QAAQ;IACdrC,KAAK,EAAC,OAAO;IACZ,sBAAoB,EAAE;;IAwBZsC,MAAM,EAAAvG,QAAA,CACf,MAKO,CALPN,mBAAA,CAKO,QALP8G,WAKO,GAJL5G,YAAA,CAA2DY,oBAAA;MAA/CE,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;;MA3O7Cd,OAAA,EAAAC,QAAA,CA2OuD,MAAEI,MAAA,SAAAA,MAAA,QA3OzDC,gBAAA,CA2OuD,IAAE,E;MA3OzDC,CAAA;QA4OUV,YAAA,CAEYY,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEE,MAAA,CAAA6F,gBAAgB;MAAGhB,OAAO,EAAE7E,MAAA,CAAA8F;;MA5OxE3G,OAAA,EAAAC,QAAA,CA4OkF,MAExEI,MAAA,SAAAA,MAAA,QA9OVC,gBAAA,CA4OkF,SAExE,E;MA9OVC,CAAA;;IAAAP,OAAA,EAAAC,QAAA,CAmNM,MAoBU,CApBVJ,YAAA,CAoBU+G,kBAAA;MApBAC,KAAK,EAAEhG,MAAA,CAAAiG,UAAU;MAAGC,KAAK,EAAElG,MAAA,CAAAmG,WAAW;MAAEC,GAAG,EAAC,eAAe;MAAC,aAAW,EAAC;;MAnNxFjH,OAAA,EAAAC,QAAA,CAoNQ,MAEe,CAFfJ,YAAA,CAEeqH,uBAAA;QAFD/D,KAAK,EAAC,MAAM;QAACQ,IAAI,EAAC;;QApNxC3D,OAAA,EAAAC,QAAA,CAqNU,MAAyE,CAAzEJ,YAAA,CAAyEsH,mBAAA;UArNnFtE,UAAA,EAqN6BhC,MAAA,CAAAiG,UAAU,CAAC/C,QAAQ;UArNhD,uBAAA1D,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAqN6BC,MAAA,CAAAiG,UAAU,CAAC/C,QAAQ,GAAAnD,MAAA;UAAEmC,WAAW,EAAC;;QArN9DxC,CAAA;UAwNQV,YAAA,CAMeqH,uBAAA;QAND/D,KAAK,EAAC,MAAM;QAACQ,IAAI,EAAC;;QAxNxC3D,OAAA,EAAAC,QAAA,CAyNU,MAIY,CAJZJ,YAAA,CAIY+C,oBAAA;UA7NtBC,UAAA,EAyN8BhC,MAAA,CAAAiG,UAAU,CAACzC,cAAc;UAzNvD,uBAAAhE,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAyN8BC,MAAA,CAAAiG,UAAU,CAACzC,cAAc,GAAAzD,MAAA;UAAEmC,WAAW,EAAC,QAAQ;UAACxB,KAAmB,EAAnB;YAAA;UAAA;;UAzN9EvB,OAAA,EAAAC,QAAA,CA0NY,MAA6D,CAA7DJ,YAAA,CAA6DqD,oBAAA;YAAlDC,KAAK,EAAC,SAAS;YAACC,KAAK,EAAC;cACjCvD,YAAA,CAA2DqD,oBAAA;YAAhDC,KAAK,EAAC,QAAQ;YAACC,KAAK,EAAC;cAChCvD,YAAA,CAA4DqD,oBAAA;YAAjDC,KAAK,EAAC,QAAQ;YAACC,KAAK,EAAC;;UA5N5C7C,CAAA;;QAAAA,CAAA;UAgOQV,YAAA,CAEeqH,uBAAA;QAFD/D,KAAK,EAAC,QAAQ;QAACQ,IAAI,EAAC;;QAhO1C3D,OAAA,EAAAC,QAAA,CAiOU,MAAmF,CAAnFJ,YAAA,CAAmFsH,mBAAA;UAjO7FtE,UAAA,EAiO6BhC,MAAA,CAAAiG,UAAU,CAACM,cAAc;UAjOtD,uBAAA/G,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAiO6BC,MAAA,CAAAiG,UAAU,CAACM,cAAc,GAAAxG,MAAA;UAAEmC,WAAW,EAAC;;QAjOpExC,CAAA;UAoOQV,YAAA,CAEeqH,uBAAA;QAFD/D,KAAK,EAAC;MAAO;QApOnCnD,OAAA,EAAAC,QAAA,CAqOU,MAAmD,CAAnDJ,YAAA,CAAmDwH,oBAAA;UArO7DxE,UAAA,EAqO8BhC,MAAA,CAAAiG,UAAU,CAACQ,MAAM;UArO/C,uBAAAjH,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAqO8BC,MAAA,CAAAiG,UAAU,CAACQ,MAAM,GAAA1G,MAAA;;QArO/CL,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}