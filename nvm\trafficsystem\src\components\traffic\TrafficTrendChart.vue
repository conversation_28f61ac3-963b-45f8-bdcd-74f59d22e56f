<template>
  <div class="traffic-trend-chart">
    <div class="chart-header">
      <h4>车流趋势分析</h4>
      <div class="trend-indicator" :class="trendClass">
        <el-icon>
          <component :is="trendIcon" />
        </el-icon>
        <span>{{ trendInfo.description }}</span>
      </div>
    </div>
    
    <div class="chart-container">
      <div ref="chartRef" class="chart"></div>
    </div>
    
    <div class="moving-averages">
      <div class="average-item">
        <span class="average-label">5帧平均:</span>
        <span class="average-value">{{ movingAverages.frame5 }}</span>
      </div>
      <div class="average-item">
        <span class="average-label">10帧平均:</span>
        <span class="average-value">{{ movingAverages.frame10 }}</span>
      </div>
      <div class="average-item">
        <span class="average-label">30帧平均:</span>
        <span class="average-value">{{ movingAverages.frame30 }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { TrendCharts, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'

export default {
  name: 'TrafficTrendChart',
  components: {
    TrendCharts,
    ArrowUp,
    ArrowDown,
    Minus
  },
  props: {
    vehicleData: {
      type: Array,
      required: true,
      default: () => []
    },
    movingAverages: {
      type: Object,
      required: true,
      default: () => ({ frame5: 0, frame10: 0, frame30: 0 })
    },
    trendInfo: {
      type: Object,
      required: true,
      default: () => ({ trend: 'stable', strength: 0, description: '车流稳定' })
    }
  },
  setup(props) {
    const chartRef = ref(null)
    let chartInstance = null
    
    const trendClass = computed(() => `trend-${props.trendInfo.trend}`)
    
    const trendIcon = computed(() => {
      switch (props.trendInfo.trend) {
        case 'rising': return ArrowUp
        case 'falling': return ArrowDown
        default: return Minus
      }
    })
    
    const initChart = () => {
      if (!chartRef.value) return
      
      chartInstance = echarts.init(chartRef.value)
      updateChart()
    }
    
    const updateChart = () => {
      if (!chartInstance) return
      
      const data = props.vehicleData.slice(-20) // 显示最近20个数据点
      const xAxisData = data.map((_, index) => `帧${index + 1}`)
      
      const option = {
        grid: {
          top: 20,
          right: 20,
          bottom: 40,
          left: 40
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisLine: {
            lineStyle: { color: '#e8e8e8' }
          }
        },
        yAxis: {
          type: 'value',
          name: '车辆数',
          nameTextStyle: {
            fontSize: 10,
            color: '#666'
          },
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisLine: {
            lineStyle: { color: '#e8e8e8' }
          },
          splitLine: {
            lineStyle: { color: '#f0f0f0' }
          }
        },
        series: [
          {
            name: '实时车辆数',
            type: 'line',
            data: data,
            smooth: true,
            lineStyle: {
              color: '#1890ff',
              width: 2
            },
            itemStyle: {
              color: '#1890ff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                  { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
                ]
              }
            },
            symbol: 'circle',
            symbolSize: 4
          },
          {
            name: '5帧平均',
            type: 'line',
            data: new Array(data.length).fill(props.movingAverages.frame5),
            lineStyle: {
              color: '#52c41a',
              width: 1,
              type: 'dashed'
            },
            symbol: 'none'
          },
          {
            name: '10帧平均',
            type: 'line',
            data: new Array(data.length).fill(props.movingAverages.frame10),
            lineStyle: {
              color: '#faad14',
              width: 1,
              type: 'dashed'
            },
            symbol: 'none'
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: { color: '#fff', fontSize: 12 },
          formatter: (params) => {
            let result = `${params[0].axisValue}<br/>`
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value}辆<br/>`
            })
            return result
          }
        }
      }
      
      chartInstance.setOption(option)
    }
    
    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }
    
    watch(() => [props.vehicleData, props.movingAverages], updateChart, { deep: true })
    
    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })
    
    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', resizeChart)
    })
    
    return {
      chartRef,
      trendClass,
      trendIcon
    }
  }
}
</script>

<style scoped>
.traffic-trend-chart {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
}

.chart-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.trend-rising {
  background: #f6ffed;
  color: #52c41a;
}

.trend-falling {
  background: #fff2e8;
  color: #fa8c16;
}

.trend-stable {
  background: #e6f7ff;
  color: #1890ff;
}

.chart-container {
  padding: 20px;
}

.chart {
  width: 100%;
  height: 200px;
}

.moving-averages {
  display: flex;
  justify-content: space-around;
  padding: 16px 20px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

.average-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.average-label {
  font-size: 12px;
  color: #666;
}

.average-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .chart {
    height: 150px;
  }
  
  .moving-averages {
    flex-direction: column;
    gap: 8px;
  }
  
  .average-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
</style>
