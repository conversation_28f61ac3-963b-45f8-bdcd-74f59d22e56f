package com.traffic.analysis.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.List;

/**
 * 优化结果实体类
 * 对应MongoDB的optimization_results集合
 */
@Data
@Document(collection = "optimization_results")
public class OptimizationResult {
    
    @Id
    private String id;
    
    /**
     * 关联的仿真任务ID
     */
    @Field("simulation_task_id")
    private String simulationTaskId;
    
    /**
     * 优化类型：signal_timing, flow_balance, comprehensive
     */
    @Field("optimization_type")
    private String optimizationType;
    
    /**
     * 优化方法：webster_method, flow_balance_analysis等
     */
    @Field("optimization_method")
    private String optimizationMethod;
    
    /**
     * 原始交通数据
     */
    @Field("original_data")
    private Map<String, Object> originalData;
    
    /**
     * 优化后的配置
     */
    @Field("optimized_config")
    private Map<String, Object> optimizedConfig;
    
    /**
     * 改善效果指标
     */
    @Field("improvement_metrics")
    private ImprovementMetrics improvementMetrics;
    
    /**
     * 信号灯配时优化结果
     */
    @Field("signal_timing_result")
    private SignalTimingResult signalTimingResult;
    
    /**
     * 流量平衡优化结果
     */
    @Field("flow_balance_result")
    private FlowBalanceResult flowBalanceResult;
    
    /**
     * 优化建议列表
     */
    @Field("recommendations")
    private List<Recommendation> recommendations;
    
    /**
     * 图表数据
     */
    @Field("charts_data")
    private Map<String, Object> chartsData;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Field("create_time")
    private LocalDateTime createTime;
    
    /**
     * 改善效果指标内部类
     */
    @Data
    public static class ImprovementMetrics {
        /**
         * 总体改善百分比
         */
        private Double overallImprovement;
        
        /**
         * 延误减少百分比
         */
        private Double delayReduction;
        
        /**
         * 通行能力提升百分比
         */
        private Double throughputIncrease;
        
        /**
         * 平均速度提升
         */
        private Double speedImprovement;
        
        /**
         * 燃油节省百分比
         */
        private Double fuelSaving;
        
        /**
         * 排放减少百分比
         */
        private Double emissionReduction;
        
        /**
         * 等待时间减少（秒）
         */
        private Double waitingTimeReduction;
    }
    
    /**
     * 信号灯配时优化结果内部类
     */
    @Data
    public static class SignalTimingResult {
        /**
         * 优化前的周期时长
         */
        private Integer originalCycleTime;
        
        /**
         * 优化后的周期时长
         */
        private Integer optimizedCycleTime;
        
        /**
         * 优化前的相位配时
         */
        private List<Phase> originalPhases;
        
        /**
         * 优化后的相位配时
         */
        private List<Phase> optimizedPhases;
        
        /**
         * 关键流量比
         */
        private Map<String, Double> criticalRatios;
        
        /**
         * 配时改善百分比
         */
        private Double timingImprovement;
        
        /**
         * 相位内部类
         */
        @Data
        public static class Phase {
            /**
             * 相位持续时间
             */
            private Integer duration;
            
            /**
             * 信号状态
             */
            private String state;
            
            /**
             * 相位描述
             */
            private String description;
        }
    }
    
    /**
     * 流量平衡优化结果内部类
     */
    @Data
    public static class FlowBalanceResult {
        /**
         * 当前流量不平衡度
         */
        private Double currentImbalance;
        
        /**
         * 优化后流量不平衡度
         */
        private Double optimizedImbalance;
        
        /**
         * 各方向流量数据
         */
        private Map<String, DirectionFlow> directionFlows;
        
        /**
         * 平衡改善百分比
         */
        private Double balanceImprovement;
        
        /**
         * 方向流量内部类
         */
        @Data
        public static class DirectionFlow {
            /**
             * 流量率（vehicles/hour）
             */
            private Double flowRate;
            
            /**
             * 车辆数量
             */
            private Integer vehicleCount;
            
            /**
             * 密度（vehicles/km）
             */
            private Double density;
            
            /**
             * 平均速度（m/s）
             */
            private Double averageSpeed;
            
            /**
             * 建议调整
             */
            private String suggestion;
        }
    }
    
    /**
     * 优化建议内部类
     */
    @Data
    public static class Recommendation {
        /**
         * 建议类型
         */
        private String type;
        
        /**
         * 优先级：high, medium, low
         */
        private String priority;
        
        /**
         * 建议标题
         */
        private String title;
        
        /**
         * 建议描述
         */
        private String description;
        
        /**
         * 实施行动
         */
        private String action;
        
        /**
         * 预期效果
         */
        private String expectedEffect;
        
        /**
         * 实施难度：easy, medium, hard
         */
        private String difficulty;
        
        /**
         * 实施时间
         */
        private String implementationTime;
    }
    
    /**
     * 构造函数
     */
    public OptimizationResult() {
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 计算总体评分
     */
    public Double calculateOverallScore() {
        if (improvementMetrics == null) {
            return 0.0;
        }
        
        Double overall = improvementMetrics.getOverallImprovement();
        Double delay = improvementMetrics.getDelayReduction();
        Double throughput = improvementMetrics.getThroughputIncrease();
        
        // 加权计算总体评分
        double score = 0.0;
        int count = 0;
        
        if (overall != null) {
            score += overall * 0.4;
            count++;
        }
        if (delay != null) {
            score += delay * 0.3;
            count++;
        }
        if (throughput != null) {
            score += throughput * 0.3;
            count++;
        }
        
        return count > 0 ? score : 0.0;
    }
    
    /**
     * 获取主要建议
     */
    public Recommendation getPrimaryRecommendation() {
        if (recommendations == null || recommendations.isEmpty()) {
            return null;
        }
        
        // 返回优先级最高的建议
        return recommendations.stream()
                .filter(r -> "high".equals(r.getPriority()))
                .findFirst()
                .orElse(recommendations.get(0));
    }
    
    /**
     * 检查是否有显著改善
     */
    public boolean hasSignificantImprovement() {
        if (improvementMetrics == null) {
            return false;
        }
        
        Double overall = improvementMetrics.getOverallImprovement();
        return overall != null && overall > 10.0; // 改善超过10%认为是显著的
    }
}
