{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'SimulationMonitor',\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    },\n    isRunning: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['data-updated'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isConnected = ref(false);\n    const simulationTime = ref(0);\n    const vehicleCount = ref(0);\n    const averageSpeed = ref(0);\n    const trafficLightCount = ref(4);\n\n    // 图表引用\n    const flowChartRef = ref(null);\n    const speedChartRef = ref(null);\n    let flowChart = null;\n    let speedChart = null;\n\n    // 方向数据\n    const directions = reactive([{\n      name: 'east',\n      label: '东向',\n      icon: 'el-icon-right',\n      vehicleCount: 0,\n      flowRate: 0,\n      avgSpeed: 0,\n      density: 0\n    }, {\n      name: 'south',\n      label: '南向',\n      icon: 'el-icon-bottom',\n      vehicleCount: 0,\n      flowRate: 0,\n      avgSpeed: 0,\n      density: 0\n    }, {\n      name: 'west',\n      label: '西向',\n      icon: 'el-icon-left',\n      vehicleCount: 0,\n      flowRate: 0,\n      avgSpeed: 0,\n      density: 0\n    }, {\n      name: 'north',\n      label: '北向',\n      icon: 'el-icon-top',\n      vehicleCount: 0,\n      flowRate: 0,\n      avgSpeed: 0,\n      density: 0\n    }]);\n\n    // 信号灯状态\n    const trafficLights = reactive({\n      north: 'red',\n      south: 'red',\n      east: 'green',\n      west: 'green'\n    });\n\n    // 信号配时方案\n    const signalPhases = reactive([{\n      phase: '相位1',\n      duration: 30,\n      state: 'GrGr',\n      description: '东西绿灯'\n    }, {\n      phase: '相位2',\n      duration: 5,\n      state: 'yryr',\n      description: '东西黄灯'\n    }, {\n      phase: '相位3',\n      duration: 30,\n      state: 'rGrG',\n      description: '南北绿灯'\n    }, {\n      phase: '相位4',\n      duration: 5,\n      state: 'ryry',\n      description: '南北黄灯'\n    }]);\n\n    // 历史数据\n    const flowData = reactive({\n      times: [],\n      values: []\n    });\n    const speedData = reactive({\n      ranges: ['0-10', '10-20', '20-30', '30-40', '40-50', '50+'],\n      values: [0, 0, 0, 0, 0, 0]\n    });\n\n    // 数据更新定时器\n    let updateTimer = null;\n\n    // 方法\n    const formatTime = seconds => {\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor(seconds % 3600 / 60);\n      const secs = seconds % 60;\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    };\n    const formatSpeed = speed => {\n      return `${speed.toFixed(1)} m/s`;\n    };\n    const getFlowStatusType = flowRate => {\n      if (flowRate < 300) return 'success';\n      if (flowRate < 600) return 'warning';\n      return 'danger';\n    };\n    const getFlowStatusText = flowRate => {\n      if (flowRate < 300) return '畅通';\n      if (flowRate < 600) return '缓慢';\n      return '拥堵';\n    };\n    const getTrafficLightClass = direction => {\n      return `light-${trafficLights[direction]}`;\n    };\n    const initCharts = () => {\n      nextTick(() => {\n        // 初始化车流量图表\n        if (flowChartRef.value) {\n          flowChart = echarts.init(flowChartRef.value);\n          updateFlowChart();\n        }\n\n        // 初始化速度分布图表\n        if (speedChartRef.value) {\n          speedChart = echarts.init(speedChartRef.value);\n          updateSpeedChart();\n        }\n      });\n    };\n    const updateFlowChart = () => {\n      if (!flowChart) return;\n      const option = {\n        title: {\n          text: '实时车流量',\n          textStyle: {\n            fontSize: 14\n          }\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: flowData.times,\n          axisLabel: {\n            formatter: value => {\n              const time = new Date(value);\n              return `${time.getHours()}:${time.getMinutes().toString().padStart(2, '0')}`;\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数'\n        },\n        series: [{\n          data: flowData.values,\n          type: 'line',\n          smooth: true,\n          areaStyle: {\n            opacity: 0.3\n          },\n          lineStyle: {\n            color: '#409eff'\n          }\n        }]\n      };\n      flowChart.setOption(option);\n    };\n    const updateSpeedChart = () => {\n      if (!speedChart) return;\n      const option = {\n        title: {\n          text: '速度分布',\n          textStyle: {\n            fontSize: 14\n          }\n        },\n        tooltip: {\n          trigger: 'item'\n        },\n        series: [{\n          type: 'pie',\n          radius: '60%',\n          data: speedData.ranges.map((range, index) => ({\n            name: `${range} km/h`,\n            value: speedData.values[index]\n          })),\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }]\n      };\n      speedChart.setOption(option);\n    };\n    const updateMonitorData = () => {\n      // 模拟实时数据更新\n      if (props.isRunning) {\n        simulationTime.value += 1;\n        vehicleCount.value = Math.floor(Math.random() * 50) + 20;\n        averageSpeed.value = Math.random() * 20 + 30;\n\n        // 更新方向数据\n        directions.forEach(direction => {\n          direction.vehicleCount = Math.floor(Math.random() * 15) + 5;\n          direction.flowRate = Math.floor(Math.random() * 400) + 200;\n          direction.avgSpeed = Math.random() * 15 + 25;\n          direction.density = Math.random() * 30 + 10;\n        });\n\n        // 更新流量历史数据\n        const now = new Date();\n        flowData.times.push(now.toISOString());\n        flowData.values.push(vehicleCount.value);\n\n        // 保持最近50个数据点\n        if (flowData.times.length > 50) {\n          flowData.times.shift();\n          flowData.values.shift();\n        }\n\n        // 更新速度分布数据\n        speedData.values = speedData.values.map(() => Math.floor(Math.random() * 20));\n\n        // 更新图表\n        updateFlowChart();\n        updateSpeedChart();\n\n        // 模拟信号灯变化\n        if (simulationTime.value % 30 === 0) {\n          // 简单的信号灯切换逻辑\n          if (trafficLights.east === 'green') {\n            trafficLights.east = 'yellow';\n            trafficLights.west = 'yellow';\n          } else if (trafficLights.east === 'yellow') {\n            trafficLights.east = 'red';\n            trafficLights.west = 'red';\n            trafficLights.north = 'green';\n            trafficLights.south = 'green';\n          } else if (trafficLights.north === 'green') {\n            trafficLights.north = 'yellow';\n            trafficLights.south = 'yellow';\n          } else {\n            trafficLights.north = 'red';\n            trafficLights.south = 'red';\n            trafficLights.east = 'green';\n            trafficLights.west = 'green';\n          }\n        }\n        isConnected.value = true;\n        emit('data-updated', {\n          simulationTime: simulationTime.value,\n          vehicleCount: vehicleCount.value,\n          averageSpeed: averageSpeed.value,\n          directions: directions\n        });\n      } else {\n        isConnected.value = false;\n      }\n    };\n    const startMonitoring = () => {\n      if (updateTimer) {\n        clearInterval(updateTimer);\n      }\n      updateTimer = setInterval(updateMonitorData, 1000); // 每秒更新\n    };\n    const stopMonitoring = () => {\n      if (updateTimer) {\n        clearInterval(updateTimer);\n        updateTimer = null;\n      }\n      isConnected.value = false;\n    };\n\n    // 生命周期\n    onMounted(() => {\n      initCharts();\n      if (props.isRunning) {\n        startMonitoring();\n      }\n    });\n    onUnmounted(() => {\n      stopMonitoring();\n      if (flowChart) {\n        flowChart.dispose();\n      }\n      if (speedChart) {\n        speedChart.dispose();\n      }\n    });\n\n    // 监听运行状态变化\n    const {\n      isRunning\n    } = props;\n    if (isRunning) {\n      startMonitoring();\n    } else {\n      stopMonitoring();\n    }\n    return {\n      // 响应式数据\n      isConnected,\n      simulationTime,\n      vehicleCount,\n      averageSpeed,\n      trafficLightCount,\n      directions,\n      trafficLights,\n      signalPhases,\n      // 图表引用\n      flowChartRef,\n      speedChartRef,\n      // 方法\n      formatTime,\n      formatSpeed,\n      getFlowStatusType,\n      getFlowStatusText,\n      getTrafficLightClass,\n      startMonitoring,\n      stopMonitoring\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "onUnmounted", "nextTick", "echarts", "name", "props", "simulationId", "type", "String", "required", "isRunning", "Boolean", "default", "emits", "setup", "emit", "isConnected", "simulationTime", "vehicleCount", "averageSpeed", "trafficLightCount", "flowChartRef", "speedChartRef", "flowChart", "speedChart", "directions", "label", "icon", "flowRate", "avgSpeed", "density", "trafficLights", "north", "south", "east", "west", "signalPhases", "phase", "duration", "state", "description", "flowData", "times", "values", "speedData", "ranges", "updateTimer", "formatTime", "seconds", "hours", "Math", "floor", "minutes", "secs", "toString", "padStart", "formatSpeed", "speed", "toFixed", "getFlowStatusType", "getFlowStatusText", "getTrafficLightClass", "direction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "init", "updateFlowChart", "updateSpeedChart", "option", "title", "text", "textStyle", "fontSize", "tooltip", "trigger", "xAxis", "data", "axisLabel", "formatter", "time", "Date", "getHours", "getMinutes", "yAxis", "series", "smooth", "areaStyle", "opacity", "lineStyle", "color", "setOption", "radius", "map", "range", "index", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "updateMonitorData", "random", "for<PERSON>ach", "now", "push", "toISOString", "length", "shift", "startMonitoring", "clearInterval", "setInterval", "stopMonitoring", "dispose"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\SimulationMonitor.vue"], "sourcesContent": ["<template>\n  <div class=\"simulation-monitor\">\n    <el-row :gutter=\"20\">\n      <!-- 实时数据卡片 -->\n      <el-col :span=\"24\">\n        <el-card class=\"monitor-card\" shadow=\"hover\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3><i class=\"el-icon-monitor\"></i> 实时仿真监控</h3>\n              <el-tag :type=\"isConnected ? 'success' : 'danger'\" size=\"small\">\n                {{ isConnected ? '已连接' : '未连接' }}\n              </el-tag>\n            </div>\n          </template>\n\n          <!-- 关键指标展示 -->\n          <div class=\"metrics-grid\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"6\">\n                <div class=\"metric-card\">\n                  <div class=\"metric-icon\">\n                    <i class=\"el-icon-time\" style=\"color: #409eff\"></i>\n                  </div>\n                  <div class=\"metric-content\">\n                    <div class=\"metric-value\">{{ formatTime(simulationTime) }}</div>\n                    <div class=\"metric-label\">仿真时间</div>\n                  </div>\n                </div>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <div class=\"metric-card\">\n                  <div class=\"metric-icon\">\n                    <i class=\"el-icon-truck\" style=\"color: #67c23a\"></i>\n                  </div>\n                  <div class=\"metric-content\">\n                    <div class=\"metric-value\">{{ vehicleCount }}</div>\n                    <div class=\"metric-label\">当前车辆</div>\n                  </div>\n                </div>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <div class=\"metric-card\">\n                  <div class=\"metric-icon\">\n                    <i class=\"el-icon-odometer\" style=\"color: #e6a23c\"></i>\n                  </div>\n                  <div class=\"metric-content\">\n                    <div class=\"metric-value\">{{ formatSpeed(averageSpeed) }}</div>\n                    <div class=\"metric-label\">平均速度</div>\n                  </div>\n                </div>\n              </el-col>\n              \n              <el-col :span=\"6\">\n                <div class=\"metric-card\">\n                  <div class=\"metric-icon\">\n                    <i class=\"el-icon-warning\" style=\"color: #f56c6c\"></i>\n                  </div>\n                  <div class=\"metric-content\">\n                    <div class=\"metric-value\">{{ trafficLightCount }}</div>\n                    <div class=\"metric-label\">信号灯数</div>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 实时图表 -->\n      <el-col :span=\"12\">\n        <el-card class=\"chart-card\" shadow=\"hover\">\n          <template #header>\n            <h4><i class=\"el-icon-data-line\"></i> 车流量变化</h4>\n          </template>\n          <div ref=\"flowChartRef\" class=\"chart-container\"></div>\n        </el-card>\n      </el-col>\n      \n      <el-col :span=\"12\">\n        <el-card class=\"chart-card\" shadow=\"hover\">\n          <template #header>\n            <h4><i class=\"el-icon-pie-chart\"></i> 速度分布</h4>\n          </template>\n          <div ref=\"speedChartRef\" class=\"chart-container\"></div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <!-- 方向流量监控 -->\n      <el-col :span=\"24\">\n        <el-card class=\"direction-card\" shadow=\"hover\">\n          <template #header>\n            <h4><i class=\"el-icon-position\"></i> 各方向流量监控</h4>\n          </template>\n          \n          <div class=\"direction-grid\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"6\" v-for=\"direction in directions\" :key=\"direction.name\">\n                <div class=\"direction-item\">\n                  <div class=\"direction-header\">\n                    <i :class=\"direction.icon\"></i>\n                    <span>{{ direction.label }}</span>\n                  </div>\n                  \n                  <div class=\"direction-metrics\">\n                    <div class=\"direction-metric\">\n                      <span class=\"metric-label\">车辆数:</span>\n                      <span class=\"metric-value\">{{ direction.vehicleCount }}</span>\n                    </div>\n                    <div class=\"direction-metric\">\n                      <span class=\"metric-label\">流量:</span>\n                      <span class=\"metric-value\">{{ direction.flowRate }} 辆/h</span>\n                    </div>\n                    <div class=\"direction-metric\">\n                      <span class=\"metric-label\">速度:</span>\n                      <span class=\"metric-value\">{{ formatSpeed(direction.avgSpeed) }}</span>\n                    </div>\n                    <div class=\"direction-metric\">\n                      <span class=\"metric-label\">密度:</span>\n                      <span class=\"metric-value\">{{ direction.density.toFixed(1) }} 辆/km</span>\n                    </div>\n                  </div>\n                  \n                  <div class=\"direction-status\">\n                    <el-tag :type=\"getFlowStatusType(direction.flowRate)\" size=\"small\">\n                      {{ getFlowStatusText(direction.flowRate) }}\n                    </el-tag>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <!-- 信号灯状态监控 -->\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n      <el-col :span=\"24\">\n        <el-card class=\"traffic-light-card\" shadow=\"hover\">\n          <template #header>\n            <h4><i class=\"el-icon-warning\"></i> 信号灯状态</h4>\n          </template>\n          \n          <div class=\"traffic-light-monitor\">\n            <div class=\"intersection-view\">\n              <div class=\"intersection\">\n                <!-- 交叉口示意图 -->\n                <div class=\"road road-horizontal\"></div>\n                <div class=\"road road-vertical\"></div>\n                \n                <!-- 信号灯 -->\n                <div class=\"traffic-light traffic-light-north\" :class=\"getTrafficLightClass('north')\">\n                  <div class=\"light red\" :class=\"{ active: trafficLights.north === 'red' }\"></div>\n                  <div class=\"light yellow\" :class=\"{ active: trafficLights.north === 'yellow' }\"></div>\n                  <div class=\"light green\" :class=\"{ active: trafficLights.north === 'green' }\"></div>\n                </div>\n                \n                <div class=\"traffic-light traffic-light-south\" :class=\"getTrafficLightClass('south')\">\n                  <div class=\"light red\" :class=\"{ active: trafficLights.south === 'red' }\"></div>\n                  <div class=\"light yellow\" :class=\"{ active: trafficLights.south === 'yellow' }\"></div>\n                  <div class=\"light green\" :class=\"{ active: trafficLights.south === 'green' }\"></div>\n                </div>\n                \n                <div class=\"traffic-light traffic-light-east\" :class=\"getTrafficLightClass('east')\">\n                  <div class=\"light red\" :class=\"{ active: trafficLights.east === 'red' }\"></div>\n                  <div class=\"light yellow\" :class=\"{ active: trafficLights.east === 'yellow' }\"></div>\n                  <div class=\"light green\" :class=\"{ active: trafficLights.east === 'green' }\"></div>\n                </div>\n                \n                <div class=\"traffic-light traffic-light-west\" :class=\"getTrafficLightClass('west')\">\n                  <div class=\"light red\" :class=\"{ active: trafficLights.west === 'red' }\"></div>\n                  <div class=\"light yellow\" :class=\"{ active: trafficLights.west === 'yellow' }\"></div>\n                  <div class=\"light green\" :class=\"{ active: trafficLights.west === 'green' }\"></div>\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"signal-timing-info\">\n              <h5>当前配时方案</h5>\n              <el-table :data=\"signalPhases\" size=\"small\" style=\"width: 100%\">\n                <el-table-column prop=\"phase\" label=\"相位\" width=\"80\"></el-table-column>\n                <el-table-column prop=\"duration\" label=\"时长(秒)\" width=\"100\"></el-table-column>\n                <el-table-column prop=\"state\" label=\"状态\" width=\"120\">\n                  <template #default=\"scope\">\n                    <el-tag size=\"small\">{{ scope.row.state }}</el-tag>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"description\" label=\"描述\"></el-table-column>\n              </el-table>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'SimulationMonitor',\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    },\n    isRunning: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['data-updated'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isConnected = ref(false)\n    const simulationTime = ref(0)\n    const vehicleCount = ref(0)\n    const averageSpeed = ref(0)\n    const trafficLightCount = ref(4)\n    \n    // 图表引用\n    const flowChartRef = ref(null)\n    const speedChartRef = ref(null)\n    let flowChart = null\n    let speedChart = null\n    \n    // 方向数据\n    const directions = reactive([\n      {\n        name: 'east',\n        label: '东向',\n        icon: 'el-icon-right',\n        vehicleCount: 0,\n        flowRate: 0,\n        avgSpeed: 0,\n        density: 0\n      },\n      {\n        name: 'south',\n        label: '南向',\n        icon: 'el-icon-bottom',\n        vehicleCount: 0,\n        flowRate: 0,\n        avgSpeed: 0,\n        density: 0\n      },\n      {\n        name: 'west',\n        label: '西向',\n        icon: 'el-icon-left',\n        vehicleCount: 0,\n        flowRate: 0,\n        avgSpeed: 0,\n        density: 0\n      },\n      {\n        name: 'north',\n        label: '北向',\n        icon: 'el-icon-top',\n        vehicleCount: 0,\n        flowRate: 0,\n        avgSpeed: 0,\n        density: 0\n      }\n    ])\n    \n    // 信号灯状态\n    const trafficLights = reactive({\n      north: 'red',\n      south: 'red',\n      east: 'green',\n      west: 'green'\n    })\n    \n    // 信号配时方案\n    const signalPhases = reactive([\n      { phase: '相位1', duration: 30, state: 'GrGr', description: '东西绿灯' },\n      { phase: '相位2', duration: 5, state: 'yryr', description: '东西黄灯' },\n      { phase: '相位3', duration: 30, state: 'rGrG', description: '南北绿灯' },\n      { phase: '相位4', duration: 5, state: 'ryry', description: '南北黄灯' }\n    ])\n    \n    // 历史数据\n    const flowData = reactive({\n      times: [],\n      values: []\n    })\n    \n    const speedData = reactive({\n      ranges: ['0-10', '10-20', '20-30', '30-40', '40-50', '50+'],\n      values: [0, 0, 0, 0, 0, 0]\n    })\n    \n    // 数据更新定时器\n    let updateTimer = null\n    \n    // 方法\n    const formatTime = (seconds) => {\n      const hours = Math.floor(seconds / 3600)\n      const minutes = Math.floor((seconds % 3600) / 60)\n      const secs = seconds % 60\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n    }\n    \n    const formatSpeed = (speed) => {\n      return `${speed.toFixed(1)} m/s`\n    }\n    \n    const getFlowStatusType = (flowRate) => {\n      if (flowRate < 300) return 'success'\n      if (flowRate < 600) return 'warning'\n      return 'danger'\n    }\n    \n    const getFlowStatusText = (flowRate) => {\n      if (flowRate < 300) return '畅通'\n      if (flowRate < 600) return '缓慢'\n      return '拥堵'\n    }\n    \n    const getTrafficLightClass = (direction) => {\n      return `light-${trafficLights[direction]}`\n    }\n    \n    const initCharts = () => {\n      nextTick(() => {\n        // 初始化车流量图表\n        if (flowChartRef.value) {\n          flowChart = echarts.init(flowChartRef.value)\n          updateFlowChart()\n        }\n        \n        // 初始化速度分布图表\n        if (speedChartRef.value) {\n          speedChart = echarts.init(speedChartRef.value)\n          updateSpeedChart()\n        }\n      })\n    }\n    \n    const updateFlowChart = () => {\n      if (!flowChart) return\n      \n      const option = {\n        title: {\n          text: '实时车流量',\n          textStyle: { fontSize: 14 }\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: flowData.times,\n          axisLabel: {\n            formatter: (value) => {\n              const time = new Date(value)\n              return `${time.getHours()}:${time.getMinutes().toString().padStart(2, '0')}`\n            }\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数'\n        },\n        series: [{\n          data: flowData.values,\n          type: 'line',\n          smooth: true,\n          areaStyle: {\n            opacity: 0.3\n          },\n          lineStyle: {\n            color: '#409eff'\n          }\n        }]\n      }\n      \n      flowChart.setOption(option)\n    }\n    \n    const updateSpeedChart = () => {\n      if (!speedChart) return\n      \n      const option = {\n        title: {\n          text: '速度分布',\n          textStyle: { fontSize: 14 }\n        },\n        tooltip: {\n          trigger: 'item'\n        },\n        series: [{\n          type: 'pie',\n          radius: '60%',\n          data: speedData.ranges.map((range, index) => ({\n            name: `${range} km/h`,\n            value: speedData.values[index]\n          })),\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }]\n      }\n      \n      speedChart.setOption(option)\n    }\n    \n    const updateMonitorData = () => {\n      // 模拟实时数据更新\n      if (props.isRunning) {\n        simulationTime.value += 1\n        vehicleCount.value = Math.floor(Math.random() * 50) + 20\n        averageSpeed.value = Math.random() * 20 + 30\n        \n        // 更新方向数据\n        directions.forEach(direction => {\n          direction.vehicleCount = Math.floor(Math.random() * 15) + 5\n          direction.flowRate = Math.floor(Math.random() * 400) + 200\n          direction.avgSpeed = Math.random() * 15 + 25\n          direction.density = Math.random() * 30 + 10\n        })\n        \n        // 更新流量历史数据\n        const now = new Date()\n        flowData.times.push(now.toISOString())\n        flowData.values.push(vehicleCount.value)\n        \n        // 保持最近50个数据点\n        if (flowData.times.length > 50) {\n          flowData.times.shift()\n          flowData.values.shift()\n        }\n        \n        // 更新速度分布数据\n        speedData.values = speedData.values.map(() => Math.floor(Math.random() * 20))\n        \n        // 更新图表\n        updateFlowChart()\n        updateSpeedChart()\n        \n        // 模拟信号灯变化\n        if (simulationTime.value % 30 === 0) {\n          // 简单的信号灯切换逻辑\n          if (trafficLights.east === 'green') {\n            trafficLights.east = 'yellow'\n            trafficLights.west = 'yellow'\n          } else if (trafficLights.east === 'yellow') {\n            trafficLights.east = 'red'\n            trafficLights.west = 'red'\n            trafficLights.north = 'green'\n            trafficLights.south = 'green'\n          } else if (trafficLights.north === 'green') {\n            trafficLights.north = 'yellow'\n            trafficLights.south = 'yellow'\n          } else {\n            trafficLights.north = 'red'\n            trafficLights.south = 'red'\n            trafficLights.east = 'green'\n            trafficLights.west = 'green'\n          }\n        }\n        \n        isConnected.value = true\n        emit('data-updated', {\n          simulationTime: simulationTime.value,\n          vehicleCount: vehicleCount.value,\n          averageSpeed: averageSpeed.value,\n          directions: directions\n        })\n      } else {\n        isConnected.value = false\n      }\n    }\n    \n    const startMonitoring = () => {\n      if (updateTimer) {\n        clearInterval(updateTimer)\n      }\n      \n      updateTimer = setInterval(updateMonitorData, 1000) // 每秒更新\n    }\n    \n    const stopMonitoring = () => {\n      if (updateTimer) {\n        clearInterval(updateTimer)\n        updateTimer = null\n      }\n      isConnected.value = false\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      initCharts()\n      if (props.isRunning) {\n        startMonitoring()\n      }\n    })\n    \n    onUnmounted(() => {\n      stopMonitoring()\n      if (flowChart) {\n        flowChart.dispose()\n      }\n      if (speedChart) {\n        speedChart.dispose()\n      }\n    })\n    \n    // 监听运行状态变化\n    const { isRunning } = props\n    if (isRunning) {\n      startMonitoring()\n    } else {\n      stopMonitoring()\n    }\n    \n    return {\n      // 响应式数据\n      isConnected,\n      simulationTime,\n      vehicleCount,\n      averageSpeed,\n      trafficLightCount,\n      directions,\n      trafficLights,\n      signalPhases,\n      \n      // 图表引用\n      flowChartRef,\n      speedChartRef,\n      \n      // 方法\n      formatTime,\n      formatSpeed,\n      getFlowStatusType,\n      getFlowStatusText,\n      getTrafficLightClass,\n      startMonitoring,\n      stopMonitoring\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simulation-monitor {\n  padding: 0;\n}\n\n.monitor-card,\n.chart-card,\n.direction-card,\n.traffic-light-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3,\n.card-header h4 {\n  margin: 0;\n  color: #303133;\n}\n\n.metrics-grid {\n  margin-bottom: 20px;\n}\n\n.metric-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border-left: 4px solid #409eff;\n}\n\n.metric-icon {\n  font-size: 24px;\n  margin-right: 15px;\n}\n\n.metric-content {\n  flex: 1;\n}\n\n.metric-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.metric-label {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.chart-container {\n  height: 300px;\n  width: 100%;\n}\n\n.direction-grid {\n  margin-top: 15px;\n}\n\n.direction-item {\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.direction-header i {\n  margin-right: 8px;\n  font-size: 18px;\n}\n\n.direction-metrics {\n  margin-bottom: 10px;\n}\n\n.direction-metric {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n\n.direction-metric .metric-label {\n  color: #909399;\n}\n\n.direction-metric .metric-value {\n  color: #303133;\n  font-weight: bold;\n}\n\n.traffic-light-monitor {\n  display: flex;\n  gap: 30px;\n}\n\n.intersection-view {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 300px;\n}\n\n.intersection {\n  position: relative;\n  width: 200px;\n  height: 200px;\n}\n\n.road {\n  position: absolute;\n  background: #ddd;\n}\n\n.road-horizontal {\n  width: 100%;\n  height: 40px;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n}\n\n.road-vertical {\n  width: 40px;\n  height: 100%;\n  left: 50%;\n  top: 0;\n  transform: translateX(-50%);\n}\n\n.traffic-light {\n  position: absolute;\n  width: 20px;\n  height: 60px;\n  background: #333;\n  border-radius: 10px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-around;\n  align-items: center;\n  padding: 5px 0;\n}\n\n.traffic-light-north {\n  top: 10px;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.traffic-light-south {\n  bottom: 10px;\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.traffic-light-east {\n  right: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.traffic-light-west {\n  left: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.light {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  opacity: 0.3;\n}\n\n.light.red {\n  background: #f56c6c;\n}\n\n.light.yellow {\n  background: #e6a23c;\n}\n\n.light.green {\n  background: #67c23a;\n}\n\n.light.active {\n  opacity: 1;\n  box-shadow: 0 0 10px currentColor;\n}\n\n.signal-timing-info {\n  flex: 1;\n}\n\n.signal-timing-info h5 {\n  margin-bottom: 15px;\n  color: #303133;\n}\n</style>\n"], "mappings": ";;;;AA4MA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAO,QAAS,KAAI;AACpE,OAAO,KAAKC,OAAM,MAAO,SAAQ;AAEjC,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,YAAY,EAAE;MACZC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAEI,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,cAAc,CAAC;EACvBC,KAAKA,CAACT,KAAK,EAAE;IAAEU;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,WAAU,GAAIlB,GAAG,CAAC,KAAK;IAC7B,MAAMmB,cAAa,GAAInB,GAAG,CAAC,CAAC;IAC5B,MAAMoB,YAAW,GAAIpB,GAAG,CAAC,CAAC;IAC1B,MAAMqB,YAAW,GAAIrB,GAAG,CAAC,CAAC;IAC1B,MAAMsB,iBAAgB,GAAItB,GAAG,CAAC,CAAC;;IAE/B;IACA,MAAMuB,YAAW,GAAIvB,GAAG,CAAC,IAAI;IAC7B,MAAMwB,aAAY,GAAIxB,GAAG,CAAC,IAAI;IAC9B,IAAIyB,SAAQ,GAAI,IAAG;IACnB,IAAIC,UAAS,GAAI,IAAG;;IAEpB;IACA,MAAMC,UAAS,GAAI1B,QAAQ,CAAC,CAC1B;MACEK,IAAI,EAAE,MAAM;MACZsB,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,eAAe;MACrBT,YAAY,EAAE,CAAC;MACfU,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC,EACD;MACE1B,IAAI,EAAE,OAAO;MACbsB,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,gBAAgB;MACtBT,YAAY,EAAE,CAAC;MACfU,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC,EACD;MACE1B,IAAI,EAAE,MAAM;MACZsB,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,cAAc;MACpBT,YAAY,EAAE,CAAC;MACfU,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,CAAC,EACD;MACE1B,IAAI,EAAE,OAAO;MACbsB,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,aAAa;MACnBT,YAAY,EAAE,CAAC;MACfU,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;IACX,EACD;;IAED;IACA,MAAMC,aAAY,GAAIhC,QAAQ,CAAC;MAC7BiC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,KAAK;MACZC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE;IACR,CAAC;;IAED;IACA,MAAMC,YAAW,GAAIrC,QAAQ,CAAC,CAC5B;MAAEsC,KAAK,EAAE,KAAK;MAAEC,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAO,CAAC,EAClE;MAAEH,KAAK,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAO,CAAC,EACjE;MAAEH,KAAK,EAAE,KAAK;MAAEC,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAO,CAAC,EAClE;MAAEH,KAAK,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAO,EACjE;;IAED;IACA,MAAMC,QAAO,GAAI1C,QAAQ,CAAC;MACxB2C,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,SAAQ,GAAI7C,QAAQ,CAAC;MACzB8C,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;MAC3DF,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B,CAAC;;IAED;IACA,IAAIG,WAAU,GAAI,IAAG;;IAErB;IACA,MAAMC,UAAS,GAAKC,OAAO,IAAK;MAC9B,MAAMC,KAAI,GAAIC,IAAI,CAACC,KAAK,CAACH,OAAM,GAAI,IAAI;MACvC,MAAMI,OAAM,GAAIF,IAAI,CAACC,KAAK,CAAEH,OAAM,GAAI,IAAI,GAAI,EAAE;MAChD,MAAMK,IAAG,GAAIL,OAAM,GAAI,EAAC;MACxB,OAAO,GAAGC,KAAK,CAACK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,OAAO,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IACzH;IAEA,MAAMC,WAAU,GAAKC,KAAK,IAAK;MAC7B,OAAO,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,MAAK;IACjC;IAEA,MAAMC,iBAAgB,GAAK/B,QAAQ,IAAK;MACtC,IAAIA,QAAO,GAAI,GAAG,EAAE,OAAO,SAAQ;MACnC,IAAIA,QAAO,GAAI,GAAG,EAAE,OAAO,SAAQ;MACnC,OAAO,QAAO;IAChB;IAEA,MAAMgC,iBAAgB,GAAKhC,QAAQ,IAAK;MACtC,IAAIA,QAAO,GAAI,GAAG,EAAE,OAAO,IAAG;MAC9B,IAAIA,QAAO,GAAI,GAAG,EAAE,OAAO,IAAG;MAC9B,OAAO,IAAG;IACZ;IAEA,MAAMiC,oBAAmB,GAAKC,SAAS,IAAK;MAC1C,OAAO,SAAS/B,aAAa,CAAC+B,SAAS,CAAC,EAAC;IAC3C;IAEA,MAAMC,UAAS,GAAIA,CAAA,KAAM;MACvB7D,QAAQ,CAAC,MAAM;QACb;QACA,IAAImB,YAAY,CAAC2C,KAAK,EAAE;UACtBzC,SAAQ,GAAIpB,OAAO,CAAC8D,IAAI,CAAC5C,YAAY,CAAC2C,KAAK;UAC3CE,eAAe,CAAC;QAClB;;QAEA;QACA,IAAI5C,aAAa,CAAC0C,KAAK,EAAE;UACvBxC,UAAS,GAAIrB,OAAO,CAAC8D,IAAI,CAAC3C,aAAa,CAAC0C,KAAK;UAC7CG,gBAAgB,CAAC;QACnB;MACF,CAAC;IACH;IAEA,MAAMD,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAI,CAAC3C,SAAS,EAAE;MAEhB,MAAM6C,MAAK,GAAI;QACbC,KAAK,EAAE;UACLC,IAAI,EAAE,OAAO;UACbC,SAAS,EAAE;YAAEC,QAAQ,EAAE;UAAG;QAC5B,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE;QACX,CAAC;QACDC,KAAK,EAAE;UACLpE,IAAI,EAAE,UAAU;UAChBqE,IAAI,EAAEnC,QAAQ,CAACC,KAAK;UACpBmC,SAAS,EAAE;YACTC,SAAS,EAAGd,KAAK,IAAK;cACpB,MAAMe,IAAG,GAAI,IAAIC,IAAI,CAAChB,KAAK;cAC3B,OAAO,GAAGe,IAAI,CAACE,QAAQ,CAAC,CAAC,IAAIF,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC5B,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;YAC7E;UACF;QACF,CAAC;QACD4B,KAAK,EAAE;UACL5E,IAAI,EAAE,OAAO;UACbH,IAAI,EAAE;QACR,CAAC;QACDgF,MAAM,EAAE,CAAC;UACPR,IAAI,EAAEnC,QAAQ,CAACE,MAAM;UACrBpC,IAAI,EAAE,MAAM;UACZ8E,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;YACTC,OAAO,EAAE;UACX,CAAC;UACDC,SAAS,EAAE;YACTC,KAAK,EAAE;UACT;QACF,CAAC;MACH;MAEAlE,SAAS,CAACmE,SAAS,CAACtB,MAAM;IAC5B;IAEA,MAAMD,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAI,CAAC3C,UAAU,EAAE;MAEjB,MAAM4C,MAAK,GAAI;QACbC,KAAK,EAAE;UACLC,IAAI,EAAE,MAAM;UACZC,SAAS,EAAE;YAAEC,QAAQ,EAAE;UAAG;QAC5B,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE;QACX,CAAC;QACDU,MAAM,EAAE,CAAC;UACP7E,IAAI,EAAE,KAAK;UACXoF,MAAM,EAAE,KAAK;UACbf,IAAI,EAAEhC,SAAS,CAACC,MAAM,CAAC+C,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;YAC5C1F,IAAI,EAAE,GAAGyF,KAAK,OAAO;YACrB7B,KAAK,EAAEpB,SAAS,CAACD,MAAM,CAACmD,KAAK;UAC/B,CAAC,CAAC,CAAC;UACHC,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTC,UAAU,EAAE,EAAE;cACdC,aAAa,EAAE,CAAC;cAChBC,WAAW,EAAE;YACf;UACF;QACF,CAAC;MACH;MAEA3E,UAAU,CAACkE,SAAS,CAACtB,MAAM;IAC7B;IAEA,MAAMgC,iBAAgB,GAAIA,CAAA,KAAM;MAC9B;MACA,IAAI/F,KAAK,CAACK,SAAS,EAAE;QACnBO,cAAc,CAAC+C,KAAI,IAAK;QACxB9C,YAAY,CAAC8C,KAAI,GAAId,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmD,MAAM,CAAC,IAAI,EAAE,IAAI,EAAC;QACvDlF,YAAY,CAAC6C,KAAI,GAAId,IAAI,CAACmD,MAAM,CAAC,IAAI,EAAC,GAAI,EAAC;;QAE3C;QACA5E,UAAU,CAAC6E,OAAO,CAACxC,SAAQ,IAAK;UAC9BA,SAAS,CAAC5C,YAAW,GAAIgC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmD,MAAM,CAAC,IAAI,EAAE,IAAI;UAC1DvC,SAAS,CAAClC,QAAO,GAAIsB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmD,MAAM,CAAC,IAAI,GAAG,IAAI,GAAE;UACzDvC,SAAS,CAACjC,QAAO,GAAIqB,IAAI,CAACmD,MAAM,CAAC,IAAI,EAAC,GAAI,EAAC;UAC3CvC,SAAS,CAAChC,OAAM,GAAIoB,IAAI,CAACmD,MAAM,CAAC,IAAI,EAAC,GAAI,EAAC;QAC5C,CAAC;;QAED;QACA,MAAME,GAAE,GAAI,IAAIvB,IAAI,CAAC;QACrBvC,QAAQ,CAACC,KAAK,CAAC8D,IAAI,CAACD,GAAG,CAACE,WAAW,CAAC,CAAC;QACrChE,QAAQ,CAACE,MAAM,CAAC6D,IAAI,CAACtF,YAAY,CAAC8C,KAAK;;QAEvC;QACA,IAAIvB,QAAQ,CAACC,KAAK,CAACgE,MAAK,GAAI,EAAE,EAAE;UAC9BjE,QAAQ,CAACC,KAAK,CAACiE,KAAK,CAAC;UACrBlE,QAAQ,CAACE,MAAM,CAACgE,KAAK,CAAC;QACxB;;QAEA;QACA/D,SAAS,CAACD,MAAK,GAAIC,SAAS,CAACD,MAAM,CAACiD,GAAG,CAAC,MAAM1C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmD,MAAM,CAAC,IAAI,EAAE,CAAC;;QAE5E;QACAnC,eAAe,CAAC;QAChBC,gBAAgB,CAAC;;QAEjB;QACA,IAAIlD,cAAc,CAAC+C,KAAI,GAAI,EAAC,KAAM,CAAC,EAAE;UACnC;UACA,IAAIjC,aAAa,CAACG,IAAG,KAAM,OAAO,EAAE;YAClCH,aAAa,CAACG,IAAG,GAAI,QAAO;YAC5BH,aAAa,CAACI,IAAG,GAAI,QAAO;UAC9B,OAAO,IAAIJ,aAAa,CAACG,IAAG,KAAM,QAAQ,EAAE;YAC1CH,aAAa,CAACG,IAAG,GAAI,KAAI;YACzBH,aAAa,CAACI,IAAG,GAAI,KAAI;YACzBJ,aAAa,CAACC,KAAI,GAAI,OAAM;YAC5BD,aAAa,CAACE,KAAI,GAAI,OAAM;UAC9B,OAAO,IAAIF,aAAa,CAACC,KAAI,KAAM,OAAO,EAAE;YAC1CD,aAAa,CAACC,KAAI,GAAI,QAAO;YAC7BD,aAAa,CAACE,KAAI,GAAI,QAAO;UAC/B,OAAO;YACLF,aAAa,CAACC,KAAI,GAAI,KAAI;YAC1BD,aAAa,CAACE,KAAI,GAAI,KAAI;YAC1BF,aAAa,CAACG,IAAG,GAAI,OAAM;YAC3BH,aAAa,CAACI,IAAG,GAAI,OAAM;UAC7B;QACF;QAEAnB,WAAW,CAACgD,KAAI,GAAI,IAAG;QACvBjD,IAAI,CAAC,cAAc,EAAE;UACnBE,cAAc,EAAEA,cAAc,CAAC+C,KAAK;UACpC9C,YAAY,EAAEA,YAAY,CAAC8C,KAAK;UAChC7C,YAAY,EAAEA,YAAY,CAAC6C,KAAK;UAChCvC,UAAU,EAAEA;QACd,CAAC;MACH,OAAO;QACLT,WAAW,CAACgD,KAAI,GAAI,KAAI;MAC1B;IACF;IAEA,MAAM4C,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAI9D,WAAW,EAAE;QACf+D,aAAa,CAAC/D,WAAW;MAC3B;MAEAA,WAAU,GAAIgE,WAAW,CAACV,iBAAiB,EAAE,IAAI,GAAE;IACrD;IAEA,MAAMW,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAIjE,WAAW,EAAE;QACf+D,aAAa,CAAC/D,WAAW;QACzBA,WAAU,GAAI,IAAG;MACnB;MACA9B,WAAW,CAACgD,KAAI,GAAI,KAAI;IAC1B;;IAEA;IACAhE,SAAS,CAAC,MAAM;MACd+D,UAAU,CAAC;MACX,IAAI1D,KAAK,CAACK,SAAS,EAAE;QACnBkG,eAAe,CAAC;MAClB;IACF,CAAC;IAED3G,WAAW,CAAC,MAAM;MAChB8G,cAAc,CAAC;MACf,IAAIxF,SAAS,EAAE;QACbA,SAAS,CAACyF,OAAO,CAAC;MACpB;MACA,IAAIxF,UAAU,EAAE;QACdA,UAAU,CAACwF,OAAO,CAAC;MACrB;IACF,CAAC;;IAED;IACA,MAAM;MAAEtG;IAAU,IAAIL,KAAI;IAC1B,IAAIK,SAAS,EAAE;MACbkG,eAAe,CAAC;IAClB,OAAO;MACLG,cAAc,CAAC;IACjB;IAEA,OAAO;MACL;MACA/F,WAAW;MACXC,cAAc;MACdC,YAAY;MACZC,YAAY;MACZC,iBAAiB;MACjBK,UAAU;MACVM,aAAa;MACbK,YAAY;MAEZ;MACAf,YAAY;MACZC,aAAa;MAEb;MACAyB,UAAU;MACVS,WAAW;MACXG,iBAAiB;MACjBC,iBAAiB;MACjBC,oBAAoB;MACpB+C,eAAe;MACfG;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}