{"ast": null, "code": "import { ref, reactive, onMounted, onUnmounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { BrainIcon, Refresh, LightbulbIcon, TrendCharts, DataAnalysis, Clock, Monitor } from '@element-plus/icons-vue';\nimport decisionApi from '@/api/decision';\nimport stompService from '@/utils/stomp-service';\nexport default {\n  name: 'DecisionSupportPanel',\n  components: {\n    BrainIcon,\n    Refresh,\n    LightbulbIcon,\n    TrendCharts,\n    DataAnalysis,\n    Clock,\n    Monitor\n  },\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['suggestion-applied', 'decision-made'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const loading = ref(false);\n    const suggestions = ref([]);\n    const realtimeSupport = ref(null);\n    const applyingIds = ref([]);\n    const showDetailDialog = ref(false);\n    const selectedSuggestion = ref(null);\n\n    // WebSocket订阅\n    let decisionSubscription = null;\n\n    // 计算属性\n    const alertTitle = computed(() => {\n      if (!realtimeSupport.value) return '等待决策数据...';\n      const level = realtimeSupport.value.alert_level;\n      const titles = {\n        'green': '交通状况良好',\n        'yellow': '交通状况一般',\n        'orange': '交通拥堵',\n        'red': '严重拥堵'\n      };\n      return titles[level] || '状态未知';\n    });\n    const alertType = computed(() => {\n      if (!realtimeSupport.value) return 'info';\n      const level = realtimeSupport.value.alert_level;\n      const types = {\n        'green': 'success',\n        'yellow': 'warning',\n        'orange': 'warning',\n        'red': 'error'\n      };\n      return types[level] || 'info';\n    });\n    const alertDescription = computed(() => {\n      if (!realtimeSupport.value) return '';\n      const congestion = realtimeSupport.value.current_congestion;\n      if (congestion) {\n        return `整体拥堵评分: ${(congestion.congestion_score * 100).toFixed(0)}%`;\n      }\n      return '';\n    });\n    return {\n      loading,\n      suggestions,\n      realtimeSupport,\n      applyingIds,\n      showDetailDialog,\n      selectedSuggestion,\n      alertTitle,\n      alertType,\n      alertDescription\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "onUnmounted", "computed", "ElMessage", "ElMessageBox", "BrainIcon", "Refresh", "LightbulbIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataAnalysis", "Clock", "Monitor", "decisionApi", "stompService", "name", "components", "props", "simulationId", "type", "String", "required", "emits", "setup", "emit", "loading", "suggestions", "realtimeSupport", "applyingIds", "showDetailDialog", "selectedSuggestion", "decisionSubscription", "alertTitle", "value", "level", "alert_level", "titles", "alertType", "types", "alertDescription", "congestion", "current_congestion", "congestion_score", "toFixed"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\DecisionSupportPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"decision-support-panel\">\n    <el-card class=\"panel-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <span class=\"title\">\n            <el-icon><BrainIcon /></el-icon>\n            智能决策支持\n          </span>\n          <div class=\"panel-actions\">\n            <el-button \n              size=\"small\" \n              @click=\"refreshSuggestions\"\n              :loading=\"loading\"\n            >\n              <el-icon><Refresh /></el-icon>\n              刷新建议\n            </el-button>\n          </div>\n        </div>\n      </template>\n\n      <!-- 实时决策状态 -->\n      <div class=\"decision-status\">\n        <el-alert\n          :title=\"alertTitle\"\n          :type=\"alertType\"\n          :description=\"alertDescription\"\n          show-icon\n          :closable=\"false\"\n        />\n      </div>\n\n      <!-- 决策建议列表 -->\n      <div class=\"suggestions-section\">\n        <h4>\n          <el-icon><LightbulbIcon /></el-icon>\n          智能建议 ({{ suggestions.length }})\n        </h4>\n        \n        <div v-if=\"suggestions.length === 0\" class=\"no-suggestions\">\n          <el-empty description=\"暂无决策建议\" />\n        </div>\n        \n        <div v-else class=\"suggestions-list\">\n          <div \n            v-for=\"suggestion in suggestions\" \n            :key=\"suggestion.id\"\n            class=\"suggestion-item\"\n            :class=\"[`priority-${suggestion.priority}`]\"\n          >\n            <div class=\"suggestion-header\">\n              <div class=\"suggestion-title\">\n                <el-tag \n                  :type=\"getPriorityTagType(suggestion.priority)\" \n                  size=\"small\"\n                >\n                  {{ getPriorityText(suggestion.priority) }}\n                </el-tag>\n                <span class=\"title-text\">{{ suggestion.title }}</span>\n              </div>\n              <div class=\"suggestion-actions\">\n                <el-button \n                  type=\"primary\" \n                  size=\"small\"\n                  @click=\"applySuggestion(suggestion)\"\n                  :loading=\"applyingIds.includes(suggestion.id)\"\n                >\n                  应用\n                </el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"viewSuggestionDetail(suggestion)\"\n                >\n                  详情\n                </el-button>\n              </div>\n            </div>\n            \n            <div class=\"suggestion-content\">\n              <p class=\"description\">{{ suggestion.description }}</p>\n              <div class=\"suggestion-meta\">\n                <span class=\"confidence\">\n                  <el-icon><TrendCharts /></el-icon>\n                  置信度: {{ (suggestion.confidence * 100).toFixed(0) }}%\n                </span>\n                <span class=\"impact\">\n                  <el-icon><DataAnalysis /></el-icon>\n                  影响评分: {{ suggestion.impact_score.toFixed(1) }}/10\n                </span>\n                <span class=\"time\">\n                  <el-icon><Clock /></el-icon>\n                  {{ suggestion.implementation_time }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 实时决策支持数据 -->\n      <div class=\"realtime-support\" v-if=\"realtimeSupport\">\n        <h4>\n          <el-icon><Monitor /></el-icon>\n          实时分析\n        </h4>\n        \n        <el-row :gutter=\"16\">\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">拥堵等级</div>\n              <div class=\"metric-value\" :class=\"`congestion-${realtimeSupport.current_congestion?.overall_level}`\">\n                {{ getCongestionText(realtimeSupport.current_congestion?.overall_level) }}\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">警报级别</div>\n              <div class=\"metric-value\" :class=\"`alert-${realtimeSupport.alert_level}`\">\n                {{ getAlertText(realtimeSupport.alert_level) }}\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"8\">\n            <div class=\"support-metric\">\n              <div class=\"metric-label\">下次更新</div>\n              <div class=\"metric-value\">\n                {{ realtimeSupport.next_update_in }}秒\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n\n        <!-- 推荐行动 -->\n        <div class=\"recommended-actions\" v-if=\"realtimeSupport.recommended_actions?.length\">\n          <h5>推荐行动:</h5>\n          <ul>\n            <li v-for=\"action in realtimeSupport.recommended_actions\" :key=\"action\">\n              {{ action }}\n            </li>\n          </ul>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 建议详情对话框 -->\n    <el-dialog\n      v-model=\"showDetailDialog\"\n      title=\"决策建议详情\"\n      width=\"600px\"\n    >\n      <div v-if=\"selectedSuggestion\" class=\"suggestion-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"建议类型\">\n            {{ selectedSuggestion.type }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"优先级\">\n            <el-tag :type=\"getPriorityTagType(selectedSuggestion.priority)\">\n              {{ getPriorityText(selectedSuggestion.priority) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"置信度\">\n            {{ (selectedSuggestion.confidence * 100).toFixed(0) }}%\n          </el-descriptions-item>\n          <el-descriptions-item label=\"影响评分\">\n            {{ selectedSuggestion.impact_score.toFixed(1) }}/10\n          </el-descriptions-item>\n          <el-descriptions-item label=\"实施时间\" span=\"2\">\n            {{ selectedSuggestion.implementation_time }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"预期效果\" span=\"2\">\n            {{ selectedSuggestion.expected_effect }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"具体行动\" span=\"2\">\n            {{ selectedSuggestion.action }}\n          </el-descriptions-item>\n        </el-descriptions>\n\n        <!-- 风险评估 -->\n        <div v-if=\"selectedSuggestion.risk_assessment\" class=\"risk-assessment\">\n          <h4>风险评估</h4>\n          <el-alert\n            :title=\"`风险等级: ${selectedSuggestion.risk_assessment.level}`\"\n            :type=\"getRiskAlertType(selectedSuggestion.risk_assessment.level)\"\n            :description=\"selectedSuggestion.risk_assessment.mitigation\"\n            show-icon\n          />\n        </div>\n\n        <!-- 适用条件 -->\n        <div v-if=\"selectedSuggestion.applicable_conditions?.length\" class=\"applicable-conditions\">\n          <h4>适用条件</h4>\n          <ul>\n            <li v-for=\"condition in selectedSuggestion.applicable_conditions\" :key=\"condition\">\n              {{ condition }}\n            </li>\n          </ul>\n        </div>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n          <el-button \n            type=\"primary\" \n            @click=\"applySuggestionFromDialog\"\n            :loading=\"applyingIds.includes(selectedSuggestion?.id)\"\n          >\n            应用建议\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { \n  BrainIcon, \n  Refresh, \n  LightbulbIcon, \n  TrendCharts, \n  DataAnalysis, \n  Clock,\n  Monitor\n} from '@element-plus/icons-vue'\nimport decisionApi from '@/api/decision'\nimport stompService from '@/utils/stomp-service'\n\nexport default {\n  name: 'DecisionSupportPanel',\n  components: {\n    BrainIcon,\n    Refresh,\n    LightbulbIcon,\n    TrendCharts,\n    DataAnalysis,\n    Clock,\n    Monitor\n  },\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['suggestion-applied', 'decision-made'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const loading = ref(false)\n    const suggestions = ref([])\n    const realtimeSupport = ref(null)\n    const applyingIds = ref([])\n    const showDetailDialog = ref(false)\n    const selectedSuggestion = ref(null)\n    \n    // WebSocket订阅\n    let decisionSubscription = null\n    \n    // 计算属性\n    const alertTitle = computed(() => {\n      if (!realtimeSupport.value) return '等待决策数据...'\n      \n      const level = realtimeSupport.value.alert_level\n      const titles = {\n        'green': '交通状况良好',\n        'yellow': '交通状况一般',\n        'orange': '交通拥堵',\n        'red': '严重拥堵'\n      }\n      return titles[level] || '状态未知'\n    })\n    \n    const alertType = computed(() => {\n      if (!realtimeSupport.value) return 'info'\n      \n      const level = realtimeSupport.value.alert_level\n      const types = {\n        'green': 'success',\n        'yellow': 'warning',\n        'orange': 'warning',\n        'red': 'error'\n      }\n      return types[level] || 'info'\n    })\n    \n    const alertDescription = computed(() => {\n      if (!realtimeSupport.value) return ''\n      \n      const congestion = realtimeSupport.value.current_congestion\n      if (congestion) {\n        return `整体拥堵评分: ${(congestion.congestion_score * 100).toFixed(0)}%`\n      }\n      return ''\n    })\n    \n    return {\n      loading,\n      suggestions,\n      realtimeSupport,\n      applyingIds,\n      showDetailDialog,\n      selectedSuggestion,\n      alertTitle,\n      alertType,\n      alertDescription\n    }\n  }\n}\n</script>\n"], "mappings": "AA0NA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAO,QAAS,KAAI;AACpE,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SACEC,SAAS,EACTC,OAAO,EACPC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,KAAK,EACLC,OAAM,QACD,yBAAwB;AAC/B,OAAOC,WAAU,MAAO,gBAAe;AACvC,OAAOC,YAAW,MAAO,uBAAsB;AAE/C,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;IACVV,SAAS;IACTC,OAAO;IACPC,aAAa;IACbC,WAAW;IACXC,YAAY;IACZC,KAAK;IACLC;EACF,CAAC;EACDK,KAAK,EAAE;IACLC,YAAY,EAAE;MACZC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC;EAC9CC,KAAKA,CAACN,KAAK,EAAE;IAAEO;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,OAAM,GAAI1B,GAAG,CAAC,KAAK;IACzB,MAAM2B,WAAU,GAAI3B,GAAG,CAAC,EAAE;IAC1B,MAAM4B,eAAc,GAAI5B,GAAG,CAAC,IAAI;IAChC,MAAM6B,WAAU,GAAI7B,GAAG,CAAC,EAAE;IAC1B,MAAM8B,gBAAe,GAAI9B,GAAG,CAAC,KAAK;IAClC,MAAM+B,kBAAiB,GAAI/B,GAAG,CAAC,IAAI;;IAEnC;IACA,IAAIgC,oBAAmB,GAAI,IAAG;;IAE9B;IACA,MAAMC,UAAS,GAAI7B,QAAQ,CAAC,MAAM;MAChC,IAAI,CAACwB,eAAe,CAACM,KAAK,EAAE,OAAO,WAAU;MAE7C,MAAMC,KAAI,GAAIP,eAAe,CAACM,KAAK,CAACE,WAAU;MAC9C,MAAMC,MAAK,GAAI;QACb,OAAO,EAAE,QAAQ;QACjB,QAAQ,EAAE,QAAQ;QAClB,QAAQ,EAAE,MAAM;QAChB,KAAK,EAAE;MACT;MACA,OAAOA,MAAM,CAACF,KAAK,KAAK,MAAK;IAC/B,CAAC;IAED,MAAMG,SAAQ,GAAIlC,QAAQ,CAAC,MAAM;MAC/B,IAAI,CAACwB,eAAe,CAACM,KAAK,EAAE,OAAO,MAAK;MAExC,MAAMC,KAAI,GAAIP,eAAe,CAACM,KAAK,CAACE,WAAU;MAC9C,MAAMG,KAAI,GAAI;QACZ,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;QACnB,KAAK,EAAE;MACT;MACA,OAAOA,KAAK,CAACJ,KAAK,KAAK,MAAK;IAC9B,CAAC;IAED,MAAMK,gBAAe,GAAIpC,QAAQ,CAAC,MAAM;MACtC,IAAI,CAACwB,eAAe,CAACM,KAAK,EAAE,OAAO,EAAC;MAEpC,MAAMO,UAAS,GAAIb,eAAe,CAACM,KAAK,CAACQ,kBAAiB;MAC1D,IAAID,UAAU,EAAE;QACd,OAAO,WAAW,CAACA,UAAU,CAACE,gBAAe,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAE;MACpE;MACA,OAAO,EAAC;IACV,CAAC;IAED,OAAO;MACLlB,OAAO;MACPC,WAAW;MACXC,eAAe;MACfC,WAAW;MACXC,gBAAgB;MAChBC,kBAAkB;MAClBE,UAAU;MACVK,SAAS;MACTE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}