<template>
  <div class="intelligent-traffic-status-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon><TrendCharts /></el-icon>
        智能交通状态面板
      </h3>
      <div class="update-info">
        <el-tag v-if="lastUpdateTime" size="small" type="info">
          {{ lastUpdateTime }}
        </el-tag>
      </div>
    </div>

    <!-- 实时车辆计数区域 -->
    <div class="realtime-vehicle-section">
      <h4 class="section-title">实时车辆检测</h4>
      <div class="vehicle-grid">
        <div 
          v-for="direction in directions" 
          :key="direction.key"
          class="vehicle-card"
          :class="getDirectionCardClass(direction.key)"
        >
          <div class="direction-header">
            <span class="direction-name">{{ direction.name }}</span>
            <el-tag 
              :type="getDirectionStatusType(direction.key)" 
              size="small"
            >
              {{ getDirectionStatusText(direction.key) }}
            </el-tag>
          </div>
          
          <div class="vehicle-metrics">
            <!-- 当前帧车辆数 -->
            <div class="metric-item primary">
              <div class="metric-value">{{ getCurrentVehicleCount(direction.key) }}</div>
              <div class="metric-label">当前帧车辆</div>
            </div>
            
            <!-- 移动平均 -->
            <div class="metric-item secondary">
              <div class="metric-value">{{ getMovingAverage(direction.key) }}</div>
              <div class="metric-label">移动平均</div>
            </div>
            
            <!-- 进度显示 -->
            <div class="metric-item progress">
              <el-progress 
                :percentage="getDirectionProgress(direction.key)"
                :stroke-width="6"
                :show-text="false"
                :status="getProgressStatus(direction.key)"
              />
              <div class="metric-label">
                {{ getDirectionProgress(direction.key) }}% 
                ({{ getCurrentFrame(direction.key) }}/{{ getTotalFrames(direction.key) }})
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 拥堵等级分析 -->
    <div class="congestion-analysis-section">
      <h4 class="section-title">拥堵等级分析</h4>
      <div class="congestion-grid">
        <div class="congestion-overview">
          <div class="congestion-level" :class="getCongestionLevelClass()">
            <div class="level-indicator">
              <el-icon><Warning /></el-icon>
              {{ getCongestionLevel() }}
            </div>
            <div class="level-description">{{ getCongestionDescription() }}</div>
          </div>
          
          <div class="congestion-metrics">
            <div class="metric">
              <span class="metric-label">拥堵指数</span>
              <span class="metric-value">{{ getCongestionIndex() }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">变化趋势</span>
              <span class="metric-value trend">{{ getCongestionTrend() }}</span>
            </div>
          </div>
        </div>
        
        <div class="flow-balance">
          <div class="balance-indicator">
            <div class="balance-title">交通流平衡度</div>
            <div class="balance-value">{{ getTrafficFlowBalance() }}%</div>
            <div class="balance-status">{{ getBalanceStatus() }}</div>
          </div>
          
          <el-progress 
            type="circle" 
            :percentage="getTrafficFlowBalance()"
            :width="80"
            :stroke-width="8"
            :color="getBalanceColor()"
          />
        </div>
      </div>
    </div>

    <!-- 交通管理建议 -->
    <div class="management-suggestions-section">
      <h4 class="section-title">智能管理建议</h4>
      <div class="suggestions-container">
        <div 
          v-for="suggestion in getManagementSuggestions()" 
          :key="suggestion.id"
          class="suggestion-card"
          :class="suggestion.priority"
        >
          <div class="suggestion-header">
            <el-icon :class="suggestion.iconClass">
              <component :is="suggestion.icon" />
            </el-icon>
            <span class="suggestion-title">{{ suggestion.title }}</span>
            <el-tag :type="suggestion.tagType" size="small">
              {{ suggestion.priority }}
            </el-tag>
          </div>
          <div class="suggestion-content">{{ suggestion.content }}</div>
          <div class="suggestion-action" v-if="suggestion.action">
            <el-button 
              :type="suggestion.actionType" 
              size="small"
              @click="applySuggestion(suggestion)"
            >
              {{ suggestion.action }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref } from 'vue'
import { 
  TrendCharts, Warning, Setting, InfoFilled, 
  SuccessFilled, WarnTriangleFilled, Clock 
} from '@element-plus/icons-vue'

export default {
  name: 'IntelligentTrafficStatusPanel',
  components: {
    TrendCharts, Warning, Setting, InfoFilled,
    SuccessFilled, WarnTriangleFilled, Clock
  },
  props: {
    directionStats: {
      type: Object,
      required: true
    },
    lastUpdateTime: {
      type: String,
      default: ''
    }
  },
  emits: ['apply-suggestion'],
  setup(props, { emit }) {
    const directions = [
      { key: 'north', name: '北向' },
      { key: 'east', name: '东向' },
      { key: 'south', name: '南向' },
      { key: 'west', name: '西向' }
    ]

    // 获取当前帧车辆数
    const getCurrentVehicleCount = (direction) => {
      return props.directionStats[direction]?.vehicleCount || 0
    }

    // 获取移动平均
    const getMovingAverage = (direction) => {
      return props.directionStats[direction]?.movingAverage || 0
    }

    // 获取方向进度
    const getDirectionProgress = (direction) => {
      const stats = props.directionStats[direction]
      if (!stats || !stats.totalFrames) return 0
      return Math.round((stats.currentFrame || 0) / stats.totalFrames * 100)
    }

    // 获取当前帧数
    const getCurrentFrame = (direction) => {
      return props.directionStats[direction]?.currentFrame || 0
    }

    // 获取总帧数
    const getTotalFrames = (direction) => {
      return props.directionStats[direction]?.totalFrames || 0
    }

    // 获取方向状态类型
    const getDirectionStatusType = (direction) => {
      const status = props.directionStats[direction]?.status || 'waiting'
      const typeMap = {
        'waiting': 'info',
        'processing': 'warning', 
        'completed': 'success',
        'error': 'danger'
      }
      return typeMap[status] || 'info'
    }

    // 获取方向状态文本
    const getDirectionStatusText = (direction) => {
      const status = props.directionStats[direction]?.status || 'waiting'
      const textMap = {
        'waiting': '等待中',
        'processing': '检测中',
        'completed': '已完成', 
        'error': '检测失败'
      }
      return textMap[status] || '未知'
    }

    // 获取进度状态
    const getProgressStatus = (direction) => {
      const progress = getDirectionProgress(direction)
      if (progress === 100) return 'success'
      if (progress > 0) return undefined
      return 'exception'
    }

    // 获取方向卡片样式类
    const getDirectionCardClass = (direction) => {
      const status = props.directionStats[direction]?.status || 'waiting'
      return `status-${status}`
    }

    // 拥堵等级计算
    const getCongestionLevel = () => {
      const totalVehicles = directions.reduce((sum, dir) =>
        sum + getCurrentVehicleCount(dir.key), 0)
      const avgMoving = directions.reduce((sum, dir) =>
        sum + getMovingAverage(dir.key), 0) / 4

      if (avgMoving >= 8 || totalVehicles >= 20) return '严重拥堵'
      if (avgMoving >= 5 || totalVehicles >= 12) return '中度拥堵'
      if (avgMoving >= 3 || totalVehicles >= 6) return '轻度拥堵'
      return '畅通'
    }

    const getCongestionLevelClass = () => {
      const level = getCongestionLevel()
      const classMap = {
        '严重拥堵': 'severe',
        '中度拥堵': 'moderate',
        '轻度拥堵': 'light',
        '畅通': 'smooth'
      }
      return classMap[level] || 'smooth'
    }

    const getCongestionDescription = () => {
      const level = getCongestionLevel()
      const descMap = {
        '严重拥堵': '交通严重拥堵，建议立即采取疏导措施',
        '中度拥堵': '交通较为拥堵，需要关注流量变化',
        '轻度拥堵': '交通略有拥堵，保持正常监控',
        '畅通': '交通状况良好，运行顺畅'
      }
      return descMap[level] || '状态未知'
    }

    const getCongestionIndex = () => {
      const totalVehicles = directions.reduce((sum, dir) =>
        sum + getCurrentVehicleCount(dir.key), 0)
      const avgMoving = directions.reduce((sum, dir) =>
        sum + getMovingAverage(dir.key), 0) / 4

      // 综合计算拥堵指数 (0-100)
      const vehicleIndex = Math.min(totalVehicles * 2.5, 50)
      const movingIndex = Math.min(avgMoving * 6, 50)
      return Math.round(vehicleIndex + movingIndex)
    }

    const getCongestionTrend = () => {
      const index = getCongestionIndex()
      if (index >= 80) return '↗️ 恶化'
      if (index >= 60) return '→ 稳定'
      if (index >= 40) return '↘️ 改善'
      return '✅ 良好'
    }

    // 交通流平衡度计算
    const getTrafficFlowBalance = () => {
      const counts = directions.map(dir => getMovingAverage(dir.key))
      const max = Math.max(...counts)
      const min = Math.min(...counts)

      if (max === 0) return 100
      const balance = ((max - min) / max) * 100
      return Math.round(100 - balance)
    }

    const getBalanceStatus = () => {
      const balance = getTrafficFlowBalance()
      if (balance >= 80) return '非常均衡'
      if (balance >= 60) return '较为均衡'
      if (balance >= 40) return '不够均衡'
      return '严重不均衡'
    }

    const getBalanceColor = () => {
      const balance = getTrafficFlowBalance()
      if (balance >= 80) return '#10b981'
      if (balance >= 60) return '#f59e0b'
      if (balance >= 40) return '#f97316'
      return '#ef4444'
    }

    // 管理建议生成
    const getManagementSuggestions = () => {
      const suggestions = []
      const congestionLevel = getCongestionLevel()
      const balance = getTrafficFlowBalance()
      const totalVehicles = directions.reduce((sum, dir) =>
        sum + getCurrentVehicleCount(dir.key), 0)

      // 基于拥堵等级的建议
      if (congestionLevel === '严重拥堵') {
        suggestions.push({
          id: 'severe-congestion',
          title: '紧急疏导措施',
          content: '检测到严重拥堵，建议立即启动应急预案，增加信号灯绿灯时长，派遣交警现场疏导。',
          priority: 'high',
          tagType: 'danger',
          icon: WarnTriangleFilled,
          iconClass: 'danger-icon',
          action: '启动应急预案',
          actionType: 'danger'
        })
      } else if (congestionLevel === '中度拥堵') {
        suggestions.push({
          id: 'moderate-congestion',
          title: '优化信号配时',
          content: '交通流量较大，建议调整信号灯配时方案，延长主要方向绿灯时间。',
          priority: 'medium',
          tagType: 'warning',
          icon: Setting,
          iconClass: 'warning-icon',
          action: '调整配时',
          actionType: 'warning'
        })
      }

      // 基于流量平衡的建议
      if (balance < 60) {
        suggestions.push({
          id: 'flow-imbalance',
          title: '流量均衡优化',
          content: '各方向流量不均衡，建议动态调整信号配时，优化交通流分配。',
          priority: 'medium',
          tagType: 'warning',
          icon: TrendCharts,
          iconClass: 'warning-icon',
          action: '均衡流量',
          actionType: 'primary'
        })
      }

      // 基于总体流量的建议
      if (totalVehicles > 15) {
        suggestions.push({
          id: 'high-volume',
          title: '高峰期管理',
          content: '当前处于交通高峰期，建议启动高峰期管理模式，加强现场监控。',
          priority: 'medium',
          tagType: 'info',
          icon: Clock,
          iconClass: 'info-icon',
          action: '启动高峰模式',
          actionType: 'primary'
        })
      }

      // 如果没有问题，给出正面建议
      if (suggestions.length === 0) {
        suggestions.push({
          id: 'normal-operation',
          title: '运行状态良好',
          content: '当前交通状况良好，建议保持现有信号配时方案，继续监控。',
          priority: 'low',
          tagType: 'success',
          icon: SuccessFilled,
          iconClass: 'success-icon',
          action: '保持监控',
          actionType: 'success'
        })
      }

      return suggestions
    }

    // 应用建议
    const applySuggestion = (suggestion) => {
      emit('apply-suggestion', suggestion)
    }

    return {
      directions,
      getCurrentVehicleCount,
      getMovingAverage,
      getDirectionProgress,
      getCurrentFrame,
      getTotalFrames,
      getDirectionStatusType,
      getDirectionStatusText,
      getProgressStatus,
      getDirectionCardClass,
      getCongestionLevel,
      getCongestionLevelClass,
      getCongestionDescription,
      getCongestionIndex,
      getCongestionTrend,
      getTrafficFlowBalance,
      getBalanceStatus,
      getBalanceColor,
      getManagementSuggestions,
      applySuggestion
    }
  }
}
</script>

<style scoped>
.intelligent-traffic-status-panel {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f2f5;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.realtime-vehicle-section {
  margin-bottom: 32px;
}

.vehicle-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

@media (max-width: 768px) {
  .vehicle-grid {
    grid-template-columns: 1fr;
  }

  .congestion-grid {
    grid-template-columns: 1fr !important;
  }
}

.vehicle-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.vehicle-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.vehicle-card.status-processing {
  border-color: #f59e0b;
  background: #fffbeb;
}

.vehicle-card.status-completed {
  border-color: #10b981;
  background: #ecfdf5;
}

.vehicle-card.status-error {
  border-color: #ef4444;
  background: #fef2f2;
}

.direction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.direction-name {
  font-weight: 500;
  color: #374151;
}

.vehicle-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-item.primary .metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.metric-item.primary .metric-value:hover {
  color: #3b82f6;
}

.metric-item.secondary .metric-value {
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
}

.metric-label {
  font-size: 12px;
  color: #9ca3af;
}

.metric-item.progress {
  flex-direction: column;
  align-items: stretch;
  gap: 4px;
}

.congestion-analysis-section {
  margin-bottom: 32px;
}

.congestion-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.congestion-overview {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
}

.congestion-level {
  text-align: center;
  margin-bottom: 16px;
}

.congestion-level.severe .level-indicator {
  color: #ef4444;
}

.congestion-level.moderate .level-indicator {
  color: #f59e0b;
}

.congestion-level.light .level-indicator {
  color: #f97316;
}

.congestion-level.smooth .level-indicator {
  color: #10b981;
}

.level-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.level-description {
  font-size: 14px;
  color: #6b7280;
}

.congestion-metrics {
  display: flex;
  justify-content: space-around;
}

.metric {
  text-align: center;
}

.metric .metric-label {
  display: block;
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 4px;
}

.metric .metric-value {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
}

.flow-balance {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.balance-indicator {
  text-align: center;
}

.balance-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.balance-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.balance-status {
  font-size: 12px;
  color: #9ca3af;
}

.management-suggestions-section {
  margin-bottom: 16px;
}

.suggestions-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
  opacity: 0;
  animation: slideInLeft 0.5s ease forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.suggestion-card:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-card.high {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.suggestion-card.medium {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.suggestion-card.low {
  border-left-color: #10b981;
  background: #ecfdf5;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.suggestion-title {
  flex: 1;
  font-weight: 500;
  color: #374151;
}

.suggestion-content {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
  line-height: 1.5;
}

.suggestion-action {
  text-align: right;
}
</style>
