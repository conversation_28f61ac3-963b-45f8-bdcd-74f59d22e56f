#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TraCI控制器模块

提供与SUMO仿真的TraCI接口控制功能
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any

try:
    import traci
    import traci.constants as tc
    TRACI_AVAILABLE = True
except ImportError:
    TRACI_AVAILABLE = False

class TraciController:
    """TraCI控制器类"""
    
    def __init__(self):
        """初始化TraCI控制器"""
        self.logger = logging.getLogger(__name__)
        self.connected = False
        self.simulation_step = 0
        
        if not TRACI_AVAILABLE:
            self.logger.error("TraCI模块不可用")
            raise ImportError("TraCI模块不可用，请安装traci")
    
    def connect(self, port: int = 8813, host: str = "localhost") -> bool:
        """
        连接到SUMO仿真
        
        Args:
            port: TraCI端口
            host: SUMO主机地址
            
        Returns:
            bool: 连接是否成功
        """
        try:
            if self.connected:
                self.logger.warning("TraCI已连接")
                return True
            
            self.logger.info(f"连接TraCI: {host}:{port}")
            
            # 尝试连接
            traci.init(port, host=host)
            self.connected = True
            self.simulation_step = 0
            
            self.logger.info("TraCI连接成功")
            return True
            
        except Exception as e:
            self.logger.error(f"TraCI连接失败: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """断开TraCI连接"""
        try:
            if self.connected:
                traci.close()
                self.connected = False
                self.simulation_step = 0
                self.logger.info("TraCI连接已断开")
        except Exception as e:
            self.logger.error(f"断开TraCI连接失败: {e}")
    
    def step(self) -> bool:
        """
        执行一个仿真步骤
        
        Returns:
            bool: 步骤执行是否成功
        """
        try:
            if not self.connected:
                self.logger.error("TraCI未连接")
                return False
            
            traci.simulationStep()
            self.simulation_step += 1
            return True
            
        except Exception as e:
            self.logger.error(f"仿真步骤执行失败: {e}")
            return False
    
    def get_current_time(self) -> float:
        """
        获取当前仿真时间
        
        Returns:
            float: 当前仿真时间（秒）
        """
        try:
            if not self.connected:
                return 0.0
            return traci.simulation.getTime()
        except Exception as e:
            self.logger.error(f"获取仿真时间失败: {e}")
            return 0.0
    
    def get_vehicle_count(self) -> int:
        """
        获取当前车辆数量
        
        Returns:
            int: 当前车辆数量
        """
        try:
            if not self.connected:
                return 0
            return traci.simulation.getMinExpectedNumber()
        except Exception as e:
            self.logger.error(f"获取车辆数量失败: {e}")
            return 0
    
    def get_vehicle_ids(self) -> List[str]:
        """
        获取所有车辆ID列表
        
        Returns:
            List[str]: 车辆ID列表
        """
        try:
            if not self.connected:
                return []
            return traci.vehicle.getIDList()
        except Exception as e:
            self.logger.error(f"获取车辆ID列表失败: {e}")
            return []
    
    def get_vehicle_info(self, vehicle_id: str) -> Optional[Dict]:
        """
        获取指定车辆的详细信息
        
        Args:
            vehicle_id: 车辆ID
            
        Returns:
            Dict: 车辆信息字典
        """
        try:
            if not self.connected:
                return None
            
            return {
                'id': vehicle_id,
                'position': traci.vehicle.getPosition(vehicle_id),
                'speed': traci.vehicle.getSpeed(vehicle_id),
                'lane_id': traci.vehicle.getLaneID(vehicle_id),
                'route_id': traci.vehicle.getRouteID(vehicle_id),
                'vehicle_type': traci.vehicle.getTypeID(vehicle_id),
                'waiting_time': traci.vehicle.getWaitingTime(vehicle_id)
            }
        except Exception as e:
            self.logger.error(f"获取车辆信息失败: {e}")
            return None
    
    def get_traffic_light_ids(self) -> List[str]:
        """
        获取所有交通信号灯ID列表
        
        Returns:
            List[str]: 信号灯ID列表
        """
        try:
            if not self.connected:
                return []
            return traci.trafficlight.getIDList()
        except Exception as e:
            self.logger.error(f"获取信号灯ID列表失败: {e}")
            return []
    
    def get_traffic_light_state(self, tl_id: str) -> Optional[str]:
        """
        获取交通信号灯状态
        
        Args:
            tl_id: 信号灯ID
            
        Returns:
            str: 信号灯状态字符串
        """
        try:
            if not self.connected:
                return None
            return traci.trafficlight.getRedYellowGreenState(tl_id)
        except Exception as e:
            self.logger.error(f"获取信号灯状态失败: {e}")
            return None
    
    def set_traffic_light_state(self, tl_id: str, state: str) -> bool:
        """
        设置交通信号灯状态
        
        Args:
            tl_id: 信号灯ID
            state: 信号灯状态字符串 (如 "GrGr", "yryr", "rGrG")
            
        Returns:
            bool: 设置是否成功
        """
        try:
            if not self.connected:
                return False
            
            traci.trafficlight.setRedYellowGreenState(tl_id, state)
            self.logger.debug(f"设置信号灯状态: {tl_id} -> {state}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置信号灯状态失败: {e}")
            return False
    
    def get_lane_vehicle_count(self, lane_id: str) -> int:
        """
        获取指定车道的车辆数量
        
        Args:
            lane_id: 车道ID
            
        Returns:
            int: 车辆数量
        """
        try:
            if not self.connected:
                return 0
            return traci.lane.getLastStepVehicleNumber(lane_id)
        except Exception as e:
            self.logger.error(f"获取车道车辆数量失败: {e}")
            return 0
    
    def get_lane_mean_speed(self, lane_id: str) -> float:
        """
        获取指定车道的平均速度
        
        Args:
            lane_id: 车道ID
            
        Returns:
            float: 平均速度 (m/s)
        """
        try:
            if not self.connected:
                return 0.0
            return traci.lane.getLastStepMeanSpeed(lane_id)
        except Exception as e:
            self.logger.error(f"获取车道平均速度失败: {e}")
            return 0.0
    
    def get_edge_vehicle_count(self, edge_id: str) -> int:
        """
        获取指定道路的车辆数量
        
        Args:
            edge_id: 道路ID
            
        Returns:
            int: 车辆数量
        """
        try:
            if not self.connected:
                return 0
            return traci.edge.getLastStepVehicleNumber(edge_id)
        except Exception as e:
            self.logger.error(f"获取道路车辆数量失败: {e}")
            return 0
    
    def add_vehicle(self, vehicle_id: str, route_id: str, vehicle_type: str = "DEFAULT_VEHTYPE") -> bool:
        """
        添加车辆到仿真
        
        Args:
            vehicle_id: 车辆ID
            route_id: 路线ID
            vehicle_type: 车辆类型
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if not self.connected:
                return False
            
            traci.vehicle.add(vehicle_id, route_id, typeID=vehicle_type)
            self.logger.debug(f"添加车辆: {vehicle_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加车辆失败: {e}")
            return False
    
    def remove_vehicle(self, vehicle_id: str) -> bool:
        """
        从仿真中移除车辆
        
        Args:
            vehicle_id: 车辆ID
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if not self.connected:
                return False
            
            traci.vehicle.remove(vehicle_id)
            self.logger.debug(f"移除车辆: {vehicle_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除车辆失败: {e}")
            return False
    
    def get_simulation_summary(self) -> Dict:
        """
        获取仿真摘要信息
        
        Returns:
            Dict: 仿真摘要
        """
        try:
            if not self.connected:
                return {}
            
            vehicle_ids = self.get_vehicle_ids()
            total_vehicles = len(vehicle_ids)
            
            # 计算平均速度
            total_speed = 0.0
            if total_vehicles > 0:
                for vid in vehicle_ids:
                    try:
                        total_speed += traci.vehicle.getSpeed(vid)
                    except:
                        pass
                avg_speed = total_speed / total_vehicles if total_vehicles > 0 else 0.0
            else:
                avg_speed = 0.0
            
            return {
                'current_time': self.get_current_time(),
                'simulation_step': self.simulation_step,
                'total_vehicles': total_vehicles,
                'average_speed': avg_speed,
                'traffic_lights': len(self.get_traffic_light_ids())
            }
            
        except Exception as e:
            self.logger.error(f"获取仿真摘要失败: {e}")
            return {}
