{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, with<PERSON><PERSON><PERSON> as _withKeys, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"four-way-history-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"selection-controls\"\n};\nconst _hoisted_5 = {\n  class: \"filter-panel\"\n};\nconst _hoisted_6 = {\n  class: \"filter-row\"\n};\nconst _hoisted_7 = {\n  class: \"filter-item\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"filter-item\"\n};\nconst _hoisted_9 = {\n  class: \"filter-item\"\n};\nconst _hoisted_10 = {\n  class: \"filter-item\"\n};\nconst _hoisted_11 = {\n  class: \"filter-actions\"\n};\nconst _hoisted_12 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_13 = {\n  key: 1,\n  class: \"error-container\"\n};\nconst _hoisted_14 = {\n  class: \"error-actions\"\n};\nconst _hoisted_15 = {\n  key: 2,\n  class: \"empty-container\"\n};\nconst _hoisted_16 = {\n  key: 3\n};\nconst _hoisted_17 = {\n  class: \"task-id-container\"\n};\nconst _hoisted_18 = {\n  class: \"task-id\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"vehicle-stats\"\n};\nconst _hoisted_20 = {\n  class: \"total-vehicles\"\n};\nconst _hoisted_21 = {\n  class: \"direction-breakdown\"\n};\nconst _hoisted_22 = {\n  key: 1,\n  class: \"text-muted\"\n};\nconst _hoisted_23 = {\n  class: \"table-actions\"\n};\nconst _hoisted_24 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_25 = {\n  class: \"batch-actions\"\n};\nconst _hoisted_26 = {\n  key: 1,\n  class: \"selected-info\"\n};\nconst _hoisted_27 = {\n  class: \"pagination-wrapper\"\n};\nconst _hoisted_28 = {\n  class: \"pagination-info\"\n};\nconst _hoisted_29 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_30 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_delete = _resolveComponent(\"delete\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_search = _resolveComponent(\"search\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_refresh = _resolveComponent(\"refresh\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_DocumentCopy = _resolveComponent(\"DocumentCopy\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"history-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[13] || (_cache[13] = _createElementVNode(\"h2\", null, \"四方向智能交通分析历史\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [$setup.fourWayTasks.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_checkbox, {\n      modelValue: $setup.selectAll,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.selectAll = $event),\n      onChange: $setup.toggleSelectAll,\n      indeterminate: $setup.isIndeterminate,\n      class: \"wider-checkbox\"\n    }, {\n      default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"全选\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\", \"indeterminate\"])])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n      class: \"custom-btn-outline\",\n      disabled: !$setup.hasSelected,\n      onClick: $setup.handleBatchDelete\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_delete)]),\n        _: 1 /* STABLE */\n      }), _cache[12] || (_cache[12] = _createTextVNode(\" 批量删除 \"))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"disabled\", \"onClick\"]), _createCommentVNode(\" 四方向上传页面已删除，暂时隐藏此按钮 \"), _createCommentVNode(\" <el-button class=\\\"custom-btn-primary\\\" disabled>\\n              <el-icon><plus /></el-icon> 新建四方向分析\\n            </el-button> \")])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"搜索：\", -1 /* HOISTED */)), _createVNode(_component_el_input, {\n      modelValue: $setup.searchQuery,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchQuery = $event),\n      placeholder: \"搜索任务名称\",\n      clearable: \"\",\n      onClear: $setup.handleSearch,\n      onKeyup: _withKeys($setup.handleSearch, [\"enter\"]),\n      size: \"small\",\n      class: \"search-input\"\n    }, {\n      prefix: _withCtx(() => [_createVNode(_component_el_icon, {\n        class: \"el-input__icon\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_search)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onClear\", \"onKeyup\"])]), _createCommentVNode(\" 管理员用户筛选功能 \"), $setup.isAdmin ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"用户：\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.userFilter,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.userFilter = $event),\n      placeholder: \"用户筛选\",\n      clearable: \"\",\n      onChange: $setup.handleSearch,\n      size: \"small\",\n      \"popper-append-to-body\": true,\n      \"reserve-keyword\": false\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部用户\",\n        value: \"\"\n      }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.userList, user => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: user.id,\n          label: user.username,\n          value: user.id\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_9, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"状态：\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.statusFilter,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.statusFilter = $event),\n      placeholder: \"状态筛选\",\n      clearable: \"\",\n      onChange: $setup.handleSearch,\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部状态\",\n        value: \"\"\n      }), _createVNode(_component_el_option, {\n        label: \"已完成\",\n        value: \"completed\"\n      }), _createVNode(_component_el_option, {\n        label: \"处理中\",\n        value: \"processing\"\n      }), _createVNode(_component_el_option, {\n        label: \"排队中\",\n        value: \"queued\"\n      }), _createVNode(_component_el_option, {\n        label: \"失败\",\n        value: \"failed\"\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createElementVNode(\"div\", _hoisted_10, [_cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"日期：\", -1 /* HOISTED */)), _createVNode(_component_el_date_picker, {\n      modelValue: $setup.dateRange,\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.dateRange = $event),\n      type: \"daterange\",\n      \"range-separator\": \"至\",\n      \"start-placeholder\": \"开始日期\",\n      \"end-placeholder\": \"结束日期\",\n      format: \"YYYY-MM-DD\",\n      \"value-format\": \"YYYY-MM-DD\",\n      shortcuts: $setup.dateShortcuts,\n      editable: false,\n      size: \"small\",\n      class: \"date-picker\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"shortcuts\"])]), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: $setup.handleSearch,\n      class: \"filter-btn\"\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"应用\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.resetFilters,\n      class: \"reset-btn\"\n    }, {\n      default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"重置\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.fetchFourWayTasks,\n      class: \"refresh-btn\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_refresh)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])])]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode(_component_el_skeleton, {\n      rows: 10,\n      animated: \"\"\n    })])) : $setup.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode(_component_el_alert, {\n      title: \"获取历史记录失败\",\n      type: \"error\",\n      description: $setup.error,\n      \"show-icon\": \"\"\n    }, null, 8 /* PROPS */, [\"description\"]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.fetchFourWayTasks\n    }, {\n      default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"重试\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])])) : $setup.fourWayTasks.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createVNode(_component_el_empty, {\n      description: \"暂无四方向分析记录\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 四方向上传页面已删除，暂时隐藏此按钮 \"), _createCommentVNode(\" <el-button class=\\\"custom-btn-primary\\\" disabled>开始四方向分析</el-button> \")]),\n      _: 1 /* STABLE */\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.fourWayTasks,\n      style: {\n        \"width\": \"100%\"\n      },\n      border: \"\",\n      stripe: \"\",\n      \"default-sort\": {\n        prop: $setup.sortProp,\n        order: $setup.sortOrder\n      },\n      onSortChange: $setup.handleSortChange,\n      onSelectionChange: $setup.handleSelectionChange,\n      \"max-height\": \"calc(100vh - 350px)\",\n      class: \"custom-table\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        width: \"55\"\n      }, {\n        header: _withCtx(() => [_createVNode(_component_el_checkbox, {\n          modelValue: $setup.selectAll,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.selectAll = $event),\n          onChange: $setup.toggleSelectAll,\n          indeterminate: $setup.isIndeterminate\n        }, null, 8 /* PROPS */, [\"modelValue\", \"onChange\", \"indeterminate\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"task_id\",\n        label: \"任务ID\",\n        \"min-width\": \"200\",\n        sortable: \"\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"span\", _hoisted_18, _toDisplayString(scope.row.task_id || scope.row.taskId), 1 /* TEXT */), _createVNode(_component_el_button, {\n          type: \"primary\",\n          size: \"small\",\n          circle: \"\",\n          onClick: $event => $setup.copyTaskId(scope.row.task_id || scope.row.taskId),\n          class: \"copy-button\",\n          title: \"复制任务ID\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_DocumentCopy)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"created_at\",\n        label: \"创建时间\",\n        width: \"180\",\n        sortable: \"\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.created_at || scope.row.createdAt)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 添加分析人列 \"), _createVNode(_component_el_table_column, {\n        prop: \"username\",\n        label: \"分析人\",\n        width: \"120\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString(scope.row.username || '未知用户'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"status\",\n        label: \"状态\",\n        width: \"120\",\n        sortable: \"\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusType(scope.row.status),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(scope.row.status)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"progress\",\n        label: \"进度\",\n        width: \"150\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_progress, {\n          percentage: scope.row.progress || 0,\n          status: $setup.getProgressStatus(scope.row.status),\n          \"stroke-width\": 10\n        }, null, 8 /* PROPS */, [\"percentage\", \"status\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"车辆统计\",\n        width: \"200\"\n      }, {\n        default: _withCtx(scope => [scope.row.status === 'completed' && scope.row.directions ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, \" 总计: \" + _toDisplayString($setup.getTotalVehicles(scope.row)) + \" 辆 \", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_21, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(scope.row.directions, (direction, key) => {\n          return _openBlock(), _createElementBlock(\"span\", {\n            key: key,\n            class: \"direction-stat\"\n          }, _toDisplayString($setup.getDirectionName(key)) + \": \" + _toDisplayString(direction.vehicleCount || 0), 1 /* TEXT */);\n        }), 128 /* KEYED_FRAGMENT */))])])) : (_openBlock(), _createElementBlock(\"span\", _hoisted_22, \"-\"))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"280\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"div\", _hoisted_23, [scope.row.status === 'completed' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          size: \"small\",\n          class: \"custom-btn-primary\",\n          onClick: $event => $setup.viewResult(scope.row)\n        }, {\n          default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\" 查看结果 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), scope.row.status === 'completed' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          size: \"small\",\n          class: \"action-btn-success\",\n          onClick: $event => $setup.viewReport(scope.row)\n        }, {\n          default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 智能报告 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), ['queued', 'processing'].includes(scope.row.status) ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 2,\n          size: \"small\",\n          class: \"action-btn\",\n          onClick: $event => $setup.checkStatus(scope.row)\n        }, {\n          default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 查看进度 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), scope.row.status === 'failed' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 3,\n          size: \"small\",\n          class: \"action-btn-warning\",\n          onClick: $event => $setup.handleRetryAnalysis(scope.row)\n        }, {\n          default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\" 重试 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n          size: \"small\",\n          class: \"custom-btn-outline\",\n          onClick: $event => $setup.showDeleteConfirm(scope.row)\n        }, {\n          default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\" 删除 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\", \"default-sort\", \"onSortChange\", \"onSelectionChange\"])), [[_directive_loading, $setup.tableLoading]]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [$setup.fourWayTasks.length > 0 ? (_openBlock(), _createBlock(_component_el_checkbox, {\n      key: 0,\n      modelValue: $setup.selectAll,\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.selectAll = $event),\n      onChange: $setup.toggleSelectAll,\n      class: \"me-2\"\n    }, {\n      default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"全选\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])) : _createCommentVNode(\"v-if\", true), $setup.selectedRows.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_26, \" 已选择 \" + _toDisplayString($setup.selectedRows.length) + \" 项 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.selectedRows.length > 0 ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 2,\n      class: \"custom-btn-outline\",\n      size: \"small\",\n      onClick: $setup.handleBatchDelete\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\" 批量删除 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, \"共 \" + _toDisplayString($setup.total) + \" 个记录\", 1 /* TEXT */), _createVNode(_component_el_pagination, {\n      background: \"\",\n      layout: \"prev, pager, next, sizes\",\n      total: $setup.total,\n      \"page-size\": $setup.pageSize,\n      \"current-page\": $setup.currentPage,\n      \"page-sizes\": [10, 20, 50, 100],\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange,\n      \"prev-text\": \"上一页\",\n      \"next-text\": \"下一页\"\n    }, null, 8 /* PROPS */, [\"total\", \"page-size\", \"current-page\", \"onSizeChange\", \"onCurrentChange\"])])])]))]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 批量删除确认对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"批量删除\",\n    modelValue: $setup.batchDeleteDialogVisible,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.batchDeleteDialogVisible = $event),\n    width: \"400px\",\n    class: \"dark-theme-dialog\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_button, {\n      class: \"confirm-delete-btn\",\n      onClick: $setup.confirmBatchDelete,\n      loading: $setup.batchOperationLoading\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\" 确认删除 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n      class: \"cancel-btn\",\n      onClick: _cache[7] || (_cache[7] = $event => $setup.batchDeleteDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [_createElementVNode(\"span\", null, \"确定要删除选中的\" + _toDisplayString($setup.selectedRows.length) + \"条记录吗？此操作不可恢复！\", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 单条记录删除确认对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"确认删除\",\n    modelValue: $setup.deleteDialogVisible,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.deleteDialogVisible = $event),\n    width: \"400px\",\n    class: \"dark-theme-dialog\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_30, [_createVNode(_component_el_button, {\n      class: \"confirm-delete-btn\",\n      onClick: $setup.confirmDelete\n    }, {\n      default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\" 确定删除 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      class: \"cancel-btn\",\n      onClick: _cache[9] || (_cache[9] = $event => $setup.deleteDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [_cache[32] || (_cache[32] = _createElementVNode(\"span\", null, \"确定要删除这条分析记录吗？此操作无法恢复。\", -1 /* HOISTED */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "$setup", "fourWayTasks", "length", "_hoisted_4", "_component_el_checkbox", "modelValue", "selectAll", "_cache", "$event", "onChange", "toggleSelectAll", "indeterminate", "isIndeterminate", "default", "_createTextVNode", "_", "_createCommentVNode", "_component_el_button", "disabled", "hasSelected", "onClick", "handleBatchDelete", "_component_el_icon", "_component_delete", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_component_el_input", "searchQuery", "placeholder", "clearable", "onClear", "handleSearch", "onKeyup", "_with<PERSON><PERSON><PERSON>", "size", "prefix", "_component_search", "isAdmin", "_hoisted_8", "_component_el_select", "userFilter", "_component_el_option", "label", "value", "_Fragment", "_renderList", "userList", "user", "_createBlock", "id", "username", "_hoisted_9", "statusFilter", "_hoisted_10", "_component_el_date_picker", "date<PERSON><PERSON><PERSON>", "type", "format", "shortcuts", "dateShortcuts", "editable", "_hoisted_11", "resetFilters", "fetchFourWayTasks", "_component_refresh", "loading", "_hoisted_12", "_component_el_skeleton", "rows", "animated", "error", "_hoisted_13", "_component_el_alert", "title", "description", "_hoisted_14", "_hoisted_15", "_component_el_empty", "_hoisted_16", "_component_el_table", "data", "style", "border", "stripe", "prop", "sortProp", "order", "sortOrder", "onSortChange", "handleSortChange", "onSelectionChange", "handleSelectionChange", "_component_el_table_column", "width", "sortable", "scope", "_hoisted_17", "_hoisted_18", "_toDisplayString", "row", "task_id", "taskId", "circle", "copyTaskId", "_component_DocumentCopy", "formatDate", "created_at", "createdAt", "_component_el_tag", "getStatusType", "status", "getStatusText", "_component_el_progress", "percentage", "progress", "getProgressStatus", "directions", "_hoisted_19", "_hoisted_20", "getTotalVehicles", "_hoisted_21", "direction", "getDirectionName", "vehicleCount", "_hoisted_22", "fixed", "_hoisted_23", "viewResult", "viewReport", "includes", "checkStatus", "handleRetryAnalysis", "showDeleteConfirm", "tableLoading", "_hoisted_24", "_hoisted_25", "selectedRows", "_hoisted_26", "_hoisted_27", "_hoisted_28", "total", "_component_el_pagination", "background", "layout", "pageSize", "currentPage", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "batchDeleteDialogVisible", "footer", "_hoisted_29", "confirmBatchDelete", "batchOperationLoading", "deleteDialogVisible", "_hoisted_30", "confirmDelete"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\FourWayHistory.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-history-container\">\n    <el-card class=\"history-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>四方向智能交通分析历史</h2>\n          <div class=\"header-actions\">\n            <div class=\"selection-controls\" v-if=\"fourWayTasks.length > 0\">\n              <el-checkbox \n                v-model=\"selectAll\" \n                @change=\"toggleSelectAll\"\n                :indeterminate=\"isIndeterminate\"\n                class=\"wider-checkbox\"\n              >全选</el-checkbox>\n            </div>\n            <el-button \n              class=\"custom-btn-outline\" \n              :disabled=\"!hasSelected\" \n              @click=\"handleBatchDelete\"\n            >\n              <el-icon><delete /></el-icon> 批量删除\n            </el-button>\n            <!-- 四方向上传页面已删除，暂时隐藏此按钮 -->\n            <!-- <el-button class=\"custom-btn-primary\" disabled>\n              <el-icon><plus /></el-icon> 新建四方向分析\n            </el-button> -->\n          </div>\n        </div>\n      </template>\n      \n      <!-- 筛选面板 -->\n      <div class=\"filter-panel\">\n        <div class=\"filter-row\">\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">搜索：</span>\n            <el-input\n              v-model=\"searchQuery\"\n              placeholder=\"搜索任务名称\"\n              clearable\n              @clear=\"handleSearch\"\n              @keyup.enter=\"handleSearch\"\n              size=\"small\"\n              class=\"search-input\"\n            >\n              <template #prefix>\n                <el-icon class=\"el-input__icon\"><search /></el-icon>\n              </template>\n            </el-input>\n          </div>\n          \n          <!-- 管理员用户筛选功能 -->\n          <div v-if=\"isAdmin\" class=\"filter-item\">\n            <span class=\"filter-label\">用户：</span>\n            <el-select\n              v-model=\"userFilter\"\n              placeholder=\"用户筛选\"\n              clearable\n              @change=\"handleSearch\"\n              size=\"small\"\n              :popper-append-to-body=\"true\"\n              :reserve-keyword=\"false\"\n            >\n              <el-option label=\"全部用户\" value=\"\"></el-option>\n              <el-option\n                v-for=\"user in userList\"\n                :key=\"user.id\"\n                :label=\"user.username\"\n                :value=\"user.id\"\n              ></el-option>\n            </el-select>\n          </div>\n          \n          <div class=\"filter-item\">\n            <span class=\"filter-label\">状态：</span>\n            <el-select\n              v-model=\"statusFilter\"\n              placeholder=\"状态筛选\"\n              clearable\n              @change=\"handleSearch\"\n              size=\"small\"\n            >\n              <el-option label=\"全部状态\" value=\"\"></el-option>\n              <el-option label=\"已完成\" value=\"completed\"></el-option>\n              <el-option label=\"处理中\" value=\"processing\"></el-option>\n              <el-option label=\"排队中\" value=\"queued\"></el-option>\n              <el-option label=\"失败\" value=\"failed\"></el-option>\n            </el-select>\n          </div>\n          \n          <div class=\"filter-item\">\n            <span class=\"filter-label\">日期：</span>\n            <el-date-picker\n              v-model=\"dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              format=\"YYYY-MM-DD\"\n              value-format=\"YYYY-MM-DD\"\n              :shortcuts=\"dateShortcuts\"\n              :editable=\"false\"\n              size=\"small\"\n              class=\"date-picker\"\n            >\n            </el-date-picker>\n          </div>\n          \n          <div class=\"filter-actions\">\n            <el-button type=\"primary\" size=\"small\" @click=\"handleSearch\" class=\"filter-btn\">应用</el-button>\n            <el-button size=\"small\" @click=\"resetFilters\" class=\"reset-btn\">重置</el-button>\n            <el-button size=\"small\" @click=\"fetchFourWayTasks\" class=\"refresh-btn\">\n              <el-icon><refresh /></el-icon>\n            </el-button>\n          </div>\n        </div>\n      </div>\n      \n      <div v-if=\"loading\" class=\"loading-container\">\n        <el-skeleton :rows=\"10\" animated />\n      </div>\n      \n      <div v-else-if=\"error\" class=\"error-container\">\n        <el-alert\n          title=\"获取历史记录失败\"\n          type=\"error\"\n          :description=\"error\"\n          show-icon\n        />\n        <div class=\"error-actions\">\n          <el-button type=\"primary\" @click=\"fetchFourWayTasks\">重试</el-button>\n        </div>\n      </div>\n      \n      <div v-else-if=\"fourWayTasks.length === 0\" class=\"empty-container\">\n        <el-empty description=\"暂无四方向分析记录\">\n          <!-- 四方向上传页面已删除，暂时隐藏此按钮 -->\n          <!-- <el-button class=\"custom-btn-primary\" disabled>开始四方向分析</el-button> -->\n        </el-empty>\n      </div>\n      \n      <div v-else>\n        <el-table\n          :data=\"fourWayTasks\"\n          style=\"width: 100%\"\n          border\n          stripe\n          :default-sort=\"{ prop: sortProp, order: sortOrder }\"\n          @sort-change=\"handleSortChange\"\n          @selection-change=\"handleSelectionChange\"\n          v-loading=\"tableLoading\"\n          max-height=\"calc(100vh - 350px)\"\n          class=\"custom-table\"\n        >\n          <el-table-column type=\"selection\" width=\"55\">\n            <template #header>\n              <el-checkbox \n                v-model=\"selectAll\" \n                @change=\"toggleSelectAll\"\n                :indeterminate=\"isIndeterminate\"\n              />\n            </template>\n          </el-table-column>\n          \n          <el-table-column prop=\"task_id\" label=\"任务ID\" min-width=\"200\" sortable>\n            <template #default=\"scope\">\n              <div class=\"task-id-container\">\n                <span class=\"task-id\">{{ scope.row.task_id || scope.row.taskId }}</span>\n                <el-button \n                  type=\"primary\" \n                  size=\"small\" \n                  circle \n                  @click=\"copyTaskId(scope.row.task_id || scope.row.taskId)\"\n                  class=\"copy-button\"\n                  title=\"复制任务ID\"\n                >\n                  <el-icon><DocumentCopy /></el-icon>\n                </el-button>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"180\" sortable>\n            <template #default=\"scope\">\n              {{ formatDate(scope.row.created_at || scope.row.createdAt) }}\n            </template>\n          </el-table-column>\n          \n          <!-- 添加分析人列 -->\n          <el-table-column prop=\"username\" label=\"分析人\" width=\"120\">\n            <template #default=\"scope\">\n              {{ scope.row.username || '未知用户' }}\n            </template>\n          </el-table-column>\n          \n          <el-table-column prop=\"status\" label=\"状态\" width=\"120\" sortable>\n            <template #default=\"scope\">\n              <el-tag :type=\"getStatusType(scope.row.status)\" size=\"small\">\n                {{ getStatusText(scope.row.status) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          \n          <el-table-column prop=\"progress\" label=\"进度\" width=\"150\">\n            <template #default=\"scope\">\n              <el-progress \n                :percentage=\"scope.row.progress || 0\" \n                :status=\"getProgressStatus(scope.row.status)\"\n                :stroke-width=\"10\"\n              />\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"车辆统计\" width=\"200\">\n            <template #default=\"scope\">\n              <div v-if=\"scope.row.status === 'completed' && scope.row.directions\" class=\"vehicle-stats\">\n                <div class=\"total-vehicles\">\n                  总计: {{ getTotalVehicles(scope.row) }} 辆\n                </div>\n                <div class=\"direction-breakdown\">\n                  <span v-for=\"(direction, key) in scope.row.directions\" :key=\"key\" class=\"direction-stat\">\n                    {{ getDirectionName(key) }}: {{ direction.vehicleCount || 0 }}\n                  </span>\n                </div>\n              </div>\n              <span v-else class=\"text-muted\">-</span>\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"操作\" width=\"280\" fixed=\"right\">\n            <template #default=\"scope\">\n              <div class=\"table-actions\">\n                <el-button \n                  size=\"small\" \n                  class=\"custom-btn-primary\" \n                  v-if=\"scope.row.status === 'completed'\"\n                  @click=\"viewResult(scope.row)\"\n                >\n                  查看结果\n                </el-button>\n                \n                <el-button \n                  size=\"small\" \n                  class=\"action-btn-success\" \n                  v-if=\"scope.row.status === 'completed'\"\n                  @click=\"viewReport(scope.row)\"\n                >\n                  智能报告\n                </el-button>\n                \n                <el-button \n                  size=\"small\" \n                  class=\"action-btn\"\n                  v-if=\"['queued', 'processing'].includes(scope.row.status)\"\n                  @click=\"checkStatus(scope.row)\"\n                >\n                  查看进度\n                </el-button>\n                \n                <el-button \n                  size=\"small\" \n                  class=\"action-btn-warning\" \n                  v-if=\"scope.row.status === 'failed'\"\n                  @click=\"handleRetryAnalysis(scope.row)\"\n                >\n                  重试\n                </el-button>\n                \n                <el-button \n                  size=\"small\" \n                  class=\"custom-btn-outline\"\n                  @click=\"showDeleteConfirm(scope.row)\"\n                >\n                  删除\n                </el-button>\n              </div>\n            </template>\n          </el-table-column>\n        </el-table>\n        \n        <div class=\"pagination-container\">\n          <div class=\"batch-actions\">\n            <el-checkbox v-if=\"fourWayTasks.length > 0\" v-model=\"selectAll\" @change=\"toggleSelectAll\" class=\"me-2\">全选</el-checkbox>\n            <span v-if=\"selectedRows.length > 0\" class=\"selected-info\">\n              已选择 {{ selectedRows.length }} 项\n            </span>\n            <el-button \n              v-if=\"selectedRows.length > 0\" \n              class=\"custom-btn-outline\"\n              size=\"small\" \n              @click=\"handleBatchDelete\"\n            >\n              批量删除\n            </el-button>\n          </div>\n          <div class=\"pagination-wrapper\">\n            <div class=\"pagination-info\">共 {{ total }} 个记录</div>\n            <el-pagination\n              background\n              layout=\"prev, pager, next, sizes\"\n              :total=\"total\"\n              :page-size=\"pageSize\"\n              :current-page=\"currentPage\"\n              :page-sizes=\"[10, 20, 50, 100]\"\n              @size-change=\"handleSizeChange\"\n              @current-change=\"handleCurrentChange\"\n              prev-text=\"上一页\"\n              next-text=\"下一页\"\n            />\n          </div>\n        </div>\n      </div>\n    </el-card>\n    \n    <!-- 批量删除确认对话框 -->\n    <el-dialog\n      title=\"批量删除\"\n      v-model=\"batchDeleteDialogVisible\"\n      width=\"400px\"\n      class=\"dark-theme-dialog\"\n      :close-on-click-modal=\"false\"\n    >\n      <span>确定要删除选中的{{ selectedRows.length }}条记录吗？此操作不可恢复！</span>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button class=\"confirm-delete-btn\" @click=\"confirmBatchDelete\" :loading=\"batchOperationLoading\">\n            确认删除\n          </el-button>\n          <el-button class=\"cancel-btn\" @click=\"batchDeleteDialogVisible = false\">取消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 单条记录删除确认对话框 -->\n    <el-dialog\n      title=\"确认删除\"\n      v-model=\"deleteDialogVisible\"\n      width=\"400px\"\n      class=\"dark-theme-dialog\"\n      :close-on-click-modal=\"false\"\n    >\n      <span>确定要删除这条分析记录吗？此操作无法恢复。</span>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button class=\"confirm-delete-btn\" @click=\"confirmDelete\">\n            确定删除\n          </el-button>\n          <el-button class=\"cancel-btn\" @click=\"deleteDialogVisible = false\">取消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, computed, watch } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport { Search, Delete, Plus, Refresh, DocumentCopy } from '@element-plus/icons-vue';\nimport { useStore } from 'vuex';\n\nexport default {\n  name: 'FourWayHistory',\n  components: {\n    Search,\n    Delete,\n    Plus,\n    Refresh,\n    DocumentCopy\n  },\n  setup() {\n    const router = useRouter();\n    const store = useStore();\n    const fourWayTasks = ref([]);\n    const loading = ref(true);\n    const tableLoading = ref(false);\n    const error = ref('');\n    \n    // 分页参数\n    const total = ref(0);\n    const pageSize = ref(10);\n    const currentPage = ref(1);\n    \n    // 排序参数\n    const sortProp = ref('created_at');\n    const sortOrder = ref('descending');\n    \n    // 筛选参数\n    const searchQuery = ref('');\n    const userFilter = ref('');\n    const statusFilter = ref('');\n    const userList = ref([]);\n    const dateRange = ref([]);\n    \n    // 日期快捷选项\n    const dateShortcuts = [\n      {\n        text: '最近一周',\n        value: () => {\n          const end = new Date();\n          const start = new Date();\n          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];\n        },\n      },\n      {\n        text: '最近一个月',\n        value: () => {\n          const end = new Date();\n          const start = new Date();\n          start.setMonth(start.getMonth() - 1);\n          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];\n        },\n      },\n      {\n        text: '最近三个月',\n        value: () => {\n          const end = new Date();\n          const start = new Date();\n          start.setMonth(start.getMonth() - 3);\n          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];\n        },\n      }\n    ];\n\n    // 格式化日期为YYYY-MM-DD\n    const formatToYYYYMMDD = (date) => {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    };\n    \n    // 选择相关\n    const selectedRows = ref([]);\n    const hasSelected = computed(() => selectedRows.value.length > 0);\n    const selectAll = ref(false);\n    const isIndeterminate = ref(false);\n    \n    // 批量操作相关\n    const batchDeleteDialogVisible = ref(false);\n    const batchOperationLoading = ref(false);\n    \n    // 单条删除相关\n    const deleteDialogVisible = ref(false);\n    const currentDeleteTask = ref(null);\n    \n    // 全选/取消全选\n    const toggleSelectAll = (val) => {\n      if (val) {\n        selectedRows.value = [...fourWayTasks.value];\n      } else {\n        selectedRows.value = [];\n      }\n      isIndeterminate.value = false;\n    };\n    \n    // 用户权限相关计算属性\n    const isAdmin = computed(() => {\n      try {\n        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');\n        const role = storedUser?.role?.toLowerCase() || store.state.user?.role?.toLowerCase();\n        return role === 'admin' || role === 'administrator';\n      } catch (e) {\n        console.error('解析用户角色出错:', e);\n        const role = store.state.user?.role?.toLowerCase();\n        return role === 'admin' || role === 'administrator';\n      }\n    });\n    \n    // 监听筛选条件变化\n    watch([searchQuery, userFilter, statusFilter, dateRange], () => {\n      currentPage.value = 1;\n      fetchFourWayTasks();\n    }, { deep: true });\n\n    return {\n      fourWayTasks,\n      loading,\n      tableLoading,\n      error,\n      total,\n      pageSize,\n      currentPage,\n      sortProp,\n      sortOrder,\n      searchQuery,\n      userFilter,\n      statusFilter,\n      userList,\n      dateRange,\n      dateShortcuts,\n      selectedRows,\n      hasSelected,\n      selectAll,\n      isIndeterminate,\n      batchDeleteDialogVisible,\n      batchOperationLoading,\n      deleteDialogVisible,\n      currentDeleteTask,\n      isAdmin,\n      \n      // 方法\n      toggleSelectAll,\n      fetchFourWayTasks,\n      handleSearch,\n      resetFilters,\n      handleSortChange,\n      handleSelectionChange,\n      handleSizeChange,\n      handleCurrentChange,\n      handleBatchDelete,\n      confirmBatchDelete,\n      showDeleteConfirm,\n      confirmDelete,\n      viewResult,\n      viewReport,\n      checkStatus,\n      handleRetryAnalysis,\n      copyTaskId,\n      formatDate,\n      getStatusType,\n      getStatusText,\n      getProgressStatus,\n      getTotalVehicles,\n      getDirectionName\n    };\n\n    // 获取四方向任务列表\n    async function fetchFourWayTasks() {\n      loading.value = true;\n      tableLoading.value = true;\n      error.value = '';\n\n      try {\n        const token = localStorage.getItem('auth_token');\n        if (!token) {\n          throw new Error('未登录或认证令牌已过期');\n        }\n\n        // 构建查询参数\n        const params = new URLSearchParams({\n          page: currentPage.value - 1,\n          size: pageSize.value,\n          sort: `${sortProp.value},${sortOrder.value === 'ascending' ? 'asc' : 'desc'}`\n        });\n\n        if (searchQuery.value) {\n          params.append('search', searchQuery.value);\n        }\n        if (userFilter.value) {\n          params.append('userId', userFilter.value);\n        }\n        if (statusFilter.value) {\n          params.append('status', statusFilter.value);\n        }\n        if (dateRange.value && dateRange.value.length === 2) {\n          params.append('startDate', dateRange.value[0]);\n          params.append('endDate', dateRange.value[1]);\n        }\n\n        const response = await fetch(`/api/video-analysis/four-way/tasks?${params}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const data = await response.json();\n        fourWayTasks.value = data.content || [];\n        total.value = data.totalElements || 0;\n\n        // 更新选择状态\n        updateSelectionState();\n\n      } catch (err) {\n        console.error('获取四方向任务列表失败:', err);\n        error.value = err.message || '获取数据失败';\n        ElMessage.error('获取四方向分析历史失败: ' + error.value);\n      } finally {\n        loading.value = false;\n        tableLoading.value = false;\n      }\n    }\n\n    // 搜索处理\n    function handleSearch() {\n      currentPage.value = 1;\n      fetchFourWayTasks();\n    }\n\n    // 重置筛选条件\n    function resetFilters() {\n      searchQuery.value = '';\n      userFilter.value = '';\n      statusFilter.value = '';\n      dateRange.value = [];\n      currentPage.value = 1;\n      fetchFourWayTasks();\n    }\n\n    // 排序变化处理\n    function handleSortChange({ prop, order }) {\n      sortProp.value = prop || 'created_at';\n      sortOrder.value = order || 'descending';\n      fetchFourWayTasks();\n    }\n\n    // 选择变化处理\n    function handleSelectionChange(selection) {\n      selectedRows.value = selection;\n      updateSelectionState();\n    }\n\n    // 更新选择状态\n    function updateSelectionState() {\n      const selectedCount = selectedRows.value.length;\n      const totalCount = fourWayTasks.value.length;\n\n      if (selectedCount === 0) {\n        selectAll.value = false;\n        isIndeterminate.value = false;\n      } else if (selectedCount === totalCount) {\n        selectAll.value = true;\n        isIndeterminate.value = false;\n      } else {\n        selectAll.value = false;\n        isIndeterminate.value = true;\n      }\n    }\n\n    // 分页大小变化\n    function handleSizeChange(newSize) {\n      pageSize.value = newSize;\n      currentPage.value = 1;\n      fetchFourWayTasks();\n    }\n\n    // 当前页变化\n    function handleCurrentChange(newPage) {\n      currentPage.value = newPage;\n      fetchFourWayTasks();\n    }\n\n    // 批量删除处理\n    function handleBatchDelete() {\n      if (selectedRows.value.length === 0) {\n        ElMessage.warning('请先选择要删除的记录');\n        return;\n      }\n      batchDeleteDialogVisible.value = true;\n    }\n\n    // 确认批量删除\n    async function confirmBatchDelete() {\n      batchOperationLoading.value = true;\n      try {\n        const taskIds = selectedRows.value.map(row => row.task_id || row.taskId);\n\n        const token = localStorage.getItem('auth_token');\n        const response = await fetch('/api/video-analysis/four-way/batch-delete', {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({ taskIds })\n        });\n\n        if (!response.ok) {\n          throw new Error('批量删除失败');\n        }\n\n        ElMessage.success(`成功删除 ${taskIds.length} 条记录`);\n        batchDeleteDialogVisible.value = false;\n        selectedRows.value = [];\n        fetchFourWayTasks();\n\n      } catch (err) {\n        console.error('批量删除失败:', err);\n        ElMessage.error('批量删除失败: ' + err.message);\n      } finally {\n        batchOperationLoading.value = false;\n      }\n    }\n\n    // 显示删除确认\n    function showDeleteConfirm(task) {\n      currentDeleteTask.value = task;\n      deleteDialogVisible.value = true;\n    }\n\n    // 确认删除单条记录\n    async function confirmDelete() {\n      if (!currentDeleteTask.value) return;\n\n      try {\n        const taskId = currentDeleteTask.value.task_id || currentDeleteTask.value.taskId;\n        const token = localStorage.getItem('auth_token');\n\n        const response = await fetch(`/api/video-analysis/four-way/${taskId}`, {\n          method: 'DELETE',\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error('删除失败');\n        }\n\n        ElMessage.success('删除成功');\n        deleteDialogVisible.value = false;\n        currentDeleteTask.value = null;\n        fetchFourWayTasks();\n\n      } catch (err) {\n        console.error('删除失败:', err);\n        ElMessage.error('删除失败: ' + err.message);\n      }\n    }\n\n    // 查看结果\n    function viewResult(task) {\n      const taskId = task.task_id || task.taskId;\n      router.push(`/four-way-result/${taskId}`);\n    }\n\n    // 查看智能报告\n    function viewReport(task) {\n      const taskId = task.task_id || task.taskId;\n      router.push(`/four-way-report/${taskId}`);\n    }\n\n    // 查看状态\n    function checkStatus(task) {\n      const taskId = task.task_id || task.taskId;\n      const status = task.status;\n\n      console.log('查看任务进度:', { taskId, status });\n\n      // 构建查询参数，包含任务状态信息\n      const queryParams = {\n        taskId: taskId\n      };\n\n      // 如果有状态信息，也传递过去\n      if (status) {\n        queryParams.status = status;\n      }\n\n      // 显示跳转提示\n      ElMessage.info(`正在跳转到任务 ${taskId} 的进度页面...`);\n\n      // 跳转到四方向控制台，传递任务ID和状态\n      router.push({\n        path: '/four-way-console',\n        query: queryParams\n      });\n    }\n\n    // 重试分析\n    async function handleRetryAnalysis(task) {\n      try {\n        const taskId = task.task_id || task.taskId;\n        const token = localStorage.getItem('auth_token');\n\n        const response = await fetch(`/api/video-analysis/four-way/${taskId}/retry`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error('重试失败');\n        }\n\n        ElMessage.success('已重新开始分析');\n        fetchFourWayTasks();\n\n      } catch (err) {\n        console.error('重试失败:', err);\n        ElMessage.error('重试失败: ' + err.message);\n      }\n    }\n\n    // 复制任务ID\n    function copyTaskId(taskId) {\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(taskId).then(() => {\n          ElMessage.success('任务ID已复制到剪贴板');\n        }).catch(() => {\n          ElMessage.error('复制失败');\n        });\n      } else {\n        // 降级方案\n        const textArea = document.createElement('textarea');\n        textArea.value = taskId;\n        document.body.appendChild(textArea);\n        textArea.select();\n        try {\n          document.execCommand('copy');\n          ElMessage.success('任务ID已复制到剪贴板');\n        } catch (err) {\n          ElMessage.error('复制失败');\n        }\n        document.body.removeChild(textArea);\n      }\n    }\n\n    // 格式化日期\n    function formatDate(dateString) {\n      if (!dateString) return '-';\n      try {\n        const date = new Date(dateString);\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } catch (e) {\n        return dateString;\n      }\n    }\n\n    // 获取状态类型\n    function getStatusType(status) {\n      const statusMap = {\n        'completed': 'success',\n        'processing': 'warning',\n        'queued': 'info',\n        'failed': 'danger'\n      };\n      return statusMap[status] || 'info';\n    }\n\n    // 获取状态文本\n    function getStatusText(status) {\n      const statusMap = {\n        'completed': '已完成',\n        'processing': '处理中',\n        'queued': '排队中',\n        'failed': '失败'\n      };\n      return statusMap[status] || '未知';\n    }\n\n    // 获取进度状态\n    function getProgressStatus(status) {\n      if (status === 'completed') return 'success';\n      if (status === 'failed') return 'exception';\n      return undefined;\n    }\n\n    // 获取总车辆数\n    function getTotalVehicles(task) {\n      if (!task.directions) return 0;\n      return Object.values(task.directions).reduce((total, direction) => {\n        return total + (direction.vehicleCount || 0);\n      }, 0);\n    }\n\n    // 获取方向名称\n    function getDirectionName(direction) {\n      const directionMap = {\n        'EAST': '东',\n        'SOUTH': '南',\n        'WEST': '西',\n        'NORTH': '北'\n      };\n      return directionMap[direction] || direction;\n    }\n\n    // 组件挂载时获取数据\n    onMounted(() => {\n      fetchFourWayTasks();\n    });\n  }\n};\n</script>\n\n<style scoped>\n.four-way-history-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 60px);\n}\n\n.history-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border: none;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0;\n}\n\n.card-header h2 {\n  margin: 0;\n  color: #2c3e50;\n  font-size: 24px;\n  font-weight: 600;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.selection-controls {\n  display: flex;\n  align-items: center;\n}\n\n.wider-checkbox {\n  margin-right: 16px;\n}\n\n.custom-btn-primary {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  color: white;\n  border-radius: 8px;\n  padding: 8px 16px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.custom-btn-primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n}\n\n.custom-btn-outline {\n  border: 2px solid #e74c3c;\n  color: #e74c3c;\n  background: transparent;\n  border-radius: 8px;\n  padding: 8px 16px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.custom-btn-outline:hover {\n  background: #e74c3c;\n  color: white;\n  transform: translateY(-2px);\n}\n\n.filter-panel {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 20px;\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-label {\n  font-weight: 500;\n  color: #555;\n  white-space: nowrap;\n}\n\n.search-input {\n  width: 200px;\n}\n\n.date-picker {\n  width: 240px;\n}\n\n.filter-actions {\n  display: flex;\n  gap: 8px;\n  margin-left: auto;\n}\n\n.filter-btn {\n  background: #3498db;\n  border: none;\n  color: white;\n}\n\n.reset-btn {\n  background: #95a5a6;\n  border: none;\n  color: white;\n}\n\n.refresh-btn {\n  background: #2ecc71;\n  border: none;\n  color: white;\n}\n\n.loading-container, .error-container, .empty-container {\n  padding: 40px;\n  text-align: center;\n}\n\n.error-actions {\n  margin-top: 16px;\n}\n\n.custom-table {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.task-id-container {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.task-id {\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  color: #666;\n}\n\n.copy-button {\n  width: 24px;\n  height: 24px;\n  padding: 0;\n  min-height: auto;\n}\n\n.vehicle-stats {\n  font-size: 12px;\n}\n\n.total-vehicles {\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 4px;\n}\n\n.direction-breakdown {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.direction-stat {\n  background: #ecf0f1;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 11px;\n  color: #555;\n}\n\n.table-actions {\n  display: flex;\n  gap: 4px;\n  flex-wrap: wrap;\n}\n\n.action-btn {\n  background: #3498db;\n  border: none;\n  color: white;\n  border-radius: 4px;\n}\n\n.action-btn-success {\n  background: #2ecc71;\n  border: none;\n  color: white;\n  border-radius: 4px;\n}\n\n.action-btn-warning {\n  background: #f39c12;\n  border: none;\n  color: white;\n  border-radius: 4px;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 20px;\n  padding: 16px 0;\n}\n\n.batch-actions {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.selected-info {\n  color: #666;\n  font-size: 14px;\n}\n\n.pagination-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.pagination-info {\n  color: #666;\n  font-size: 14px;\n}\n\n.dark-theme-dialog {\n  border-radius: 12px;\n}\n\n.dialog-footer {\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n}\n\n.confirm-delete-btn {\n  background: #e74c3c;\n  border: none;\n  color: white;\n  border-radius: 6px;\n}\n\n.cancel-btn {\n  background: #95a5a6;\n  border: none;\n  color: white;\n  border-radius: 6px;\n}\n\n.text-muted {\n  color: #999;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .four-way-history-container {\n    padding: 10px;\n  }\n\n  .card-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: flex-start;\n  }\n\n  .header-actions {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .filter-row {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .filter-item {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .search-input, .date-picker {\n    width: 100%;\n  }\n\n  .pagination-container {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .table-actions {\n    flex-direction: column;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAG5BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;EANrCC,GAAA;EAOiBD,KAAK,EAAC;;;EAwBZA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;EAjClCC,GAAA;EAmD8BD,KAAK,EAAC;;;EAqBrBA,KAAK,EAAC;AAAa;;EAiBnBA,KAAK,EAAC;AAAa;;EAkBnBA,KAAK,EAAC;AAAgB;;EA3GrCC,GAAA;EAqH0BD,KAAK,EAAC;;;EArHhCC,GAAA;EAyH6BD,KAAK,EAAC;;;EAOtBA,KAAK,EAAC;AAAe;;EAhIlCC,GAAA;EAqIiDD,KAAK,EAAC;;;EArIvDC,GAAA;AAAA;;EAqKmBD,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAS;;EAtKrCC,GAAA;EAsNmFD,KAAK,EAAC;;;EACpEA,KAAK,EAAC;AAAgB;;EAGtBA,KAAK,EAAC;AAAqB;;EA1NhDC,GAAA;EAgO2BD,KAAK,EAAC;;;EAMdA,KAAK,EAAC;AAAe;;EAiD3BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAe;;EAxRpCC,GAAA;EA0RiDD,KAAK,EAAC;;;EAYxCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EA4B3BA,KAAK,EAAC;AAAe;;EAmBrBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;;;uBArVhCE,mBAAA,CA6VM,OA7VNC,UA6VM,GA5VJC,YAAA,CAqTUC,kBAAA;IArTDL,KAAK,EAAC;EAAc;IAChBM,MAAM,EAAAC,QAAA,CACf,MAuBM,CAvBNC,mBAAA,CAuBM,OAvBNC,UAuBM,G,4BAtBJD,mBAAA,CAAoB,YAAhB,aAAW,sBACfA,mBAAA,CAoBM,OApBNE,UAoBM,GAnBkCC,MAAA,CAAAC,YAAY,CAACC,MAAM,Q,cAAzDX,mBAAA,CAOM,OAPNY,UAOM,GANJV,YAAA,CAKiBW,sBAAA;MAb/BC,UAAA,EASyBL,MAAA,CAAAM,SAAS;MATlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IASyBR,MAAA,CAAAM,SAAS,GAAAE,MAAA;MACjBC,QAAM,EAAET,MAAA,CAAAU,eAAe;MACvBC,aAAa,EAAEX,MAAA,CAAAY,eAAe;MAC/BvB,KAAK,EAAC;;MAZtBwB,OAAA,EAAAjB,QAAA,CAae,MAAEW,MAAA,SAAAA,MAAA,QAbjBO,gBAAA,CAae,IAAE,E;MAbjBC,CAAA;wEAAAC,mBAAA,gBAeYvB,YAAA,CAMYwB,oBAAA;MALV5B,KAAK,EAAC,oBAAoB;MACzB6B,QAAQ,GAAGlB,MAAA,CAAAmB,WAAW;MACtBC,OAAK,EAAEpB,MAAA,CAAAqB;;MAlBtBR,OAAA,EAAAjB,QAAA,CAoBc,MAA6B,CAA7BH,YAAA,CAA6B6B,kBAAA;QApB3CT,OAAA,EAAAjB,QAAA,CAoBuB,MAAU,CAAVH,YAAA,CAAU8B,iBAAA,E;QApBjCR,CAAA;sCAAAD,gBAAA,CAoB2C,QAC/B,G;MArBZC,CAAA;gDAsBYC,mBAAA,wBAA2B,EAC3BA,mBAAA,oIAEgB,C;IAzB5BH,OAAA,EAAAjB,QAAA,CA+BM,MAoFM,CApFNC,mBAAA,CAoFM,OApFN2B,UAoFM,GAnFJ3B,mBAAA,CAkFM,OAlFN4B,UAkFM,GAjFJ5B,mBAAA,CAeM,OAfN6B,UAeM,G,4BAdJ7B,mBAAA,CAAqC;MAA/BR,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BI,YAAA,CAYWkC,mBAAA;MA/CvBtB,UAAA,EAoCuBL,MAAA,CAAA4B,WAAW;MApClC,uBAAArB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAoCuBR,MAAA,CAAA4B,WAAW,GAAApB,MAAA;MACpBqB,WAAW,EAAC,QAAQ;MACpBC,SAAS,EAAT,EAAS;MACRC,OAAK,EAAE/B,MAAA,CAAAgC,YAAY;MACnBC,OAAK,EAxCpBC,SAAA,CAwC4BlC,MAAA,CAAAgC,YAAY;MAC1BG,IAAI,EAAC,OAAO;MACZ9C,KAAK,EAAC;;MAEK+C,MAAM,EAAAxC,QAAA,CACf,MAAoD,CAApDH,YAAA,CAAoD6B,kBAAA;QAA3CjC,KAAK,EAAC;MAAgB;QA7C/CwB,OAAA,EAAAjB,QAAA,CA6CgD,MAAU,CAAVH,YAAA,CAAU4C,iBAAA,E;QA7C1DtB,CAAA;;MAAAA,CAAA;+DAkDUC,mBAAA,eAAkB,EACPhB,MAAA,CAAAsC,OAAO,I,cAAlB/C,mBAAA,CAmBM,OAnBNgD,UAmBM,G,4BAlBJ1C,mBAAA,CAAqC;MAA/BR,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BI,YAAA,CAgBY+C,oBAAA;MArExBnC,UAAA,EAsDuBL,MAAA,CAAAyC,UAAU;MAtDjC,uBAAAlC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAsDuBR,MAAA,CAAAyC,UAAU,GAAAjC,MAAA;MACnBqB,WAAW,EAAC,MAAM;MAClBC,SAAS,EAAT,EAAS;MACRrB,QAAM,EAAET,MAAA,CAAAgC,YAAY;MACrBG,IAAI,EAAC,OAAO;MACX,uBAAqB,EAAE,IAAI;MAC3B,iBAAe,EAAE;;MA5DhCtB,OAAA,EAAAjB,QAAA,CA8Dc,MAA6C,CAA7CH,YAAA,CAA6CiD,oBAAA;QAAlCC,KAAK,EAAC,MAAM;QAACC,KAAK,EAAC;6BAC9BrD,mBAAA,CAKasD,SAAA,QApE3BC,WAAA,CAgE+B9C,MAAA,CAAA+C,QAAQ,EAAhBC,IAAI;6BADbC,YAAA,CAKaP,oBAAA;UAHVpD,GAAG,EAAE0D,IAAI,CAACE,EAAE;UACZP,KAAK,EAAEK,IAAI,CAACG,QAAQ;UACpBP,KAAK,EAAEI,IAAI,CAACE;;;MAnE7BnC,CAAA;uDAAAC,mBAAA,gBAwEUnB,mBAAA,CAeM,OAfNuD,UAeM,G,4BAdJvD,mBAAA,CAAqC;MAA/BR,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BI,YAAA,CAYY+C,oBAAA;MAtFxBnC,UAAA,EA2EuBL,MAAA,CAAAqD,YAAY;MA3EnC,uBAAA9C,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2EuBR,MAAA,CAAAqD,YAAY,GAAA7C,MAAA;MACrBqB,WAAW,EAAC,MAAM;MAClBC,SAAS,EAAT,EAAS;MACRrB,QAAM,EAAET,MAAA,CAAAgC,YAAY;MACrBG,IAAI,EAAC;;MA/EnBtB,OAAA,EAAAjB,QAAA,CAiFc,MAA6C,CAA7CH,YAAA,CAA6CiD,oBAAA;QAAlCC,KAAK,EAAC,MAAM;QAACC,KAAK,EAAC;UAC9BnD,YAAA,CAAqDiD,oBAAA;QAA1CC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7BnD,YAAA,CAAsDiD,oBAAA;QAA3CC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7BnD,YAAA,CAAkDiD,oBAAA;QAAvCC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7BnD,YAAA,CAAiDiD,oBAAA;QAAtCC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;;MArF1C7B,CAAA;qDAyFUlB,mBAAA,CAgBM,OAhBNyD,WAgBM,G,4BAfJzD,mBAAA,CAAqC;MAA/BR,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BI,YAAA,CAaiB8D,yBAAA;MAxG7BlD,UAAA,EA4FuBL,MAAA,CAAAwD,SAAS;MA5FhC,uBAAAjD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4FuBR,MAAA,CAAAwD,SAAS,GAAAhD,MAAA;MAClBiD,IAAI,EAAC,WAAW;MAChB,iBAAe,EAAC,GAAG;MACnB,mBAAiB,EAAC,MAAM;MACxB,iBAAe,EAAC,MAAM;MACtBC,MAAM,EAAC,YAAY;MACnB,cAAY,EAAC,YAAY;MACxBC,SAAS,EAAE3D,MAAA,CAAA4D,aAAa;MACxBC,QAAQ,EAAE,KAAK;MAChB1B,IAAI,EAAC,OAAO;MACZ9C,KAAK,EAAC;4DAKVQ,mBAAA,CAMM,OANNiE,WAMM,GALJrE,YAAA,CAA8FwB,oBAAA;MAAnFwC,IAAI,EAAC,SAAS;MAACtB,IAAI,EAAC,OAAO;MAAEf,OAAK,EAAEpB,MAAA,CAAAgC,YAAY;MAAE3C,KAAK,EAAC;;MA5G/EwB,OAAA,EAAAjB,QAAA,CA4G4F,MAAEW,MAAA,SAAAA,MAAA,QA5G9FO,gBAAA,CA4G4F,IAAE,E;MA5G9FC,CAAA;oCA6GYtB,YAAA,CAA8EwB,oBAAA;MAAnEkB,IAAI,EAAC,OAAO;MAAEf,OAAK,EAAEpB,MAAA,CAAA+D,YAAY;MAAE1E,KAAK,EAAC;;MA7GhEwB,OAAA,EAAAjB,QAAA,CA6G4E,MAAEW,MAAA,SAAAA,MAAA,QA7G9EO,gBAAA,CA6G4E,IAAE,E;MA7G9EC,CAAA;oCA8GYtB,YAAA,CAEYwB,oBAAA;MAFDkB,IAAI,EAAC,OAAO;MAAEf,OAAK,EAAEpB,MAAA,CAAAgE,iBAAiB;MAAE3E,KAAK,EAAC;;MA9GrEwB,OAAA,EAAAjB,QAAA,CA+Gc,MAA8B,CAA9BH,YAAA,CAA8B6B,kBAAA;QA/G5CT,OAAA,EAAAjB,QAAA,CA+GuB,MAAW,CAAXH,YAAA,CAAWwE,kBAAA,E;QA/GlClD,CAAA;;MAAAA,CAAA;0CAqHiBf,MAAA,CAAAkE,OAAO,I,cAAlB3E,mBAAA,CAEM,OAFN4E,WAEM,GADJ1E,YAAA,CAAmC2E,sBAAA;MAArBC,IAAI,EAAE,EAAE;MAAEC,QAAQ,EAAR;YAGVtE,MAAA,CAAAuE,KAAK,I,cAArBhF,mBAAA,CAUM,OAVNiF,WAUM,GATJ/E,YAAA,CAKEgF,mBAAA;MAJAC,KAAK,EAAC,UAAU;MAChBjB,IAAI,EAAC,OAAO;MACXkB,WAAW,EAAE3E,MAAA,CAAAuE,KAAK;MACnB,WAAS,EAAT;8CAEF1E,mBAAA,CAEM,OAFN+E,WAEM,GADJnF,YAAA,CAAmEwB,oBAAA;MAAxDwC,IAAI,EAAC,SAAS;MAAErC,OAAK,EAAEpB,MAAA,CAAAgE;;MAjI5CnD,OAAA,EAAAjB,QAAA,CAiI+D,MAAEW,MAAA,SAAAA,MAAA,QAjIjEO,gBAAA,CAiI+D,IAAE,E;MAjIjEC,CAAA;0CAqIsBf,MAAA,CAAAC,YAAY,CAACC,MAAM,U,cAAnCX,mBAAA,CAKM,OALNsF,WAKM,GAJJpF,YAAA,CAGWqF,mBAAA;MAHDH,WAAW,EAAC;IAAW;MAtIzC9D,OAAA,EAAAjB,QAAA,CAuIU,MAA2B,CAA3BoB,mBAAA,wBAA2B,EAC3BA,mBAAA,0EAA2E,C;MAxIrFD,CAAA;2BA4IMxB,mBAAA,CA0KM,OAtTZwF,WAAA,G,+BA6IQ9B,YAAA,CAwIW+B,mBAAA;MAvIRC,IAAI,EAAEjF,MAAA,CAAAC,YAAY;MACnBiF,KAAmB,EAAnB;QAAA;MAAA,CAAmB;MACnBC,MAAM,EAAN,EAAM;MACNC,MAAM,EAAN,EAAM;MACL,cAAY;QAAAC,IAAA,EAAUrF,MAAA,CAAAsF,QAAQ;QAAAC,KAAA,EAASvF,MAAA,CAAAwF;MAAS;MAChDC,YAAW,EAAEzF,MAAA,CAAA0F,gBAAgB;MAC7BC,iBAAgB,EAAE3F,MAAA,CAAA4F,qBAAqB;MAExC,YAAU,EAAC,qBAAqB;MAChCvG,KAAK,EAAC;;MAvJhBwB,OAAA,EAAAjB,QAAA,CAyJU,MAQkB,CARlBH,YAAA,CAQkBoG,0BAAA;QARDpC,IAAI,EAAC,WAAW;QAACqC,KAAK,EAAC;;QAC3BnG,MAAM,EAAAC,QAAA,CACf,MAIE,CAJFH,YAAA,CAIEW,sBAAA;UA/JhBC,UAAA,EA4JyBL,MAAA,CAAAM,SAAS;UA5JlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4JyBR,MAAA,CAAAM,SAAS,GAAAE,MAAA;UACjBC,QAAM,EAAET,MAAA,CAAAU,eAAe;UACvBC,aAAa,EAAEX,MAAA,CAAAY;;QA9JhCG,CAAA;UAmKUtB,YAAA,CAgBkBoG,0BAAA;QAhBDR,IAAI,EAAC,SAAS;QAAC1C,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAACoD,QAAQ,EAAR;;QAChDlF,OAAO,EAAAjB,QAAA,CAaVoG,KAbiB,KACvBnG,mBAAA,CAYM,OAZNoG,WAYM,GAXJpG,mBAAA,CAAwE,QAAxEqG,WAAwE,EAAAC,gBAAA,CAA/CH,KAAK,CAACI,GAAG,CAACC,OAAO,IAAIL,KAAK,CAACI,GAAG,CAACE,MAAM,kBAC9D7G,YAAA,CASYwB,oBAAA;UARVwC,IAAI,EAAC,SAAS;UACdtB,IAAI,EAAC,OAAO;UACZoE,MAAM,EAAN,EAAM;UACLnF,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAwG,UAAU,CAACR,KAAK,CAACI,GAAG,CAACC,OAAO,IAAIL,KAAK,CAACI,GAAG,CAACE,MAAM;UACxDjH,KAAK,EAAC,aAAa;UACnBqF,KAAK,EAAC;;UA7KxB7D,OAAA,EAAAjB,QAAA,CA+KkB,MAAmC,CAAnCH,YAAA,CAAmC6B,kBAAA;YA/KrDT,OAAA,EAAAjB,QAAA,CA+K2B,MAAgB,CAAhBH,YAAA,CAAgBgH,uBAAA,E;YA/K3C1F,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAqLUtB,YAAA,CAIkBoG,0BAAA;QAJDR,IAAI,EAAC,YAAY;QAAC1C,KAAK,EAAC,MAAM;QAACmD,KAAK,EAAC,KAAK;QAACC,QAAQ,EAAR;;QAC/ClF,OAAO,EAAAjB,QAAA,CAC6CoG,KADtC,KAtLrClF,gBAAA,CAAAqF,gBAAA,CAuLiBnG,MAAA,CAAA0G,UAAU,CAACV,KAAK,CAACI,GAAG,CAACO,UAAU,IAAIX,KAAK,CAACI,GAAG,CAACQ,SAAS,kB;QAvLvE7F,CAAA;UA2LUC,mBAAA,YAAe,EACfvB,YAAA,CAIkBoG,0BAAA;QAJDR,IAAI,EAAC,UAAU;QAAC1C,KAAK,EAAC,KAAK;QAACmD,KAAK,EAAC;;QACtCjF,OAAO,EAAAjB,QAAA,CACkBoG,KADX,KA7LrClF,gBAAA,CAAAqF,gBAAA,CA8LiBH,KAAK,CAACI,GAAG,CAACjD,QAAQ,2B;QA9LnCpC,CAAA;UAkMUtB,YAAA,CAMkBoG,0BAAA;QANDR,IAAI,EAAC,QAAQ;QAAC1C,KAAK,EAAC,IAAI;QAACmD,KAAK,EAAC,KAAK;QAACC,QAAQ,EAAR;;QACzClF,OAAO,EAAAjB,QAAA,CAGPoG,KAHc,KACvBvG,YAAA,CAESoH,iBAAA;UAFApD,IAAI,EAAEzD,MAAA,CAAA8G,aAAa,CAACd,KAAK,CAACI,GAAG,CAACW,MAAM;UAAG5E,IAAI,EAAC;;UApMnEtB,OAAA,EAAAjB,QAAA,CAqMgB,MAAqC,CArMrDkB,gBAAA,CAAAqF,gBAAA,CAqMmBnG,MAAA,CAAAgH,aAAa,CAAChB,KAAK,CAACI,GAAG,CAACW,MAAM,kB;UArMjDhG,CAAA;;QAAAA,CAAA;UA0MUtB,YAAA,CAQkBoG,0BAAA;QARDR,IAAI,EAAC,UAAU;QAAC1C,KAAK,EAAC,IAAI;QAACmD,KAAK,EAAC;;QACrCjF,OAAO,EAAAjB,QAAA,CAKdoG,KALqB,KACvBvG,YAAA,CAIEwH,sBAAA;UAHCC,UAAU,EAAElB,KAAK,CAACI,GAAG,CAACe,QAAQ;UAC9BJ,MAAM,EAAE/G,MAAA,CAAAoH,iBAAiB,CAACpB,KAAK,CAACI,GAAG,CAACW,MAAM;UAC1C,cAAY,EAAE;;QA/M/BhG,CAAA;UAoNUtB,YAAA,CAckBoG,0BAAA;QAdDlD,KAAK,EAAC,MAAM;QAACmD,KAAK,EAAC;;QACvBjF,OAAO,EAAAjB,QAAA,CAwBqBoG,KAxBd,KACZA,KAAK,CAACI,GAAG,CAACW,MAAM,oBAAoBf,KAAK,CAACI,GAAG,CAACiB,UAAU,I,cAAnE9H,mBAAA,CASM,OATN+H,WASM,GARJzH,mBAAA,CAEM,OAFN0H,WAEM,EAFsB,OACtB,GAAApB,gBAAA,CAAGnG,MAAA,CAAAwH,gBAAgB,CAACxB,KAAK,CAACI,GAAG,KAAI,KACvC,iBACAvG,mBAAA,CAIM,OAJN4H,WAIM,I,kBAHJlI,mBAAA,CAEOsD,SAAA,QA7NzBC,WAAA,CA2NmDkD,KAAK,CAACI,GAAG,CAACiB,UAAU,EA3NvE,CA2NgCK,SAAS,EAAEpI,GAAG;+BAA5BC,mBAAA,CAEO;YAFiDD,GAAG,EAAEA,GAAG;YAAED,KAAK,EAAC;8BACnEW,MAAA,CAAA2H,gBAAgB,CAACrI,GAAG,KAAI,IAAE,GAAA6G,gBAAA,CAAGuB,SAAS,CAACE,YAAY;6DAI5DrI,mBAAA,CAAwC,QAAxCsI,WAAwC,EAAR,GAAC,G;QAhO/C9G,CAAA;UAoOUtB,YAAA,CAgDkBoG,0BAAA;QAhDDlD,KAAK,EAAC,IAAI;QAACmD,KAAK,EAAC,KAAK;QAACgC,KAAK,EAAC;;QACjCjH,OAAO,EAAAjB,QAAA,CA6CVoG,KA7CiB,KACvBnG,mBAAA,CA4CM,OA5CNkI,WA4CM,GAxCI/B,KAAK,CAACI,GAAG,CAACW,MAAM,oB,cAHxB9D,YAAA,CAOYhC,oBAAA;UA9O5B3B,GAAA;UAwOkB6C,IAAI,EAAC,OAAO;UACZ9C,KAAK,EAAC,oBAAoB;UAEzB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAgI,UAAU,CAAChC,KAAK,CAACI,GAAG;;UA3O9CvF,OAAA,EAAAjB,QAAA,CA4OiB,MAEDW,MAAA,SAAAA,MAAA,QA9OhBO,gBAAA,CA4OiB,QAED,E;UA9OhBC,CAAA;4DAAAC,mBAAA,gBAmPwBgF,KAAK,CAACI,GAAG,CAACW,MAAM,oB,cAHxB9D,YAAA,CAOYhC,oBAAA;UAvP5B3B,GAAA;UAiPkB6C,IAAI,EAAC,OAAO;UACZ9C,KAAK,EAAC,oBAAoB;UAEzB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAiI,UAAU,CAACjC,KAAK,CAACI,GAAG;;UApP9CvF,OAAA,EAAAjB,QAAA,CAqPiB,MAEDW,MAAA,SAAAA,MAAA,QAvPhBO,gBAAA,CAqPiB,QAED,E;UAvPhBC,CAAA;4DAAAC,mBAAA,gB,yBA4PiDkH,QAAQ,CAAClC,KAAK,CAACI,GAAG,CAACW,MAAM,K,cAH1D9D,YAAA,CAOYhC,oBAAA;UAhQ5B3B,GAAA;UA0PkB6C,IAAI,EAAC,OAAO;UACZ9C,KAAK,EAAC,YAAY;UAEjB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAmI,WAAW,CAACnC,KAAK,CAACI,GAAG;;UA7P/CvF,OAAA,EAAAjB,QAAA,CA8PiB,MAEDW,MAAA,SAAAA,MAAA,QAhQhBO,gBAAA,CA8PiB,QAED,E;UAhQhBC,CAAA;4DAAAC,mBAAA,gBAqQwBgF,KAAK,CAACI,GAAG,CAACW,MAAM,iB,cAHxB9D,YAAA,CAOYhC,oBAAA;UAzQ5B3B,GAAA;UAmQkB6C,IAAI,EAAC,OAAO;UACZ9C,KAAK,EAAC,oBAAoB;UAEzB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAoI,mBAAmB,CAACpC,KAAK,CAACI,GAAG;;UAtQvDvF,OAAA,EAAAjB,QAAA,CAuQiB,MAEDW,MAAA,SAAAA,MAAA,QAzQhBO,gBAAA,CAuQiB,MAED,E;UAzQhBC,CAAA;4DAAAC,mBAAA,gBA2QgBvB,YAAA,CAMYwB,oBAAA;UALVkB,IAAI,EAAC,OAAO;UACZ9C,KAAK,EAAC,oBAAoB;UACzB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAqI,iBAAiB,CAACrC,KAAK,CAACI,GAAG;;UA9QrDvF,OAAA,EAAAjB,QAAA,CA+QiB,MAEDW,MAAA,SAAAA,MAAA,QAjRhBO,gBAAA,CA+QiB,MAED,E;UAjRhBC,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;6GAqJqBf,MAAA,CAAAsI,YAAY,E,GAkIzBzI,mBAAA,CA8BM,OA9BN0I,WA8BM,GA7BJ1I,mBAAA,CAaM,OAbN2I,WAaM,GAZexI,MAAA,CAAAC,YAAY,CAACC,MAAM,Q,cAAtC+C,YAAA,CAAuH7C,sBAAA;MAzRnId,GAAA;MAAAe,UAAA,EAyRiEL,MAAA,CAAAM,SAAS;MAzR1E,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAyRiER,MAAA,CAAAM,SAAS,GAAAE,MAAA;MAAGC,QAAM,EAAET,MAAA,CAAAU,eAAe;MAAErB,KAAK,EAAC;;MAzR5GwB,OAAA,EAAAjB,QAAA,CAyRmH,MAAEW,MAAA,SAAAA,MAAA,QAzRrHO,gBAAA,CAyRmH,IAAE,E;MAzRrHC,CAAA;qDAAAC,mBAAA,gBA0RwBhB,MAAA,CAAAyI,YAAY,CAACvI,MAAM,Q,cAA/BX,mBAAA,CAEO,QAFPmJ,WAEO,EAFoD,OACrD,GAAAvC,gBAAA,CAAGnG,MAAA,CAAAyI,YAAY,CAACvI,MAAM,IAAG,KAC/B,mBA5RZc,mBAAA,gBA8RoBhB,MAAA,CAAAyI,YAAY,CAACvI,MAAM,Q,cAD3B+C,YAAA,CAOYhC,oBAAA;MApSxB3B,GAAA;MA+RcD,KAAK,EAAC,oBAAoB;MAC1B8C,IAAI,EAAC,OAAO;MACXf,OAAK,EAAEpB,MAAA,CAAAqB;;MAjStBR,OAAA,EAAAjB,QAAA,CAkSa,MAEDW,MAAA,SAAAA,MAAA,QApSZO,gBAAA,CAkSa,QAED,E;MApSZC,CAAA;sCAAAC,mBAAA,e,GAsSUnB,mBAAA,CAcM,OAdN8I,WAcM,GAbJ9I,mBAAA,CAAoD,OAApD+I,WAAoD,EAAvB,IAAE,GAAAzC,gBAAA,CAAGnG,MAAA,CAAA6I,KAAK,IAAG,MAAI,iBAC9CpJ,YAAA,CAWEqJ,wBAAA;MAVAC,UAAU,EAAV,EAAU;MACVC,MAAM,EAAC,0BAA0B;MAChCH,KAAK,EAAE7I,MAAA,CAAA6I,KAAK;MACZ,WAAS,EAAE7I,MAAA,CAAAiJ,QAAQ;MACnB,cAAY,EAAEjJ,MAAA,CAAAkJ,WAAW;MACzB,YAAU,EAAE,iBAAiB;MAC7BC,YAAW,EAAEnJ,MAAA,CAAAoJ,gBAAgB;MAC7BC,eAAc,EAAErJ,MAAA,CAAAsJ,mBAAmB;MACpC,WAAS,EAAC,KAAK;MACf,WAAS,EAAC;;IAlTxBvI,CAAA;MAyTIC,mBAAA,eAAkB,EAClBvB,YAAA,CAgBY8J,oBAAA;IAfV7E,KAAK,EAAC,MAAM;IA3TlBrE,UAAA,EA4TeL,MAAA,CAAAwJ,wBAAwB;IA5TvC,uBAAAjJ,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4TeR,MAAA,CAAAwJ,wBAAwB,GAAAhJ,MAAA;IACjCsF,KAAK,EAAC,OAAO;IACbzG,KAAK,EAAC,mBAAmB;IACxB,sBAAoB,EAAE;;IAGZoK,MAAM,EAAA7J,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALN6J,WAKM,GAJJjK,YAAA,CAEYwB,oBAAA;MAFD5B,KAAK,EAAC,oBAAoB;MAAE+B,OAAK,EAAEpB,MAAA,CAAA2J,kBAAkB;MAAGzF,OAAO,EAAElE,MAAA,CAAA4J;;MApUtF/I,OAAA,EAAAjB,QAAA,CAoU6G,MAEnGW,MAAA,SAAAA,MAAA,QAtUVO,gBAAA,CAoU6G,QAEnG,E;MAtUVC,CAAA;+CAuUUtB,YAAA,CAAsFwB,oBAAA;MAA3E5B,KAAK,EAAC,YAAY;MAAE+B,OAAK,EAAAb,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAER,MAAA,CAAAwJ,wBAAwB;;MAvUxE3I,OAAA,EAAAjB,QAAA,CAuUkF,MAAEW,MAAA,SAAAA,MAAA,QAvUpFO,gBAAA,CAuUkF,IAAE,E;MAvUpFC,CAAA;;IAAAF,OAAA,EAAAjB,QAAA,CAiUM,MAA2D,CAA3DC,mBAAA,CAA2D,cAArD,UAAQ,GAAAsG,gBAAA,CAAGnG,MAAA,CAAAyI,YAAY,CAACvI,MAAM,IAAG,eAAa,gB;IAjU1Da,CAAA;qCA4UIC,mBAAA,iBAAoB,EACpBvB,YAAA,CAgBY8J,oBAAA;IAfV7E,KAAK,EAAC,MAAM;IA9UlBrE,UAAA,EA+UeL,MAAA,CAAA6J,mBAAmB;IA/UlC,uBAAAtJ,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA+UeR,MAAA,CAAA6J,mBAAmB,GAAArJ,MAAA;IAC5BsF,KAAK,EAAC,OAAO;IACbzG,KAAK,EAAC,mBAAmB;IACxB,sBAAoB,EAAE;;IAGZoK,MAAM,EAAA7J,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALNiK,WAKM,GAJJrK,YAAA,CAEYwB,oBAAA;MAFD5B,KAAK,EAAC,oBAAoB;MAAE+B,OAAK,EAAEpB,MAAA,CAAA+J;;MAvVxDlJ,OAAA,EAAAjB,QAAA,CAuVuE,MAE7DW,MAAA,SAAAA,MAAA,QAzVVO,gBAAA,CAuVuE,QAE7D,E;MAzVVC,CAAA;oCA0VUtB,YAAA,CAAiFwB,oBAAA;MAAtE5B,KAAK,EAAC,YAAY;MAAE+B,OAAK,EAAAb,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAER,MAAA,CAAA6J,mBAAmB;;MA1VnEhJ,OAAA,EAAAjB,QAAA,CA0V6E,MAAEW,MAAA,SAAAA,MAAA,QA1V/EO,gBAAA,CA0V6E,IAAE,E;MA1V/EC,CAAA;;IAAAF,OAAA,EAAAjB,QAAA,CAoVM,MAAkC,C,4BAAlCC,mBAAA,CAAkC,cAA5B,uBAAqB,qB;IApVjCkB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}