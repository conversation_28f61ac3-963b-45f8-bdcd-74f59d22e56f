// MongoDB数据库优化脚本
// 包含集合优化、数据清理和性能调优

// 切换到traffic_analysis数据库
use('traffic_analysis');

print('开始MongoDB数据库优化...');

// ==================== 集合优化配置 ====================

// 设置集合验证规则
print('设置集合验证规则...');

// 用户集合验证
db.runCommand({
  collMod: "users",
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["username", "password", "role", "createdAt"],
      properties: {
        username: {
          bsonType: "string",
          minLength: 3,
          maxLength: 50,
          description: "用户名必须是3-50个字符的字符串"
        },
        email: {
          bsonType: ["string", "null"],
          pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
          description: "邮箱格式必须有效"
        },
        role: {
          enum: ["admin", "user", "analyst"],
          description: "角色必须是admin、user或analyst"
        },
        status: {
          enum: ["active", "inactive", "suspended"],
          description: "状态必须是active、inactive或suspended"
        }
      }
    }
  },
  validationLevel: "moderate",
  validationAction: "warn"
});

// 四方向分析集合验证
db.runCommand({
  collMod: "intersection_analysis_four_way",
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["taskId", "userId", "status", "createdAt"],
      properties: {
        taskId: {
          bsonType: "string",
          minLength: 10,
          description: "任务ID必须是至少10个字符的字符串"
        },
        status: {
          enum: ["queued", "processing", "completed", "failed", "cancelled", "paused"],
          description: "状态必须是有效的任务状态"
        },
        progress: {
          bsonType: "int",
          minimum: 0,
          maximum: 100,
          description: "进度必须是0-100的整数"
        },
        analysisType: {
          enum: ["single_video", "intersection", "four_way"],
          description: "分析类型必须是有效值"
        }
      }
    }
  },
  validationLevel: "moderate",
  validationAction: "warn"
});

// ==================== 数据清理 ====================
print('执行数据清理...');

// 清理过期的临时数据（超过30天的失败任务）
const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

print('清理过期的失败任务...');
const failedTasksResult = db.analysis_videos.deleteMany({
  "status": "failed",
  "created_at": { $lt: thirtyDaysAgo }
});
print(`删除了 ${failedTasksResult.deletedCount} 个过期的失败视频分析任务`);

const failedFourWayResult = db.intersection_analysis_four_way.deleteMany({
  "status": "failed",
  "createdAt": { $lt: thirtyDaysAgo }
});
print(`删除了 ${failedFourWayResult.deletedCount} 个过期的失败四方向分析任务`);

// 清理孤立的分析结果（没有对应任务的结果）
print('清理孤立的分析结果...');
const allTaskIds = db.intersection_analysis_four_way.distinct("taskId");
const orphanedResults = db.traffic_analysis_results.deleteMany({
  "task_id": { $nin: allTaskIds }
});
print(`删除了 ${orphanedResults.deletedCount} 个孤立的分析结果`);

// 清理过期的报告数据（超过90天的草稿报告）
const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
const draftReports = db.report_data.deleteMany({
  "status": "draft",
  "generated_at": { $lt: ninetyDaysAgo }
});
print(`删除了 ${draftReports.deletedCount} 个过期的草稿报告`);

// ==================== 性能优化 ====================
print('执行性能优化...');

// 压缩集合以回收空间
print('压缩集合...');
try {
  db.runCommand({ compact: "users" });
  print('users集合压缩完成');
} catch (e) {
  print('users集合压缩失败: ' + e.message);
}

try {
  db.runCommand({ compact: "analysis_videos" });
  print('analysis_videos集合压缩完成');
} catch (e) {
  print('analysis_videos集合压缩失败: ' + e.message);
}

try {
  db.runCommand({ compact: "intersection_analysis_four_way" });
  print('intersection_analysis_four_way集合压缩完成');
} catch (e) {
  print('intersection_analysis_four_way集合压缩失败: ' + e.message);
}

// 重建索引以优化性能
print('重建关键索引...');
try {
  db.intersection_analysis_four_way.reIndex();
  print('intersection_analysis_four_way索引重建完成');
} catch (e) {
  print('索引重建失败: ' + e.message);
}

// ==================== 统计信息更新 ====================
print('更新统计信息...');

// 获取集合统计信息
const collections = ['users', 'analysis_videos', 'intersection_analysis_four_way', 
                    'traffic_analysis_results', 'signal_optimizations', 'report_data'];

collections.forEach(collName => {
  try {
    const stats = db.getCollection(collName).stats();
    print(`\n${collName}集合统计:`);
    print(`  - 文档数量: ${stats.count}`);
    print(`  - 平均文档大小: ${Math.round(stats.avgObjSize)} bytes`);
    print(`  - 存储大小: ${Math.round(stats.storageSize / 1024 / 1024 * 100) / 100} MB`);
    print(`  - 索引数量: ${stats.nindexes}`);
    print(`  - 索引大小: ${Math.round(stats.totalIndexSize / 1024 / 1024 * 100) / 100} MB`);
  } catch (e) {
    print(`获取${collName}统计信息失败: ${e.message}`);
  }
});

// ==================== 数据完整性检查 ====================
print('\n执行数据完整性检查...');

// 检查用户数据完整性
const usersWithoutUsername = db.users.countDocuments({ username: { $exists: false } });
print(`缺少用户名的用户记录: ${usersWithoutUsername}`);

const usersWithoutRole = db.users.countDocuments({ role: { $exists: false } });
print(`缺少角色的用户记录: ${usersWithoutRole}`);

// 检查任务数据完整性
const tasksWithoutUserId = db.intersection_analysis_four_way.countDocuments({ userId: { $exists: false } });
print(`缺少用户ID的任务记录: ${tasksWithoutUserId}`);

const tasksWithoutStatus = db.intersection_analysis_four_way.countDocuments({ status: { $exists: false } });
print(`缺少状态的任务记录: ${tasksWithoutStatus}`);

// 检查分析结果完整性
const resultsWithoutTaskId = db.traffic_analysis_results.countDocuments({ task_id: { $exists: false } });
print(`缺少任务ID的分析结果: ${resultsWithoutTaskId}`);

// ==================== 性能建议 ====================
print('\n=== 性能优化建议 ===');

// 检查大集合
collections.forEach(collName => {
  const count = db.getCollection(collName).countDocuments();
  if (count > 100000) {
    print(`⚠️  ${collName}集合文档数量较大(${count})，建议考虑分片或归档策略`);
  }
});

// 检查缺失索引的查询
print('\n建议添加的索引:');
print('- 如果经常按时间范围查询，考虑添加时间范围复合索引');
print('- 如果有全文搜索需求，考虑添加文本索引');
print('- 对于经常用于排序的字段，确保有对应的索引');

// ==================== 备份建议 ====================
print('\n=== 备份建议 ===');
print('建议定期执行以下备份操作:');
print('1. 每日增量备份: mongodump --db traffic_analysis --out /backup/daily/$(date +%Y%m%d)');
print('2. 每周全量备份: mongodump --db traffic_analysis --out /backup/weekly/$(date +%Y%m%d)');
print('3. 重要数据实时同步到副本集');

print('\nMongoDB数据库优化完成！');

// ==================== 创建维护任务 ====================
print('\n创建定期维护任务配置...');

// 创建维护任务配置集合
db.createCollection("maintenance_tasks");

// 插入维护任务配置
db.maintenance_tasks.insertMany([
  {
    name: "daily_cleanup",
    description: "每日数据清理任务",
    schedule: "0 2 * * *", // 每天凌晨2点
    enabled: true,
    lastRun: null,
    actions: [
      "清理过期的临时文件",
      "删除超过7天的失败任务",
      "压缩日志文件"
    ]
  },
  {
    name: "weekly_optimization",
    description: "每周性能优化任务",
    schedule: "0 3 * * 0", // 每周日凌晨3点
    enabled: true,
    lastRun: null,
    actions: [
      "重建索引",
      "压缩集合",
      "更新统计信息",
      "检查数据完整性"
    ]
  },
  {
    name: "monthly_archive",
    description: "每月数据归档任务",
    schedule: "0 4 1 * *", // 每月1号凌晨4点
    enabled: true,
    lastRun: null,
    actions: [
      "归档超过6个月的历史数据",
      "清理过期的分析结果",
      "生成月度统计报告"
    ]
  }
]);

print('维护任务配置创建完成');
print('\n数据库优化和维护配置全部完成！');
