/* 交通分析系统专用样式 */

/* 智能交通面板主题色彩 */
.intelligent-traffic-panel[data-grade="A"] {
  --theme-color: #52c41a;
  --theme-bg: #f6ffed;
  --theme-border: #b7eb8f;
}

.intelligent-traffic-panel[data-grade="B"] {
  --theme-color: #1890ff;
  --theme-bg: #e6f7ff;
  --theme-border: #91d5ff;
}

.intelligent-traffic-panel[data-grade="C"] {
  --theme-color: #faad14;
  --theme-bg: #fffbe6;
  --theme-border: #ffe58f;
}

.intelligent-traffic-panel[data-grade="D"] {
  --theme-color: #fa8c16;
  --theme-bg: #fff2e8;
  --theme-border: #ffbb96;
}

.intelligent-traffic-panel[data-grade="E"] {
  --theme-color: #f5222d;
  --theme-bg: #fff1f0;
  --theme-border: #ffa39e;
}

.intelligent-traffic-panel[data-grade="F"] {
  --theme-color: #a8071a;
  --theme-bg: #ffebee;
  --theme-border: #ff7875;
}

/* 拥挤等级指示器动画 */
.congestion-grade-indicator .grade-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 var(--theme-color, #1890ff);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

/* 趋势指示器样式 */
.trend-rising {
  background: linear-gradient(45deg, #f6ffed, #d9f7be);
  border-left: 4px solid #52c41a;
}

.trend-falling {
  background: linear-gradient(45deg, #fff2e8, #ffd8bf);
  border-left: 4px solid #fa8c16;
}

.trend-stable {
  background: linear-gradient(45deg, #e6f7ff, #bae7ff);
  border-left: 4px solid #1890ff;
}

/* 策略推荐优先级样式 */
.strategy-recommendation[data-priority="critical"] {
  border-left: 5px solid #f5222d;
  background: linear-gradient(135deg, #fff1f0, #ffffff);
}

.strategy-recommendation[data-priority="high"] {
  border-left: 5px solid #fa8c16;
  background: linear-gradient(135deg, #fff2e8, #ffffff);
}

.strategy-recommendation[data-priority="medium"] {
  border-left: 5px solid #1890ff;
  background: linear-gradient(135deg, #e6f7ff, #ffffff);
}

.strategy-recommendation[data-priority="low"] {
  border-left: 5px solid #52c41a;
  background: linear-gradient(135deg, #f6ffed, #ffffff);
}

/* 车辆数量显示动画 */
.vehicle-count .count-number {
  transition: all 0.3s ease;
}

.vehicle-count .count-number.updated {
  animation: countUpdate 0.6s ease;
}

@keyframes countUpdate {
  0% {
    transform: scale(1);
    color: inherit;
  }
  50% {
    transform: scale(1.2);
    color: #1890ff;
  }
  100% {
    transform: scale(1);
    color: inherit;
  }
}

/* 移动平均值显示 */
.moving-averages .average-item {
  transition: all 0.3s ease;
}

.moving-averages .average-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 图表容器样式 */
.chart-container {
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 49%, rgba(255, 255, 255, 0.1) 50%, transparent 51%);
  pointer-events: none;
  z-index: 1;
}

/* 响应式网格布局 */
.traffic-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

@media (max-width: 768px) {
  .traffic-analysis-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }
}

/* 加载状态样式 */
.traffic-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.traffic-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 数据更新闪烁效果 */
.data-updated {
  animation: dataFlash 0.5s ease;
}

@keyframes dataFlash {
  0% { background-color: transparent; }
  50% { background-color: rgba(24, 144, 255, 0.1); }
  100% { background-color: transparent; }
}

/* 警告和提示样式 */
.traffic-warning {
  background: #fff2e8;
  border: 1px solid #ffbb96;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.traffic-warning .warning-icon {
  color: #fa8c16;
  font-size: 16px;
}

.traffic-success {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.traffic-success .success-icon {
  color: #52c41a;
  font-size: 16px;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .intelligent-traffic-panel {
    background: #1f1f1f;
    color: #ffffff;
  }
  
  .congestion-grade-indicator,
  .traffic-trend-chart,
  .strategy-recommendation {
    background: #2d2d2d;
    border-color: #404040;
  }
  
  .chart-container {
    background: #2d2d2d;
  }
  
  .moving-averages {
    background: #262626;
  }
}

/* 打印样式 */
@media print {
  .intelligent-traffic-panel {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
  
  .panel-header {
    background: #f5f5f5 !important;
    color: #333 !important;
  }
  
  .chart-container {
    height: 300px !important;
  }
}
