<template>
  <div class="intelligent-traffic-panel">
    <div class="panel-header">
      <h3>智能交通状态面板</h3>
      <div class="update-info">
        <el-icon><Refresh /></el-icon>
        <span>{{ lastUpdateTime }}</span>
      </div>
    </div>
    
    <div class="panel-content">
      <!-- 拥挤等级指示器 -->
      <div class="section">
        <CongestionGradeIndicator 
          :grade="currentGrade"
          :vehicle-count="currentVehicleCount"
        />
      </div>
      
      <!-- 交通趋势图表 -->
      <div class="section">
        <TrafficTrendChart 
          :vehicle-data="vehicleHistory"
          :moving-averages="movingAverages"
          :trend-info="trendInfo"
        />
      </div>
      
      <!-- 策略推荐 -->
      <div class="section">
        <StrategyRecommendation 
          :strategy="currentStrategy"
          :grade="currentGrade"
          @apply-strategy="handleApplyStrategy"
          @view-details="handleViewDetails"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import CongestionGradeIndicator from './CongestionGradeIndicator.vue'
import TrafficTrendChart from './TrafficTrendChart.vue'
import StrategyRecommendation from './StrategyRecommendation.vue'
import {
  calculateCongestionGrade,
  calculateMultipleMovingAverages,
  analyzeTrend,
  generateTrafficStrategy
} from '@/utils/trafficAnalysisUtils'

export default {
  name: 'IntelligentTrafficPanel',
  components: {
    Refresh,
    CongestionGradeIndicator,
    TrafficTrendChart,
    StrategyRecommendation
  },
  props: {
    // 当前帧的车辆检测数量
    currentVehicleCount: {
      type: Number,
      default: 0
    },
    // 是否自动更新
    autoUpdate: {
      type: Boolean,
      default: true
    },
    // 历史数据最大长度
    maxHistoryLength: {
      type: Number,
      default: 50
    }
  },
  emits: ['strategy-applied', 'data-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const vehicleHistory = ref([])
    const lastUpdateTime = ref('')
    
    // 计算属性
    const currentGrade = computed(() => 
      calculateCongestionGrade(props.currentVehicleCount)
    )
    
    const movingAverages = computed(() => 
      calculateMultipleMovingAverages(vehicleHistory.value)
    )
    
    const trendInfo = computed(() => 
      analyzeTrend(vehicleHistory.value)
    )
    
    const currentStrategy = computed(() => 
      generateTrafficStrategy(currentGrade.value, trendInfo.value, movingAverages.value)
    )
    
    // 方法
    const updateVehicleHistory = (newCount) => {
      vehicleHistory.value.push(newCount)
      
      // 限制历史数据长度
      if (vehicleHistory.value.length > props.maxHistoryLength) {
        vehicleHistory.value.shift()
      }
      
      // 更新时间戳
      lastUpdateTime.value = new Date().toLocaleTimeString()
      
      // 发出数据更新事件
      emit('data-updated', {
        currentCount: newCount,
        grade: currentGrade.value,
        movingAverages: movingAverages.value,
        trend: trendInfo.value,
        strategy: currentStrategy.value
      })
    }
    
    const handleApplyStrategy = (strategy) => {
      emit('strategy-applied', {
        strategy,
        grade: currentGrade.value,
        vehicleCount: props.currentVehicleCount,
        timestamp: new Date().toISOString()
      })
    }
    
    const handleViewDetails = (details) => {
      console.log('查看策略详情:', details)
    }
    
    // 监听车辆数量变化
    watch(() => props.currentVehicleCount, (newCount) => {
      if (props.autoUpdate) {
        updateVehicleHistory(newCount)
      }
    })
    
    // 手动更新数据的方法
    const manualUpdate = () => {
      updateVehicleHistory(props.currentVehicleCount)
    }
    
    // 清空历史数据
    const clearHistory = () => {
      vehicleHistory.value = []
      lastUpdateTime.value = ''
    }
    
    // 获取当前状态摘要
    const getStatusSummary = () => {
      return {
        currentVehicleCount: props.currentVehicleCount,
        grade: currentGrade.value,
        movingAverages: movingAverages.value,
        trend: trendInfo.value,
        strategy: currentStrategy.value,
        historyLength: vehicleHistory.value.length,
        lastUpdate: lastUpdateTime.value
      }
    }
    
    // 组件挂载时初始化
    onMounted(() => {
      if (props.currentVehicleCount > 0) {
        updateVehicleHistory(props.currentVehicleCount)
      }
    })
    
    // 暴露方法给父组件
    const updateData = manualUpdate
    const clearData = clearHistory
    const getStatus = getStatusSummary
    
    return {
      // 响应式数据
      vehicleHistory,
      lastUpdateTime,
      
      // 计算属性
      currentGrade,
      movingAverages,
      trendInfo,
      currentStrategy,
      
      // 方法
      handleApplyStrategy,
      handleViewDetails,
      
      // 暴露给父组件的方法
      updateData,
      clearData,
      getStatus
    }
  }
}
</script>

<style scoped>
.intelligent-traffic-panel {
  background: #f8f9fa;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.panel-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.update-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.9;
}

.panel-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .panel-content {
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .panel-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    padding: 16px 20px;
  }
  
  .panel-header h3 {
    font-size: 18px;
  }
  
  .panel-content {
    padding: 20px;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .panel-header {
    padding: 12px 16px;
  }
  
  .panel-content {
    padding: 16px;
    gap: 12px;
  }
}

/* 动画效果 */
.section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为不同等级添加主题色彩 */
.intelligent-traffic-panel[data-grade="A"] .panel-header {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.intelligent-traffic-panel[data-grade="B"] .panel-header {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.intelligent-traffic-panel[data-grade="C"] .panel-header {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
}

.intelligent-traffic-panel[data-grade="D"] .panel-header {
  background: linear-gradient(135deg, #fa8c16 0%, #ff9c6e 100%);
}

.intelligent-traffic-panel[data-grade="E"] .panel-header {
  background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);
}

.intelligent-traffic-panel[data-grade="F"] .panel-header {
  background: linear-gradient(135deg, #a8071a 0%, #cf1322 100%);
}
</style>
