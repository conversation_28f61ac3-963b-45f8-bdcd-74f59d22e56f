package com.traffic.analysis.repository;

import com.traffic.analysis.model.SimulationTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 仿真任务数据访问接口
 */
@Repository
public interface SimulationTaskRepository extends MongoRepository<SimulationTask, String> {
    
    /**
     * 根据仿真ID查找任务
     * @param simulationId 仿真ID
     * @return 仿真任务
     */
    Optional<SimulationTask> findBySimulationId(String simulationId);
    
    /**
     * 根据用户ID查询仿真任务列表（按创建时间降序）
     * @param userId 用户ID
     * @return 仿真任务列表
     */
    List<SimulationTask> findByUserIdOrderByCreateTimeDesc(String userId);
    
    /**
     * 根据状态查询仿真任务列表
     * @param status 状态
     * @return 仿真任务列表
     */
    List<SimulationTask> findByStatus(String status);
    
    /**
     * 根据用户ID和状态查询仿真任务列表
     * @param userId 用户ID
     * @param status 状态
     * @return 仿真任务列表
     */
    List<SimulationTask> findByUserIdAndStatus(String userId, String status);
    
    /**
     * 根据仿真类型查询任务列表
     * @param simulationType 仿真类型
     * @return 仿真任务列表
     */
    List<SimulationTask> findBySimulationType(String simulationType);
    
    /**
     * 根据用户ID和仿真类型查询任务列表
     * @param userId 用户ID
     * @param simulationType 仿真类型
     * @return 仿真任务列表
     */
    List<SimulationTask> findByUserIdAndSimulationType(String userId, String simulationType);
    
    /**
     * 查询正在运行的仿真任务
     * @return 正在运行的任务列表
     */
    @Query("{'status': 'running'}")
    List<SimulationTask> findRunningTasks();
    
    /**
     * 查询用户正在运行的仿真任务
     * @param userId 用户ID
     * @return 正在运行的任务列表
     */
    @Query("{'userId': ?0, 'status': 'running'}")
    List<SimulationTask> findRunningTasksByUserId(String userId);
    
    /**
     * 查询指定时间范围内的仿真任务
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 仿真任务列表
     */
    List<SimulationTask> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查询用户在指定时间范围内的仿真任务
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 仿真任务列表
     */
    List<SimulationTask> findByUserIdAndCreateTimeBetween(String userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据关联的分析任务ID查询仿真任务
     * @param analysisTaskId 分析任务ID
     * @return 仿真任务列表
     */
    List<SimulationTask> findByAnalysisTaskId(String analysisTaskId);
    
    /**
     * 查询最近的仿真任务（限制数量）
     * @param limit 限制数量
     * @return 仿真任务列表
     */
    @Query(value = "{}", sort = "{'createTime': -1}")
    List<SimulationTask> findRecentTasks(int limit);
    
    /**
     * 查询用户最近的仿真任务（限制数量）
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 仿真任务列表
     */
    @Query(value = "{'userId': ?0}", sort = "{'createTime': -1}")
    List<SimulationTask> findRecentTasksByUserId(String userId, int limit);
    
    /**
     * 统计用户的仿真任务数量
     * @param userId 用户ID
     * @return 任务数量
     */
    long countByUserId(String userId);
    
    /**
     * 统计指定状态的任务数量
     * @param status 状态
     * @return 任务数量
     */
    long countByStatus(String status);
    
    /**
     * 统计用户指定状态的任务数量
     * @param userId 用户ID
     * @param status 状态
     * @return 任务数量
     */
    long countByUserIdAndStatus(String userId, String status);
    
    /**
     * 查询长时间运行的任务（超过指定时间仍在运行）
     * @param cutoffTime 截止时间
     * @return 长时间运行的任务列表
     */
    @Query("{'status': 'running', 'startTime': {'$lt': ?0}}")
    List<SimulationTask> findLongRunningTasks(LocalDateTime cutoffTime);
    
    /**
     * 查询失败的任务（用于错误分析）
     * @return 失败的任务列表
     */
    @Query("{'status': 'failed'}")
    List<SimulationTask> findFailedTasks();
    
    /**
     * 查询用户失败的任务
     * @param userId 用户ID
     * @return 失败的任务列表
     */
    @Query("{'userId': ?0, 'status': 'failed'}")
    List<SimulationTask> findFailedTasksByUserId(String userId);
    
    /**
     * 删除指定时间之前的已完成任务（清理历史数据）
     * @param cutoffTime 截止时间
     * @return 删除的任务数量
     */
    @Query(value = "{'status': {'$in': ['completed', 'failed', 'stopped']}, 'createTime': {'$lt': ?0}}", delete = true)
    long deleteOldCompletedTasks(LocalDateTime cutoffTime);
    
    /**
     * 查询高性能改善的任务（改善超过指定阈值）
     * @param improvementThreshold 改善阈值
     * @return 高性能改善的任务列表
     */
    @Query("{'performanceMetrics.improvementPercentage': {'$gte': ?0}}")
    List<SimulationTask> findHighPerformanceTasks(Double improvementThreshold);
    
    /**
     * 根据仿真类型统计任务数量
     * @return 统计结果
     */
    @Query(value = "{}", fields = "{'simulationType': 1}")
    List<SimulationTask> findAllSimulationTypes();
}
