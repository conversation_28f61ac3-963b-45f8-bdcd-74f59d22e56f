/**
 * 交通分析工具函数
 * 用于计算车流量、拥挤等级、趋势分析等
 */

/**
 * 拥挤等级配置
 */
export const CONGESTION_GRADES = {
  A: { min: 0, max: 3, label: '优秀', description: '畅通无阻', color: '#52c41a', bgColor: '#f6ffed' },
  B: { min: 4, max: 7, label: '良好', description: '车流正常', color: '#1890ff', bgColor: '#e6f7ff' },
  C: { min: 8, max: 12, label: '一般', description: '略有拥挤', color: '#faad14', bgColor: '#fffbe6' },
  D: { min: 13, max: 18, label: '较差', description: '明显拥挤', color: '#fa8c16', bgColor: '#fff2e8' },
  E: { min: 19, max: 25, label: '很差', description: '严重拥挤', color: '#f5222d', bgColor: '#fff1f0' },
  F: { min: 26, max: Infinity, label: '极差', description: '极度拥挤', color: '#a8071a', bgColor: '#ffebee' }
}

/**
 * 计算拥挤等级
 * @param {number} vehicleCount 车辆数量
 * @returns {string} 拥挤等级 (A-F)
 */
export function calculateCongestionGrade(vehicleCount) {
  for (const [grade, config] of Object.entries(CONGESTION_GRADES)) {
    if (vehicleCount >= config.min && vehicleCount <= config.max) {
      return grade
    }
  }
  return 'F' // 默认返回最高拥挤等级
}

/**
 * 获取拥挤等级配置
 * @param {string} grade 拥挤等级
 * @returns {object} 等级配置信息
 */
export function getCongestionGradeConfig(grade) {
  return CONGESTION_GRADES[grade] || CONGESTION_GRADES.F
}

/**
 * 计算移动平均值
 * @param {Array} dataArray 数据数组
 * @param {number} windowSize 窗口大小
 * @returns {number} 移动平均值
 */
export function calculateMovingAverage(dataArray, windowSize) {
  if (!dataArray || dataArray.length === 0) return 0
  
  const window = dataArray.slice(-windowSize)
  const sum = window.reduce((acc, val) => acc + (val || 0), 0)
  return Math.round((sum / window.length) * 10) / 10 // 保留一位小数
}

/**
 * 计算多个窗口的移动平均值
 * @param {Array} dataArray 车辆数量数据数组
 * @returns {object} 包含不同窗口大小的移动平均值
 */
export function calculateMultipleMovingAverages(dataArray) {
  return {
    frame5: calculateMovingAverage(dataArray, 5),
    frame10: calculateMovingAverage(dataArray, 10),
    frame30: calculateMovingAverage(dataArray, 30)
  }
}

/**
 * 分析车流趋势
 * @param {Array} recentData 最近的车辆数量数据
 * @param {number} windowSize 分析窗口大小，默认为5
 * @returns {object} 趋势分析结果
 */
export function analyzeTrend(recentData, windowSize = 5) {
  if (!recentData || recentData.length < 2) {
    return { trend: 'stable', strength: 0, description: '数据不足' }
  }

  const window = recentData.slice(-windowSize)
  if (window.length < 2) {
    return { trend: 'stable', strength: 0, description: '数据不足' }
  }

  // 计算趋势斜率
  const n = window.length
  const sumX = (n * (n - 1)) / 2
  const sumY = window.reduce((sum, val) => sum + val, 0)
  const sumXY = window.reduce((sum, val, index) => sum + val * index, 0)
  const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6

  const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
  
  // 判断趋势方向和强度
  let trend, strength, description
  const absSlope = Math.abs(slope)
  
  if (absSlope < 0.1) {
    trend = 'stable'
    strength = 0
    description = '车流稳定'
  } else if (slope > 0) {
    trend = 'rising'
    strength = Math.min(absSlope * 10, 5) // 强度1-5
    description = absSlope > 0.5 ? '车流快速增加' : '车流缓慢增加'
  } else {
    trend = 'falling'
    strength = Math.min(absSlope * 10, 5)
    description = absSlope > 0.5 ? '车流快速减少' : '车流缓慢减少'
  }

  return { trend, strength, description }
}

/**
 * 生成交通管理策略建议
 * @param {string} grade 拥挤等级
 * @param {object} trendInfo 趋势信息
 * @param {object} movingAverages 移动平均值
 * @returns {object} 策略建议
 */
export function generateTrafficStrategy(grade, trendInfo, movingAverages) {
  const strategies = {
    A: {
      primary: '保持现状',
      secondary: '正常通行，无需特殊措施',
      icon: '✅',
      priority: 'low'
    },
    B: {
      primary: '正常管理',
      secondary: '继续监控，准备应对可能的车流增加',
      icon: '👍',
      priority: 'low'
    },
    C: {
      primary: '加强监控',
      secondary: '建议优化信号灯配时，引导车流分散',
      icon: '⚠️',
      priority: 'medium'
    },
    D: {
      primary: '主动干预',
      secondary: '延长绿灯时间，考虑开放应急车道',
      icon: '🚨',
      priority: 'medium'
    },
    E: {
      primary: '紧急措施',
      secondary: '启动交通疏导，派遣交警现场指挥',
      icon: '🚨',
      priority: 'high'
    },
    F: {
      primary: '全面管制',
      secondary: '实施交通管制，寻找替代路线',
      icon: '🔴',
      priority: 'critical'
    }
  }

  const baseStrategy = strategies[grade] || strategies.F
  
  // 根据趋势调整策略
  let adjustedStrategy = { ...baseStrategy }
  
  if (trendInfo.trend === 'rising' && trendInfo.strength > 2) {
    adjustedStrategy.secondary += '，车流上升趋势明显，需提前准备'
    adjustedStrategy.priority = adjustedStrategy.priority === 'low' ? 'medium' : 'high'
  } else if (trendInfo.trend === 'falling' && trendInfo.strength > 2) {
    adjustedStrategy.secondary += '，车流下降趋势明显，可适当放松管制'
  }

  return adjustedStrategy
}

/**
 * 格式化车辆数量显示
 * @param {number} count 车辆数量
 * @returns {string} 格式化后的显示文本
 */
export function formatVehicleCount(count) {
  if (count === 0) return '无车辆'
  if (count === 1) return '1辆车'
  return `${count}辆车`
}

/**
 * 计算拥挤度百分比
 * @param {string} grade 拥挤等级
 * @param {number} vehicleCount 当前车辆数
 * @returns {number} 拥挤度百分比 (0-100)
 */
export function calculateCongestionPercentage(grade, vehicleCount) {
  const config = getCongestionGradeConfig(grade)
  const maxReasonable = 30 // 设定一个合理的最大值用于百分比计算
  
  return Math.min((vehicleCount / maxReasonable) * 100, 100)
}
