#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SUMO仿真服务核心模块

提供SUMO仿真的启动、控制、监控和数据管理功能
"""

import os
import sys
import time
import logging
import subprocess
import tempfile
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import json

# SUMO相关导入
try:
    import traci
    import sumolib
    SUMO_AVAILABLE = True
except ImportError as e:
    SUMO_AVAILABLE = False
    logging.warning(f"SUMO模块导入失败: {e}")

from .traci_controller import TraciController
from .data_converter import DataConverter
from .scenario_generator import ScenarioGenerator
from .optimization_engine import OptimizationEngine

class SumoService:
    """SUMO仿真服务主类"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化SUMO服务
        
        Args:
            config: 配置字典，包含SUMO路径、端口等信息
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        
        # 检查SUMO可用性
        if not SUMO_AVAILABLE:
            raise ImportError("SUMO模块不可用，请安装traci和sumolib")
        
        # 初始化组件
        self.traci_controller = TraciController()
        self.data_converter = DataConverter()
        self.scenario_generator = ScenarioGenerator()
        self.optimization_engine = OptimizationEngine()
        
        # 仿真状态
        self.simulation_id = None
        self.is_running = False
        self.simulation_port = None
        self.simulation_process = None
        
        # 配置SUMO路径
        self._setup_sumo_environment()
        
        self.logger.info("SUMO服务初始化完成")
    
    def _setup_sumo_environment(self):
        """设置SUMO环境变量和路径"""
        try:
            # 尝试从环境变量获取SUMO_HOME
            sumo_home = os.environ.get('SUMO_HOME')
            if not sumo_home:
                # 尝试常见的SUMO安装路径
                possible_paths = [
                    'C:/Program Files (x86)/Eclipse/Sumo',
                    'C:/Program Files/Eclipse/Sumo',
                    '/usr/share/sumo',
                    '/opt/sumo'
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        sumo_home = path
                        os.environ['SUMO_HOME'] = path
                        break
            
            if sumo_home:
                # 添加SUMO tools到Python路径
                tools_path = os.path.join(sumo_home, 'tools')
                if tools_path not in sys.path:
                    sys.path.append(tools_path)
                
                self.sumo_binary = os.path.join(sumo_home, 'bin', 'sumo')
                self.sumo_gui_binary = os.path.join(sumo_home, 'bin', 'sumo-gui')
                
                # Windows系统添加.exe扩展名
                if sys.platform.startswith('win'):
                    self.sumo_binary += '.exe'
                    self.sumo_gui_binary += '.exe'
                
                self.logger.info(f"SUMO环境设置完成: {sumo_home}")
            else:
                # 假设SUMO在系统PATH中
                self.sumo_binary = 'sumo'
                self.sumo_gui_binary = 'sumo-gui'
                self.logger.warning("未找到SUMO_HOME，假设SUMO在系统PATH中")
                
        except Exception as e:
            self.logger.error(f"设置SUMO环境失败: {e}")
            raise
    
    def create_simulation(self, traffic_data: Dict, simulation_config: Optional[Dict] = None) -> str:
        """
        创建新的仿真任务
        
        Args:
            traffic_data: 从视频分析获得的交通数据
            simulation_config: 仿真配置参数
            
        Returns:
            str: 仿真任务ID
        """
        try:
            # 生成唯一的仿真ID
            simulation_id = str(uuid.uuid4())
            self.simulation_id = simulation_id
            
            self.logger.info(f"创建仿真任务: {simulation_id}")
            
            # 转换交通数据为SUMO格式
            sumo_data = self.data_converter.convert_traffic_data_to_sumo(traffic_data)
            
            # 生成仿真场景文件
            scenario_files = self.scenario_generator.generate_scenario(
                simulation_id, sumo_data, simulation_config
            )
            
            # 保存仿真配置
            self._save_simulation_config(simulation_id, {
                'traffic_data': traffic_data,
                'simulation_config': simulation_config,
                'scenario_files': scenario_files,
                'created_at': datetime.now().isoformat()
            })
            
            self.logger.info(f"仿真任务创建成功: {simulation_id}")
            return simulation_id
            
        except Exception as e:
            self.logger.error(f"创建仿真任务失败: {e}")
            raise
    
    def start_simulation(self, simulation_id: str, use_gui: bool = False) -> bool:
        """
        启动仿真
        
        Args:
            simulation_id: 仿真任务ID
            use_gui: 是否使用GUI界面
            
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_running:
                self.logger.warning("仿真已在运行中")
                return False
            
            self.logger.info(f"启动仿真: {simulation_id}")
            
            # 获取仿真配置
            config = self._load_simulation_config(simulation_id)
            if not config:
                raise ValueError(f"未找到仿真配置: {simulation_id}")
            
            # 选择SUMO二进制文件
            sumo_binary = self.sumo_gui_binary if use_gui else self.sumo_binary
            
            # 构建SUMO命令
            scenario_files = config['scenario_files']
            sumo_cmd = [
                sumo_binary,
                '-c', scenario_files['config_file'],
                '--remote-port', str(self._get_free_port()),
                '--start',
                '--quit-on-end'
            ]
            
            # 启动SUMO进程
            self.simulation_process = subprocess.Popen(
                sumo_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待SUMO启动
            time.sleep(2)
            
            # 连接TraCI
            success = self.traci_controller.connect(self.simulation_port)
            if success:
                self.is_running = True
                self.simulation_id = simulation_id
                self.logger.info(f"仿真启动成功: {simulation_id}")
                return True
            else:
                self.logger.error("TraCI连接失败")
                return False
                
        except Exception as e:
            self.logger.error(f"启动仿真失败: {e}")
            return False
    
    def stop_simulation(self) -> bool:
        """
        停止当前仿真
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.is_running:
                self.logger.warning("没有正在运行的仿真")
                return True
            
            self.logger.info(f"停止仿真: {self.simulation_id}")
            
            # 断开TraCI连接
            self.traci_controller.disconnect()
            
            # 终止SUMO进程
            if self.simulation_process:
                self.simulation_process.terminate()
                self.simulation_process.wait(timeout=10)
                self.simulation_process = None
            
            # 重置状态
            self.is_running = False
            self.simulation_id = None
            self.simulation_port = None
            
            self.logger.info("仿真停止成功")
            return True
            
        except Exception as e:
            self.logger.error(f"停止仿真失败: {e}")
            return False
    
    def get_simulation_status(self) -> Dict:
        """
        获取仿真状态信息
        
        Returns:
            Dict: 仿真状态信息
        """
        return {
            'simulation_id': self.simulation_id,
            'is_running': self.is_running,
            'simulation_port': self.simulation_port,
            'current_time': self.traci_controller.get_current_time() if self.is_running else None,
            'vehicle_count': self.traci_controller.get_vehicle_count() if self.is_running else 0
        }
    
    def _get_free_port(self) -> int:
        """获取可用端口"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            port = s.getsockname()[1]
            self.simulation_port = port
            return port
    
    def _save_simulation_config(self, simulation_id: str, config: Dict):
        """保存仿真配置到临时文件"""
        config_dir = Path(tempfile.gettempdir()) / 'sumo_simulations'
        config_dir.mkdir(exist_ok=True)
        
        config_file = config_dir / f"{simulation_id}_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def _load_simulation_config(self, simulation_id: str) -> Optional[Dict]:
        """从临时文件加载仿真配置"""
        config_dir = Path(tempfile.gettempdir()) / 'sumo_simulations'
        config_file = config_dir / f"{simulation_id}_config.json"
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
