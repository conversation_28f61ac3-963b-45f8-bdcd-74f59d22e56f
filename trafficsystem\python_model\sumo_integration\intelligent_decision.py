#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能决策算法模块

实现基于机器学习和优化算法的智能交通决策
"""

import logging
import numpy as np
import math
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass

@dataclass
class TrafficState:
    """交通状态数据类"""
    direction: str
    vehicle_count: int
    flow_rate: float
    average_speed: float
    density: float
    waiting_time: float
    queue_length: float

@dataclass
class SignalPhase:
    """信号相位数据类"""
    phase_id: str
    duration: int
    state: str
    description: str
    directions: List[str]

@dataclass
class OptimizationResult:
    """优化结果数据类"""
    improvement_score: float
    delay_reduction: float
    throughput_increase: float
    fuel_saving: float
    recommendations: List[Dict]
    optimized_timing: Dict

class IntelligentDecisionEngine:
    """智能决策引擎"""
    
    def __init__(self):
        """初始化智能决策引擎"""
        self.logger = logging.getLogger(__name__)
        
        # 算法参数
        self.congestion_thresholds = {
            'low': 0.3,      # 低拥堵阈值
            'medium': 0.6,   # 中等拥堵阈值
            'high': 0.8      # 高拥堵阈值
        }
        
        # 权重参数
        self.weights = {
            'delay': 0.4,
            'throughput': 0.3,
            'fuel': 0.2,
            'safety': 0.1
        }
        
        # 历史数据缓存
        self.historical_data = []
        self.max_history_size = 1000
    
    def analyze_traffic_state(self, traffic_data: Dict) -> Dict[str, TrafficState]:
        """
        分析交通状态
        
        Args:
            traffic_data: 交通数据
            
        Returns:
            Dict: 各方向的交通状态
        """
        try:
            self.logger.info("分析交通状态")
            
            traffic_states = {}
            
            if 'directions' in traffic_data:
                for direction, data in traffic_data['directions'].items():
                    state = TrafficState(
                        direction=direction,
                        vehicle_count=data.get('vehicleCount', 0),
                        flow_rate=data.get('flow_rate', 0),
                        average_speed=data.get('averageSpeed', 0),
                        density=data.get('density', 0),
                        waiting_time=data.get('waiting_time', 0),
                        queue_length=data.get('queue_length', 0)
                    )
                    traffic_states[direction] = state
            
            return traffic_states
            
        except Exception as e:
            self.logger.error(f"分析交通状态失败: {e}")
            return {}
    
    def calculate_congestion_level(self, traffic_states: Dict[str, TrafficState]) -> Dict:
        """
        计算拥堵等级
        
        Args:
            traffic_states: 交通状态数据
            
        Returns:
            Dict: 拥堵等级分析结果
        """
        try:
            congestion_analysis = {
                'overall_level': 'low',
                'direction_levels': {},
                'congestion_score': 0.0,
                'bottleneck_directions': []
            }
            
            total_congestion = 0.0
            direction_count = len(traffic_states)
            
            for direction, state in traffic_states.items():
                # 计算拥堵指数
                congestion_index = self._calculate_direction_congestion(state)
                
                # 确定拥堵等级
                if congestion_index < self.congestion_thresholds['low']:
                    level = 'low'
                elif congestion_index < self.congestion_thresholds['medium']:
                    level = 'medium'
                elif congestion_index < self.congestion_thresholds['high']:
                    level = 'high'
                else:
                    level = 'severe'
                
                congestion_analysis['direction_levels'][direction] = {
                    'level': level,
                    'index': congestion_index,
                    'flow_rate': state.flow_rate,
                    'density': state.density,
                    'speed': state.average_speed
                }
                
                total_congestion += congestion_index
                
                # 识别瓶颈方向
                if congestion_index > self.congestion_thresholds['medium']:
                    congestion_analysis['bottleneck_directions'].append(direction)
            
            # 计算总体拥堵等级
            if direction_count > 0:
                avg_congestion = total_congestion / direction_count
                congestion_analysis['congestion_score'] = avg_congestion
                
                if avg_congestion < self.congestion_thresholds['low']:
                    congestion_analysis['overall_level'] = 'low'
                elif avg_congestion < self.congestion_thresholds['medium']:
                    congestion_analysis['overall_level'] = 'medium'
                elif avg_congestion < self.congestion_thresholds['high']:
                    congestion_analysis['overall_level'] = 'high'
                else:
                    congestion_analysis['overall_level'] = 'severe'
            
            return congestion_analysis
            
        except Exception as e:
            self.logger.error(f"计算拥堵等级失败: {e}")
            return {'overall_level': 'unknown', 'direction_levels': {}, 'congestion_score': 0.0}
    
    def _calculate_direction_congestion(self, state: TrafficState) -> float:
        """计算单个方向的拥堵指数"""
        try:
            # 基于多个指标计算拥堵指数
            density_factor = min(state.density / 50.0, 1.0)  # 密度因子
            speed_factor = max(0, 1.0 - state.average_speed / 50.0)  # 速度因子
            queue_factor = min(state.queue_length / 100.0, 1.0)  # 排队因子
            
            # 加权计算拥堵指数
            congestion_index = (
                density_factor * 0.4 +
                speed_factor * 0.4 +
                queue_factor * 0.2
            )
            
            return min(congestion_index, 1.0)
            
        except Exception:
            return 0.0
    
    def generate_adaptive_signal_timing(self, traffic_states: Dict[str, TrafficState], 
                                       current_timing: Optional[Dict] = None) -> Dict:
        """
        生成自适应信号配时方案
        
        Args:
            traffic_states: 交通状态数据
            current_timing: 当前信号配时
            
        Returns:
            Dict: 自适应信号配时方案
        """
        try:
            self.logger.info("生成自适应信号配时方案")
            
            # 分析流量需求
            flow_demands = self._analyze_flow_demands(traffic_states)
            
            # 计算最优周期时长
            optimal_cycle = self._calculate_optimal_cycle(flow_demands)
            
            # 分配绿灯时间
            green_splits = self._calculate_green_splits(flow_demands, optimal_cycle)
            
            # 生成相位方案
            phases = self._generate_signal_phases(green_splits)
            
            adaptive_timing = {
                'cycle_time': optimal_cycle,
                'phases': phases,
                'optimization_method': 'adaptive_control',
                'flow_demands': flow_demands,
                'adaptation_reason': self._get_adaptation_reason(traffic_states),
                'timestamp': datetime.now().isoformat()
            }
            
            return adaptive_timing
            
        except Exception as e:
            self.logger.error(f"生成自适应信号配时失败: {e}")
            return {}
    
    def _analyze_flow_demands(self, traffic_states: Dict[str, TrafficState]) -> Dict:
        """分析流量需求"""
        flow_demands = {}
        
        for direction, state in traffic_states.items():
            # 计算需求强度
            demand_intensity = self._calculate_demand_intensity(state)
            
            flow_demands[direction] = {
                'flow_rate': state.flow_rate,
                'demand_intensity': demand_intensity,
                'priority_score': self._calculate_priority_score(state),
                'saturation_ratio': self._calculate_saturation_ratio(state)
            }
        
        return flow_demands
    
    def _calculate_demand_intensity(self, state: TrafficState) -> float:
        """计算需求强度"""
        # 基于流量、密度和等待时间计算需求强度
        flow_factor = min(state.flow_rate / 1000.0, 1.0)
        density_factor = min(state.density / 50.0, 1.0)
        wait_factor = min(state.waiting_time / 60.0, 1.0)
        
        return (flow_factor * 0.5 + density_factor * 0.3 + wait_factor * 0.2)
    
    def _calculate_priority_score(self, state: TrafficState) -> float:
        """计算优先级评分"""
        # 基于拥堵程度和等待时间计算优先级
        congestion_score = self._calculate_direction_congestion(state)
        wait_score = min(state.waiting_time / 120.0, 1.0)
        
        return (congestion_score * 0.7 + wait_score * 0.3)
    
    def _calculate_saturation_ratio(self, state: TrafficState) -> float:
        """计算饱和度比"""
        # 估算饱和流量（vehicles/hour）
        estimated_saturation_flow = 1800  # 基础饱和流量
        
        if estimated_saturation_flow > 0:
            return min(state.flow_rate / estimated_saturation_flow, 1.0)
        return 0.0
    
    def _calculate_optimal_cycle(self, flow_demands: Dict) -> int:
        """计算最优周期时长"""
        # 使用Webster公式计算最优周期
        total_lost_time = 16  # 假设总损失时间
        
        # 计算关键饱和度比
        critical_ratios = []
        for direction, demand in flow_demands.items():
            critical_ratios.append(demand['saturation_ratio'])
        
        if critical_ratios:
            sum_critical_ratios = sum(critical_ratios)
            if sum_critical_ratios < 0.9:  # 避免过饱和
                optimal_cycle = (1.5 * total_lost_time + 5) / (1 - sum_critical_ratios)
                return max(60, min(180, int(optimal_cycle)))
        
        return 120  # 默认周期
    
    def _calculate_green_splits(self, flow_demands: Dict, cycle_time: int) -> Dict:
        """计算绿灯时间分配"""
        green_splits = {}
        
        # 计算有效绿灯时间
        total_lost_time = 16
        effective_green = cycle_time - total_lost_time
        
        # 计算总需求强度
        total_demand = sum(demand['demand_intensity'] for demand in flow_demands.values())
        
        if total_demand > 0:
            for direction, demand in flow_demands.items():
                # 按需求强度比例分配绿灯时间
                proportion = demand['demand_intensity'] / total_demand
                green_time = max(10, int(effective_green * proportion))  # 最小绿灯时间10秒
                green_splits[direction] = green_time
        else:
            # 均匀分配
            equal_green = effective_green // len(flow_demands) if flow_demands else 30
            for direction in flow_demands:
                green_splits[direction] = equal_green
        
        return green_splits
    
    def _generate_signal_phases(self, green_splits: Dict) -> List[Dict]:
        """生成信号相位方案"""
        phases = []
        
        # 简化的两相位方案：东西-南北
        ew_green = green_splits.get('east', 30) + green_splits.get('west', 0)
        ns_green = green_splits.get('north', 30) + green_splits.get('south', 0)
        
        # 东西绿灯相位
        phases.append({
            'phase_id': 'phase_1',
            'duration': max(10, ew_green),
            'state': 'GrGr',
            'description': '东西方向绿灯',
            'directions': ['east', 'west']
        })
        
        # 东西黄灯相位
        phases.append({
            'phase_id': 'phase_2',
            'duration': 4,
            'state': 'yryr',
            'description': '东西方向黄灯',
            'directions': ['east', 'west']
        })
        
        # 南北绿灯相位
        phases.append({
            'phase_id': 'phase_3',
            'duration': max(10, ns_green),
            'state': 'rGrG',
            'description': '南北方向绿灯',
            'directions': ['north', 'south']
        })
        
        # 南北黄灯相位
        phases.append({
            'phase_id': 'phase_4',
            'duration': 4,
            'state': 'ryry',
            'description': '南北方向黄灯',
            'directions': ['north', 'south']
        })
        
        return phases
    
    def _get_adaptation_reason(self, traffic_states: Dict[str, TrafficState]) -> str:
        """获取自适应调整原因"""
        reasons = []
        
        for direction, state in traffic_states.items():
            congestion = self._calculate_direction_congestion(state)
            if congestion > self.congestion_thresholds['medium']:
                reasons.append(f"{direction}方向拥堵严重")
            elif state.waiting_time > 60:
                reasons.append(f"{direction}方向等待时间过长")
        
        if not reasons:
            reasons.append("基于当前流量优化配时")
        
        return "; ".join(reasons)
    
    def generate_intelligent_recommendations(self, traffic_states: Dict[str, TrafficState], 
                                           optimization_result: Dict) -> List[Dict]:
        """
        生成智能优化建议
        
        Args:
            traffic_states: 交通状态数据
            optimization_result: 优化结果
            
        Returns:
            List[Dict]: 智能建议列表
        """
        try:
            self.logger.info("生成智能优化建议")
            
            recommendations = []
            
            # 分析拥堵情况
            congestion_analysis = self.calculate_congestion_level(traffic_states)
            
            # 基于拥堵等级生成建议
            recommendations.extend(self._generate_congestion_recommendations(congestion_analysis))
            
            # 基于优化结果生成建议
            recommendations.extend(self._generate_optimization_recommendations(optimization_result))
            
            # 基于流量不平衡生成建议
            recommendations.extend(self._generate_balance_recommendations(traffic_states))
            
            # 按优先级排序
            recommendations.sort(key=lambda x: self._get_priority_weight(x['priority']), reverse=True)
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成智能建议失败: {e}")
            return []
    
    def _generate_congestion_recommendations(self, congestion_analysis: Dict) -> List[Dict]:
        """基于拥堵分析生成建议"""
        recommendations = []
        
        overall_level = congestion_analysis.get('overall_level', 'low')
        bottlenecks = congestion_analysis.get('bottleneck_directions', [])
        
        if overall_level == 'severe':
            recommendations.append({
                'type': 'emergency_control',
                'priority': 'high',
                'title': '启动应急交通控制',
                'description': '检测到严重拥堵，建议启动应急交通控制措施',
                'action': '实施动态信号控制，增加警力疏导',
                'expected_effect': '快速缓解拥堵，恢复正常通行',
                'implementation_time': '立即执行'
            })
        elif overall_level == 'high':
            recommendations.append({
                'type': 'signal_optimization',
                'priority': 'high',
                'title': '优化信号配时',
                'description': '当前拥堵较为严重，建议调整信号配时方案',
                'action': '应用自适应信号控制算法',
                'expected_effect': '减少15-25%的延误时间',
                'implementation_time': '5分钟内'
            })
        
        # 针对瓶颈方向的建议
        for direction in bottlenecks:
            recommendations.append({
                'type': 'direction_optimization',
                'priority': 'medium',
                'title': f'优化{direction}方向通行',
                'description': f'{direction}方向出现拥堵瓶颈，需要重点优化',
                'action': f'增加{direction}方向绿灯时间，优化车道配置',
                'expected_effect': '改善该方向通行效率',
                'implementation_time': '10分钟内'
            })
        
        return recommendations
    
    def _generate_optimization_recommendations(self, optimization_result: Dict) -> List[Dict]:
        """基于优化结果生成建议"""
        recommendations = []
        
        improvement = optimization_result.get('improvement_percentage', 0)
        
        if improvement > 20:
            recommendations.append({
                'type': 'high_impact_optimization',
                'priority': 'high',
                'title': '应用高效优化方案',
                'description': f'检测到高效优化方案，可提升{improvement:.1f}%的通行效率',
                'action': '立即应用优化后的信号配时方案',
                'expected_effect': f'显著提升{improvement:.1f}%的整体通行效率',
                'implementation_time': '立即执行'
            })
        elif improvement > 10:
            recommendations.append({
                'type': 'moderate_optimization',
                'priority': 'medium',
                'title': '应用优化配时方案',
                'description': f'优化方案可提升{improvement:.1f}%的通行效率',
                'action': '应用优化后的信号配时',
                'expected_effect': f'提升{improvement:.1f}%的通行效率',
                'implementation_time': '5分钟内'
            })
        
        return recommendations
    
    def _generate_balance_recommendations(self, traffic_states: Dict[str, TrafficState]) -> List[Dict]:
        """基于流量平衡生成建议"""
        recommendations = []
        
        # 计算流量不平衡度
        flow_rates = [state.flow_rate for state in traffic_states.values()]
        if flow_rates:
            mean_flow = sum(flow_rates) / len(flow_rates)
            max_flow = max(flow_rates)
            min_flow = min(flow_rates)
            
            if mean_flow > 0:
                imbalance = (max_flow - min_flow) / mean_flow
                
                if imbalance > 0.5:
                    recommendations.append({
                        'type': 'flow_balance',
                        'priority': 'medium',
                        'title': '平衡各方向流量',
                        'description': '各方向流量不平衡较为严重，建议调整配时平衡流量',
                        'action': '调整信号配时，增加高流量方向的绿灯时间',
                        'expected_effect': '平衡各方向流量，提升整体效率',
                        'implementation_time': '10分钟内'
                    })
        
        return recommendations
    
    def _get_priority_weight(self, priority: str) -> int:
        """获取优先级权重"""
        priority_weights = {
            'high': 3,
            'medium': 2,
            'low': 1
        }
        return priority_weights.get(priority, 1)
    
    def evaluate_optimization_effectiveness(self, before_data: Dict, after_data: Dict) -> OptimizationResult:
        """
        评估优化效果
        
        Args:
            before_data: 优化前数据
            after_data: 优化后数据
            
        Returns:
            OptimizationResult: 优化效果评估结果
        """
        try:
            self.logger.info("评估优化效果")
            
            # 计算各项改善指标
            delay_reduction = self._calculate_delay_improvement(before_data, after_data)
            throughput_increase = self._calculate_throughput_improvement(before_data, after_data)
            fuel_saving = self._calculate_fuel_saving(before_data, after_data)
            
            # 计算综合改善评分
            improvement_score = (
                delay_reduction * self.weights['delay'] +
                throughput_increase * self.weights['throughput'] +
                fuel_saving * self.weights['fuel']
            )
            
            # 生成建议
            recommendations = self._generate_effectiveness_recommendations(
                delay_reduction, throughput_increase, fuel_saving
            )
            
            result = OptimizationResult(
                improvement_score=improvement_score,
                delay_reduction=delay_reduction,
                throughput_increase=throughput_increase,
                fuel_saving=fuel_saving,
                recommendations=recommendations,
                optimized_timing=after_data.get('signal_timing', {})
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"评估优化效果失败: {e}")
            return OptimizationResult(0, 0, 0, 0, [], {})
    
    def _calculate_delay_improvement(self, before_data: Dict, after_data: Dict) -> float:
        """计算延误改善"""
        before_delay = before_data.get('average_delay', 0)
        after_delay = after_data.get('average_delay', 0)
        
        if before_delay > 0:
            return ((before_delay - after_delay) / before_delay) * 100
        return 0
    
    def _calculate_throughput_improvement(self, before_data: Dict, after_data: Dict) -> float:
        """计算通行能力改善"""
        before_throughput = before_data.get('throughput', 0)
        after_throughput = after_data.get('throughput', 0)
        
        if before_throughput > 0:
            return ((after_throughput - before_throughput) / before_throughput) * 100
        return 0
    
    def _calculate_fuel_saving(self, before_data: Dict, after_data: Dict) -> float:
        """计算燃油节省"""
        before_fuel = before_data.get('fuel_consumption', 0)
        after_fuel = after_data.get('fuel_consumption', 0)
        
        if before_fuel > 0:
            return ((before_fuel - after_fuel) / before_fuel) * 100
        return 0
    
    def _generate_effectiveness_recommendations(self, delay_reduction: float, 
                                              throughput_increase: float, 
                                              fuel_saving: float) -> List[Dict]:
        """基于效果评估生成建议"""
        recommendations = []
        
        if delay_reduction > 15:
            recommendations.append({
                'type': 'maintain_optimization',
                'priority': 'high',
                'title': '保持当前优化方案',
                'description': f'当前方案显著减少了{delay_reduction:.1f}%的延误',
                'action': '继续使用当前信号配时方案'
            })
        
        if throughput_increase > 10:
            recommendations.append({
                'type': 'expand_optimization',
                'priority': 'medium',
                'title': '扩展优化范围',
                'description': f'通行能力提升{throughput_increase:.1f}%，建议扩展到相邻路口',
                'action': '将优化方案应用到相邻交叉口'
            })
        
        return recommendations
