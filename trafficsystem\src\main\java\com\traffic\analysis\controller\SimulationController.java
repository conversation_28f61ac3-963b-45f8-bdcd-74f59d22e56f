package com.traffic.analysis.controller;

import com.traffic.analysis.model.SimulationTask;
import com.traffic.analysis.model.OptimizationResult;
import com.traffic.analysis.service.SimulationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 仿真控制器
 * 提供SUMO仿真相关的REST API接口
 */
@Slf4j
@RestController
@CrossOrigin(origins = {"http://localhost:8081", "http://localhost:8080", "http://localhost:5173", "http://localhost:5000", "http://localhost:5001"}, 
            allowCredentials = "true", maxAge = 3600)
@RequestMapping("/api/simulation")
public class SimulationController {
    
    @Autowired
    private SimulationService simulationService;
    
    /**
     * 创建仿真任务
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createSimulation(@RequestBody Map<String, Object> request) {
        try {
            log.info("收到创建仿真任务请求");
            
            // 提取请求参数
            String userId = (String) request.get("user_id");
            String username = (String) request.get("username");
            String taskName = (String) request.get("task_name");
            String simulationType = (String) request.get("simulation_type");
            Map<String, Object> trafficData = (Map<String, Object>) request.get("traffic_data");
            Map<String, Object> simulationConfig = (Map<String, Object>) request.get("simulation_config");
            
            // 参数验证
            if (userId == null || username == null || simulationType == null || trafficData == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("缺少必要参数"));
            }
            
            // 创建仿真任务
            SimulationTask task = simulationService.createSimulationTask(
                userId, username, taskName, simulationType, trafficData, simulationConfig
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "仿真任务创建成功");
            response.put("simulation_task", task);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("创建仿真任务失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("创建仿真任务失败: " + e.getMessage()));
        }
    }
    
    /**
     * 启动仿真
     */
    @PostMapping("/{simulationId}/start")
    public ResponseEntity<Map<String, Object>> startSimulation(
            @PathVariable String simulationId,
            @RequestBody(required = false) Map<String, Object> request) {
        try {
            log.info("启动仿真: {}", simulationId);
            
            boolean useGui = false;
            if (request != null && request.containsKey("use_gui")) {
                useGui = (Boolean) request.get("use_gui");
            }
            
            Map<String, Object> result = simulationService.startSimulation(simulationId, useGui);
            
            if ("success".equals(result.get("status"))) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("启动仿真失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("启动仿真失败: " + e.getMessage()));
        }
    }
    
    /**
     * 停止仿真
     */
    @PostMapping("/{simulationId}/stop")
    public ResponseEntity<Map<String, Object>> stopSimulation(@PathVariable String simulationId) {
        try {
            log.info("停止仿真: {}", simulationId);
            
            Map<String, Object> result = simulationService.stopSimulation(simulationId);
            
            if ("success".equals(result.get("status"))) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("停止仿真失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("停止仿真失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取仿真状态
     */
    @GetMapping("/{simulationId}/status")
    public ResponseEntity<Map<String, Object>> getSimulationStatus(@PathVariable String simulationId) {
        try {
            Map<String, Object> status = simulationService.getSimulationStatus(simulationId);
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            log.error("获取仿真状态失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取仿真状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取用户的仿真任务列表
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserSimulations(@PathVariable String userId) {
        try {
            List<SimulationTask> tasks = simulationService.getUserSimulationTasks(userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("simulation_tasks", tasks);
            response.put("total_count", tasks.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取用户仿真任务失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取用户仿真任务失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取仿真任务详情
     */
    @GetMapping("/{simulationId}")
    public ResponseEntity<Map<String, Object>> getSimulationTask(@PathVariable String simulationId) {
        try {
            Optional<SimulationTask> taskOpt = simulationService.getSimulationTask(simulationId);
            
            if (taskOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", "success");
                response.put("simulation_task", taskOpt.get());
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("获取仿真任务详情失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取仿真任务详情失败: " + e.getMessage()));
        }
    }
    
    /**
     * 删除仿真任务
     */
    @DeleteMapping("/{simulationId}")
    public ResponseEntity<Map<String, Object>> deleteSimulation(
            @PathVariable String simulationId,
            @RequestParam String userId) {
        try {
            Map<String, Object> result = simulationService.deleteSimulationTask(simulationId, userId);
            
            if ("success".equals(result.get("status"))) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("删除仿真任务失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("删除仿真任务失败: " + e.getMessage()));
        }
    }
    
    /**
     * 信号灯配时优化
     */
    @PostMapping("/optimization/signal")
    public ResponseEntity<Map<String, Object>> optimizeSignalTiming(@RequestBody Map<String, Object> request) {
        try {
            log.info("执行信号灯配时优化");
            
            Map<String, Object> trafficData = (Map<String, Object>) request.get("traffic_data");
            Map<String, Object> currentTiming = (Map<String, Object>) request.get("current_timing");
            
            if (trafficData == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("缺少交通数据"));
            }
            
            Map<String, Object> result = simulationService.optimizeSignalTiming(trafficData, currentTiming);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("信号灯配时优化失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("信号灯配时优化失败: " + e.getMessage()));
        }
    }
    
    /**
     * 流量平衡优化
     */
    @PostMapping("/optimization/flow")
    public ResponseEntity<Map<String, Object>> optimizeFlowBalance(@RequestBody Map<String, Object> request) {
        try {
            log.info("执行流量平衡优化");
            
            Map<String, Object> trafficData = (Map<String, Object>) request.get("traffic_data");
            
            if (trafficData == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("缺少交通数据"));
            }
            
            Map<String, Object> result = simulationService.optimizeFlowBalance(trafficData);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("流量平衡优化失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("流量平衡优化失败: " + e.getMessage()));
        }
    }
    
    /**
     * 综合优化分析
     */
    @PostMapping("/optimization/comprehensive")
    public ResponseEntity<Map<String, Object>> comprehensiveOptimization(@RequestBody Map<String, Object> request) {
        try {
            log.info("执行综合优化分析");
            
            Map<String, Object> trafficData = (Map<String, Object>) request.get("traffic_data");
            
            if (trafficData == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("缺少交通数据"));
            }
            
            Map<String, Object> result = simulationService.performComprehensiveOptimization(trafficData);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("综合优化分析失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("综合优化分析失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取优化结果
     */
    @GetMapping("/{simulationId}/optimization")
    public ResponseEntity<Map<String, Object>> getOptimizationResult(@PathVariable String simulationId) {
        try {
            // 首先获取仿真任务
            Optional<SimulationTask> taskOpt = simulationService.getSimulationTask(simulationId);
            if (!taskOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            // 获取优化结果
            Optional<OptimizationResult> resultOpt = simulationService.getOptimizationResult(taskOpt.get().getId());
            
            if (resultOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", "success");
                response.put("optimization_result", resultOpt.get());
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("获取优化结果失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取优化结果失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取仿真统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics(@RequestParam(required = false) String userId) {
        try {
            Map<String, Object> stats = simulationService.getSimulationStatistics(userId);
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("获取仿真统计失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取仿真统计失败: " + e.getMessage()));
        }
    }
    
    /**
     * 检查SUMO服务状态
     */
    @GetMapping("/service/status")
    public ResponseEntity<Map<String, Object>> checkServiceStatus() {
        try {
            Map<String, Object> status = simulationService.checkSumoServiceStatus();
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            log.error("检查服务状态失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("检查服务状态失败: " + e.getMessage()));
        }
    }
    
    // 辅助方法
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "error");
        response.put("message", message);
        return response;
    }
}
