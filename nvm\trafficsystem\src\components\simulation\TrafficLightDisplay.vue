<template>
  <div class="traffic-light-display">
    <div class="traffic-light">
      <!-- 红灯 -->
      <div 
        class="light red"
        :class="{ active: state.red, clickable: isManual }"
        @click="onLightClick('red')"
      >
        <div class="light-inner"></div>
      </div>
      
      <!-- 黄灯 -->
      <div 
        class="light yellow"
        :class="{ active: state.yellow, clickable: isManual }"
        @click="onLightClick('yellow')"
      >
        <div class="light-inner"></div>
      </div>
      
      <!-- 绿灯 -->
      <div 
        class="light green"
        :class="{ active: state.green, clickable: isManual }"
        @click="onLightClick('green')"
      >
        <div class="light-inner"></div>
      </div>
    </div>
    
    <!-- 状态指示 -->
    <div class="status-indicator">
      <el-tag 
        :type="getStatusType()" 
        size="small"
        effect="plain"
      >
        {{ getStatusText() }}
      </el-tag>
    </div>
    
    <!-- 手动控制提示 -->
    <div v-if="isManual" class="manual-hint">
      <el-text size="small" type="info">
        点击切换状态
      </el-text>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { ElTag, ElText } from 'element-plus'

export default {
  name: 'TrafficLightDisplay',
  components: {
    ElTag,
    ElText
  },
  props: {
    state: {
      type: Object,
      required: true,
      default: () => ({
        red: false,
        yellow: false,
        green: false
      })
    },
    isManual: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      required: true
    }
  },
  emits: ['manual-change'],
  setup(props, { emit }) {
    
    const getStatusType = () => {
      if (props.state.red) return 'danger'
      if (props.state.yellow) return 'warning'
      if (props.state.green) return 'success'
      return 'info'
    }
    
    const getStatusText = () => {
      if (props.state.red) return '停止'
      if (props.state.yellow) return '警告'
      if (props.state.green) return '通行'
      return '未知'
    }
    
    const onLightClick = (color) => {
      if (!props.isManual) return
      
      // 创建新的状态对象
      const newState = {
        red: false,
        yellow: false,
        green: false
      }
      
      // 设置选中的颜色为true
      newState[color] = true
      
      // 发射变更事件
      emit('manual-change', props.direction, newState)
    }
    
    return {
      getStatusType,
      getStatusText,
      onLightClick
    }
  }
}
</script>

<style scoped>
.traffic-light-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.traffic-light {
  display: flex;
  flex-direction: column;
  background: #2c3e50;
  border-radius: 20px;
  padding: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  gap: 6px;
}

.light {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid #34495e;
}

.light-inner {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.light.red .light-inner {
  background: #2c3e50;
}

.light.red.active .light-inner {
  background: #e74c3c;
  box-shadow: 0 0 20px #e74c3c, inset 0 0 10px #c0392b;
}

.light.yellow .light-inner {
  background: #2c3e50;
}

.light.yellow.active .light-inner {
  background: #f39c12;
  box-shadow: 0 0 20px #f39c12, inset 0 0 10px #e67e22;
}

.light.green .light-inner {
  background: #2c3e50;
}

.light.green.active .light-inner {
  background: #27ae60;
  box-shadow: 0 0 20px #27ae60, inset 0 0 10px #229954;
}

.light.clickable {
  cursor: pointer;
}

.light.clickable:hover {
  transform: scale(1.05);
  border-color: #3498db;
}

.status-indicator {
  margin-top: 4px;
}

.manual-hint {
  margin-top: 4px;
  text-align: center;
}

/* 动画效果 */
.light.active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .light {
    width: 32px;
    height: 32px;
  }
  
  .light-inner {
    width: 24px;
    height: 24px;
  }
  
  .traffic-light {
    padding: 6px;
    gap: 4px;
  }
}
</style>
