{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport simulationApi from '@/api/simulation';\nexport default {\n  name: 'SimulationDashboard',\n  setup() {\n    const router = useRouter();\n\n    // 响应式数据\n    const loading = ref(false);\n    const creating = ref(false);\n    const showCreateDialog = ref(false);\n    const tasks = ref([]);\n    const statistics = ref({});\n\n    // 筛选和分页\n    const filterStatus = ref('');\n    const filterType = ref('');\n    const currentPage = ref(1);\n    const pageSize = ref(20);\n    const totalTasks = ref(0);\n\n    // 创建表单\n    const createForm = reactive({\n      taskName: '',\n      simulationType: 'comprehensive',\n      analysisTaskId: '',\n      useGui: false\n    });\n    const createRules = {\n      taskName: [{\n        required: true,\n        message: '请输入任务名称',\n        trigger: 'blur'\n      }],\n      simulationType: [{\n        required: true,\n        message: '请选择仿真类型',\n        trigger: 'change'\n      }],\n      analysisTaskId: [{\n        required: true,\n        message: '请输入分析任务ID',\n        trigger: 'blur'\n      }]\n    };\n    const createFormRef = ref(null);\n\n    // 计算属性\n    const filteredTasks = computed(() => {\n      let filtered = tasks.value;\n      if (filterStatus.value) {\n        filtered = filtered.filter(task => task.status === filterStatus.value);\n      }\n      if (filterType.value) {\n        filtered = filtered.filter(task => task.simulationType === filterType.value);\n      }\n      return filtered;\n    });\n\n    // 方法\n    const getStatusTagType = status => {\n      const statusMap = {\n        'created': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      };\n      return statusMap[status] || 'info';\n    };\n    const getStatusText = status => {\n      const statusMap = {\n        'created': '已创建',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      };\n      return statusMap[status] || '未知';\n    };\n    const getTypeTagType = type => {\n      const typeMap = {\n        'signal_timing': 'primary',\n        'flow_balance': 'success',\n        'comprehensive': 'warning'\n      };\n      return typeMap[type] || 'info';\n    };\n    const getTypeText = type => {\n      const typeMap = {\n        'signal_timing': '信号配时',\n        'flow_balance': '流量平衡',\n        'comprehensive': '综合优化'\n      };\n      return typeMap[type] || '未知';\n    };\n    const getProgressStatus = status => {\n      if (status === 'failed') return 'exception';\n      if (status === 'completed') return 'success';\n      return null;\n    };\n    const formatTime = timeStr => {\n      if (!timeStr) return '-';\n      const date = new Date(timeStr);\n      return date.toLocaleString('zh-CN');\n    };\n    const formatDuration = duration => {\n      if (!duration) return '-';\n      const hours = Math.floor(duration / 3600);\n      const minutes = Math.floor(duration % 3600 / 60);\n      const seconds = duration % 60;\n      if (hours > 0) {\n        return `${hours}h ${minutes}m ${seconds}s`;\n      } else if (minutes > 0) {\n        return `${minutes}m ${seconds}s`;\n      } else {\n        return `${seconds}s`;\n      }\n    };\n    const loadTasks = async () => {\n      try {\n        loading.value = true;\n        const userId = localStorage.getItem('userId');\n        const response = await simulationApi.getUserSimulations(userId);\n        if (response.status === 'success') {\n          tasks.value = response.simulation_tasks || [];\n          totalTasks.value = response.total_count || 0;\n        }\n      } catch (error) {\n        console.error('加载仿真任务失败:', error);\n        ElMessage.error('加载仿真任务失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n    const loadStatistics = async () => {\n      try {\n        const userId = localStorage.getItem('userId');\n        const response = await simulationApi.getStatistics(userId);\n        statistics.value = response;\n      } catch (error) {\n        console.error('加载统计信息失败:', error);\n      }\n    };\n    const refreshData = async () => {\n      await Promise.all([loadTasks(), loadStatistics()]);\n    };\n    const createSimulation = async () => {\n      try {\n        await createFormRef.value.validate();\n        creating.value = true;\n        const userId = localStorage.getItem('userId');\n        const username = localStorage.getItem('username');\n        const response = await simulationApi.createSimulation({\n          user_id: userId,\n          username: username,\n          task_name: createForm.taskName,\n          simulation_type: createForm.simulationType,\n          analysis_task_id: createForm.analysisTaskId,\n          traffic_data: {} // 这里应该从分析结果获取\n        });\n        if (response.status === 'success') {\n          ElMessage.success('仿真任务创建成功');\n          showCreateDialog.value = false;\n\n          // 如果需要立即启动\n          const simulationId = response.simulation_task.simulationId;\n          await simulationApi.startSimulation(simulationId, {\n            use_gui: createForm.useGui\n          });\n\n          // 跳转到仿真详情页\n          router.push(`/simulation/${simulationId}`);\n        }\n      } catch (error) {\n        console.error('创建仿真失败:', error);\n        ElMessage.error('创建仿真失败');\n      } finally {\n        creating.value = false;\n      }\n    };\n    const viewTask = task => {\n      router.push(`/simulation/${task.simulationId}`);\n    };\n    const stopTask = async task => {\n      try {\n        await ElMessageBox.confirm('确定要停止此仿真任务吗？', '确认停止', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const response = await simulationApi.stopSimulation(task.simulationId);\n        if (response.status === 'success') {\n          ElMessage.success('仿真已停止');\n          await refreshData();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('停止仿真失败:', error);\n          ElMessage.error('停止仿真失败');\n        }\n      }\n    };\n    const restartTask = async task => {\n      try {\n        await ElMessageBox.confirm('确定要重启此仿真任务吗？', '确认重启', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const response = await simulationApi.restartSimulation(task.simulationId);\n        if (response.status === 'success') {\n          ElMessage.success('仿真重启成功');\n          await refreshData();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('重启仿真失败:', error);\n          ElMessage.error('重启仿真失败');\n        }\n      }\n    };\n    const deleteTask = async task => {\n      try {\n        await ElMessageBox.confirm('确定要删除此仿真任务吗？删除后无法恢复。', '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const userId = localStorage.getItem('userId');\n        const response = await simulationApi.deleteSimulation(task.simulationId, userId);\n        if (response.status === 'success') {\n          ElMessage.success('仿真任务已删除');\n          await refreshData();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除仿真失败:', error);\n          ElMessage.error('删除仿真失败');\n        }\n      }\n    };\n    const handleSizeChange = newSize => {\n      pageSize.value = newSize;\n      currentPage.value = 1;\n      loadTasks();\n    };\n    const handleCurrentChange = newPage => {\n      currentPage.value = newPage;\n      loadTasks();\n    };\n\n    // 生命周期\n    onMounted(() => {\n      refreshData();\n    });\n    return {\n      // 响应式数据\n      loading,\n      creating,\n      showCreateDialog,\n      tasks,\n      statistics,\n      filterStatus,\n      filterType,\n      currentPage,\n      pageSize,\n      totalTasks,\n      createForm,\n      createRules,\n      createFormRef,\n      // 计算属性\n      filteredTasks,\n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getTypeTagType,\n      getTypeText,\n      getProgressStatus,\n      formatTime,\n      formatDuration,\n      refreshData,\n      createSimulation,\n      viewTask,\n      stopTask,\n      restartTask,\n      deleteTask,\n      handleSizeChange,\n      handleCurrentChange\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "useRouter", "ElMessage", "ElMessageBox", "simulationApi", "name", "setup", "router", "loading", "creating", "showCreateDialog", "tasks", "statistics", "filterStatus", "filterType", "currentPage", "pageSize", "totalTasks", "createForm", "taskName", "simulationType", "analysisTaskId", "useGui", "createRules", "required", "message", "trigger", "createFormRef", "filteredTasks", "filtered", "value", "filter", "task", "status", "getStatusTagType", "statusMap", "getStatusText", "getTypeTagType", "type", "typeMap", "getTypeText", "getProgressStatus", "formatTime", "timeStr", "date", "Date", "toLocaleString", "formatDuration", "duration", "hours", "Math", "floor", "minutes", "seconds", "loadTasks", "userId", "localStorage", "getItem", "response", "getUserSimulations", "simulation_tasks", "total_count", "error", "console", "loadStatistics", "getStatistics", "refreshData", "Promise", "all", "createSimulation", "validate", "username", "user_id", "task_name", "simulation_type", "analysis_task_id", "traffic_data", "success", "simulationId", "simulation_task", "startSimulation", "use_gui", "push", "viewTask", "stopTask", "confirm", "confirmButtonText", "cancelButtonText", "stopSimulation", "restartTask", "restartSimulation", "deleteTask", "deleteSimulation", "handleSizeChange", "newSize", "handleCurrentChange", "newPage"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\simulation\\SimulationDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"simulation-dashboard\">\n    <!-- 页面头部 -->\n    <div class=\"dashboard-header\">\n      <el-breadcrumb separator=\"/\">\n        <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\n        <el-breadcrumb-item>仿真分析</el-breadcrumb-item>\n        <el-breadcrumb-item>仿真控制台</el-breadcrumb-item>\n      </el-breadcrumb>\n      \n      <div class=\"header-actions\">\n        <el-button type=\"primary\" @click=\"showCreateDialog = true\">\n          <i class=\"el-icon-plus\"></i> 创建仿真\n        </el-button>\n        <el-button @click=\"refreshData\">\n          <i class=\"el-icon-refresh\"></i> 刷新\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon\">\n                <i class=\"el-icon-cpu\" style=\"color: #409eff\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.total_tasks || 0 }}</div>\n                <div class=\"stat-label\">总仿真任务</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon\">\n                <i class=\"el-icon-video-play\" style=\"color: #67c23a\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.running_tasks || 0 }}</div>\n                <div class=\"stat-label\">运行中</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon\">\n                <i class=\"el-icon-check\" style=\"color: #e6a23c\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.completed_tasks || 0 }}</div>\n                <div class=\"stat-label\">已完成</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-card class=\"stat-card\">\n            <div class=\"stat-content\">\n              <div class=\"stat-icon\">\n                <i class=\"el-icon-warning\" style=\"color: #f56c6c\"></i>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-value\">{{ statistics.failed_tasks || 0 }}</div>\n                <div class=\"stat-label\">失败</div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 仿真任务列表 -->\n    <el-card class=\"task-list-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>仿真任务列表</h3>\n          <div class=\"header-filters\">\n            <el-select v-model=\"filterStatus\" placeholder=\"状态筛选\" clearable size=\"small\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option label=\"运行中\" value=\"running\"></el-option>\n              <el-option label=\"已完成\" value=\"completed\"></el-option>\n              <el-option label=\"失败\" value=\"failed\"></el-option>\n              <el-option label=\"已停止\" value=\"stopped\"></el-option>\n            </el-select>\n            \n            <el-select v-model=\"filterType\" placeholder=\"类型筛选\" clearable size=\"small\">\n              <el-option label=\"全部\" value=\"\"></el-option>\n              <el-option label=\"信号配时优化\" value=\"signal_timing\"></el-option>\n              <el-option label=\"流量平衡优化\" value=\"flow_balance\"></el-option>\n              <el-option label=\"综合优化\" value=\"comprehensive\"></el-option>\n            </el-select>\n          </div>\n        </div>\n      </template>\n\n      <el-table :data=\"filteredTasks\" v-loading=\"loading\" style=\"width: 100%\">\n        <el-table-column prop=\"taskName\" label=\"任务名称\" min-width=\"200\">\n          <template #default=\"scope\">\n            <div class=\"task-name\">\n              <strong>{{ scope.row.taskName }}</strong>\n              <div class=\"task-id\">ID: {{ scope.row.simulationId }}</div>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"simulationType\" label=\"仿真类型\" width=\"150\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getTypeTagType(scope.row.simulationType)\" size=\"small\">\n              {{ getTypeText(scope.row.simulationType) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"status\" label=\"状态\" width=\"120\">\n          <template #default=\"scope\">\n            <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">\n              {{ getStatusText(scope.row.status) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"progress\" label=\"进度\" width=\"150\">\n          <template #default=\"scope\">\n            <el-progress \n              :percentage=\"scope.row.progress || 0\" \n              :status=\"getProgressStatus(scope.row.status)\"\n              :stroke-width=\"6\">\n            </el-progress>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"180\">\n          <template #default=\"scope\">\n            {{ formatTime(scope.row.createTime) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"duration\" label=\"耗时\" width=\"100\">\n          <template #default=\"scope\">\n            {{ formatDuration(scope.row.duration) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\n          <template #default=\"scope\">\n            <el-button-group size=\"small\">\n              <el-button \n                type=\"primary\" \n                @click=\"viewTask(scope.row)\"\n                :icon=\"'el-icon-view'\">\n                查看\n              </el-button>\n              \n              <el-button \n                v-if=\"scope.row.status === 'running'\"\n                type=\"warning\" \n                @click=\"stopTask(scope.row)\"\n                :icon=\"'el-icon-video-pause'\">\n                停止\n              </el-button>\n              \n              <el-button \n                v-if=\"scope.row.status === 'failed' || scope.row.status === 'stopped'\"\n                type=\"success\" \n                @click=\"restartTask(scope.row)\"\n                :icon=\"'el-icon-refresh'\">\n                重启\n              </el-button>\n              \n              <el-button \n                type=\"danger\" \n                @click=\"deleteTask(scope.row)\"\n                :icon=\"'el-icon-delete'\">\n                删除\n              </el-button>\n            </el-button-group>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :total=\"totalTasks\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 创建仿真对话框 -->\n    <el-dialog\n      v-model=\"showCreateDialog\"\n      title=\"创建仿真任务\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\">\n      \n      <el-form :model=\"createForm\" :rules=\"createRules\" ref=\"createFormRef\" label-width=\"120px\">\n        <el-form-item label=\"任务名称\" prop=\"taskName\">\n          <el-input v-model=\"createForm.taskName\" placeholder=\"请输入任务名称\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"仿真类型\" prop=\"simulationType\">\n          <el-select v-model=\"createForm.simulationType\" placeholder=\"选择仿真类型\" style=\"width: 100%\">\n            <el-option label=\"信号灯配时优化\" value=\"signal_timing\"></el-option>\n            <el-option label=\"流量平衡优化\" value=\"flow_balance\"></el-option>\n            <el-option label=\"综合优化分析\" value=\"comprehensive\"></el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"分析任务ID\" prop=\"analysisTaskId\">\n          <el-input v-model=\"createForm.analysisTaskId\" placeholder=\"请输入视频分析任务ID\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"使用GUI\">\n          <el-switch v-model=\"createForm.useGui\"></el-switch>\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showCreateDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"createSimulation\" :loading=\"creating\">\n            创建并启动\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport simulationApi from '@/api/simulation'\n\nexport default {\n  name: 'SimulationDashboard',\n  setup() {\n    const router = useRouter()\n    \n    // 响应式数据\n    const loading = ref(false)\n    const creating = ref(false)\n    const showCreateDialog = ref(false)\n    const tasks = ref([])\n    const statistics = ref({})\n    \n    // 筛选和分页\n    const filterStatus = ref('')\n    const filterType = ref('')\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const totalTasks = ref(0)\n    \n    // 创建表单\n    const createForm = reactive({\n      taskName: '',\n      simulationType: 'comprehensive',\n      analysisTaskId: '',\n      useGui: false\n    })\n    \n    const createRules = {\n      taskName: [\n        { required: true, message: '请输入任务名称', trigger: 'blur' }\n      ],\n      simulationType: [\n        { required: true, message: '请选择仿真类型', trigger: 'change' }\n      ],\n      analysisTaskId: [\n        { required: true, message: '请输入分析任务ID', trigger: 'blur' }\n      ]\n    }\n    \n    const createFormRef = ref(null)\n    \n    // 计算属性\n    const filteredTasks = computed(() => {\n      let filtered = tasks.value\n      \n      if (filterStatus.value) {\n        filtered = filtered.filter(task => task.status === filterStatus.value)\n      }\n      \n      if (filterType.value) {\n        filtered = filtered.filter(task => task.simulationType === filterType.value)\n      }\n      \n      return filtered\n    })\n    \n    // 方法\n    const getStatusTagType = (status) => {\n      const statusMap = {\n        'created': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      }\n      return statusMap[status] || 'info'\n    }\n    \n    const getStatusText = (status) => {\n      const statusMap = {\n        'created': '已创建',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      }\n      return statusMap[status] || '未知'\n    }\n    \n    const getTypeTagType = (type) => {\n      const typeMap = {\n        'signal_timing': 'primary',\n        'flow_balance': 'success',\n        'comprehensive': 'warning'\n      }\n      return typeMap[type] || 'info'\n    }\n    \n    const getTypeText = (type) => {\n      const typeMap = {\n        'signal_timing': '信号配时',\n        'flow_balance': '流量平衡',\n        'comprehensive': '综合优化'\n      }\n      return typeMap[type] || '未知'\n    }\n    \n    const getProgressStatus = (status) => {\n      if (status === 'failed') return 'exception'\n      if (status === 'completed') return 'success'\n      return null\n    }\n    \n    const formatTime = (timeStr) => {\n      if (!timeStr) return '-'\n      const date = new Date(timeStr)\n      return date.toLocaleString('zh-CN')\n    }\n    \n    const formatDuration = (duration) => {\n      if (!duration) return '-'\n      const hours = Math.floor(duration / 3600)\n      const minutes = Math.floor((duration % 3600) / 60)\n      const seconds = duration % 60\n      \n      if (hours > 0) {\n        return `${hours}h ${minutes}m ${seconds}s`\n      } else if (minutes > 0) {\n        return `${minutes}m ${seconds}s`\n      } else {\n        return `${seconds}s`\n      }\n    }\n    \n    const loadTasks = async () => {\n      try {\n        loading.value = true\n        const userId = localStorage.getItem('userId')\n        const response = await simulationApi.getUserSimulations(userId)\n        \n        if (response.status === 'success') {\n          tasks.value = response.simulation_tasks || []\n          totalTasks.value = response.total_count || 0\n        }\n      } catch (error) {\n        console.error('加载仿真任务失败:', error)\n        ElMessage.error('加载仿真任务失败')\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    const loadStatistics = async () => {\n      try {\n        const userId = localStorage.getItem('userId')\n        const response = await simulationApi.getStatistics(userId)\n        statistics.value = response\n      } catch (error) {\n        console.error('加载统计信息失败:', error)\n      }\n    }\n    \n    const refreshData = async () => {\n      await Promise.all([loadTasks(), loadStatistics()])\n    }\n    \n    const createSimulation = async () => {\n      try {\n        await createFormRef.value.validate()\n        creating.value = true\n        \n        const userId = localStorage.getItem('userId')\n        const username = localStorage.getItem('username')\n        \n        const response = await simulationApi.createSimulation({\n          user_id: userId,\n          username: username,\n          task_name: createForm.taskName,\n          simulation_type: createForm.simulationType,\n          analysis_task_id: createForm.analysisTaskId,\n          traffic_data: {} // 这里应该从分析结果获取\n        })\n        \n        if (response.status === 'success') {\n          ElMessage.success('仿真任务创建成功')\n          showCreateDialog.value = false\n          \n          // 如果需要立即启动\n          const simulationId = response.simulation_task.simulationId\n          await simulationApi.startSimulation(simulationId, {\n            use_gui: createForm.useGui\n          })\n          \n          // 跳转到仿真详情页\n          router.push(`/simulation/${simulationId}`)\n        }\n      } catch (error) {\n        console.error('创建仿真失败:', error)\n        ElMessage.error('创建仿真失败')\n      } finally {\n        creating.value = false\n      }\n    }\n    \n    const viewTask = (task) => {\n      router.push(`/simulation/${task.simulationId}`)\n    }\n    \n    const stopTask = async (task) => {\n      try {\n        await ElMessageBox.confirm('确定要停止此仿真任务吗？', '确认停止', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await simulationApi.stopSimulation(task.simulationId)\n        if (response.status === 'success') {\n          ElMessage.success('仿真已停止')\n          await refreshData()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('停止仿真失败:', error)\n          ElMessage.error('停止仿真失败')\n        }\n      }\n    }\n    \n    const restartTask = async (task) => {\n      try {\n        await ElMessageBox.confirm('确定要重启此仿真任务吗？', '确认重启', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const response = await simulationApi.restartSimulation(task.simulationId)\n        if (response.status === 'success') {\n          ElMessage.success('仿真重启成功')\n          await refreshData()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('重启仿真失败:', error)\n          ElMessage.error('重启仿真失败')\n        }\n      }\n    }\n    \n    const deleteTask = async (task) => {\n      try {\n        await ElMessageBox.confirm('确定要删除此仿真任务吗？删除后无法恢复。', '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n        \n        const userId = localStorage.getItem('userId')\n        const response = await simulationApi.deleteSimulation(task.simulationId, userId)\n        if (response.status === 'success') {\n          ElMessage.success('仿真任务已删除')\n          await refreshData()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除仿真失败:', error)\n          ElMessage.error('删除仿真失败')\n        }\n      }\n    }\n    \n    const handleSizeChange = (newSize) => {\n      pageSize.value = newSize\n      currentPage.value = 1\n      loadTasks()\n    }\n    \n    const handleCurrentChange = (newPage) => {\n      currentPage.value = newPage\n      loadTasks()\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      refreshData()\n    })\n    \n    return {\n      // 响应式数据\n      loading,\n      creating,\n      showCreateDialog,\n      tasks,\n      statistics,\n      filterStatus,\n      filterType,\n      currentPage,\n      pageSize,\n      totalTasks,\n      createForm,\n      createRules,\n      createFormRef,\n      \n      // 计算属性\n      filteredTasks,\n      \n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getTypeTagType,\n      getTypeText,\n      getProgressStatus,\n      formatTime,\n      formatDuration,\n      refreshData,\n      createSimulation,\n      viewTask,\n      stopTask,\n      restartTask,\n      deleteTask,\n      handleSizeChange,\n      handleCurrentChange\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simulation-dashboard {\n  padding: 20px;\n}\n\n.dashboard-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.stats-cards {\n  margin-bottom: 20px;\n}\n\n.stat-card {\n  border-radius: 8px;\n}\n\n.stat-content {\n  display: flex;\n  align-items: center;\n}\n\n.stat-icon {\n  font-size: 32px;\n  margin-right: 15px;\n}\n\n.stat-info {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.task-list-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  color: #303133;\n}\n\n.header-filters {\n  display: flex;\n  gap: 10px;\n}\n\n.task-name {\n  line-height: 1.4;\n}\n\n.task-id {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 2px;\n}\n\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: right;\n}\n</style>\n"], "mappings": ";;;AAsPA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AACpE,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,OAAOC,aAAY,MAAO,kBAAiB;AAE3C,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIN,SAAS,CAAC;;IAEzB;IACA,MAAMO,OAAM,GAAIZ,GAAG,CAAC,KAAK;IACzB,MAAMa,QAAO,GAAIb,GAAG,CAAC,KAAK;IAC1B,MAAMc,gBAAe,GAAId,GAAG,CAAC,KAAK;IAClC,MAAMe,KAAI,GAAIf,GAAG,CAAC,EAAE;IACpB,MAAMgB,UAAS,GAAIhB,GAAG,CAAC,CAAC,CAAC;;IAEzB;IACA,MAAMiB,YAAW,GAAIjB,GAAG,CAAC,EAAE;IAC3B,MAAMkB,UAAS,GAAIlB,GAAG,CAAC,EAAE;IACzB,MAAMmB,WAAU,GAAInB,GAAG,CAAC,CAAC;IACzB,MAAMoB,QAAO,GAAIpB,GAAG,CAAC,EAAE;IACvB,MAAMqB,UAAS,GAAIrB,GAAG,CAAC,CAAC;;IAExB;IACA,MAAMsB,UAAS,GAAIrB,QAAQ,CAAC;MAC1BsB,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,eAAe;MAC/BC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,WAAU,GAAI;MAClBJ,QAAQ,EAAE,CACR;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,EACvD;MACDN,cAAc,EAAE,CACd;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS,EACzD;MACDL,cAAc,EAAE,CACd;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO;IAE5D;IAEA,MAAMC,aAAY,GAAI/B,GAAG,CAAC,IAAI;;IAE9B;IACA,MAAMgC,aAAY,GAAI9B,QAAQ,CAAC,MAAM;MACnC,IAAI+B,QAAO,GAAIlB,KAAK,CAACmB,KAAI;MAEzB,IAAIjB,YAAY,CAACiB,KAAK,EAAE;QACtBD,QAAO,GAAIA,QAAQ,CAACE,MAAM,CAACC,IAAG,IAAKA,IAAI,CAACC,MAAK,KAAMpB,YAAY,CAACiB,KAAK;MACvE;MAEA,IAAIhB,UAAU,CAACgB,KAAK,EAAE;QACpBD,QAAO,GAAIA,QAAQ,CAACE,MAAM,CAACC,IAAG,IAAKA,IAAI,CAACZ,cAAa,KAAMN,UAAU,CAACgB,KAAK;MAC7E;MAEA,OAAOD,QAAO;IAChB,CAAC;;IAED;IACA,MAAMK,gBAAe,GAAKD,MAAM,IAAK;MACnC,MAAME,SAAQ,GAAI;QAChB,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,SAAS;QACpB,WAAW,EAAE,SAAS;QACtB,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE;MACb;MACA,OAAOA,SAAS,CAACF,MAAM,KAAK,MAAK;IACnC;IAEA,MAAMG,aAAY,GAAKH,MAAM,IAAK;MAChC,MAAME,SAAQ,GAAI;QAChB,SAAS,EAAE,KAAK;QAChB,SAAS,EAAE,KAAK;QAChB,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE;MACb;MACA,OAAOA,SAAS,CAACF,MAAM,KAAK,IAAG;IACjC;IAEA,MAAMI,cAAa,GAAKC,IAAI,IAAK;MAC/B,MAAMC,OAAM,GAAI;QACd,eAAe,EAAE,SAAS;QAC1B,cAAc,EAAE,SAAS;QACzB,eAAe,EAAE;MACnB;MACA,OAAOA,OAAO,CAACD,IAAI,KAAK,MAAK;IAC/B;IAEA,MAAME,WAAU,GAAKF,IAAI,IAAK;MAC5B,MAAMC,OAAM,GAAI;QACd,eAAe,EAAE,MAAM;QACvB,cAAc,EAAE,MAAM;QACtB,eAAe,EAAE;MACnB;MACA,OAAOA,OAAO,CAACD,IAAI,KAAK,IAAG;IAC7B;IAEA,MAAMG,iBAAgB,GAAKR,MAAM,IAAK;MACpC,IAAIA,MAAK,KAAM,QAAQ,EAAE,OAAO,WAAU;MAC1C,IAAIA,MAAK,KAAM,WAAW,EAAE,OAAO,SAAQ;MAC3C,OAAO,IAAG;IACZ;IAEA,MAAMS,UAAS,GAAKC,OAAO,IAAK;MAC9B,IAAI,CAACA,OAAO,EAAE,OAAO,GAAE;MACvB,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,OAAO;MAC7B,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO;IACpC;IAEA,MAAMC,cAAa,GAAKC,QAAQ,IAAK;MACnC,IAAI,CAACA,QAAQ,EAAE,OAAO,GAAE;MACxB,MAAMC,KAAI,GAAIC,IAAI,CAACC,KAAK,CAACH,QAAO,GAAI,IAAI;MACxC,MAAMI,OAAM,GAAIF,IAAI,CAACC,KAAK,CAAEH,QAAO,GAAI,IAAI,GAAI,EAAE;MACjD,MAAMK,OAAM,GAAIL,QAAO,GAAI,EAAC;MAE5B,IAAIC,KAAI,GAAI,CAAC,EAAE;QACb,OAAO,GAAGA,KAAK,KAAKG,OAAO,KAAKC,OAAO,GAAE;MAC3C,OAAO,IAAID,OAAM,GAAI,CAAC,EAAE;QACtB,OAAO,GAAGA,OAAO,KAAKC,OAAO,GAAE;MACjC,OAAO;QACL,OAAO,GAAGA,OAAO,GAAE;MACrB;IACF;IAEA,MAAMC,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF9C,OAAO,CAACsB,KAAI,GAAI,IAAG;QACnB,MAAMyB,MAAK,GAAIC,YAAY,CAACC,OAAO,CAAC,QAAQ;QAC5C,MAAMC,QAAO,GAAI,MAAMtD,aAAa,CAACuD,kBAAkB,CAACJ,MAAM;QAE9D,IAAIG,QAAQ,CAACzB,MAAK,KAAM,SAAS,EAAE;UACjCtB,KAAK,CAACmB,KAAI,GAAI4B,QAAQ,CAACE,gBAAe,IAAK,EAAC;UAC5C3C,UAAU,CAACa,KAAI,GAAI4B,QAAQ,CAACG,WAAU,IAAK;QAC7C;MACF,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC5D,SAAS,CAAC4D,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRtD,OAAO,CAACsB,KAAI,GAAI,KAAI;MACtB;IACF;IAEA,MAAMkC,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF,MAAMT,MAAK,GAAIC,YAAY,CAACC,OAAO,CAAC,QAAQ;QAC5C,MAAMC,QAAO,GAAI,MAAMtD,aAAa,CAAC6D,aAAa,CAACV,MAAM;QACzD3C,UAAU,CAACkB,KAAI,GAAI4B,QAAO;MAC5B,EAAE,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF;IAEA,MAAMI,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,MAAMC,OAAO,CAACC,GAAG,CAAC,CAACd,SAAS,CAAC,CAAC,EAAEU,cAAc,CAAC,CAAC,CAAC;IACnD;IAEA,MAAMK,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAM1C,aAAa,CAACG,KAAK,CAACwC,QAAQ,CAAC;QACnC7D,QAAQ,CAACqB,KAAI,GAAI,IAAG;QAEpB,MAAMyB,MAAK,GAAIC,YAAY,CAACC,OAAO,CAAC,QAAQ;QAC5C,MAAMc,QAAO,GAAIf,YAAY,CAACC,OAAO,CAAC,UAAU;QAEhD,MAAMC,QAAO,GAAI,MAAMtD,aAAa,CAACiE,gBAAgB,CAAC;UACpDG,OAAO,EAAEjB,MAAM;UACfgB,QAAQ,EAAEA,QAAQ;UAClBE,SAAS,EAAEvD,UAAU,CAACC,QAAQ;UAC9BuD,eAAe,EAAExD,UAAU,CAACE,cAAc;UAC1CuD,gBAAgB,EAAEzD,UAAU,CAACG,cAAc;UAC3CuD,YAAY,EAAE,CAAC,EAAE;QACnB,CAAC;QAED,IAAIlB,QAAQ,CAACzB,MAAK,KAAM,SAAS,EAAE;UACjC/B,SAAS,CAAC2E,OAAO,CAAC,UAAU;UAC5BnE,gBAAgB,CAACoB,KAAI,GAAI,KAAI;;UAE7B;UACA,MAAMgD,YAAW,GAAIpB,QAAQ,CAACqB,eAAe,CAACD,YAAW;UACzD,MAAM1E,aAAa,CAAC4E,eAAe,CAACF,YAAY,EAAE;YAChDG,OAAO,EAAE/D,UAAU,CAACI;UACtB,CAAC;;UAED;UACAf,MAAM,CAAC2E,IAAI,CAAC,eAAeJ,YAAY,EAAE;QAC3C;MACF,EAAE,OAAOhB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B5D,SAAS,CAAC4D,KAAK,CAAC,QAAQ;MAC1B,UAAU;QACRrD,QAAQ,CAACqB,KAAI,GAAI,KAAI;MACvB;IACF;IAEA,MAAMqD,QAAO,GAAKnD,IAAI,IAAK;MACzBzB,MAAM,CAAC2E,IAAI,CAAC,eAAelD,IAAI,CAAC8C,YAAY,EAAE;IAChD;IAEA,MAAMM,QAAO,GAAI,MAAOpD,IAAI,IAAK;MAC/B,IAAI;QACF,MAAM7B,YAAY,CAACkF,OAAO,CAAC,cAAc,EAAE,MAAM,EAAE;UACjDC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBjD,IAAI,EAAE;QACR,CAAC;QAED,MAAMoB,QAAO,GAAI,MAAMtD,aAAa,CAACoF,cAAc,CAACxD,IAAI,CAAC8C,YAAY;QACrE,IAAIpB,QAAQ,CAACzB,MAAK,KAAM,SAAS,EAAE;UACjC/B,SAAS,CAAC2E,OAAO,CAAC,OAAO;UACzB,MAAMX,WAAW,CAAC;QACpB;MACF,EAAE,OAAOJ,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B5D,SAAS,CAAC4D,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;IAEA,MAAM2B,WAAU,GAAI,MAAOzD,IAAI,IAAK;MAClC,IAAI;QACF,MAAM7B,YAAY,CAACkF,OAAO,CAAC,cAAc,EAAE,MAAM,EAAE;UACjDC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBjD,IAAI,EAAE;QACR,CAAC;QAED,MAAMoB,QAAO,GAAI,MAAMtD,aAAa,CAACsF,iBAAiB,CAAC1D,IAAI,CAAC8C,YAAY;QACxE,IAAIpB,QAAQ,CAACzB,MAAK,KAAM,SAAS,EAAE;UACjC/B,SAAS,CAAC2E,OAAO,CAAC,QAAQ;UAC1B,MAAMX,WAAW,CAAC;QACpB;MACF,EAAE,OAAOJ,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B5D,SAAS,CAAC4D,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;IAEA,MAAM6B,UAAS,GAAI,MAAO3D,IAAI,IAAK;MACjC,IAAI;QACF,MAAM7B,YAAY,CAACkF,OAAO,CAAC,sBAAsB,EAAE,MAAM,EAAE;UACzDC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBjD,IAAI,EAAE;QACR,CAAC;QAED,MAAMiB,MAAK,GAAIC,YAAY,CAACC,OAAO,CAAC,QAAQ;QAC5C,MAAMC,QAAO,GAAI,MAAMtD,aAAa,CAACwF,gBAAgB,CAAC5D,IAAI,CAAC8C,YAAY,EAAEvB,MAAM;QAC/E,IAAIG,QAAQ,CAACzB,MAAK,KAAM,SAAS,EAAE;UACjC/B,SAAS,CAAC2E,OAAO,CAAC,SAAS;UAC3B,MAAMX,WAAW,CAAC;QACpB;MACF,EAAE,OAAOJ,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B5D,SAAS,CAAC4D,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;IAEA,MAAM+B,gBAAe,GAAKC,OAAO,IAAK;MACpC9E,QAAQ,CAACc,KAAI,GAAIgE,OAAM;MACvB/E,WAAW,CAACe,KAAI,GAAI;MACpBwB,SAAS,CAAC;IACZ;IAEA,MAAMyC,mBAAkB,GAAKC,OAAO,IAAK;MACvCjF,WAAW,CAACe,KAAI,GAAIkE,OAAM;MAC1B1C,SAAS,CAAC;IACZ;;IAEA;IACAvD,SAAS,CAAC,MAAM;MACdmE,WAAW,CAAC;IACd,CAAC;IAED,OAAO;MACL;MACA1D,OAAO;MACPC,QAAQ;MACRC,gBAAgB;MAChBC,KAAK;MACLC,UAAU;MACVC,YAAY;MACZC,UAAU;MACVC,WAAW;MACXC,QAAQ;MACRC,UAAU;MACVC,UAAU;MACVK,WAAW;MACXI,aAAa;MAEb;MACAC,aAAa;MAEb;MACAM,gBAAgB;MAChBE,aAAa;MACbC,cAAc;MACdG,WAAW;MACXC,iBAAiB;MACjBC,UAAU;MACVK,cAAc;MACdmB,WAAW;MACXG,gBAAgB;MAChBc,QAAQ;MACRC,QAAQ;MACRK,WAAW;MACXE,UAAU;MACVE,gBAAgB;MAChBE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}