<template>
  <div class="decision-support-panel">
    <el-card class="panel-card">
      <template #header>
        <div class="card-header">
          <span class="title">
            <el-icon><BrainIcon /></el-icon>
            智能决策支持
          </span>
          <div class="panel-actions">
            <el-button 
              size="small" 
              @click="refreshSuggestions"
              :loading="loading"
            >
              <el-icon><Refresh /></el-icon>
              刷新建议
            </el-button>
          </div>
        </div>
      </template>

      <!-- 实时决策状态 -->
      <div class="decision-status">
        <el-alert
          :title="alertTitle"
          :type="alertType"
          :description="alertDescription"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 决策建议列表 -->
      <div class="suggestions-section">
        <h4>
          <el-icon><LightbulbIcon /></el-icon>
          智能建议 ({{ suggestions.length }})
        </h4>
        
        <div v-if="suggestions.length === 0" class="no-suggestions">
          <el-empty description="暂无决策建议" />
        </div>
        
        <div v-else class="suggestions-list">
          <div 
            v-for="suggestion in suggestions" 
            :key="suggestion.id"
            class="suggestion-item"
            :class="[`priority-${suggestion.priority}`]"
          >
            <div class="suggestion-header">
              <div class="suggestion-title">
                <el-tag 
                  :type="getPriorityTagType(suggestion.priority)" 
                  size="small"
                >
                  {{ getPriorityText(suggestion.priority) }}
                </el-tag>
                <span class="title-text">{{ suggestion.title }}</span>
              </div>
              <div class="suggestion-actions">
                <el-button 
                  type="primary" 
                  size="small"
                  @click="applySuggestion(suggestion)"
                  :loading="applyingIds.includes(suggestion.id)"
                >
                  应用
                </el-button>
                <el-button 
                  size="small"
                  @click="viewSuggestionDetail(suggestion)"
                >
                  详情
                </el-button>
              </div>
            </div>
            
            <div class="suggestion-content">
              <p class="description">{{ suggestion.description }}</p>
              <div class="suggestion-meta">
                <span class="confidence">
                  <el-icon><TrendCharts /></el-icon>
                  置信度: {{ (suggestion.confidence * 100).toFixed(0) }}%
                </span>
                <span class="impact">
                  <el-icon><DataAnalysis /></el-icon>
                  影响评分: {{ suggestion.impact_score.toFixed(1) }}/10
                </span>
                <span class="time">
                  <el-icon><Clock /></el-icon>
                  {{ suggestion.implementation_time }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时决策支持数据 -->
      <div class="realtime-support" v-if="realtimeSupport">
        <h4>
          <el-icon><Monitor /></el-icon>
          实时分析
        </h4>
        
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="support-metric">
              <div class="metric-label">拥堵等级</div>
              <div class="metric-value" :class="`congestion-${realtimeSupport.current_congestion?.overall_level}`">
                {{ getCongestionText(realtimeSupport.current_congestion?.overall_level) }}
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="support-metric">
              <div class="metric-label">警报级别</div>
              <div class="metric-value" :class="`alert-${realtimeSupport.alert_level}`">
                {{ getAlertText(realtimeSupport.alert_level) }}
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="support-metric">
              <div class="metric-label">下次更新</div>
              <div class="metric-value">
                {{ realtimeSupport.next_update_in }}秒
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 推荐行动 -->
        <div class="recommended-actions" v-if="realtimeSupport.recommended_actions?.length">
          <h5>推荐行动:</h5>
          <ul>
            <li v-for="action in realtimeSupport.recommended_actions" :key="action">
              {{ action }}
            </li>
          </ul>
        </div>
      </div>
    </el-card>

    <!-- 建议详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="决策建议详情"
      width="600px"
    >
      <div v-if="selectedSuggestion" class="suggestion-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="建议类型">
            {{ selectedSuggestion.type }}
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityTagType(selectedSuggestion.priority)">
              {{ getPriorityText(selectedSuggestion.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="置信度">
            {{ (selectedSuggestion.confidence * 100).toFixed(0) }}%
          </el-descriptions-item>
          <el-descriptions-item label="影响评分">
            {{ selectedSuggestion.impact_score.toFixed(1) }}/10
          </el-descriptions-item>
          <el-descriptions-item label="实施时间" span="2">
            {{ selectedSuggestion.implementation_time }}
          </el-descriptions-item>
          <el-descriptions-item label="预期效果" span="2">
            {{ selectedSuggestion.expected_effect }}
          </el-descriptions-item>
          <el-descriptions-item label="具体行动" span="2">
            {{ selectedSuggestion.action }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 风险评估 -->
        <div v-if="selectedSuggestion.risk_assessment" class="risk-assessment">
          <h4>风险评估</h4>
          <el-alert
            :title="`风险等级: ${selectedSuggestion.risk_assessment.level}`"
            :type="getRiskAlertType(selectedSuggestion.risk_assessment.level)"
            :description="selectedSuggestion.risk_assessment.mitigation"
            show-icon
          />
        </div>

        <!-- 适用条件 -->
        <div v-if="selectedSuggestion.applicable_conditions?.length" class="applicable-conditions">
          <h4>适用条件</h4>
          <ul>
            <li v-for="condition in selectedSuggestion.applicable_conditions" :key="condition">
              {{ condition }}
            </li>
          </ul>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="applySuggestionFromDialog"
            :loading="applyingIds.includes(selectedSuggestion?.id)"
          >
            应用建议
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  BrainIcon, 
  Refresh, 
  LightbulbIcon, 
  TrendCharts, 
  DataAnalysis, 
  Clock,
  Monitor
} from '@element-plus/icons-vue'
import decisionApi from '@/api/decision'
import stompService from '@/utils/stomp-service'

export default {
  name: 'DecisionSupportPanel',
  components: {
    BrainIcon,
    Refresh,
    LightbulbIcon,
    TrendCharts,
    DataAnalysis,
    Clock,
    Monitor
  },
  props: {
    simulationId: {
      type: String,
      required: true
    }
  },
  emits: ['suggestion-applied', 'decision-made'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const suggestions = ref([])
    const realtimeSupport = ref(null)
    const applyingIds = ref([])
    const showDetailDialog = ref(false)
    const selectedSuggestion = ref(null)
    
    // WebSocket订阅
    let decisionSubscription = null
    
    // 计算属性
    const alertTitle = computed(() => {
      if (!realtimeSupport.value) return '等待决策数据...'
      
      const level = realtimeSupport.value.alert_level
      const titles = {
        'green': '交通状况良好',
        'yellow': '交通状况一般',
        'orange': '交通拥堵',
        'red': '严重拥堵'
      }
      return titles[level] || '状态未知'
    })
    
    const alertType = computed(() => {
      if (!realtimeSupport.value) return 'info'
      
      const level = realtimeSupport.value.alert_level
      const types = {
        'green': 'success',
        'yellow': 'warning',
        'orange': 'warning',
        'red': 'error'
      }
      return types[level] || 'info'
    })
    
    const alertDescription = computed(() => {
      if (!realtimeSupport.value) return ''
      
      const congestion = realtimeSupport.value.current_congestion
      if (congestion) {
        return `整体拥堵评分: ${(congestion.congestion_score * 100).toFixed(0)}%`
      }
      return ''
    })
    
    // 方法
    const getPriorityTagType = (priority) => {
      const types = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'info'
      }
      return types[priority] || 'info'
    }

    const getPriorityText = (priority) => {
      const texts = {
        'high': '高优先级',
        'medium': '中优先级',
        'low': '低优先级'
      }
      return texts[priority] || '未知'
    }

    const getCongestionText = (level) => {
      const texts = {
        'low': '畅通',
        'medium': '一般',
        'high': '拥堵',
        'severe': '严重拥堵'
      }
      return texts[level] || '未知'
    }

    const getAlertText = (level) => {
      const texts = {
        'green': '正常',
        'yellow': '注意',
        'orange': '警告',
        'red': '紧急'
      }
      return texts[level] || '未知'
    }

    const getRiskAlertType = (level) => {
      const types = {
        'low': 'success',
        'medium': 'warning',
        'high': 'error'
      }
      return types[level] || 'info'
    }

    const loadSuggestions = async () => {
      try {
        loading.value = true

        // 这里应该获取当前交通数据
        const trafficData = {
          // 模拟交通数据
          directions: {
            north: { vehicleCount: 25, averageSpeed: 30, density: 0.6 },
            south: { vehicleCount: 30, averageSpeed: 25, density: 0.7 },
            east: { vehicleCount: 20, averageSpeed: 35, density: 0.5 },
            west: { vehicleCount: 28, averageSpeed: 28, density: 0.65 }
          }
        }

        const response = await decisionApi.generateSuggestions({
          simulation_id: props.simulationId,
          traffic_data: trafficData,
          current_conditions: {}
        })

        if (response.status === 'success') {
          suggestions.value = response.suggestions || []
        }

      } catch (error) {
        console.error('加载决策建议失败:', error)
        ElMessage.error('加载决策建议失败')
      } finally {
        loading.value = false
      }
    }

    const loadRealtimeSupport = async () => {
      try {
        const currentState = {
          // 模拟当前状态数据
          directions: {
            north: { vehicleCount: 25, averageSpeed: 30, density: 0.6 },
            south: { vehicleCount: 30, averageSpeed: 25, density: 0.7 },
            east: { vehicleCount: 20, averageSpeed: 35, density: 0.5 },
            west: { vehicleCount: 28, averageSpeed: 28, density: 0.65 }
          }
        }

        const response = await decisionApi.getRealtimeSupport({
          simulation_id: props.simulationId,
          current_state: currentState
        })

        if (response.status === 'success') {
          realtimeSupport.value = response.decision_support
        }

      } catch (error) {
        console.error('加载实时决策支持失败:', error)
      }
    }

    const refreshSuggestions = async () => {
      await Promise.all([loadSuggestions(), loadRealtimeSupport()])
    }

    const applySuggestion = async (suggestion) => {
      try {
        await ElMessageBox.confirm(
          `确定要应用建议"${suggestion.title}"吗？`,
          '确认应用',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        applyingIds.value.push(suggestion.id)

        const userId = localStorage.getItem('userId')
        const response = await decisionApi.applySuggestion(suggestion.id, {
          simulation_id: props.simulationId,
          user_id: userId
        })

        if (response.status === 'success') {
          ElMessage.success('决策建议应用成功')
          emit('suggestion-applied', suggestion)

          // 刷新建议列表
          await refreshSuggestions()
        }

      } catch (error) {
        if (error !== 'cancel') {
          console.error('应用决策建议失败:', error)
          ElMessage.error('应用决策建议失败')
        }
      } finally {
        applyingIds.value = applyingIds.value.filter(id => id !== suggestion.id)
      }
    }

    const viewSuggestionDetail = (suggestion) => {
      selectedSuggestion.value = suggestion
      showDetailDialog.value = true
    }

    const applySuggestionFromDialog = async () => {
      if (selectedSuggestion.value) {
        await applySuggestion(selectedSuggestion.value)
        showDetailDialog.value = false
      }
    }

    const setupWebSocketSubscription = async () => {
      try {
        await stompService.connect()

        decisionSubscription = await stompService.subscribe(
          `/topic/decision-support/${props.simulationId}`,
          (message) => {
            try {
              const data = JSON.parse(message.body)
              handleDecisionUpdate(data)
            } catch (error) {
              console.error('解析决策支持数据失败:', error)
            }
          }
        )

      } catch (error) {
        console.error('设置决策支持WebSocket订阅失败:', error)
      }
    }

    const handleDecisionUpdate = (data) => {
      if (data.type === 'decision_suggestions') {
        suggestions.value = data.suggestions || []
      } else if (data.type === 'realtime_decision_support') {
        realtimeSupport.value = data.decisionSupport
      } else if (data.type === 'decision_application_result') {
        ElMessage.success('决策应用结果已更新')
        refreshSuggestions()
      }
    }

    const cleanup = () => {
      if (decisionSubscription) {
        stompService.unsubscribe(decisionSubscription)
        decisionSubscription = null
      }
    }

    // 生命周期
    onMounted(async () => {
      await refreshSuggestions()
      await setupWebSocketSubscription()
    })

    onUnmounted(() => {
      cleanup()
    })

    return {
      loading,
      suggestions,
      realtimeSupport,
      applyingIds,
      showDetailDialog,
      selectedSuggestion,
      alertTitle,
      alertType,
      alertDescription,
      getPriorityTagType,
      getPriorityText,
      getCongestionText,
      getAlertText,
      getRiskAlertType,
      refreshSuggestions,
      applySuggestion,
      viewSuggestionDetail,
      applySuggestionFromDialog
    }
  }
}
</script>

<style scoped>
.decision-support-panel {
  width: 100%;
}

.panel-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.decision-status {
  margin-bottom: 20px;
}

.suggestions-section {
  margin-bottom: 20px;
}

.suggestions-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #2c3e50;
}

.no-suggestions {
  text-align: center;
  padding: 20px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.suggestion-item.priority-high {
  border-left: 4px solid #f56c6c;
}

.suggestion-item.priority-medium {
  border-left: 4px solid #e6a23c;
}

.suggestion-item.priority-low {
  border-left: 4px solid #909399;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.suggestion-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.title-text {
  font-weight: 600;
  color: #2c3e50;
}

.suggestion-content .description {
  margin: 0 0 12px 0;
  color: #606266;
  line-height: 1.5;
}

.suggestion-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.suggestion-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.realtime-support {
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.realtime-support h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #2c3e50;
}

.support-metric {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
}

.metric-value.congestion-low {
  color: #67c23a;
}

.metric-value.congestion-medium {
  color: #e6a23c;
}

.metric-value.congestion-high {
  color: #f56c6c;
}

.metric-value.congestion-severe {
  color: #f56c6c;
  animation: pulse 2s infinite;
}

.metric-value.alert-green {
  color: #67c23a;
}

.metric-value.alert-yellow {
  color: #e6a23c;
}

.metric-value.alert-orange {
  color: #f56c6c;
}

.metric-value.alert-red {
  color: #f56c6c;
  animation: pulse 2s infinite;
}

.recommended-actions {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 6px;
}

.recommended-actions h5 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.recommended-actions ul {
  margin: 0;
  padding-left: 20px;
}

.recommended-actions li {
  margin-bottom: 4px;
  color: #606266;
}

.suggestion-detail {
  margin-bottom: 20px;
}

.risk-assessment,
.applicable-conditions {
  margin-top: 20px;
}

.risk-assessment h4,
.applicable-conditions h4 {
  margin-bottom: 12px;
  color: #2c3e50;
}

.applicable-conditions ul {
  margin: 0;
  padding-left: 20px;
}

.applicable-conditions li {
  margin-bottom: 4px;
  color: #606266;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .suggestion-header {
    flex-direction: column;
    gap: 12px;
  }

  .suggestion-meta {
    flex-direction: column;
    gap: 8px;
  }

  .support-metric {
    margin-bottom: 12px;
  }
}
</style>
