<template>
  <div class="strategy-recommendation">
    <div class="strategy-header">
      <h4>交通管理建议</h4>
      <el-tag 
        :type="priorityTagType" 
        size="small"
        class="priority-tag"
      >
        {{ priorityText }}
      </el-tag>
    </div>
    
    <div class="strategy-content">
      <div class="strategy-main">
        <div class="strategy-icon">{{ strategy.icon }}</div>
        <div class="strategy-info">
          <div class="strategy-primary">{{ strategy.primary }}</div>
          <div class="strategy-secondary">{{ strategy.secondary }}</div>
        </div>
      </div>
      
      <div class="strategy-actions" v-if="showActions">
        <el-button 
          type="primary" 
          size="small"
          @click="handleApplyStrategy"
          :loading="applying"
        >
          应用建议
        </el-button>
        <el-button 
          size="small"
          @click="handleViewDetails"
        >
          查看详情
        </el-button>
      </div>
    </div>
    
    <!-- 详细信息展开区域 -->
    <el-collapse-transition>
      <div v-show="showDetails" class="strategy-details">
        <div class="detail-section">
          <h5>实施步骤:</h5>
          <ol class="implementation-steps">
            <li v-for="step in implementationSteps" :key="step">{{ step }}</li>
          </ol>
        </div>
        
        <div class="detail-section">
          <h5>预期效果:</h5>
          <ul class="expected-results">
            <li v-for="result in expectedResults" :key="result">{{ result }}</li>
          </ul>
        </div>
        
        <div class="detail-section" v-if="warnings.length > 0">
          <h5>注意事项:</h5>
          <ul class="warnings">
            <li v-for="warning in warnings" :key="warning" class="warning-item">
              <el-icon><Warning /></el-icon>
              {{ warning }}
            </li>
          </ul>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'

export default {
  name: 'StrategyRecommendation',
  components: {
    Warning
  },
  props: {
    strategy: {
      type: Object,
      required: true,
      default: () => ({
        primary: '',
        secondary: '',
        icon: '📋',
        priority: 'low'
      })
    },
    showActions: {
      type: Boolean,
      default: true
    },
    grade: {
      type: String,
      default: 'A'
    }
  },
  emits: ['apply-strategy', 'view-details'],
  setup(props, { emit }) {
    const applying = ref(false)
    const showDetails = ref(false)
    
    const priorityTagType = computed(() => {
      switch (props.strategy.priority) {
        case 'critical': return 'danger'
        case 'high': return 'warning'
        case 'medium': return 'primary'
        default: return 'success'
      }
    })
    
    const priorityText = computed(() => {
      switch (props.strategy.priority) {
        case 'critical': return '紧急'
        case 'high': return '高优先级'
        case 'medium': return '中优先级'
        default: return '低优先级'
      }
    })
    
    const implementationSteps = computed(() => {
      const stepMap = {
        A: ['保持当前信号配时', '继续常规监控'],
        B: ['维持现有交通管制', '准备应急预案'],
        C: ['调整信号灯配时', '增加交通引导标识', '启动实时监控'],
        D: ['延长主要方向绿灯时间', '部署交通协管员', '开启可变车道'],
        E: ['启动应急交通管制', '派遣现场交警', '发布交通预警'],
        F: ['实施全面交通管制', '启用替代路线', '协调应急部门']
      }
      return stepMap[props.grade] || stepMap.A
    })
    
    const expectedResults = computed(() => {
      const resultMap = {
        A: ['维持良好通行状态', '预防拥堵发生'],
        B: ['保持交通顺畅', '及时应对突发情况'],
        C: ['缓解轻微拥堵', '提高通行效率'],
        D: ['显著改善交通状况', '减少等待时间'],
        E: ['快速疏散车流', '恢复正常通行'],
        F: ['最大化道路通行能力', '避免交通瘫痪']
      }
      return resultMap[props.grade] || resultMap.A
    })
    
    const warnings = computed(() => {
      const warningMap = {
        A: [],
        B: ['注意监控车流变化'],
        C: ['避免过度调整信号配时'],
        D: ['确保其他方向车辆安全', '注意行人通行'],
        E: ['协调各部门行动', '确保应急车辆通行'],
        F: ['及时发布公告', '做好长期管制准备']
      }
      return warningMap[props.grade] || []
    })
    
    const handleApplyStrategy = async () => {
      applying.value = true
      try {
        // 模拟应用策略的过程
        await new Promise(resolve => setTimeout(resolve, 1500))
        ElMessage.success('策略应用成功')
        emit('apply-strategy', props.strategy)
      } catch (error) {
        ElMessage.error('策略应用失败')
      } finally {
        applying.value = false
      }
    }
    
    const handleViewDetails = () => {
      showDetails.value = !showDetails.value
      emit('view-details', { strategy: props.strategy, showDetails: showDetails.value })
    }
    
    return {
      applying,
      showDetails,
      priorityTagType,
      priorityText,
      implementationSteps,
      expectedResults,
      warnings,
      handleApplyStrategy,
      handleViewDetails
    }
  }
}
</script>

<style scoped>
.strategy-recommendation {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
}

.strategy-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.priority-tag {
  font-weight: 500;
}

.strategy-content {
  padding: 20px;
}

.strategy-main {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.strategy-icon {
  font-size: 32px;
  line-height: 1;
}

.strategy-info {
  flex: 1;
}

.strategy-primary {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.strategy-secondary {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.strategy-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.strategy-details {
  padding: 20px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.implementation-steps,
.expected-results,
.warnings {
  margin: 0;
  padding-left: 20px;
}

.implementation-steps li,
.expected-results li {
  margin-bottom: 8px;
  color: #666;
  line-height: 1.5;
}

.warnings {
  list-style: none;
  padding-left: 0;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #fa8c16;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .strategy-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .strategy-main {
    flex-direction: column;
    gap: 12px;
  }
  
  .strategy-actions {
    flex-direction: column;
  }
  
  .strategy-actions .el-button {
    width: 100%;
  }
}
</style>
