{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, reactive, onMounted, onUnmounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Traffic, Connection, Grid, Check, Refresh, Warning } from '@element-plus/icons-vue';\nimport stompService from '@/utils/stomp-service';\nimport TrafficLightDisplay from './TrafficLightDisplay.vue';\nexport default {\n  name: 'TrafficLightController',\n  components: {\n    TrafficLightDisplay,\n    Traffic,\n    Connection,\n    Grid,\n    Check,\n    Refresh,\n    Warning\n  },\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['status-change', 'control-change'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isConnected = ref(false);\n    const isManualMode = ref(false);\n    const lastUpdateTime = ref(null);\n    const logCollapsed = ref(['log']);\n\n    // 信号灯状态\n    const trafficLights = reactive({\n      north: {\n        red: true,\n        yellow: false,\n        green: false\n      },\n      south: {\n        red: true,\n        yellow: false,\n        green: false\n      },\n      east: {\n        red: false,\n        yellow: false,\n        green: true\n      },\n      west: {\n        red: false,\n        yellow: false,\n        green: true\n      }\n    });\n\n    // 当前相位信息\n    const currentPhase = reactive({\n      phaseId: '',\n      remainingTime: 0,\n      description: '',\n      nextPhase: ''\n    });\n\n    // 操作日志\n    const operationLogs = ref([]);\n\n    // WebSocket订阅\n    let trafficLightSubscription = null;\n\n    // 计算属性\n    const formatTime = computed(() => {\n      return timestamp => {\n        if (!timestamp) return '';\n        return new Date(timestamp).toLocaleTimeString();\n      };\n    });\n\n    // 方法\n    const addLog = (message, type = 'info') => {\n      operationLogs.value.unshift({\n        timestamp: new Date(),\n        message,\n        type\n      });\n\n      // 保持日志数量在合理范围内\n      if (operationLogs.value.length > 50) {\n        operationLogs.value = operationLogs.value.slice(0, 50);\n      }\n    };\n    const onModeChange = async manual => {\n      try {\n        addLog(`切换到${manual ? '手动' : '自动'}模式`, 'info');\n        if (!manual) {\n          // 切换到自动模式时重置所有手动设置\n          await resetToAuto();\n        }\n        emit('control-change', {\n          mode: manual ? 'manual' : 'auto'\n        });\n      } catch (error) {\n        console.error('模式切换失败:', error);\n        ElMessage.error('模式切换失败');\n        addLog(`模式切换失败: ${error.message}`, 'error');\n      }\n    };\n    const onManualLightChange = (direction, state) => {\n      if (!isManualMode.value) return;\n      trafficLights[direction] = {\n        ...state\n      };\n      addLog(`手动设置${direction}方向信号灯`, 'warning');\n    };\n    const applyManualControl = async () => {\n      try {\n        // 发送手动控制指令到后端\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/manual`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            lights: trafficLights,\n            timestamp: new Date().toISOString()\n          })\n        });\n        if (response.ok) {\n          ElMessage.success('手动控制设置已应用');\n          addLog('手动控制设置已应用', 'success');\n        } else {\n          throw new Error('应用手动控制失败');\n        }\n      } catch (error) {\n        console.error('应用手动控制失败:', error);\n        ElMessage.error('应用手动控制失败');\n        addLog(`应用手动控制失败: ${error.message}`, 'error');\n      }\n    };\n    const resetToAuto = async () => {\n      try {\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/auto`, {\n          method: 'POST'\n        });\n        if (response.ok) {\n          isManualMode.value = false;\n          ElMessage.success('已恢复自动控制');\n          addLog('已恢复自动控制', 'success');\n        } else {\n          throw new Error('恢复自动控制失败');\n        }\n      } catch (error) {\n        console.error('恢复自动控制失败:', error);\n        ElMessage.error('恢复自动控制失败');\n        addLog(`恢复自动控制失败: ${error.message}`, 'error');\n      }\n    };\n    const emergencyStop = async () => {\n      try {\n        await ElMessageBox.confirm('确定要执行紧急停止吗？这将使所有方向显示红灯。', '紧急停止确认', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/emergency`, {\n          method: 'POST'\n        });\n        if (response.ok) {\n          // 设置所有方向为红灯\n          Object.keys(trafficLights).forEach(direction => {\n            trafficLights[direction] = {\n              red: true,\n              yellow: false,\n              green: false\n            };\n          });\n          ElMessage.warning('紧急停止已执行，所有方向红灯');\n          addLog('执行紧急停止，所有方向红灯', 'error');\n        } else {\n          throw new Error('紧急停止执行失败');\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('紧急停止失败:', error);\n          ElMessage.error('紧急停止失败');\n          addLog(`紧急停止失败: ${error.message}`, 'error');\n        }\n      }\n    };\n    const setupWebSocketConnection = async () => {\n      try {\n        await stompService.connect();\n        isConnected.value = true;\n        addLog('WebSocket连接已建立', 'success');\n\n        // 订阅信号灯状态更新\n        trafficLightSubscription = await stompService.subscribe(`/topic/simulation/${props.simulationId}/traffic-lights`, message => {\n          try {\n            const data = JSON.parse(message.body);\n            handleTrafficLightUpdate(data);\n          } catch (error) {\n            console.error('解析信号灯数据失败:', error);\n          }\n        });\n        emit('status-change', {\n          connected: true\n        });\n      } catch (error) {\n        console.error('WebSocket连接失败:', error);\n        isConnected.value = false;\n        addLog(`WebSocket连接失败: ${error.message}`, 'error');\n        emit('status-change', {\n          connected: false\n        });\n      }\n    };\n    const handleTrafficLightUpdate = data => {\n      if (!data) return;\n\n      // 更新信号灯状态\n      if (data.lights) {\n        Object.keys(data.lights).forEach(direction => {\n          if (trafficLights[direction]) {\n            trafficLights[direction] = {\n              ...data.lights[direction]\n            };\n          }\n        });\n      }\n\n      // 更新相位信息\n      if (data.phase) {\n        Object.assign(currentPhase, data.phase);\n      }\n      lastUpdateTime.value = new Date();\n\n      // 如果是自动模式下的更新，记录日志\n      if (!isManualMode.value && data.source === 'auto') {\n        addLog(`自动更新: ${data.phase?.description || '信号灯状态更新'}`, 'info');\n      }\n    };\n    const cleanup = () => {\n      if (trafficLightSubscription) {\n        stompService.unsubscribe(trafficLightSubscription);\n        trafficLightSubscription = null;\n      }\n      if (stompService.isConnected()) {\n        stompService.disconnect();\n      }\n      isConnected.value = false;\n    };\n\n    // 生命周期钩子\n    onMounted(() => {\n      setupWebSocketConnection();\n      addLog('信号灯控制器已初始化', 'info');\n    });\n    onUnmounted(() => {\n      cleanup();\n    });\n    return {\n      isConnected,\n      isManualMode,\n      lastUpdateTime,\n      logCollapsed,\n      trafficLights,\n      currentPhase,\n      operationLogs,\n      formatTime,\n      onModeChange,\n      onManualLightChange,\n      applyManualControl,\n      resetToAuto,\n      emergencyStop\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "onUnmounted", "computed", "ElMessage", "ElMessageBox", "Traffic", "Connection", "Grid", "Check", "Refresh", "Warning", "stompService", "TrafficLightDisplay", "name", "components", "props", "simulationId", "type", "String", "required", "emits", "setup", "emit", "isConnected", "isManualMode", "lastUpdateTime", "logCollapsed", "trafficLights", "north", "red", "yellow", "green", "south", "east", "west", "currentPhase", "phaseId", "remainingTime", "description", "nextPhase", "operationLogs", "trafficLightSubscription", "formatTime", "timestamp", "Date", "toLocaleTimeString", "addLog", "message", "value", "unshift", "length", "slice", "onModeChange", "manual", "resetToAuto", "mode", "error", "console", "onManualLightChange", "direction", "state", "applyManualControl", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "lights", "toISOString", "ok", "success", "Error", "emergencyStop", "confirm", "confirmButtonText", "cancelButtonText", "Object", "keys", "for<PERSON>ach", "warning", "setupWebSocketConnection", "connect", "subscribe", "data", "parse", "handleTrafficLightUpdate", "connected", "phase", "assign", "source", "cleanup", "unsubscribe", "disconnect"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\TrafficLightController.vue"], "sourcesContent": ["<template>\n  <div class=\"traffic-light-controller\">\n    <el-card class=\"controller-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <span class=\"title\">\n            <el-icon><Traffic /></el-icon>\n            信号灯控制器\n          </span>\n          <div class=\"control-mode\">\n            <el-switch\n              v-model=\"isManualMode\"\n              :disabled=\"!isConnected\"\n              active-text=\"手动\"\n              inactive-text=\"自动\"\n              @change=\"onModeChange\"\n            />\n          </div>\n        </div>\n      </template>\n\n      <!-- 连接状态 -->\n      <div class=\"connection-status\">\n        <el-tag :type=\"isConnected ? 'success' : 'danger'\" size=\"small\">\n          <el-icon><Connection /></el-icon>\n          {{ isConnected ? '已连接' : '未连接' }}\n        </el-tag>\n        <span class=\"last-update\" v-if=\"lastUpdateTime\">\n          最后更新: {{ formatTime(lastUpdateTime) }}\n        </span>\n      </div>\n\n      <!-- 十字路口信号灯状态 -->\n      <div class=\"intersection-layout\">\n        <!-- 北方向 -->\n        <div class=\"direction north\">\n          <div class=\"direction-label\">北</div>\n          <traffic-light-display\n            :state=\"trafficLights.north\"\n            :is-manual=\"isManualMode\"\n            direction=\"north\"\n            @manual-change=\"onManualLightChange\"\n          />\n        </div>\n\n        <!-- 西方向 -->\n        <div class=\"direction west\">\n          <div class=\"direction-label\">西</div>\n          <traffic-light-display\n            :state=\"trafficLights.west\"\n            :is-manual=\"isManualMode\"\n            direction=\"west\"\n            @manual-change=\"onManualLightChange\"\n          />\n        </div>\n\n        <!-- 中央路口 -->\n        <div class=\"intersection-center\">\n          <el-icon class=\"intersection-icon\"><Grid /></el-icon>\n        </div>\n\n        <!-- 东方向 -->\n        <div class=\"direction east\">\n          <div class=\"direction-label\">东</div>\n          <traffic-light-display\n            :state=\"trafficLights.east\"\n            :is-manual=\"isManualMode\"\n            direction=\"east\"\n            @manual-change=\"onManualLightChange\"\n          />\n        </div>\n\n        <!-- 南方向 -->\n        <div class=\"direction south\">\n          <div class=\"direction-label\">南</div>\n          <traffic-light-display\n            :state=\"trafficLights.south\"\n            :is-manual=\"isManualMode\"\n            direction=\"south\"\n            @manual-change=\"onManualLightChange\"\n          />\n        </div>\n      </div>\n\n      <!-- 当前相位信息 -->\n      <div class=\"phase-info\">\n        <el-descriptions title=\"当前相位信息\" :column=\"2\" size=\"small\" border>\n          <el-descriptions-item label=\"相位ID\">\n            {{ currentPhase.phaseId || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"剩余时间\">\n            {{ currentPhase.remainingTime || 0 }}秒\n          </el-descriptions-item>\n          <el-descriptions-item label=\"相位描述\">\n            {{ currentPhase.description || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"下一相位\">\n            {{ currentPhase.nextPhase || '-' }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n\n      <!-- 控制按钮 -->\n      <div class=\"control-buttons\" v-if=\"isManualMode\">\n        <el-button-group>\n          <el-button \n            type=\"success\" \n            :disabled=\"!isConnected\"\n            @click=\"applyManualControl\"\n          >\n            <el-icon><Check /></el-icon>\n            应用设置\n          </el-button>\n          <el-button \n            type=\"warning\" \n            :disabled=\"!isConnected\"\n            @click=\"resetToAuto\"\n          >\n            <el-icon><Refresh /></el-icon>\n            恢复自动\n          </el-button>\n          <el-button \n            type=\"danger\" \n            :disabled=\"!isConnected\"\n            @click=\"emergencyStop\"\n          >\n            <el-icon><Warning /></el-icon>\n            紧急停止\n          </el-button>\n        </el-button-group>\n      </div>\n\n      <!-- 操作日志 -->\n      <div class=\"operation-log\">\n        <el-collapse v-model=\"logCollapsed\">\n          <el-collapse-item title=\"操作日志\" name=\"log\">\n            <div class=\"log-container\">\n              <div \n                v-for=\"(log, index) in operationLogs\" \n                :key=\"index\"\n                class=\"log-item\"\n                :class=\"log.type\"\n              >\n                <span class=\"log-time\">{{ formatTime(log.timestamp) }}</span>\n                <span class=\"log-message\">{{ log.message }}</span>\n              </div>\n            </div>\n          </el-collapse-item>\n        </el-collapse>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { \n  Traffic, \n  Connection, \n  Grid, \n  Check, \n  Refresh, \n  Warning \n} from '@element-plus/icons-vue'\nimport stompService from '@/utils/stomp-service'\nimport TrafficLightDisplay from './TrafficLightDisplay.vue'\n\nexport default {\n  name: 'TrafficLightController',\n  components: {\n    TrafficLightDisplay,\n    Traffic,\n    Connection,\n    Grid,\n    Check,\n    Refresh,\n    Warning\n  },\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['status-change', 'control-change'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isConnected = ref(false)\n    const isManualMode = ref(false)\n    const lastUpdateTime = ref(null)\n    const logCollapsed = ref(['log'])\n    \n    // 信号灯状态\n    const trafficLights = reactive({\n      north: { red: true, yellow: false, green: false },\n      south: { red: true, yellow: false, green: false },\n      east: { red: false, yellow: false, green: true },\n      west: { red: false, yellow: false, green: true }\n    })\n    \n    // 当前相位信息\n    const currentPhase = reactive({\n      phaseId: '',\n      remainingTime: 0,\n      description: '',\n      nextPhase: ''\n    })\n    \n    // 操作日志\n    const operationLogs = ref([])\n    \n    // WebSocket订阅\n    let trafficLightSubscription = null\n    \n    // 计算属性\n    const formatTime = computed(() => {\n      return (timestamp) => {\n        if (!timestamp) return ''\n        return new Date(timestamp).toLocaleTimeString()\n      }\n    })\n    \n    // 方法\n    const addLog = (message, type = 'info') => {\n      operationLogs.value.unshift({\n        timestamp: new Date(),\n        message,\n        type\n      })\n      \n      // 保持日志数量在合理范围内\n      if (operationLogs.value.length > 50) {\n        operationLogs.value = operationLogs.value.slice(0, 50)\n      }\n    }\n    \n    const onModeChange = async (manual) => {\n      try {\n        addLog(`切换到${manual ? '手动' : '自动'}模式`, 'info')\n        \n        if (!manual) {\n          // 切换到自动模式时重置所有手动设置\n          await resetToAuto()\n        }\n        \n        emit('control-change', { mode: manual ? 'manual' : 'auto' })\n        \n      } catch (error) {\n        console.error('模式切换失败:', error)\n        ElMessage.error('模式切换失败')\n        addLog(`模式切换失败: ${error.message}`, 'error')\n      }\n    }\n    \n    const onManualLightChange = (direction, state) => {\n      if (!isManualMode.value) return\n      \n      trafficLights[direction] = { ...state }\n      addLog(`手动设置${direction}方向信号灯`, 'warning')\n    }\n    \n    const applyManualControl = async () => {\n      try {\n        // 发送手动控制指令到后端\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/manual`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            lights: trafficLights,\n            timestamp: new Date().toISOString()\n          })\n        })\n        \n        if (response.ok) {\n          ElMessage.success('手动控制设置已应用')\n          addLog('手动控制设置已应用', 'success')\n        } else {\n          throw new Error('应用手动控制失败')\n        }\n        \n      } catch (error) {\n        console.error('应用手动控制失败:', error)\n        ElMessage.error('应用手动控制失败')\n        addLog(`应用手动控制失败: ${error.message}`, 'error')\n      }\n    }\n    \n    const resetToAuto = async () => {\n      try {\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/auto`, {\n          method: 'POST'\n        })\n        \n        if (response.ok) {\n          isManualMode.value = false\n          ElMessage.success('已恢复自动控制')\n          addLog('已恢复自动控制', 'success')\n        } else {\n          throw new Error('恢复自动控制失败')\n        }\n        \n      } catch (error) {\n        console.error('恢复自动控制失败:', error)\n        ElMessage.error('恢复自动控制失败')\n        addLog(`恢复自动控制失败: ${error.message}`, 'error')\n      }\n    }\n    \n    const emergencyStop = async () => {\n      try {\n        await ElMessageBox.confirm(\n          '确定要执行紧急停止吗？这将使所有方向显示红灯。',\n          '紧急停止确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/emergency`, {\n          method: 'POST'\n        })\n\n        if (response.ok) {\n          // 设置所有方向为红灯\n          Object.keys(trafficLights).forEach(direction => {\n            trafficLights[direction] = { red: true, yellow: false, green: false }\n          })\n\n          ElMessage.warning('紧急停止已执行，所有方向红灯')\n          addLog('执行紧急停止，所有方向红灯', 'error')\n        } else {\n          throw new Error('紧急停止执行失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('紧急停止失败:', error)\n          ElMessage.error('紧急停止失败')\n          addLog(`紧急停止失败: ${error.message}`, 'error')\n        }\n      }\n    }\n\n    const setupWebSocketConnection = async () => {\n      try {\n        await stompService.connect()\n        isConnected.value = true\n        addLog('WebSocket连接已建立', 'success')\n\n        // 订阅信号灯状态更新\n        trafficLightSubscription = await stompService.subscribe(\n          `/topic/simulation/${props.simulationId}/traffic-lights`,\n          (message) => {\n            try {\n              const data = JSON.parse(message.body)\n              handleTrafficLightUpdate(data)\n            } catch (error) {\n              console.error('解析信号灯数据失败:', error)\n            }\n          }\n        )\n\n        emit('status-change', { connected: true })\n\n      } catch (error) {\n        console.error('WebSocket连接失败:', error)\n        isConnected.value = false\n        addLog(`WebSocket连接失败: ${error.message}`, 'error')\n        emit('status-change', { connected: false })\n      }\n    }\n\n    const handleTrafficLightUpdate = (data) => {\n      if (!data) return\n\n      // 更新信号灯状态\n      if (data.lights) {\n        Object.keys(data.lights).forEach(direction => {\n          if (trafficLights[direction]) {\n            trafficLights[direction] = { ...data.lights[direction] }\n          }\n        })\n      }\n\n      // 更新相位信息\n      if (data.phase) {\n        Object.assign(currentPhase, data.phase)\n      }\n\n      lastUpdateTime.value = new Date()\n\n      // 如果是自动模式下的更新，记录日志\n      if (!isManualMode.value && data.source === 'auto') {\n        addLog(`自动更新: ${data.phase?.description || '信号灯状态更新'}`, 'info')\n      }\n    }\n\n    const cleanup = () => {\n      if (trafficLightSubscription) {\n        stompService.unsubscribe(trafficLightSubscription)\n        trafficLightSubscription = null\n      }\n\n      if (stompService.isConnected()) {\n        stompService.disconnect()\n      }\n\n      isConnected.value = false\n    }\n\n    // 生命周期钩子\n    onMounted(() => {\n      setupWebSocketConnection()\n      addLog('信号灯控制器已初始化', 'info')\n    })\n\n    onUnmounted(() => {\n      cleanup()\n    })\n\n    return {\n      isConnected,\n      isManualMode,\n      lastUpdateTime,\n      logCollapsed,\n      trafficLights,\n      currentPhase,\n      operationLogs,\n      formatTime,\n      onModeChange,\n      onManualLightChange,\n      applyManualControl,\n      resetToAuto,\n      emergencyStop\n    }\n  }\n}\n</script>\n\n<style scoped>\n.traffic-light-controller {\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.controller-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 18px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.control-mode {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.connection-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.last-update {\n  font-size: 12px;\n  color: #6c757d;\n}\n\n.intersection-layout {\n  position: relative;\n  display: grid;\n  grid-template-columns: 1fr 120px 1fr;\n  grid-template-rows: 1fr 120px 1fr;\n  gap: 20px;\n  margin: 30px 0;\n  min-height: 300px;\n}\n\n.direction {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n}\n\n.direction.north {\n  grid-column: 2;\n  grid-row: 1;\n}\n\n.direction.south {\n  grid-column: 2;\n  grid-row: 3;\n}\n\n.direction.west {\n  grid-column: 1;\n  grid-row: 2;\n}\n\n.direction.east {\n  grid-column: 3;\n  grid-row: 2;\n}\n\n.direction-label {\n  font-weight: 600;\n  font-size: 14px;\n  color: #495057;\n  background: #e9ecef;\n  padding: 4px 12px;\n  border-radius: 20px;\n}\n\n.intersection-center {\n  grid-column: 2;\n  grid-row: 2;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 12px;\n}\n\n.intersection-icon {\n  font-size: 48px;\n  color: #6c757d;\n}\n\n.phase-info {\n  margin: 20px 0;\n}\n\n.control-buttons {\n  margin: 20px 0;\n  text-align: center;\n}\n\n.operation-log {\n  margin-top: 20px;\n}\n\n.log-container {\n  max-height: 200px;\n  overflow-y: auto;\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 10px;\n}\n\n.log-item {\n  display: flex;\n  gap: 10px;\n  padding: 4px 0;\n  border-bottom: 1px solid #e9ecef;\n  font-size: 12px;\n}\n\n.log-item:last-child {\n  border-bottom: none;\n}\n\n.log-time {\n  color: #6c757d;\n  min-width: 80px;\n}\n\n.log-message {\n  flex: 1;\n}\n\n.log-item.success .log-message {\n  color: #28a745;\n}\n\n.log-item.warning .log-message {\n  color: #ffc107;\n}\n\n.log-item.error .log-message {\n  color: #dc3545;\n}\n\n.log-item.info .log-message {\n  color: #17a2b8;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .intersection-layout {\n    grid-template-columns: 80px 100px 80px;\n    grid-template-rows: 80px 100px 80px;\n    gap: 10px;\n    min-height: 260px;\n  }\n\n  .intersection-icon {\n    font-size: 32px;\n  }\n\n  .direction-label {\n    font-size: 12px;\n    padding: 2px 8px;\n  }\n}\n</style>\n"], "mappings": ";;AA2JA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAO,QAAS,KAAI;AACpE,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SACEC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,OAAM,QACD,yBAAwB;AAC/B,OAAOC,YAAW,MAAO,uBAAsB;AAC/C,OAAOC,mBAAkB,MAAO,2BAA0B;AAE1D,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IACVF,mBAAmB;IACnBP,OAAO;IACPC,UAAU;IACVC,IAAI;IACJC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC;EACDK,KAAK,EAAE;IACLC,YAAY,EAAE;MACZC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,eAAe,EAAE,gBAAgB,CAAC;EAC1CC,KAAKA,CAACN,KAAK,EAAE;IAAEO;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,WAAU,GAAIzB,GAAG,CAAC,KAAK;IAC7B,MAAM0B,YAAW,GAAI1B,GAAG,CAAC,KAAK;IAC9B,MAAM2B,cAAa,GAAI3B,GAAG,CAAC,IAAI;IAC/B,MAAM4B,YAAW,GAAI5B,GAAG,CAAC,CAAC,KAAK,CAAC;;IAEhC;IACA,MAAM6B,aAAY,GAAI5B,QAAQ,CAAC;MAC7B6B,KAAK,EAAE;QAAEC,GAAG,EAAE,IAAI;QAAEC,MAAM,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAM,CAAC;MACjDC,KAAK,EAAE;QAAEH,GAAG,EAAE,IAAI;QAAEC,MAAM,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAM,CAAC;MACjDE,IAAI,EAAE;QAAEJ,GAAG,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAK,CAAC;MAChDG,IAAI,EAAE;QAAEL,GAAG,EAAE,KAAK;QAAEC,MAAM,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAK;IACjD,CAAC;;IAED;IACA,MAAMI,YAAW,GAAIpC,QAAQ,CAAC;MAC5BqC,OAAO,EAAE,EAAE;MACXC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE;IACb,CAAC;;IAED;IACA,MAAMC,aAAY,GAAI1C,GAAG,CAAC,EAAE;;IAE5B;IACA,IAAI2C,wBAAuB,GAAI,IAAG;;IAElC;IACA,MAAMC,UAAS,GAAIxC,QAAQ,CAAC,MAAM;MAChC,OAAQyC,SAAS,IAAK;QACpB,IAAI,CAACA,SAAS,EAAE,OAAO,EAAC;QACxB,OAAO,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,kBAAkB,CAAC;MAChD;IACF,CAAC;;IAED;IACA,MAAMC,MAAK,GAAIA,CAACC,OAAO,EAAE9B,IAAG,GAAI,MAAM,KAAK;MACzCuB,aAAa,CAACQ,KAAK,CAACC,OAAO,CAAC;QAC1BN,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBG,OAAO;QACP9B;MACF,CAAC;;MAED;MACA,IAAIuB,aAAa,CAACQ,KAAK,CAACE,MAAK,GAAI,EAAE,EAAE;QACnCV,aAAa,CAACQ,KAAI,GAAIR,aAAa,CAACQ,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,EAAE;MACvD;IACF;IAEA,MAAMC,YAAW,GAAI,MAAOC,MAAM,IAAK;MACrC,IAAI;QACFP,MAAM,CAAC,MAAMO,MAAK,GAAI,IAAG,GAAI,IAAI,IAAI,EAAE,MAAM;QAE7C,IAAI,CAACA,MAAM,EAAE;UACX;UACA,MAAMC,WAAW,CAAC;QACpB;QAEAhC,IAAI,CAAC,gBAAgB,EAAE;UAAEiC,IAAI,EAAEF,MAAK,GAAI,QAAO,GAAI;QAAO,CAAC;MAE7D,EAAE,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BrD,SAAS,CAACqD,KAAK,CAAC,QAAQ;QACxBV,MAAM,CAAC,WAAWU,KAAK,CAACT,OAAO,EAAE,EAAE,OAAO;MAC5C;IACF;IAEA,MAAMW,mBAAkB,GAAIA,CAACC,SAAS,EAAEC,KAAK,KAAK;MAChD,IAAI,CAACpC,YAAY,CAACwB,KAAK,EAAE;MAEzBrB,aAAa,CAACgC,SAAS,IAAI;QAAE,GAAGC;MAAM;MACtCd,MAAM,CAAC,OAAOa,SAAS,OAAO,EAAE,SAAS;IAC3C;IAEA,MAAME,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF;QACA,MAAMC,QAAO,GAAI,MAAMC,KAAK,CAAC,mBAAmBhD,KAAK,CAACC,YAAY,wBAAwB,EAAE;UAC1FgD,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,MAAM,EAAE1C,aAAa;YACrBgB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC0B,WAAW,CAAC;UACpC,CAAC;QACH,CAAC;QAED,IAAIR,QAAQ,CAACS,EAAE,EAAE;UACfpE,SAAS,CAACqE,OAAO,CAAC,WAAW;UAC7B1B,MAAM,CAAC,WAAW,EAAE,SAAS;QAC/B,OAAO;UACL,MAAM,IAAI2B,KAAK,CAAC,UAAU;QAC5B;MAEF,EAAE,OAAOjB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCrD,SAAS,CAACqD,KAAK,CAAC,UAAU;QAC1BV,MAAM,CAAC,aAAaU,KAAK,CAACT,OAAO,EAAE,EAAE,OAAO;MAC9C;IACF;IAEA,MAAMO,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMQ,QAAO,GAAI,MAAMC,KAAK,CAAC,mBAAmBhD,KAAK,CAACC,YAAY,sBAAsB,EAAE;UACxFgD,MAAM,EAAE;QACV,CAAC;QAED,IAAIF,QAAQ,CAACS,EAAE,EAAE;UACf/C,YAAY,CAACwB,KAAI,GAAI,KAAI;UACzB7C,SAAS,CAACqE,OAAO,CAAC,SAAS;UAC3B1B,MAAM,CAAC,SAAS,EAAE,SAAS;QAC7B,OAAO;UACL,MAAM,IAAI2B,KAAK,CAAC,UAAU;QAC5B;MAEF,EAAE,OAAOjB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCrD,SAAS,CAACqD,KAAK,CAAC,UAAU;QAC1BV,MAAM,CAAC,aAAaU,KAAK,CAACT,OAAO,EAAE,EAAE,OAAO;MAC9C;IACF;IAEA,MAAM2B,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMtE,YAAY,CAACuE,OAAO,CACxB,yBAAyB,EACzB,QAAQ,EACR;UACEC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtB5D,IAAI,EAAE;QACR,CACF;QAEA,MAAM6C,QAAO,GAAI,MAAMC,KAAK,CAAC,mBAAmBhD,KAAK,CAACC,YAAY,2BAA2B,EAAE;UAC7FgD,MAAM,EAAE;QACV,CAAC;QAED,IAAIF,QAAQ,CAACS,EAAE,EAAE;UACf;UACAO,MAAM,CAACC,IAAI,CAACpD,aAAa,CAAC,CAACqD,OAAO,CAACrB,SAAQ,IAAK;YAC9ChC,aAAa,CAACgC,SAAS,IAAI;cAAE9B,GAAG,EAAE,IAAI;cAAEC,MAAM,EAAE,KAAK;cAAEC,KAAK,EAAE;YAAM;UACtE,CAAC;UAED5B,SAAS,CAAC8E,OAAO,CAAC,gBAAgB;UAClCnC,MAAM,CAAC,eAAe,EAAE,OAAO;QACjC,OAAO;UACL,MAAM,IAAI2B,KAAK,CAAC,UAAU;QAC5B;MAEF,EAAE,OAAOjB,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BrD,SAAS,CAACqD,KAAK,CAAC,QAAQ;UACxBV,MAAM,CAAC,WAAWU,KAAK,CAACT,OAAO,EAAE,EAAE,OAAO;QAC5C;MACF;IACF;IAEA,MAAMmC,wBAAuB,GAAI,MAAAA,CAAA,KAAY;MAC3C,IAAI;QACF,MAAMvE,YAAY,CAACwE,OAAO,CAAC;QAC3B5D,WAAW,CAACyB,KAAI,GAAI,IAAG;QACvBF,MAAM,CAAC,gBAAgB,EAAE,SAAS;;QAElC;QACAL,wBAAuB,GAAI,MAAM9B,YAAY,CAACyE,SAAS,CACrD,qBAAqBrE,KAAK,CAACC,YAAY,iBAAiB,EACvD+B,OAAO,IAAK;UACX,IAAI;YACF,MAAMsC,IAAG,GAAIlB,IAAI,CAACmB,KAAK,CAACvC,OAAO,CAACmB,IAAI;YACpCqB,wBAAwB,CAACF,IAAI;UAC/B,EAAE,OAAO7B,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK;UACnC;QACF,CACF;QAEAlC,IAAI,CAAC,eAAe,EAAE;UAAEkE,SAAS,EAAE;QAAK,CAAC;MAE3C,EAAE,OAAOhC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK;QACrCjC,WAAW,CAACyB,KAAI,GAAI,KAAI;QACxBF,MAAM,CAAC,kBAAkBU,KAAK,CAACT,OAAO,EAAE,EAAE,OAAO;QACjDzB,IAAI,CAAC,eAAe,EAAE;UAAEkE,SAAS,EAAE;QAAM,CAAC;MAC5C;IACF;IAEA,MAAMD,wBAAuB,GAAKF,IAAI,IAAK;MACzC,IAAI,CAACA,IAAI,EAAE;;MAEX;MACA,IAAIA,IAAI,CAAChB,MAAM,EAAE;QACfS,MAAM,CAACC,IAAI,CAACM,IAAI,CAAChB,MAAM,CAAC,CAACW,OAAO,CAACrB,SAAQ,IAAK;UAC5C,IAAIhC,aAAa,CAACgC,SAAS,CAAC,EAAE;YAC5BhC,aAAa,CAACgC,SAAS,IAAI;cAAE,GAAG0B,IAAI,CAAChB,MAAM,CAACV,SAAS;YAAE;UACzD;QACF,CAAC;MACH;;MAEA;MACA,IAAI0B,IAAI,CAACI,KAAK,EAAE;QACdX,MAAM,CAACY,MAAM,CAACvD,YAAY,EAAEkD,IAAI,CAACI,KAAK;MACxC;MAEAhE,cAAc,CAACuB,KAAI,GAAI,IAAIJ,IAAI,CAAC;;MAEhC;MACA,IAAI,CAACpB,YAAY,CAACwB,KAAI,IAAKqC,IAAI,CAACM,MAAK,KAAM,MAAM,EAAE;QACjD7C,MAAM,CAAC,SAASuC,IAAI,CAACI,KAAK,EAAEnD,WAAU,IAAK,SAAS,EAAE,EAAE,MAAM;MAChE;IACF;IAEA,MAAMsD,OAAM,GAAIA,CAAA,KAAM;MACpB,IAAInD,wBAAwB,EAAE;QAC5B9B,YAAY,CAACkF,WAAW,CAACpD,wBAAwB;QACjDA,wBAAuB,GAAI,IAAG;MAChC;MAEA,IAAI9B,YAAY,CAACY,WAAW,CAAC,CAAC,EAAE;QAC9BZ,YAAY,CAACmF,UAAU,CAAC;MAC1B;MAEAvE,WAAW,CAACyB,KAAI,GAAI,KAAI;IAC1B;;IAEA;IACAhD,SAAS,CAAC,MAAM;MACdkF,wBAAwB,CAAC;MACzBpC,MAAM,CAAC,YAAY,EAAE,MAAM;IAC7B,CAAC;IAED7C,WAAW,CAAC,MAAM;MAChB2F,OAAO,CAAC;IACV,CAAC;IAED,OAAO;MACLrE,WAAW;MACXC,YAAY;MACZC,cAAc;MACdC,YAAY;MACZC,aAAa;MACbQ,YAAY;MACZK,aAAa;MACbE,UAAU;MACVU,YAAY;MACZM,mBAAmB;MACnBG,kBAAkB;MAClBP,WAAW;MACXoB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}