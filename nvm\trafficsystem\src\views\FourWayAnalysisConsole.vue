<template>
  <div class="four-way-analysis-console">
    <!-- 页面头部 -->
    <div class="console-header">
      <div class="header-content">
        <h1 class="console-title">
          <el-icon><Grid /></el-icon>
          四方向智能交通分析控制台
        </h1>
        <p class="console-description">
          集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台
        </p>
      </div>
      
      <div class="header-stats">
        <div class="stat-item">
          <div class="stat-value">{{ totalTasks }}</div>
          <div class="stat-label">总任务数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ activeTasks }}</div>
          <div class="stat-label">活跃任务</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ completedTasks }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
    </div>

    <!-- 工作流程导航 -->
    <div class="workflow-navigation">
      <el-steps :active="currentStep" align-center>
        <el-step
          title="视频上传"
          description="上传四方向视频文件"
          icon="Upload"
        />
        <el-step
          title="实时检测"
          description="AI模型实时分析"
          icon="VideoCamera"
        />
        <el-step
          title="智能分析"
          description="生成分析结果"
          icon="DataAnalysis"
        />
        <el-step
          title="仿真优化"
          description="SUMO仿真与优化"
          icon="Setting"
        />
        <el-step
          title="报告生成"
          description="导出分析报告"
          icon="Document"
        />
      </el-steps>
    </div>

    <!-- 主要内容区域 -->
    <div class="console-content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <!-- 步骤导航器 -->
        <StepNavigator
          :current-step="currentStep"
          :task-status="currentTask.status"
          :task-progress="currentTask.progress"
          :loading="isInitializing"
          :allow-navigation="true"
          @step-change="handleStepChange"
          @action="handleQuickAction"
          @refresh="handleRefreshStatus"
        />
      </div>

      <!-- 右侧主内容 -->
      <div class="main-content">
        <!-- 当前任务信息 -->
        <div v-if="currentTask" class="current-task-info">
          <el-card>
            <template #header>
              <div class="task-header">
                <div class="task-title-section">
                  <h3>{{ currentTask.name }}</h3>
                  <el-tag :type="getTaskStatusType(currentTask.status)">
                    {{ getTaskStatusText(currentTask.status) }}
                  </el-tag>
                </div>
                <div class="task-progress-section">
                  <el-progress 
                    :percentage="currentTask.progress" 
                    :status="getProgressStatus(currentTask.status)"
                    :stroke-width="8"
                  />
                  <span class="progress-text">{{ currentTask.progress }}%</span>
                </div>
              </div>
            </template>
            
            <div class="task-details">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="detail-item">
                    <span class="detail-label">任务ID:</span>
                    <span class="detail-value">{{ currentTask.id }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="detail-item">
                    <span class="detail-label">创建时间:</span>
                    <span class="detail-value">{{ formatTime(currentTask.createdAt) }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="detail-item">
                    <span class="detail-label">处理时长:</span>
                    <span class="detail-value">{{ getProcessingDuration(currentTask) }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>

        <!-- 动态内容区域 -->
        <div class="dynamic-content">
          <!-- 初始化加载状态 -->
          <div v-if="isInitializing" class="loading-container">
            <el-skeleton :rows="8" animated>
              <template #template>
                <div class="loading-content">
                  <el-skeleton-item variant="h1" style="width: 40%; margin-bottom: 20px;" />
                  <el-skeleton-item variant="text" style="width: 80%; margin-bottom: 10px;" />
                  <el-skeleton-item variant="text" style="width: 60%; margin-bottom: 20px;" />
                  <el-skeleton-item variant="rect" style="width: 100%; height: 200px;" />
                </div>
              </template>
            </el-skeleton>
            <div class="loading-text">
              <el-icon class="loading-icon"><Loading /></el-icon>
              正在初始化页面状态...
            </div>
          </div>

          <!-- 步骤1: 视频上传 -->
          <div v-else-if="currentStep === 0" class="step-content">
            <FourWayVideoUpload
              @upload-success="handleUploadSuccess"
              @upload-error="handleUploadError"
              @upload-progress="handleUploadProgress"
              @status-change="handleUploadStatusChange"
            />
          </div>

          <!-- 步骤2: 实时检测 -->
          <div v-if="currentStep === 1" class="step-content">
            <FourWayRealtimeViewer
              v-if="currentTaskId"
              :task-id="currentTaskId"
              :auto-start="true"
              @detection-update="handleDetectionUpdate"
              @status-change="handleDetectionStatusChange"
              @analysis-complete="handleAnalysisComplete"
            />
            <el-empty v-else description="请先上传视频文件">
              <el-button type="primary" @click="currentStep = 0">
                返回上传
              </el-button>
            </el-empty>
          </div>

          <!-- 步骤3: 智能分析 -->
          <div v-if="currentStep === 2" class="step-content">
            <TrafficAnalysisDashboard
              v-if="currentTaskId"
              :task-id="currentTaskId"
              @data-updated="handleAnalysisDataUpdate"
            />
            <el-empty v-else description="请先完成视频检测">
              <el-button type="primary" @click="currentStep = 1">
                返回检测
              </el-button>
            </el-empty>
          </div>

          <!-- 步骤4: SUMO仿真优化 -->
          <div v-if="currentStep === 3" class="step-content">
            <div class="simulation-container">
              <!-- 仿真控制面板 -->
              <SimulationControlPanel
                v-if="currentTaskId"
                :analysisTaskId="currentTaskId"
                :trafficData="currentTrafficData"
                @simulation-started="handleSimulationStarted"
                @simulation-stopped="handleSimulationStopped"
                @simulation-completed="handleSimulationCompleted"
                @status-updated="handleSimulationStatusUpdated"
              />

              <!-- 仿真监控面板 -->
              <SimulationMonitor
                v-if="currentSimulationId && isSimulationRunning"
                :simulationId="currentSimulationId"
                :isRunning="isSimulationRunning"
                @data-updated="handleSimulationDataUpdated"
              />

              <!-- 优化对比面板 -->
              <OptimizationComparison
                v-if="optimizationResult"
                :optimizationResult="optimizationResult"
                @refresh-requested="loadOptimizationResult"
              />
            </div>

            <el-empty v-if="!currentTaskId" description="请先完成智能分析">
              <el-button type="primary" @click="currentStep = 2">
                返回分析
              </el-button>
            </el-empty>
          </div>

          <!-- 步骤5: 报告生成 -->
          <div v-if="currentStep === 4" class="step-content">
            <IntelligentTrafficReport
              v-if="currentTaskId && reportData"
              :task-id="currentTaskId"
              :report-data="reportData"
              @export-report="handleExportReport"
              @refresh-data="handleRefreshReportData"
            />
            <el-empty v-else description="请先完成智能分析">
              <el-button type="primary" @click="currentStep = 2">
                返回分析
              </el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="console-footer">
      <div class="footer-info">
        <span>系统状态: </span>
        <el-tag :type="systemStatus.type" size="small">{{ systemStatus.text }}</el-tag>
        <span class="separator">|</span>
        <span>活跃连接: {{ activeConnections }}</span>
        <span class="separator">|</span>
        <span>最后更新: {{ lastUpdateTime }}</span>
      </div>
      
      <div class="footer-actions">
        <el-button size="small" @click="refreshSystem">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button size="small" @click="showSystemInfo">
          <el-icon><InfoFilled /></el-icon>
          系统信息
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Grid, Upload, VideoCamera, DataAnalysis, Document, Plus,
  MoreFilled, Refresh, InfoFilled, Loading
} from '@element-plus/icons-vue'

// 导入组件
import FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue'
import FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'
import TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue'
import IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue'
import StepNavigator from '@/components/analysis/StepNavigator.vue'

// 导入仿真组件
import SimulationControlPanel from '@/components/simulation/SimulationControlPanel.vue'
import SimulationMonitor from '@/components/simulation/SimulationMonitor.vue'
import OptimizationComparison from '@/components/simulation/OptimizationComparison.vue'

// 导入API
import {
  getFourWayTaskStatus,
  getFourWayAnalysisResult,
  generateFourWayTrafficReport
} from '@/api/video'

// 导入仿真API
import simulationApi from '@/api/simulation'

// 导入数据转换工具
import { transformToReportData } from '@/utils/reportDataTransformer'

export default {
  name: 'FourWayAnalysisConsole',
  components: {
    Grid, Upload, VideoCamera, DataAnalysis, Document, Plus,
    MoreFilled, Refresh, InfoFilled, Loading,
    FourWayVideoUpload,
    FourWayRealtimeViewer,
    TrafficAnalysisDashboard,
    IntelligentTrafficReport,
    StepNavigator,
    SimulationControlPanel,
    SimulationMonitor,
    OptimizationComparison
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 响应式数据
    const currentStep = ref(0)
    const currentTaskId = ref('')
    const currentTask = ref({
      id: '',
      name: '四方向交通分析任务',
      status: 'waiting',
      progress: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    const reportData = ref(null)
    const activeConnections = ref(0)
    const lastUpdateTime = ref('')
    const isInitializing = ref(true)
    const taskStatusPolling = ref(null)

    // 仿真相关数据
    const currentSimulationId = ref('')
    const showSimulationPanel = ref(false)
    const showSimulationMonitor = ref(false)
    const showOptimizationComparison = ref(false)
    const isSimulationRunning = ref(false)
    const optimizationResult = ref(null)
    const currentTrafficData = ref({})

    // 系统状态
    const systemStatus = reactive({
      type: 'success',
      text: '正常运行'
    })

    // 计算属性
    const totalTasks = computed(() => 1)
    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0)
    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0)

    const canUpload = computed(() => true)
    const canDetect = computed(() => currentStep.value >= 1)
    const canAnalyze = computed(() => currentStep.value >= 2)
    const canExport = computed(() => currentStep.value >= 3)
    
    // 方法

    // 初始化和状态检测方法
    const initializeFromRoute = async () => {
      try {
        isInitializing.value = true

        // 检查URL参数中的mode参数
        const sessionMode = route.query.mode
        const urlTaskId = route.query.taskId || route.params.taskId

        console.log('页面初始化 - 模式:', sessionMode, '任务ID:', urlTaskId)

        // 根据模式参数决定初始化策略
        if (sessionMode === 'new') {
          console.log('检测到新会话模式，清理之前的状态')
          await initializeNewSession()
        } else if (urlTaskId) {
          console.log('从URL参数检测到任务ID:', urlTaskId)
          currentTaskId.value = urlTaskId
          // 获取任务状态并设置对应步骤
          await fetchTaskStatusAndSetStep(urlTaskId)
        } else {
          // 智能检测会话模式
          await detectSessionMode()
        }

      } catch (error) {
        console.error('初始化失败:', error)
        ElMessage.error('初始化页面状态失败: ' + error.message)
      } finally {
        isInitializing.value = false
      }
    }

    const fetchTaskStatusAndSetStep = async (taskId) => {
      try {
        const token = localStorage.getItem('auth_token')
        if (!token) {
          throw new Error('未找到认证令牌')
        }

        const response = await fetch(`/api/video-analysis/four-way/${taskId}/status`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error(`获取任务状态失败: ${response.status}`)
        }

        const taskData = await response.json()
        console.log('获取到任务状态:', taskData)

        // 更新当前任务信息
        currentTask.value = {
          id: taskData.taskId || taskId,
          name: taskData.name || '四方向交通分析任务',
          status: taskData.status || 'waiting',
          progress: taskData.progress || 0,
          createdAt: taskData.createdAt ? new Date(taskData.createdAt) : new Date(),
          updatedAt: taskData.updatedAt ? new Date(taskData.updatedAt) : new Date()
        }

        // 根据任务状态设置对应步骤
        setStepByTaskStatus(taskData.status, taskData.progress)

        // 如果任务正在处理中，开始轮询状态
        if (['queued', 'processing'].includes(taskData.status)) {
          startTaskStatusPolling(taskId)
        }

      } catch (error) {
        console.error('获取任务状态失败:', error)
        ElMessage.warning('无法获取任务状态，将从上传步骤开始')
        currentStep.value = 0
      }
    }

    const setStepByTaskStatus = (status, progress = 0) => {
      console.log('根据任务状态设置步骤:', { status, progress })

      switch (status) {
        case 'queued':
        case 'uploading':
          currentStep.value = 0 // 上传步骤
          ElMessage.info('任务正在排队中，请等待处理')
          break

        case 'processing':
          if (progress < 50) {
            currentStep.value = 1 // 实时检测步骤
            ElMessage.info('任务正在进行实时检测')
          } else if (progress < 90) {
            currentStep.value = 1 // 仍在检测阶段
            ElMessage.info('实时检测进行中')
          } else {
            currentStep.value = 2 // 智能分析步骤
            ElMessage.info('正在进行智能分析')
          }
          break

        case 'completed':
          currentStep.value = 2 // 跳转到智能分析步骤
          ElMessage.success('任务已完成，可以查看分析结果')
          break

        case 'failed':
          currentStep.value = 0 // 回到上传步骤
          ElMessage.error('任务处理失败，请重新上传')
          break

        default:
          currentStep.value = 0 // 默认从上传开始
          break
      }
    }

    // 智能会话检测机制
    const detectSessionMode = async () => {
      try {
        // 检查会话存储中是否有活跃任务
        const sessionData = sessionStorage.getItem('fourWayActiveTask')
        if (sessionData) {
          const taskInfo = JSON.parse(sessionData)
          const sessionAge = Date.now() - taskInfo.timestamp
          const maxSessionAge = 24 * 60 * 60 * 1000 // 24小时

          console.log('检测到会话数据:', taskInfo, '会话年龄:', sessionAge / 1000 / 60, '分钟')

          // 如果会话数据过期，清理并开始新会话
          if (sessionAge > maxSessionAge) {
            console.log('会话数据已过期，开始新会话')
            await initializeNewSession()
            return
          }

          // 显示用户选择界面：继续任务 vs 开始新任务
          await showSessionChoiceDialog(taskInfo)
        } else {
          // 没有活跃任务，开始新会话
          console.log('没有检测到活跃任务，开始新会话')
          await initializeNewSession()
        }

      } catch (error) {
        console.error('会话检测失败:', error)
        await initializeNewSession()
      }
    }

    // 初始化新会话
    const initializeNewSession = async () => {
      try {
        console.log('初始化新会话')

        // 清理之前的会话数据
        await clearPreviousSession()

        // 重置页面状态
        currentStep.value = 0
        currentTaskId.value = null
        currentTask.value = {}
        reportData.value = null

        // 停止任何正在进行的轮询
        stopTaskStatusPolling()

        // 清理URL参数中的taskId
        if (route.query.taskId) {
          router.replace({
            path: route.path,
            query: { ...route.query, taskId: undefined }
          })
        }

        ElMessage.success('已开始新的分析会话')

      } catch (error) {
        console.error('初始化新会话失败:', error)
        ElMessage.error('初始化新会话失败: ' + error.message)
      }
    }

    // 清理之前的会话
    const clearPreviousSession = async () => {
      try {
        console.log('清理之前的会话数据')

        // 清理会话存储
        sessionStorage.removeItem('fourWayActiveTask')

        // 清理其他相关的存储数据
        const keysToRemove = ['uploadState', 'fourWayProgress', 'fourWayResults']
        keysToRemove.forEach(key => {
          try {
            sessionStorage.removeItem(key)
          } catch (e) {
            console.warn(`清理存储键 ${key} 失败:`, e)
          }
        })

      } catch (error) {
        console.error('清理会话数据失败:', error)
      }
    }

    // 显示会话选择对话框
    const showSessionChoiceDialog = async (taskInfo) => {
      try {
        const result = await ElMessageBox.confirm(
          `检测到您有一个未完成的四方向分析任务（任务ID: ${taskInfo.taskId}）。您希望：`,
          '会话选择',
          {
            confirmButtonText: '继续之前的任务',
            cancelButtonText: '开始新的分析',
            type: 'question',
            distinguishCancelAndClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: false,
            customClass: 'session-choice-dialog'
          }
        )

        // 用户选择继续之前的任务
        if (result === 'confirm') {
          console.log('用户选择继续之前的任务')
          currentTaskId.value = taskInfo.taskId
          await fetchTaskStatusAndSetStep(taskInfo.taskId)
          ElMessage.info('已恢复之前的分析任务')
        }

      } catch (action) {
        // 用户选择开始新分析或关闭对话框
        if (action === 'cancel') {
          console.log('用户选择开始新的分析')
          await initializeNewSession()
        } else {
          // 用户关闭对话框，默认开始新会话
          console.log('用户关闭对话框，开始新会话')
          await initializeNewSession()
        }
      }
    }

    const checkActiveTask = async () => {
      // 这个方法现在被 detectSessionMode 替代，保留用于向后兼容
      await detectSessionMode()
    }

    const startTaskStatusPolling = (taskId) => {
      // 清除现有的轮询
      if (taskStatusPolling.value) {
        clearInterval(taskStatusPolling.value)
      }

      console.log('开始轮询任务状态:', taskId)

      taskStatusPolling.value = setInterval(async () => {
        try {
          await fetchTaskStatusAndSetStep(taskId)

          // 如果任务完成或失败，停止轮询
          if (['completed', 'failed'].includes(currentTask.value.status)) {
            clearInterval(taskStatusPolling.value)
            taskStatusPolling.value = null
          }

        } catch (error) {
          console.error('轮询任务状态失败:', error)
        }
      }, 3000) // 每3秒轮询一次
    }

    const stopTaskStatusPolling = () => {
      if (taskStatusPolling.value) {
        clearInterval(taskStatusPolling.value)
        taskStatusPolling.value = null
      }
    }

    // 增强的会话存储管理
    const saveTaskToSession = (taskId, additionalData = {}) => {
      try {
        const sessionData = {
          taskId: taskId,
          timestamp: Date.now(),
          sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          mode: 'active',
          ...additionalData
        }

        sessionStorage.setItem('fourWayActiveTask', JSON.stringify(sessionData))
        console.log('任务已保存到会话存储:', sessionData)

      } catch (error) {
        console.warn('保存任务信息到会话存储失败:', error)
      }
    }

    const getTaskFromSession = () => {
      try {
        const sessionData = sessionStorage.getItem('fourWayActiveTask')
        if (sessionData) {
          const taskInfo = JSON.parse(sessionData)

          // 检查会话有效性
          const sessionAge = Date.now() - taskInfo.timestamp
          const maxSessionAge = 24 * 60 * 60 * 1000 // 24小时

          if (sessionAge <= maxSessionAge) {
            return taskInfo
          } else {
            console.log('会话数据已过期，清理存储')
            sessionStorage.removeItem('fourWayActiveTask')
          }
        }
      } catch (error) {
        console.warn('从会话存储获取任务信息失败:', error)
      }
      return null
    }

    // 事件处理
    const handleUploadSuccess = (response) => {
      const taskId = response.data?.taskId || response.taskId || `task_${Date.now()}`
      currentTaskId.value = taskId

      // 使用增强的会话存储管理
      saveTaskToSession(taskId, {
        uploadTime: new Date().toISOString(),
        status: 'processing'
      })

      // 更新当前任务信息
      currentTask.value = {
        id: taskId,
        name: '四方向交通分析任务',
        status: 'processing',
        progress: 10,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // 更新URL参数但不导航
      router.replace({
        path: route.path,
        query: { ...route.query, taskId: taskId }
      })

      currentStep.value = 1
      ElMessage.success('视频上传成功，开始实时检测')

      // 开始轮询任务状态
      startTaskStatusPolling(taskId)
    }

    const handleUploadError = (error) => {
      ElMessage.error('视频上传失败: ' + error.message)
    }

    const handleUploadProgress = (progress) => {
      console.log('上传进度:', progress)
    }

    const handleUploadStatusChange = (status) => {
      console.log('上传状态变化:', status)
    }

    const handleDetectionUpdate = (data) => {
      console.log('检测更新:', data)

      // 更新任务进度
      if (currentTask.value && data.progress) {
        currentTask.value.progress = Math.min(data.progress, 90) // 检测阶段最多90%
        currentTask.value.updatedAt = new Date()
      }
    }

    const handleDetectionStatusChange = (status) => {
      if (status === 'completed') {
        // 更新任务状态
        if (currentTask.value) {
          currentTask.value.status = 'completed'
          currentTask.value.progress = 100
          currentTask.value.updatedAt = new Date()
        }

        currentStep.value = 2
        ElMessage.success('实时检测完成，开始智能分析')
      }
    }

    // 处理四方向分析完成事件
    const handleAnalysisComplete = async (completeData) => {
      try {
        console.log('🎉 收到四方向分析完成事件:', completeData)

        // 更新任务状态
        if (currentTask.value) {
          currentTask.value.status = 'completed'
          currentTask.value.progress = 100
          currentTask.value.updatedAt = new Date()
        }

        // 显示完成提示
        ElMessage({
          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在生成智能报告...`,
          type: 'success',
          duration: 4000
        })

        // 自动生成智能报告数据
        try {
          await generateReportFromAnalysisData(completeData)

          // 延迟跳转到智能分析模块
          setTimeout(() => {
            currentStep.value = 2
            ElMessage.info('已自动跳转到智能分析模块')
          }, 1500)
        } catch (error) {
          console.error('自动生成报告失败:', error)
          // 即使报告生成失败，也跳转到智能分析模块
          setTimeout(() => {
            currentStep.value = 2
            ElMessage.warning('已跳转到智能分析模块，请手动生成报告')
          }, 2000)
        }

      } catch (error) {
        console.error('处理分析完成事件失败:', error)
        ElMessage.error('处理完成事件失败，请手动跳转到智能分析')
      }
    }

    const handleAnalysisDataUpdate = (data) => {
      reportData.value = data
      currentStep.value = 3
      ElMessage.success('智能分析完成，可以生成报告')
    }

    // 从分析数据生成报告数据
    const generateReportFromAnalysisData = async (completeData) => {
      try {
        if (!currentTaskId.value) {
          throw new Error('缺少任务ID')
        }

        console.log('正在从分析数据生成报告...', completeData)

        // 首先尝试从API获取完整的分析结果
        let apiAnalysisData = null
        try {
          const response = await getFourWayAnalysisResult(currentTaskId.value)
          apiAnalysisData = response.data
          console.log('从API获取到分析数据:', apiAnalysisData)
        } catch (apiError) {
          console.warn('从API获取分析数据失败，使用WebSocket数据:', apiError)
        }

        // 使用数据转换工具生成标准化的报告数据
        const reportData = transformToReportData(apiAnalysisData, completeData)

        // 设置报告数据
        handleAnalysisDataUpdate(reportData)

        console.log('报告数据生成成功:', reportData)
        return reportData

      } catch (error) {
        console.error('生成报告数据失败:', error)
        throw error
      }
    }

    const handleExportReport = (taskId) => {
      ElMessage.success('报告导出成功')
    }

    const handleRefreshReportData = (taskId) => {
      ElMessage.success('报告数据刷新成功')
    }
    
    // 快速操作
    const goToUpload = () => {
      currentStep.value = 0
    }
    
    const startDetection = () => {
      if (canDetect.value) {
        currentStep.value = 1
      } else {
        ElMessage.warning('请先上传视频文件')
      }
    }
    
    const generateAnalysis = () => {
      if (canAnalyze.value) {
        currentStep.value = 2
      } else {
        ElMessage.warning('请先完成视频检测')
      }
    }
    
    const exportReport = () => {
      if (canExport.value) {
        currentStep.value = 3
      } else {
        ElMessage.warning('请先完成智能分析')
      }
    }

    const refreshSystem = () => {
      lastUpdateTime.value = new Date().toLocaleTimeString()
      ElMessage.success('系统状态已刷新')
    }

    const showSystemInfo = () => {
      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {
        confirmButtonText: '确定'
      })
    }

    // StepNavigator事件处理
    const handleStepChange = (stepIndex) => {
      console.log('步骤切换请求:', stepIndex)

      // 检查是否可以切换到目标步骤
      if (stepIndex <= currentStep.value) {
        currentStep.value = stepIndex
        ElMessage.info(`已切换到步骤 ${stepIndex + 1}`)
      } else {
        ElMessage.warning('请先完成前面的步骤')
      }
    }

    const handleQuickAction = (actionKey) => {
      console.log('快速操作:', actionKey)

      switch (actionKey) {
        case 'upload':
          goToUpload()
          break
        case 'detection':
          startDetection()
          break
        case 'analysis':
          generateAnalysis()
          break
        case 'report':
          exportReport()
          break
        default:
          console.warn('未知的快速操作:', actionKey)
      }
    }

    // 页面刷新优化
    const handlePageRefresh = async () => {
      try {
        console.log('处理页面刷新事件')

        // 检查是否是强制刷新（Ctrl+F5 或 Cmd+Shift+R）
        const isHardRefresh = performance.navigation?.type === 1

        if (isHardRefresh) {
          console.log('检测到强制刷新，开始新会话')
          await initializeNewSession()
          return
        }

        // 普通刷新，检查会话状态
        const sessionTask = getTaskFromSession()
        if (sessionTask) {
          console.log('页面刷新 - 恢复会话:', sessionTask)
          currentTaskId.value = sessionTask.taskId
          await fetchTaskStatusAndSetStep(sessionTask.taskId)
        } else {
          console.log('页面刷新 - 没有有效会话，开始新会话')
          await initializeNewSession()
        }

      } catch (error) {
        console.error('处理页面刷新失败:', error)
        await initializeNewSession()
      }
    }

    const handleRefreshStatus = async () => {
      if (currentTaskId.value) {
        ElMessage.info('正在刷新任务状态...')
        await fetchTaskStatusAndSetStep(currentTaskId.value)
      } else {
        ElMessage.warning('没有活跃的任务')
      }
    }

    // 页面状态一致性检查
    const checkPageStateConsistency = () => {
      try {
        const urlTaskId = route.query.taskId
        const sessionTask = getTaskFromSession()
        const currentTaskIdValue = currentTaskId.value

        console.log('页面状态一致性检查:', {
          urlTaskId,
          sessionTaskId: sessionTask?.taskId,
          currentTaskIdValue
        })

        // 如果URL和会话存储不一致，以URL为准
        if (urlTaskId && sessionTask && urlTaskId !== sessionTask.taskId) {
          console.log('检测到状态不一致，以URL参数为准')
          saveTaskToSession(urlTaskId)
          currentTaskId.value = urlTaskId
        }

      } catch (error) {
        console.error('页面状态一致性检查失败:', error)
      }
    }

    // 任务状态辅助方法
    const getTaskStatusType = (status) => {
      const statusMap = {
        'waiting': 'info',
        'processing': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'paused': 'info'
      }
      return statusMap[status] || 'info'
    }

    const getTaskStatusText = (status) => {
      const statusMap = {
        'waiting': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败',
        'paused': '已暂停'
      }
      return statusMap[status] || '未知'
    }

    const getProgressStatus = (status) => {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return null
    }

    const formatTime = (time) => {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }

    const getProcessingDuration = (task) => {
      if (!task || !task.createdAt) return '-'
      const start = new Date(task.createdAt)
      const end = task.updatedAt ? new Date(task.updatedAt) : new Date()
      const duration = Math.floor((end - start) / 1000)

      if (duration < 60) return `${duration}秒`
      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`
      return `${Math.floor(duration / 3600)}小时`
    }

    // 监听路由变化 - 增强版
    watch(() => route.query, (newQuery, oldQuery) => {
      const newTaskId = newQuery.taskId
      const newMode = newQuery.mode
      const oldTaskId = oldQuery?.taskId
      const oldMode = oldQuery?.mode

      console.log('路由参数变化:', { newTaskId, newMode, oldTaskId, oldMode })

      // 检测模式变化
      if (newMode !== oldMode) {
        if (newMode === 'new') {
          console.log('检测到新会话模式参数')
          initializeNewSession()
          return
        }
      }

      // 检测任务ID变化
      if (newTaskId && newTaskId !== currentTaskId.value) {
        console.log('检测到URL中的taskId变化:', newTaskId)
        currentTaskId.value = newTaskId
        fetchTaskStatusAndSetStep(newTaskId)
      }
    }, { deep: true })

    // 状态隔离机制
    const createSessionNamespace = (taskId) => {
      return `fourWay_${taskId}_${Date.now()}`
    }

    const getNamespacedStorageKey = (key, taskId = null) => {
      const baseKey = taskId ? `${key}_${taskId}` : key
      return `fourWayConsole_${baseKey}`
    }

    const setNamespacedStorage = (key, value, taskId = null) => {
      try {
        const namespacedKey = getNamespacedStorageKey(key, taskId)
        sessionStorage.setItem(namespacedKey, JSON.stringify(value))
      } catch (error) {
        console.warn('设置命名空间存储失败:', error)
      }
    }

    const getNamespacedStorage = (key, taskId = null) => {
      try {
        const namespacedKey = getNamespacedStorageKey(key, taskId)
        const value = sessionStorage.getItem(namespacedKey)
        return value ? JSON.parse(value) : null
      } catch (error) {
        console.warn('获取命名空间存储失败:', error)
        return null
      }
    }

    const clearNamespacedStorage = (taskId = null) => {
      try {
        const prefix = taskId ? `fourWayConsole_${taskId}` : 'fourWayConsole_'
        const keysToRemove = []

        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i)
          if (key && key.startsWith(prefix)) {
            keysToRemove.push(key)
          }
        }

        keysToRemove.forEach(key => sessionStorage.removeItem(key))
        console.log('已清理命名空间存储:', keysToRemove.length, '个项目')

      } catch (error) {
        console.warn('清理命名空间存储失败:', error)
      }
    }

    // 生命周期
    onMounted(async () => {
      // 初始化系统状态
      lastUpdateTime.value = new Date().toLocaleTimeString()
      activeConnections.value = 1

      // 页面状态一致性检查
      checkPageStateConsistency()

      // 初始化页面状态
      await initializeFromRoute()
    })

    // 仿真事件处理方法
    const handleSimulationStarted = (simulationId) => {
      console.log('仿真已启动:', simulationId)
      currentSimulationId.value = simulationId
      isSimulationRunning.value = true
      showSimulationMonitor.value = true
      ElMessage.success('仿真已启动，正在进行交通优化分析')
    }

    const handleSimulationStopped = (simulationId) => {
      console.log('仿真已停止:', simulationId)
      isSimulationRunning.value = false
      showSimulationMonitor.value = false
      ElMessage.info('仿真已停止')
    }

    const handleSimulationCompleted = (simulationId) => {
      console.log('仿真已完成:', simulationId)
      isSimulationRunning.value = false
      showSimulationMonitor.value = false
      showOptimizationComparison.value = true

      // 加载优化结果
      loadOptimizationResult()

      // 自动进入下一步
      currentStep.value = 4
      ElMessage.success('仿真优化完成，可以查看优化结果和生成报告')
    }

    const handleSimulationStatusUpdated = (status) => {
      console.log('仿真状态更新:', status)
      // 更新仿真状态显示
    }

    const handleSimulationDataUpdated = (data) => {
      console.log('仿真数据更新:', data)
      // 更新实时监控数据
    }

    const loadOptimizationResult = async () => {
      try {
        if (!currentSimulationId.value) return

        const response = await simulationApi.getOptimizationResult(currentSimulationId.value)
        if (response.status === 'success') {
          optimizationResult.value = response.optimization_result
        }
      } catch (error) {
        console.error('加载优化结果失败:', error)
      }
    }

    const handleAnalysisDataUpdate = (data) => {
      console.log('分析数据更新:', data)
      currentTrafficData.value = data

      // 当分析完成时，显示仿真面板
      if (data.status === 'completed') {
        showSimulationPanel.value = true
        ElMessage.success('智能分析完成，可以开始仿真优化')
      }
    }

    onUnmounted(() => {
      // 清理轮询
      stopTaskStatusPolling()
    })

    return {
      // 图标组件
      Upload,
      VideoCamera,
      DataAnalysis,
      Document,
      Plus,
      MoreFilled,
      Refresh,
      InfoFilled,

      // 响应式数据
      currentStep,
      currentTaskId,
      currentTask,
      reportData,
      activeConnections,
      lastUpdateTime,
      systemStatus,
      isInitializing,

      // 仿真相关数据
      currentSimulationId,
      showSimulationPanel,
      showSimulationMonitor,
      showOptimizationComparison,
      isSimulationRunning,
      optimizationResult,
      currentTrafficData,

      // 计算属性
      totalTasks,
      activeTasks,
      completedTasks,
      canUpload,
      canDetect,
      canAnalyze,
      canExport,

      // 用户反馈和提示
      showUserFeedback: (message, type = 'info', duration = 3000) => {
        ElMessage({
          message,
          type,
          duration,
          showClose: true
        })
      },

      showConfirmDialog: async (message, title = '确认操作') => {
        try {
          await ElMessageBox.confirm(message, title, {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          return true
        } catch {
          return false
        }
      },

      // 方法
      initializeFromRoute,
      fetchTaskStatusAndSetStep,
      setStepByTaskStatus,
      checkActiveTask,
      detectSessionMode,
      initializeNewSession,
      clearPreviousSession,
      showSessionChoiceDialog,
      saveTaskToSession,
      getTaskFromSession,
      handlePageRefresh,
      checkPageStateConsistency,
      createSessionNamespace,
      getNamespacedStorageKey,
      setNamespacedStorage,
      getNamespacedStorage,
      clearNamespacedStorage,
      startTaskStatusPolling,
      stopTaskStatusPolling,
      handleUploadSuccess,
      handleUploadError,
      handleUploadProgress,
      handleUploadStatusChange,
      handleDetectionUpdate,
      handleDetectionStatusChange,
      handleAnalysisComplete,
      handleAnalysisDataUpdate,
      handleExportReport,
      handleRefreshReportData,

      // 仿真事件处理方法
      handleSimulationStarted,
      handleSimulationStopped,
      handleSimulationCompleted,
      handleSimulationStatusUpdated,
      handleSimulationDataUpdated,
      loadOptimizationResult,
      goToUpload,
      startDetection,
      generateAnalysis,
      exportReport,
      refreshSystem,
      showSystemInfo,
      handleStepChange,
      handleQuickAction,
      handleRefreshStatus,

      // 任务状态辅助方法
      getTaskStatusType,
      getTaskStatusText,
      getProgressStatus,
      formatTime,
      getProcessingDuration
    }
  }
}
</script>

<style scoped>
.four-way-analysis-console {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* 控制台头部 */
.console-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.console-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.console-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
  margin-top: 4px;
}

/* 工作流程导航 */
.workflow-navigation {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
}

/* 主要内容区域 */
.console-content {
  flex: 1;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 24px;
  padding: 24px 32px;
  min-height: 0;
}

/* 左侧面板 */
.left-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-actions-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-actions .el-button {
  justify-content: flex-start;
}

/* 主内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
}

.current-task-info {
  flex-shrink: 0;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-title-section h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.task-progress-section {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  min-width: 40px;
}

.task-details {
  margin-top: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.dynamic-content {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: auto;
}

.step-content {
  height: 100%;
}

/* 仿真容器样式 */
.simulation-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 20px;
}

.loading-content {
  width: 100%;
  max-width: 600px;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #6b7280;
  margin-top: 20px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 底部状态栏 */
.console-footer {
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 12px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
}

.separator {
  color: #d1d5db;
}

.footer-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .console-content {
    grid-template-columns: 300px 1fr;
  }

  .header-stats {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .console-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-stats {
    justify-content: center;
  }

  .console-content {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .workflow-navigation {
    padding: 16px;
  }

  .console-footer {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .task-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .task-progress-section {
    width: 100%;
    min-width: auto;
  }
}

/* 滚动条样式 */
.task-list::-webkit-scrollbar {
  width: 6px;
}

.task-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.task-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-content {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
