// MongoDB索引创建脚本
// 用于优化交通分析系统的数据库性能

// 切换到traffic_analysis数据库
use('traffic_analysis');

print('开始创建MongoDB索引...');

// ==================== 用户集合索引 ====================
print('创建users集合索引...');

// 用户名唯一索引
db.users.createIndex(
  { "username": 1 }, 
  { 
    unique: true, 
    name: "idx_username_unique",
    background: true 
  }
);

// 邮箱唯一索引
db.users.createIndex(
  { "email": 1 }, 
  { 
    unique: true, 
    name: "idx_email_unique",
    background: true,
    sparse: true  // 允许null值
  }
);

// 角色索引
db.users.createIndex(
  { "role": 1 }, 
  { 
    name: "idx_role",
    background: true 
  }
);

// 创建时间索引
db.users.createIndex(
  { "createdAt": -1 }, 
  { 
    name: "idx_created_at_desc",
    background: true 
  }
);

// ==================== 视频分析集合索引 ====================
print('创建analysis_videos集合索引...');

// 任务ID索引
db.analysis_videos.createIndex(
  { "task_id": 1 }, 
  { 
    name: "idx_task_id",
    background: true 
  }
);

// 用户ID索引
db.analysis_videos.createIndex(
  { "user_id": 1 }, 
  { 
    name: "idx_user_id",
    background: true 
  }
);

// 状态索引
db.analysis_videos.createIndex(
  { "status": 1 }, 
  { 
    name: "idx_status",
    background: true 
  }
);

// 用户ID和状态复合索引
db.analysis_videos.createIndex(
  { "user_id": 1, "status": 1 }, 
  { 
    name: "idx_user_status",
    background: true 
  }
);

// 创建时间索引
db.analysis_videos.createIndex(
  { "created_at": -1 }, 
  { 
    name: "idx_created_at_desc",
    background: true 
  }
);

// 用户ID和创建时间复合索引（用于分页查询）
db.analysis_videos.createIndex(
  { "user_id": 1, "created_at": -1 }, 
  { 
    name: "idx_user_created_desc",
    background: true 
  }
);

// 方向索引
db.analysis_videos.createIndex(
  { "direction": 1 }, 
  { 
    name: "idx_direction",
    background: true 
  }
);

// 角色索引
db.analysis_videos.createIndex(
  { "role": 1 }, 
  { 
    name: "idx_role",
    background: true 
  }
);

// ==================== 四方向交通分析集合索引 ====================
print('创建intersection_analysis_four_way集合索引...');

// 任务ID唯一索引
db.intersection_analysis_four_way.createIndex(
  { "taskId": 1 }, 
  { 
    unique: true,
    name: "idx_task_id_unique",
    background: true 
  }
);

// 用户ID索引
db.intersection_analysis_four_way.createIndex(
  { "userId": 1 }, 
  { 
    name: "idx_user_id",
    background: true 
  }
);

// 状态索引
db.intersection_analysis_four_way.createIndex(
  { "status": 1 }, 
  { 
    name: "idx_status",
    background: true 
  }
);

// 用户ID和状态复合索引
db.intersection_analysis_four_way.createIndex(
  { "userId": 1, "status": 1 }, 
  { 
    name: "idx_user_status",
    background: true 
  }
);

// 创建时间索引
db.intersection_analysis_four_way.createIndex(
  { "createdAt": -1 }, 
  { 
    name: "idx_created_at_desc",
    background: true 
  }
);

// 用户ID和创建时间复合索引
db.intersection_analysis_four_way.createIndex(
  { "userId": 1, "createdAt": -1 }, 
  { 
    name: "idx_user_created_desc",
    background: true 
  }
);

// 用户名索引
db.intersection_analysis_four_way.createIndex(
  { "username": 1 }, 
  { 
    name: "idx_username",
    background: true 
  }
);

// 角色索引
db.intersection_analysis_four_way.createIndex(
  { "role": 1 }, 
  { 
    name: "idx_role",
    background: true 
  }
);

// 分析类型索引
db.intersection_analysis_four_way.createIndex(
  { "analysisType": 1 }, 
  { 
    name: "idx_analysis_type",
    background: true 
  }
);

// 处理时间范围索引
db.intersection_analysis_four_way.createIndex(
  { "processingStartTime": 1, "processingEndTime": 1 }, 
  { 
    name: "idx_processing_time_range",
    background: true,
    sparse: true
  }
);

// ==================== 交通分析结果集合索引 ====================
print('创建traffic_analysis_results集合索引...');

// 任务ID索引
db.traffic_analysis_results.createIndex(
  { "task_id": 1 }, 
  { 
    name: "idx_task_id",
    background: true 
  }
);

// 分析结果ID索引
db.traffic_analysis_results.createIndex(
  { "analysis_result_id": 1 }, 
  { 
    name: "idx_analysis_result_id",
    background: true 
  }
);

// 分析类型索引
db.traffic_analysis_results.createIndex(
  { "analysis_type": 1 }, 
  { 
    name: "idx_analysis_type",
    background: true 
  }
);

// 创建时间索引
db.traffic_analysis_results.createIndex(
  { "created_at": -1 }, 
  { 
    name: "idx_created_at_desc",
    background: true 
  }
);

// 拥堵等级索引
db.traffic_analysis_results.createIndex(
  { "congestion_level": 1 }, 
  { 
    name: "idx_congestion_level",
    background: true 
  }
);

// 车辆总数索引
db.traffic_analysis_results.createIndex(
  { "total_vehicle_count": -1 }, 
  { 
    name: "idx_total_vehicle_count_desc",
    background: true 
  }
);

// ==================== 信号优化集合索引 ====================
print('创建signal_optimizations集合索引...');

// 任务ID索引
db.signal_optimizations.createIndex(
  { "task_id": 1 }, 
  { 
    name: "idx_task_id",
    background: true 
  }
);

// 分析结果ID索引
db.signal_optimizations.createIndex(
  { "analysis_result_id": 1 }, 
  { 
    name: "idx_analysis_result_id",
    background: true 
  }
);

// 状态索引
db.signal_optimizations.createIndex(
  { "status": 1 }, 
  { 
    name: "idx_status",
    background: true 
  }
);

// 优先级索引
db.signal_optimizations.createIndex(
  { "priority": 1 }, 
  { 
    name: "idx_priority",
    background: true 
  }
);

// 创建时间索引
db.signal_optimizations.createIndex(
  { "created_at": -1 }, 
  { 
    name: "idx_created_at_desc",
    background: true 
  }
);

// 应用时间索引
db.signal_optimizations.createIndex(
  { "applied_at": -1 }, 
  { 
    name: "idx_applied_at_desc",
    background: true,
    sparse: true
  }
);

// ==================== 报告数据集合索引 ====================
print('创建report_data集合索引...');

// 任务ID索引
db.report_data.createIndex(
  { "task_id": 1 }, 
  { 
    name: "idx_task_id",
    background: true 
  }
);

// 分析结果ID索引
db.report_data.createIndex(
  { "analysis_result_id": 1 }, 
  { 
    name: "idx_analysis_result_id",
    background: true 
  }
);

// 报告类型索引
db.report_data.createIndex(
  { "report_type": 1 }, 
  { 
    name: "idx_report_type",
    background: true 
  }
);

// 状态索引
db.report_data.createIndex(
  { "status": 1 }, 
  { 
    name: "idx_status",
    background: true 
  }
);

// 生成时间索引
db.report_data.createIndex(
  { "generated_at": -1 }, 
  { 
    name: "idx_generated_at_desc",
    background: true 
  }
);

print('MongoDB索引创建完成！');

// ==================== 查看索引信息 ====================
print('\n=== 索引创建结果 ===');

print('\nusers集合索引:');
db.users.getIndexes().forEach(index => print(`  - ${index.name}: ${JSON.stringify(index.key)}`));

print('\nanalysis_videos集合索引:');
db.analysis_videos.getIndexes().forEach(index => print(`  - ${index.name}: ${JSON.stringify(index.key)}`));

print('\nintersection_analysis_four_way集合索引:');
db.intersection_analysis_four_way.getIndexes().forEach(index => print(`  - ${index.name}: ${JSON.stringify(index.key)}`));

print('\ntraffic_analysis_results集合索引:');
db.traffic_analysis_results.getIndexes().forEach(index => print(`  - ${index.name}: ${JSON.stringify(index.key)}`));

print('\nsignal_optimizations集合索引:');
db.signal_optimizations.getIndexes().forEach(index => print(`  - ${index.name}: ${JSON.stringify(index.key)}`));

print('\nreport_data集合索引:');
db.report_data.getIndexes().forEach(index => print(`  - ${index.name}: ${JSON.stringify(index.key)}`));

print('\n索引优化完成！');
