package com.traffic.analysis.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.List;

/**
 * SUMO仿真任务实体类
 * 对应MongoDB的simulation_tasks集合
 */
@Data
@Document(collection = "simulation_tasks")
public class SimulationTask {
    
    @Id
    private String id;
    
    /**
     * 仿真任务ID（UUID）
     */
    @Field("simulation_id")
    private String simulationId;
    
    /**
     * 关联的视频分析任务ID
     */
    @Field("analysis_task_id")
    private String analysisTaskId;
    
    /**
     * 用户ID
     */
    @Field("user_id")
    private String userId;
    
    /**
     * 用户名
     */
    @Field("username")
    private String username;
    
    /**
     * 仿真任务名称
     */
    @Field("task_name")
    private String taskName;
    
    /**
     * 仿真状态：created, running, completed, failed, stopped
     */
    @Field("status")
    private String status;
    
    /**
     * 仿真类型：signal_optimization, flow_balance, comprehensive
     */
    @Field("simulation_type")
    private String simulationType;
    
    /**
     * 输入的交通数据
     */
    @Field("traffic_data")
    private Map<String, Object> trafficData;
    
    /**
     * 仿真配置参数
     */
    @Field("simulation_config")
    private Map<String, Object> simulationConfig;
    
    /**
     * 仿真结果数据
     */
    @Field("simulation_results")
    private Map<String, Object> simulationResults;
    
    /**
     * 优化建议
     */
    @Field("optimization_suggestions")
    private List<Map<String, Object>> optimizationSuggestions;
    
    /**
     * 仿真进度（0-100）
     */
    @Field("progress")
    private Integer progress;
    
    /**
     * 仿真开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Field("start_time")
    private LocalDateTime startTime;
    
    /**
     * 仿真结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Field("end_time")
    private LocalDateTime endTime;
    
    /**
     * 仿真持续时间（秒）
     */
    @Field("duration")
    private Long duration;
    
    /**
     * 错误信息
     */
    @Field("error_message")
    private String errorMessage;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Field("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Field("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 仿真参数
     */
    @Field("simulation_parameters")
    private SimulationParameters simulationParameters;
    
    /**
     * 性能指标
     */
    @Field("performance_metrics")
    private PerformanceMetrics performanceMetrics;
    
    /**
     * 仿真参数内部类
     */
    @Data
    public static class SimulationParameters {
        /**
         * 仿真时长（秒）
         */
        private Integer simulationDuration;
        
        /**
         * 仿真步长（秒）
         */
        private Double stepLength;
        
        /**
         * 是否使用GUI
         */
        private Boolean useGui;
        
        /**
         * 随机种子
         */
        private Integer randomSeed;
        
        /**
         * 网络类型
         */
        private String networkType;
        
        /**
         * 车道数量
         */
        private Integer laneCount;
        
        /**
         * 速度限制（km/h）
         */
        private Integer speedLimit;
    }
    
    /**
     * 性能指标内部类
     */
    @Data
    public static class PerformanceMetrics {
        /**
         * 总车辆数
         */
        private Integer totalVehicles;
        
        /**
         * 平均速度（m/s）
         */
        private Double averageSpeed;
        
        /**
         * 总等待时间（秒）
         */
        private Double totalWaitingTime;
        
        /**
         * 通行能力（vehicles/hour）
         */
        private Double throughput;
        
        /**
         * 改善百分比
         */
        private Double improvementPercentage;
        
        /**
         * 延误减少（秒）
         */
        private Double delayReduction;
        
        /**
         * 燃油消耗（升）
         */
        private Double fuelConsumption;
        
        /**
         * CO2排放（kg）
         */
        private Double co2Emission;
    }
    
    /**
     * 构造函数
     */
    public SimulationTask() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.status = "created";
        this.progress = 0;
    }
    
    /**
     * 更新状态
     */
    public void updateStatus(String status) {
        this.status = status;
        this.updateTime = LocalDateTime.now();
        
        if ("running".equals(status)) {
            this.startTime = LocalDateTime.now();
        } else if ("completed".equals(status) || "failed".equals(status) || "stopped".equals(status)) {
            this.endTime = LocalDateTime.now();
            if (this.startTime != null) {
                this.duration = java.time.Duration.between(this.startTime, this.endTime).getSeconds();
            }
        }
    }
    
    /**
     * 更新进度
     */
    public void updateProgress(Integer progress) {
        this.progress = progress;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 设置错误信息
     */
    public void setError(String errorMessage) {
        this.errorMessage = errorMessage;
        this.status = "failed";
        this.updateTime = LocalDateTime.now();
        if (this.startTime != null && this.endTime == null) {
            this.endTime = LocalDateTime.now();
            this.duration = java.time.Duration.between(this.startTime, this.endTime).getSeconds();
        }
    }
    
    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return "running".equals(this.status);
    }
    
    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return "completed".equals(this.status);
    }
    
    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return "failed".equals(this.status);
    }
}
