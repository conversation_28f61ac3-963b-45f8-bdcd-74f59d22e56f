package com.traffic.analysis.controller;

import com.traffic.analysis.model.DecisionSuggestion;
import com.traffic.analysis.model.DecisionHistory;
import com.traffic.analysis.service.DecisionSupportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 决策支持控制器
 * 提供智能决策支持相关的REST API接口
 */
@Slf4j
@RestController
@CrossOrigin(origins = {"http://localhost:8081", "http://localhost:8080", "http://localhost:5173", "http://localhost:5000", "http://localhost:5001"}, 
            allowCredentials = "true", maxAge = 3600)
@RequestMapping("/api/decision")
public class DecisionSupportController {
    
    @Autowired
    private DecisionSupportService decisionSupportService;
    
    /**
     * 生成智能决策建议
     */
    @PostMapping("/suggestions/generate")
    public ResponseEntity<Map<String, Object>> generateDecisionSuggestions(@RequestBody Map<String, Object> request) {
        try {
            log.info("收到生成决策建议请求");
            
            // 提取请求参数
            String simulationId = (String) request.get("simulation_id");
            Map<String, Object> trafficData = (Map<String, Object>) request.get("traffic_data");
            Map<String, Object> currentConditions = (Map<String, Object>) request.get("current_conditions");
            
            // 参数验证
            if (simulationId == null || trafficData == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("缺少必要参数"));
            }
            
            // 生成决策建议
            List<DecisionSuggestion> suggestions = decisionSupportService.generateDecisionSuggestions(
                simulationId, trafficData, currentConditions
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "决策建议生成成功");
            response.put("suggestions", suggestions);
            response.put("total_count", suggestions.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("生成决策建议失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("生成决策建议失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取实时决策支持
     */
    @PostMapping("/realtime")
    public ResponseEntity<Map<String, Object>> getRealtimeDecisionSupport(@RequestBody Map<String, Object> request) {
        try {
            log.info("收到实时决策支持请求");
            
            String simulationId = (String) request.get("simulation_id");
            Map<String, Object> currentState = (Map<String, Object>) request.get("current_state");
            
            if (simulationId == null || currentState == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("缺少必要参数"));
            }
            
            // 获取实时决策支持
            Map<String, Object> decisionSupport = decisionSupportService.getRealtimeDecisionSupport(
                simulationId, currentState
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("decision_support", decisionSupport);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取实时决策支持失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取实时决策支持失败: " + e.getMessage()));
        }
    }
    
    /**
     * 应用决策建议
     */
    @PostMapping("/suggestions/{suggestionId}/apply")
    public ResponseEntity<Map<String, Object>> applyDecisionSuggestion(
            @PathVariable String suggestionId,
            @RequestBody Map<String, Object> request) {
        try {
            log.info("应用决策建议: {}", suggestionId);
            
            String simulationId = (String) request.get("simulation_id");
            String userId = (String) request.get("user_id");
            
            if (simulationId == null || userId == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("缺少必要参数"));
            }
            
            // 应用决策建议
            Map<String, Object> result = decisionSupportService.applyDecisionSuggestion(
                suggestionId, simulationId, userId
            );
            
            if ("success".equals(result.get("status"))) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("应用决策建议失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("应用决策建议失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取决策历史
     */
    @GetMapping("/history")
    public ResponseEntity<Map<String, Object>> getDecisionHistory(
            @RequestParam(required = false) String simulationId,
            @RequestParam(required = false) String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            log.info("获取决策历史: simulationId={}, userId={}, page={}, size={}", 
                    simulationId, userId, page, size);
            
            List<DecisionHistory> history = decisionSupportService.getDecisionHistory(
                simulationId, userId, page, size
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("decision_history", history);
            response.put("total_count", history.size());
            response.put("page", page);
            response.put("size", size);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取决策历史失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取决策历史失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取决策建议详情
     */
    @GetMapping("/suggestions/{suggestionId}")
    public ResponseEntity<Map<String, Object>> getDecisionSuggestion(@PathVariable String suggestionId) {
        try {
            Optional<DecisionSuggestion> suggestionOpt = decisionSupportService.getDecisionSuggestion(suggestionId);
            
            if (suggestionOpt.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("status", "success");
                response.put("suggestion", suggestionOpt.get());
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("获取决策建议详情失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取决策建议详情失败: " + e.getMessage()));
        }
    }
    
    /**
     * 评估决策效果
     */
    @PostMapping("/evaluation")
    public ResponseEntity<Map<String, Object>> evaluateDecisionEffect(@RequestBody Map<String, Object> request) {
        try {
            log.info("评估决策效果");
            
            String simulationId = (String) request.get("simulation_id");
            String decisionId = (String) request.get("decision_id");
            Map<String, Object> beforeData = (Map<String, Object>) request.get("before_data");
            Map<String, Object> afterData = (Map<String, Object>) request.get("after_data");
            
            if (simulationId == null || decisionId == null || beforeData == null || afterData == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("缺少必要参数"));
            }
            
            // 评估决策效果
            Map<String, Object> evaluation = decisionSupportService.evaluateDecisionEffect(
                simulationId, decisionId, beforeData, afterData
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("evaluation", evaluation);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("评估决策效果失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("评估决策效果失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取决策统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getDecisionStatistics(
            @RequestParam(required = false) String simulationId,
            @RequestParam(required = false) String userId) {
        try {
            Map<String, Object> statistics = decisionSupportService.getDecisionStatistics(simulationId, userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("statistics", statistics);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取决策统计失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("获取决策统计失败: " + e.getMessage()));
        }
    }
    
    /**
     * 检查决策支持服务状态
     */
    @GetMapping("/service/status")
    public ResponseEntity<Map<String, Object>> checkDecisionServiceStatus() {
        try {
            Map<String, Object> status = decisionSupportService.checkServiceStatus();
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            log.error("检查决策支持服务状态失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(createErrorResponse("检查服务状态失败: " + e.getMessage()));
        }
    }
    
    // 辅助方法
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "error");
        response.put("message", message);
        return response;
    }
}
