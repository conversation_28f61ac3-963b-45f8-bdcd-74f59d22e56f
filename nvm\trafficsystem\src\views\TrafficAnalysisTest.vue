<template>
  <div class="traffic-analysis-test">
    <div class="test-header">
      <h2>智能交通状态面板测试</h2>
      <div class="test-controls">
        <el-button @click="startSimulation" :disabled="isSimulating">开始模拟</el-button>
        <el-button @click="stopSimulation" :disabled="!isSimulating">停止模拟</el-button>
        <el-button @click="resetData">重置数据</el-button>
      </div>
    </div>

    <div class="test-content">
      <!-- 模拟控制面板 -->
      <el-card class="simulation-control">
        <template #header>
          <span>模拟控制</span>
        </template>
        <div class="control-group">
          <label>当前车辆数量: {{ currentVehicleCount }}</label>
          <el-slider 
            v-model="currentVehicleCount" 
            :min="0" 
            :max="50" 
            show-input
            @change="onVehicleCountChange"
          />
        </div>
        <div class="control-group">
          <label>模拟速度 (ms):</label>
          <el-input-number 
            v-model="simulationSpeed" 
            :min="500" 
            :max="5000" 
            :step="500"
          />
        </div>
        <div class="status-info">
          <p>模拟状态: {{ isSimulating ? '运行中' : '已停止' }}</p>
          <p>数据点数量: {{ dataPoints }}</p>
        </div>
      </el-card>

      <!-- 智能交通状态面板 -->
      <div class="panel-container">
        <IntelligentTrafficPanel 
          :current-vehicle-count="currentVehicleCount"
          :auto-update="true"
          @strategy-applied="handleStrategyApplied"
          @data-updated="handleDataUpdated"
          ref="trafficPanel"
        />
      </div>

      <!-- 事件日志 -->
      <el-card class="event-log">
        <template #header>
          <div class="log-header">
            <span>事件日志</span>
            <el-button size="small" @click="clearLog">清空日志</el-button>
          </div>
        </template>
        <div class="log-content">
          <div 
            v-for="(log, index) in eventLogs" 
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import IntelligentTrafficPanel from '@/components/traffic/IntelligentTrafficPanel.vue'

export default {
  name: 'TrafficAnalysisTest',
  components: {
    IntelligentTrafficPanel
  },
  setup() {
    const currentVehicleCount = ref(5)
    const isSimulating = ref(false)
    const simulationSpeed = ref(2000)
    const dataPoints = ref(0)
    const eventLogs = ref([])
    const trafficPanel = ref(null)
    
    let simulationTimer = null
    
    // 添加日志
    const addLog = (message, type = 'info') => {
      const log = {
        time: new Date().toLocaleTimeString(),
        message,
        type
      }
      eventLogs.value.unshift(log)
      
      // 限制日志数量
      if (eventLogs.value.length > 50) {
        eventLogs.value.pop()
      }
    }
    
    // 生成随机车辆数量
    const generateRandomVehicleCount = () => {
      // 模拟真实的交通流量变化
      const patterns = [
        // 低峰时段 (0-5辆)
        () => Math.floor(Math.random() * 6),
        // 正常时段 (3-12辆)
        () => Math.floor(Math.random() * 10) + 3,
        // 高峰时段 (8-25辆)
        () => Math.floor(Math.random() * 18) + 8,
        // 拥堵时段 (15-40辆)
        () => Math.floor(Math.random() * 26) + 15
      ]
      
      const pattern = patterns[Math.floor(Math.random() * patterns.length)]
      return pattern()
    }
    
    // 开始模拟
    const startSimulation = () => {
      if (isSimulating.value) return
      
      isSimulating.value = true
      addLog('开始交通流量模拟', 'success')
      
      simulationTimer = setInterval(() => {
        currentVehicleCount.value = generateRandomVehicleCount()
        dataPoints.value++
        addLog(`更新车辆数量: ${currentVehicleCount.value}`)
      }, simulationSpeed.value)
    }
    
    // 停止模拟
    const stopSimulation = () => {
      if (!isSimulating.value) return
      
      isSimulating.value = false
      if (simulationTimer) {
        clearInterval(simulationTimer)
        simulationTimer = null
      }
      addLog('停止交通流量模拟', 'warning')
    }
    
    // 重置数据
    const resetData = () => {
      stopSimulation()
      currentVehicleCount.value = 5
      dataPoints.value = 0
      if (trafficPanel.value) {
        trafficPanel.value.clearData()
      }
      addLog('重置所有数据', 'info')
    }
    
    // 车辆数量变化处理
    const onVehicleCountChange = (value) => {
      addLog(`手动设置车辆数量: ${value}`)
    }
    
    // 策略应用处理
    const handleStrategyApplied = (strategyData) => {
      addLog(`应用交通策略: ${strategyData.strategy.primary}`, 'success')
      ElMessage.success(`策略已应用: ${strategyData.strategy.primary}`)
    }
    
    // 数据更新处理
    const handleDataUpdated = (trafficData) => {
      addLog(`数据更新 - 等级: ${trafficData.grade}, 趋势: ${trafficData.trend.description}`)
    }
    
    // 清空日志
    const clearLog = () => {
      eventLogs.value = []
      addLog('日志已清空')
    }
    
    // 组件挂载时初始化
    onMounted(() => {
      addLog('交通分析测试页面已加载', 'success')
    })
    
    // 组件卸载时清理
    onUnmounted(() => {
      stopSimulation()
    })
    
    return {
      currentVehicleCount,
      isSimulating,
      simulationSpeed,
      dataPoints,
      eventLogs,
      trafficPanel,
      startSimulation,
      stopSimulation,
      resetData,
      onVehicleCountChange,
      handleStrategyApplied,
      handleDataUpdated,
      clearLog
    }
  }
}
</script>

<style scoped>
.traffic-analysis-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.test-header h2 {
  margin: 0;
  color: #333;
}

.test-controls {
  display: flex;
  gap: 12px;
}

.test-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  grid-template-rows: auto 1fr;
  gap: 20px;
  height: calc(100vh - 200px);
}

.simulation-control {
  grid-row: 1 / 3;
}

.panel-container {
  grid-column: 2;
  grid-row: 1;
}

.event-log {
  grid-column: 2;
  grid-row: 2;
  max-height: 400px;
}

.control-group {
  margin-bottom: 20px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.status-info {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-top: 20px;
}

.status-info p {
  margin: 4px 0;
  color: #666;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 12px;
  color: #999;
  min-width: 80px;
}

.log-message {
  flex: 1;
  font-size: 14px;
}

.log-item.success .log-message {
  color: #52c41a;
}

.log-item.warning .log-message {
  color: #fa8c16;
}

.log-item.error .log-message {
  color: #f5222d;
}

.log-item.info .log-message {
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .test-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .simulation-control {
    grid-row: 1;
    grid-column: 1;
  }
  
  .panel-container {
    grid-row: 2;
    grid-column: 1;
  }
  
  .event-log {
    grid-row: 3;
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .traffic-analysis-test {
    padding: 16px;
  }
  
  .test-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .test-controls {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
