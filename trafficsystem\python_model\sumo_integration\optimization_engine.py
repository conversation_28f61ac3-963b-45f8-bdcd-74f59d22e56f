#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交通优化算法引擎

实现各种交通优化算法，包括信号灯配时优化、流量平衡等
"""

import logging
import math
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

class OptimizationEngine:
    """交通优化算法引擎"""
    
    def __init__(self):
        """初始化优化引擎"""
        self.logger = logging.getLogger(__name__)
        
        # 优化参数
        self.min_green_time = 10  # 最小绿灯时间（秒）
        self.max_green_time = 60  # 最大绿灯时间（秒）
        self.yellow_time = 5      # 黄灯时间（秒）
        self.all_red_time = 2     # 全红时间（秒）
        self.min_cycle_time = 60  # 最小周期时间（秒）
        self.max_cycle_time = 180 # 最大周期时间（秒）
    
    def optimize_signal_timing(self, traffic_data: Dict, current_timing: Optional[Dict] = None) -> Dict:
        """
        优化信号灯配时
        
        Args:
            traffic_data: 交通流量数据
            current_timing: 当前信号灯配时
            
        Returns:
            Dict: 优化后的信号灯配时方案
        """
        try:
            self.logger.info("开始信号灯配时优化")
            
            # 提取各方向流量数据
            flows = self._extract_flow_data(traffic_data)
            
            # 计算最优配时
            optimal_timing = self._calculate_optimal_timing(flows)
            
            # 评估优化效果
            improvement = self._evaluate_improvement(flows, current_timing, optimal_timing)
            
            result = {
                'optimized_timing': optimal_timing,
                'improvement_percentage': improvement,
                'optimization_method': 'webster_method',
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"信号灯配时优化完成，改善效果: {improvement:.1f}%")
            return result
            
        except Exception as e:
            self.logger.error(f"信号灯配时优化失败: {e}")
            raise
    
    def _extract_flow_data(self, traffic_data: Dict) -> Dict:
        """提取流量数据"""
        flows = {}
        
        if 'directions' in traffic_data:
            for direction, data in traffic_data['directions'].items():
                vehicle_count = data.get('vehicleCount', 0)
                duration = data.get('duration', 3600)  # 默认1小时
                
                # 计算流量率 (vehicles/hour)
                flow_rate = (vehicle_count / duration) * 3600 if duration > 0 else 0
                
                flows[direction] = {
                    'flow_rate': flow_rate,
                    'vehicle_count': vehicle_count,
                    'saturation_flow': self._estimate_saturation_flow(data)
                }
        
        return flows
    
    def _estimate_saturation_flow(self, direction_data: Dict) -> float:
        """估算饱和流量"""
        # 基础饱和流量 (vehicles/hour/lane)
        base_saturation = 1800
        
        # 根据车辆类型调整
        vehicle_types = direction_data.get('vehicleTypes', {})
        adjustment_factor = 1.0
        
        if vehicle_types:
            total_vehicles = sum(vehicle_types.values())
            if total_vehicles > 0:
                # 大型车辆降低饱和流量
                truck_ratio = vehicle_types.get('truck', 0) / total_vehicles
                bus_ratio = vehicle_types.get('bus', 0) / total_vehicles
                adjustment_factor = 1.0 - (truck_ratio * 0.3 + bus_ratio * 0.4)
        
        # 假设每个方向2车道
        lane_count = 2
        return base_saturation * lane_count * adjustment_factor
    
    def _calculate_optimal_timing(self, flows: Dict) -> Dict:
        """使用Webster方法计算最优配时"""
        # 计算总损失时间
        total_lost_time = 4 * (self.yellow_time + self.all_red_time)  # 4个相位
        
        # 计算关键流量比
        critical_ratios = {}
        for direction, flow_data in flows.items():
            flow_rate = flow_data['flow_rate']
            saturation_flow = flow_data['saturation_flow']
            critical_ratios[direction] = flow_rate / saturation_flow if saturation_flow > 0 else 0
        
        # 分组相位（东西一组，南北一组）
        ew_ratio = max(critical_ratios.get('east', 0), critical_ratios.get('west', 0))
        ns_ratio = max(critical_ratios.get('north', 0), critical_ratios.get('south', 0))
        
        total_critical_ratio = ew_ratio + ns_ratio
        
        # 计算最优周期时长（Webster公式）
        if total_critical_ratio > 0:
            optimal_cycle = (1.5 * total_lost_time + 5) / (1 - total_critical_ratio)
            optimal_cycle = max(self.min_cycle_time, min(self.max_cycle_time, optimal_cycle))
        else:
            optimal_cycle = self.min_cycle_time
        
        # 计算有效绿灯时间
        effective_green = optimal_cycle - total_lost_time
        
        # 分配绿灯时间
        if total_critical_ratio > 0:
            ew_green = (ew_ratio / total_critical_ratio) * effective_green
            ns_green = (ns_ratio / total_critical_ratio) * effective_green
        else:
            ew_green = ns_green = effective_green / 2
        
        # 确保绿灯时间在合理范围内
        ew_green = max(self.min_green_time, min(self.max_green_time, ew_green))
        ns_green = max(self.min_green_time, min(self.max_green_time, ns_green))
        
        # 构建配时方案
        timing = {
            'cycle_time': int(optimal_cycle),
            'phases': [
                {
                    'duration': int(ew_green),
                    'state': 'GrGr',
                    'description': '东西方向绿灯'
                },
                {
                    'duration': self.yellow_time,
                    'state': 'yryr',
                    'description': '东西方向黄灯'
                },
                {
                    'duration': int(ns_green),
                    'state': 'rGrG',
                    'description': '南北方向绿灯'
                },
                {
                    'duration': self.yellow_time,
                    'state': 'ryry',
                    'description': '南北方向黄灯'
                }
            ],
            'critical_ratios': critical_ratios,
            'optimization_parameters': {
                'ew_ratio': ew_ratio,
                'ns_ratio': ns_ratio,
                'total_lost_time': total_lost_time
            }
        }
        
        return timing
    
    def _evaluate_improvement(self, flows: Dict, current_timing: Optional[Dict], optimal_timing: Dict) -> float:
        """评估优化改善效果"""
        if not current_timing:
            # 如果没有当前配时，假设使用固定配时
            current_timing = {
                'cycle_time': 120,
                'phases': [
                    {'duration': 30, 'state': 'GrGr'},
                    {'duration': 5, 'state': 'yryr'},
                    {'duration': 30, 'state': 'rGrG'},
                    {'duration': 5, 'state': 'ryry'}
                ]
            }
        
        # 计算当前配时的延误
        current_delay = self._calculate_delay(flows, current_timing)
        
        # 计算优化配时的延误
        optimal_delay = self._calculate_delay(flows, optimal_timing)
        
        # 计算改善百分比
        if current_delay > 0:
            improvement = ((current_delay - optimal_delay) / current_delay) * 100
            return max(0, improvement)  # 确保不为负数
        
        return 0
    
    def _calculate_delay(self, flows: Dict, timing: Dict) -> float:
        """计算平均延误时间（Webster延误公式）"""
        total_delay = 0
        cycle_time = timing['cycle_time']
        
        # 提取绿灯时间
        ew_green = 0
        ns_green = 0
        
        for phase in timing['phases']:
            if phase['state'] == 'GrGr':
                ew_green = phase['duration']
            elif phase['state'] == 'rGrG':
                ns_green = phase['duration']
        
        # 计算各方向延误
        direction_green_mapping = {
            'east': ew_green,
            'west': ew_green,
            'north': ns_green,
            'south': ns_green
        }
        
        for direction, flow_data in flows.items():
            green_time = direction_green_mapping.get(direction, 0)
            if green_time <= 0:
                continue
            
            flow_rate = flow_data['flow_rate'] / 3600  # 转换为vehicles/second
            saturation_flow = flow_data['saturation_flow'] / 3600  # 转换为vehicles/second
            
            if saturation_flow <= 0:
                continue
            
            # 计算饱和度
            saturation = flow_rate / saturation_flow
            
            if saturation >= 1.0:
                # 过饱和情况，延误很大
                delay = 999999
            else:
                # Webster延误公式
                red_time = cycle_time - green_time
                uniform_delay = (cycle_time * (1 - green_time/cycle_time)**2) / (2 * (1 - saturation))
                random_delay = (saturation**2) / (2 * flow_rate * (1 - saturation))
                delay = uniform_delay + random_delay
            
            total_delay += delay * flow_rate
        
        return total_delay
    
    def optimize_flow_balance(self, traffic_data: Dict) -> Dict:
        """
        优化交通流量平衡
        
        Args:
            traffic_data: 交通数据
            
        Returns:
            Dict: 流量平衡优化结果
        """
        try:
            self.logger.info("开始交通流量平衡优化")
            
            flows = self._extract_flow_data(traffic_data)
            
            # 计算流量不平衡度
            imbalance = self._calculate_flow_imbalance(flows)
            
            # 生成平衡建议
            balance_suggestions = self._generate_balance_suggestions(flows, imbalance)
            
            result = {
                'current_imbalance': imbalance,
                'balance_suggestions': balance_suggestions,
                'optimization_method': 'flow_balance_analysis',
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"流量平衡优化完成，当前不平衡度: {imbalance:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"流量平衡优化失败: {e}")
            raise
    
    def _calculate_flow_imbalance(self, flows: Dict) -> float:
        """计算流量不平衡度"""
        if not flows:
            return 0.0
        
        flow_rates = [flow_data['flow_rate'] for flow_data in flows.values()]
        
        if not flow_rates:
            return 0.0
        
        mean_flow = sum(flow_rates) / len(flow_rates)
        
        if mean_flow == 0:
            return 0.0
        
        # 计算变异系数作为不平衡度指标
        variance = sum((flow - mean_flow)**2 for flow in flow_rates) / len(flow_rates)
        std_dev = math.sqrt(variance)
        coefficient_of_variation = std_dev / mean_flow
        
        return coefficient_of_variation
    
    def _generate_balance_suggestions(self, flows: Dict, imbalance: float) -> List[Dict]:
        """生成流量平衡建议"""
        suggestions = []
        
        if imbalance > 0.3:  # 高不平衡
            suggestions.append({
                'priority': 'high',
                'type': 'signal_timing',
                'description': '流量严重不平衡，建议调整信号灯配时以平衡各方向流量',
                'action': '增加高流量方向的绿灯时间'
            })
        elif imbalance > 0.2:  # 中等不平衡
            suggestions.append({
                'priority': 'medium',
                'type': 'traffic_management',
                'description': '流量存在一定不平衡，建议优化交通组织',
                'action': '考虑调整车道功能或增加引导标识'
            })
        else:  # 低不平衡
            suggestions.append({
                'priority': 'low',
                'type': 'monitoring',
                'description': '流量相对平衡，建议继续监控',
                'action': '保持现有配时方案，定期评估'
            })
        
        return suggestions
    
    def generate_comprehensive_optimization(self, traffic_data: Dict) -> Dict:
        """
        生成综合优化方案
        
        Args:
            traffic_data: 交通数据
            
        Returns:
            Dict: 综合优化方案
        """
        try:
            self.logger.info("开始生成综合优化方案")
            
            # 信号灯配时优化
            signal_optimization = self.optimize_signal_timing(traffic_data)
            
            # 流量平衡优化
            flow_optimization = self.optimize_flow_balance(traffic_data)
            
            # 综合评估
            overall_score = self._calculate_overall_score(signal_optimization, flow_optimization)
            
            result = {
                'signal_timing_optimization': signal_optimization,
                'flow_balance_optimization': flow_optimization,
                'overall_improvement_score': overall_score,
                'recommendations': self._generate_comprehensive_recommendations(signal_optimization, flow_optimization),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"综合优化方案生成完成，总体评分: {overall_score:.1f}")
            return result
            
        except Exception as e:
            self.logger.error(f"生成综合优化方案失败: {e}")
            raise
    
    def _calculate_overall_score(self, signal_opt: Dict, flow_opt: Dict) -> float:
        """计算总体优化评分"""
        signal_improvement = signal_opt.get('improvement_percentage', 0)
        flow_imbalance = flow_opt.get('current_imbalance', 1.0)
        
        # 信号优化权重70%，流量平衡权重30%
        signal_score = min(100, signal_improvement)
        balance_score = max(0, 100 - flow_imbalance * 100)
        
        overall_score = signal_score * 0.7 + balance_score * 0.3
        return overall_score
    
    def _generate_comprehensive_recommendations(self, signal_opt: Dict, flow_opt: Dict) -> List[Dict]:
        """生成综合建议"""
        recommendations = []
        
        # 添加信号优化建议
        signal_improvement = signal_opt.get('improvement_percentage', 0)
        if signal_improvement > 10:
            recommendations.append({
                'type': 'signal_timing',
                'priority': 'high',
                'title': '应用优化信号配时',
                'description': f'建议应用优化后的信号配时方案，可提升{signal_improvement:.1f}%的通行效率',
                'implementation': '立即实施'
            })
        
        # 添加流量平衡建议
        balance_suggestions = flow_opt.get('balance_suggestions', [])
        recommendations.extend(balance_suggestions)
        
        return recommendations
