{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, openBlock as _openBlock, createBlock as _createBlock, createElementB<PERSON> as _createElementBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"optimization-comparison\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"improvement-overview\"\n};\nconst _hoisted_4 = {\n  class: \"improvement-card\"\n};\nconst _hoisted_5 = {\n  class: \"improvement-content\"\n};\nconst _hoisted_6 = {\n  class: \"improvement-value\"\n};\nconst _hoisted_7 = {\n  class: \"improvement-card\"\n};\nconst _hoisted_8 = {\n  class: \"improvement-content\"\n};\nconst _hoisted_9 = {\n  class: \"improvement-value\"\n};\nconst _hoisted_10 = {\n  class: \"improvement-card\"\n};\nconst _hoisted_11 = {\n  class: \"improvement-content\"\n};\nconst _hoisted_12 = {\n  class: \"improvement-value\"\n};\nconst _hoisted_13 = {\n  class: \"improvement-card\"\n};\nconst _hoisted_14 = {\n  class: \"improvement-content\"\n};\nconst _hoisted_15 = {\n  class: \"improvement-value\"\n};\nconst _hoisted_16 = {\n  class: \"comparison-charts\"\n};\nconst _hoisted_17 = {\n  class: \"chart-section\"\n};\nconst _hoisted_18 = {\n  ref: \"performanceChartRef\",\n  class: \"chart-container\"\n};\nconst _hoisted_19 = {\n  class: \"chart-section\"\n};\nconst _hoisted_20 = {\n  ref: \"improvementChartRef\",\n  class: \"chart-container\"\n};\nconst _hoisted_21 = {\n  class: \"comparison-table\"\n};\nconst _hoisted_22 = {\n  class: \"value-before\"\n};\nconst _hoisted_23 = {\n  class: \"value-after\"\n};\nconst _hoisted_24 = {\n  key: 0,\n  class: \"signal-timing-comparison\"\n};\nconst _hoisted_25 = {\n  class: \"timing-section\"\n};\nconst _hoisted_26 = {\n  class: \"cycle-info\"\n};\nconst _hoisted_27 = {\n  class: \"timing-section\"\n};\nconst _hoisted_28 = {\n  class: \"cycle-info\"\n};\nconst _hoisted_29 = {\n  class: \"optimization-recommendations\"\n};\nconst _hoisted_30 = {\n  class: \"recommendations-list\"\n};\nconst _hoisted_31 = {\n  class: \"recommendation-header\"\n};\nconst _hoisted_32 = {\n  class: \"recommendation-title\"\n};\nconst _hoisted_33 = {\n  class: \"recommendation-content\"\n};\nconst _hoisted_34 = {\n  class: \"recommendation-actions\"\n};\nconst _hoisted_35 = {\n  class: \"action-text\"\n};\nconst _hoisted_36 = {\n  key: 0,\n  class: \"recommendation-effect\"\n};\nconst _hoisted_37 = {\n  class: \"effect-text\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"comparison-card\",\n    shadow: \"hover\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"h3\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-data-analysis\"\n    }), _createTextVNode(\" 优化效果对比分析\")], -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: $setup.refreshData\n    }, {\n      default: _withCtx(() => _cache[0] || (_cache[0] = [_createElementVNode(\"i\", {\n        class: \"el-icon-refresh\"\n      }, null, -1 /* HOISTED */), _createTextVNode(\" 刷新数据 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_cache[10] || (_cache[10] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-trend-charts\"\n    }), _createTextVNode(\" 总体改善效果\")], -1 /* HOISTED */)), _createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n          class: \"improvement-icon\"\n        }, [_createElementVNode(\"i\", {\n          class: \"el-icon-timer\",\n          style: {\n            \"color\": \"#409eff\"\n          }\n        })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.formatPercentage($setup.delayReduction)), 1 /* TEXT */), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n          class: \"improvement-label\"\n        }, \"延误减少\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"improvement-trend\", $setup.getTrendClass($setup.delayReduction)])\n        }, [_createElementVNode(\"i\", {\n          class: _normalizeClass($setup.getTrendIcon($setup.delayReduction))\n        }, null, 2 /* CLASS */), _createTextVNode(\" \" + _toDisplayString($setup.formatChange($setup.delayReduction)), 1 /* TEXT */)], 2 /* CLASS */)])])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n          class: \"improvement-icon\"\n        }, [_createElementVNode(\"i\", {\n          class: \"el-icon-right\",\n          style: {\n            \"color\": \"#67c23a\"\n          }\n        })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.formatPercentage($setup.throughputIncrease)), 1 /* TEXT */), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n          class: \"improvement-label\"\n        }, \"通行能力提升\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"improvement-trend\", $setup.getTrendClass($setup.throughputIncrease)])\n        }, [_createElementVNode(\"i\", {\n          class: _normalizeClass($setup.getTrendIcon($setup.throughputIncrease))\n        }, null, 2 /* CLASS */), _createTextVNode(\" \" + _toDisplayString($setup.formatChange($setup.throughputIncrease)), 1 /* TEXT */)], 2 /* CLASS */)])])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n          class: \"improvement-icon\"\n        }, [_createElementVNode(\"i\", {\n          class: \"el-icon-odometer\",\n          style: {\n            \"color\": \"#e6a23c\"\n          }\n        })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString($setup.formatPercentage($setup.speedImprovement)), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n          class: \"improvement-label\"\n        }, \"平均速度提升\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"improvement-trend\", $setup.getTrendClass($setup.speedImprovement)])\n        }, [_createElementVNode(\"i\", {\n          class: _normalizeClass($setup.getTrendIcon($setup.speedImprovement))\n        }, null, 2 /* CLASS */), _createTextVNode(\" \" + _toDisplayString($setup.formatChange($setup.speedImprovement)), 1 /* TEXT */)], 2 /* CLASS */)])])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n          class: \"improvement-icon\"\n        }, [_createElementVNode(\"i\", {\n          class: \"el-icon-cpu\",\n          style: {\n            \"color\": \"#f56c6c\"\n          }\n        })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, _toDisplayString($setup.formatPercentage($setup.fuelSaving)), 1 /* TEXT */), _cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n          class: \"improvement-label\"\n        }, \"燃油节省\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"improvement-trend\", $setup.getTrendClass($setup.fuelSaving)])\n        }, [_createElementVNode(\"i\", {\n          class: _normalizeClass($setup.getTrendIcon($setup.fuelSaving))\n        }, null, 2 /* CLASS */), _createTextVNode(\" \" + _toDisplayString($setup.formatChange($setup.fuelSaving)), 1 /* TEXT */)], 2 /* CLASS */)])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, [_cache[11] || (_cache[11] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n          class: \"el-icon-data-line\"\n        }), _createTextVNode(\" 性能指标对比\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, null, 512 /* NEED_PATCH */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_cache[12] || (_cache[12] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n          class: \"el-icon-pie-chart\"\n        }), _createTextVNode(\" 改善效果分布\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_20, null, 512 /* NEED_PATCH */)])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_21, [_cache[13] || (_cache[13] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-s-data\"\n    }), _createTextVNode(\" 详细数据对比\")], -1 /* HOISTED */)), _createVNode(_component_el_table, {\n      data: $setup.comparisonData,\n      border: \"\",\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        prop: \"metric\",\n        label: \"指标\",\n        width: \"150\",\n        fixed: \"left\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"strong\", null, _toDisplayString(scope.row.metric), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"unit\",\n        label: \"单位\",\n        width: \"80\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"before\",\n        label: \"优化前\",\n        width: \"120\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"span\", _hoisted_22, _toDisplayString($setup.formatValue(scope.row.before, scope.row.unit)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"after\",\n        label: \"优化后\",\n        width: \"120\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"span\", _hoisted_23, _toDisplayString($setup.formatValue(scope.row.after, scope.row.unit)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"improvement\",\n        label: \"改善幅度\",\n        width: \"120\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: $setup.getImprovementTagType(scope.row.improvement),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatPercentage(scope.row.improvement)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"description\",\n        label: \"说明\",\n        \"min-width\": \"200\"\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])]), $setup.signalTimingData ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_cache[16] || (_cache[16] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-warning\"\n    }), _createTextVNode(\" 信号配时方案对比\")], -1 /* HOISTED */)), _createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_25, [_cache[14] || (_cache[14] = _createElementVNode(\"h5\", null, \"优化前配时\", -1 /* HOISTED */)), _createVNode(_component_el_table, {\n          data: $setup.signalTimingData.before,\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_table_column, {\n            prop: \"phase\",\n            label: \"相位\",\n            width: \"80\"\n          }), _createVNode(_component_el_table_column, {\n            prop: \"duration\",\n            label: \"时长(秒)\",\n            width: \"100\"\n          }), _createVNode(_component_el_table_column, {\n            prop: \"state\",\n            label: \"状态\",\n            width: \"100\"\n          }, {\n            default: _withCtx(scope => [_createVNode(_component_el_tag, {\n              size: \"small\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.state), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_table_column, {\n            prop: \"description\",\n            label: \"描述\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"data\"]), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"span\", null, \"周期时长: \" + _toDisplayString($setup.signalTimingData.beforeCycle) + \"秒\", 1 /* TEXT */)])])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_27, [_cache[15] || (_cache[15] = _createElementVNode(\"h5\", null, \"优化后配时\", -1 /* HOISTED */)), _createVNode(_component_el_table, {\n          data: $setup.signalTimingData.after,\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_table_column, {\n            prop: \"phase\",\n            label: \"相位\",\n            width: \"80\"\n          }), _createVNode(_component_el_table_column, {\n            prop: \"duration\",\n            label: \"时长(秒)\",\n            width: \"100\"\n          }, {\n            default: _withCtx(scope => [_createElementVNode(\"span\", {\n              class: _normalizeClass({\n                'improved-value': scope.row.improved\n              })\n            }, _toDisplayString(scope.row.duration), 3 /* TEXT, CLASS */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_table_column, {\n            prop: \"state\",\n            label: \"状态\",\n            width: \"100\"\n          }, {\n            default: _withCtx(scope => [_createVNode(_component_el_tag, {\n              size: \"small\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.state), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_table_column, {\n            prop: \"description\",\n            label: \"描述\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"data\"]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"span\", null, \"周期时长: \" + _toDisplayString($setup.signalTimingData.afterCycle) + \"秒\", 1 /* TEXT */), $setup.signalTimingData.cycleImprovement ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          type: \"success\",\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(\" 优化 \" + _toDisplayString($setup.formatPercentage($setup.signalTimingData.cycleImprovement)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        })) : _createCommentVNode(\"v-if\", true)])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_29, [_cache[19] || (_cache[19] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-document\"\n    }), _createTextVNode(\" 优化建议\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_30, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recommendations, (recommendation, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: \"recommendation-item\"\n      }, [_createElementVNode(\"div\", _hoisted_31, [_createVNode(_component_el_tag, {\n        type: $setup.getPriorityTagType(recommendation.priority),\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getPriorityText(recommendation.priority)), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"]), _createElementVNode(\"span\", _hoisted_32, _toDisplayString(recommendation.title), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"p\", null, _toDisplayString(recommendation.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_34, [_cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n        class: \"action-label\"\n      }, \"建议措施:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_35, _toDisplayString(recommendation.action), 1 /* TEXT */)]), recommendation.expectedEffect ? (_openBlock(), _createElementBlock(\"div\", _hoisted_36, [_cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n        class: \"effect-label\"\n      }, \"预期效果:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_37, _toDisplayString(recommendation.expectedEffect), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]);\n    }), 128 /* KEYED_FRAGMENT */))])])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "ref", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "shadow", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_createTextVNode", "_component_el_button", "type", "size", "onClick", "$setup", "refreshData", "default", "_cache", "_", "_hoisted_3", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_4", "style", "_hoisted_5", "_hoisted_6", "_toDisplayString", "formatPercentage", "delayReduction", "_normalizeClass", "getTrendClass", "getTrendIcon", "formatChange", "_hoisted_7", "_hoisted_8", "_hoisted_9", "throughputIncrease", "_hoisted_10", "_hoisted_11", "_hoisted_12", "speedImprovement", "_hoisted_13", "_hoisted_14", "_hoisted_15", "fuelSaving", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_table", "data", "comparisonData", "border", "_component_el_table_column", "prop", "label", "width", "fixed", "scope", "row", "metric", "_hoisted_22", "formatValue", "before", "unit", "_hoisted_23", "after", "_component_el_tag", "getImprovementTagType", "improvement", "signalTimingData", "_hoisted_24", "_hoisted_25", "state", "_hoisted_26", "beforeCycle", "_hoisted_27", "improved", "duration", "_hoisted_28", "afterCycle", "cycleImprovement", "_createBlock", "_createCommentVNode", "_hoisted_29", "_hoisted_30", "_Fragment", "_renderList", "recommendations", "recommendation", "index", "_hoisted_31", "getPriorityTagType", "priority", "getPriorityText", "_hoisted_32", "title", "_hoisted_33", "description", "_hoisted_34", "_hoisted_35", "action", "expectedEffect", "_hoisted_36", "_hoisted_37"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\OptimizationComparison.vue"], "sourcesContent": ["<template>\n  <div class=\"optimization-comparison\">\n    <el-card class=\"comparison-card\" shadow=\"hover\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3><i class=\"el-icon-data-analysis\"></i> 优化效果对比分析</h3>\n          <el-button type=\"primary\" size=\"small\" @click=\"refreshData\">\n            <i class=\"el-icon-refresh\"></i> 刷新数据\n          </el-button>\n        </div>\n      </template>\n\n      <!-- 总体改善指标 -->\n      <div class=\"improvement-overview\">\n        <h4><i class=\"el-icon-trend-charts\"></i> 总体改善效果</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <div class=\"improvement-card\">\n              <div class=\"improvement-icon\">\n                <i class=\"el-icon-timer\" style=\"color: #409eff\"></i>\n              </div>\n              <div class=\"improvement-content\">\n                <div class=\"improvement-value\">{{ formatPercentage(delayReduction) }}</div>\n                <div class=\"improvement-label\">延误减少</div>\n                <div class=\"improvement-trend\" :class=\"getTrendClass(delayReduction)\">\n                  <i :class=\"getTrendIcon(delayReduction)\"></i>\n                  {{ formatChange(delayReduction) }}\n                </div>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"6\">\n            <div class=\"improvement-card\">\n              <div class=\"improvement-icon\">\n                <i class=\"el-icon-right\" style=\"color: #67c23a\"></i>\n              </div>\n              <div class=\"improvement-content\">\n                <div class=\"improvement-value\">{{ formatPercentage(throughputIncrease) }}</div>\n                <div class=\"improvement-label\">通行能力提升</div>\n                <div class=\"improvement-trend\" :class=\"getTrendClass(throughputIncrease)\">\n                  <i :class=\"getTrendIcon(throughputIncrease)\"></i>\n                  {{ formatChange(throughputIncrease) }}\n                </div>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"6\">\n            <div class=\"improvement-card\">\n              <div class=\"improvement-icon\">\n                <i class=\"el-icon-odometer\" style=\"color: #e6a23c\"></i>\n              </div>\n              <div class=\"improvement-content\">\n                <div class=\"improvement-value\">{{ formatPercentage(speedImprovement) }}</div>\n                <div class=\"improvement-label\">平均速度提升</div>\n                <div class=\"improvement-trend\" :class=\"getTrendClass(speedImprovement)\">\n                  <i :class=\"getTrendIcon(speedImprovement)\"></i>\n                  {{ formatChange(speedImprovement) }}\n                </div>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"6\">\n            <div class=\"improvement-card\">\n              <div class=\"improvement-icon\">\n                <i class=\"el-icon-cpu\" style=\"color: #f56c6c\"></i>\n              </div>\n              <div class=\"improvement-content\">\n                <div class=\"improvement-value\">{{ formatPercentage(fuelSaving) }}</div>\n                <div class=\"improvement-label\">燃油节省</div>\n                <div class=\"improvement-trend\" :class=\"getTrendClass(fuelSaving)\">\n                  <i :class=\"getTrendIcon(fuelSaving)\"></i>\n                  {{ formatChange(fuelSaving) }}\n                </div>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 对比图表 -->\n      <div class=\"comparison-charts\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"chart-section\">\n              <h4><i class=\"el-icon-data-line\"></i> 性能指标对比</h4>\n              <div ref=\"performanceChartRef\" class=\"chart-container\"></div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"12\">\n            <div class=\"chart-section\">\n              <h4><i class=\"el-icon-pie-chart\"></i> 改善效果分布</h4>\n              <div ref=\"improvementChartRef\" class=\"chart-container\"></div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 详细对比表格 -->\n      <div class=\"comparison-table\">\n        <h4><i class=\"el-icon-s-data\"></i> 详细数据对比</h4>\n        <el-table :data=\"comparisonData\" border style=\"width: 100%\">\n          <el-table-column prop=\"metric\" label=\"指标\" width=\"150\" fixed=\"left\">\n            <template #default=\"scope\">\n              <strong>{{ scope.row.metric }}</strong>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\"></el-table-column>\n          <el-table-column prop=\"before\" label=\"优化前\" width=\"120\">\n            <template #default=\"scope\">\n              <span class=\"value-before\">{{ formatValue(scope.row.before, scope.row.unit) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"after\" label=\"优化后\" width=\"120\">\n            <template #default=\"scope\">\n              <span class=\"value-after\">{{ formatValue(scope.row.after, scope.row.unit) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"improvement\" label=\"改善幅度\" width=\"120\">\n            <template #default=\"scope\">\n              <el-tag :type=\"getImprovementTagType(scope.row.improvement)\" size=\"small\">\n                {{ formatPercentage(scope.row.improvement) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"description\" label=\"说明\" min-width=\"200\"></el-table-column>\n        </el-table>\n      </div>\n\n      <!-- 信号配时对比 -->\n      <div class=\"signal-timing-comparison\" v-if=\"signalTimingData\">\n        <h4><i class=\"el-icon-warning\"></i> 信号配时方案对比</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"timing-section\">\n              <h5>优化前配时</h5>\n              <el-table :data=\"signalTimingData.before\" size=\"small\">\n                <el-table-column prop=\"phase\" label=\"相位\" width=\"80\"></el-table-column>\n                <el-table-column prop=\"duration\" label=\"时长(秒)\" width=\"100\"></el-table-column>\n                <el-table-column prop=\"state\" label=\"状态\" width=\"100\">\n                  <template #default=\"scope\">\n                    <el-tag size=\"small\">{{ scope.row.state }}</el-tag>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"description\" label=\"描述\"></el-table-column>\n              </el-table>\n              <div class=\"cycle-info\">\n                <span>周期时长: {{ signalTimingData.beforeCycle }}秒</span>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"12\">\n            <div class=\"timing-section\">\n              <h5>优化后配时</h5>\n              <el-table :data=\"signalTimingData.after\" size=\"small\">\n                <el-table-column prop=\"phase\" label=\"相位\" width=\"80\"></el-table-column>\n                <el-table-column prop=\"duration\" label=\"时长(秒)\" width=\"100\">\n                  <template #default=\"scope\">\n                    <span :class=\"{ 'improved-value': scope.row.improved }\">\n                      {{ scope.row.duration }}\n                    </span>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"state\" label=\"状态\" width=\"100\">\n                  <template #default=\"scope\">\n                    <el-tag size=\"small\">{{ scope.row.state }}</el-tag>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"description\" label=\"描述\"></el-table-column>\n              </el-table>\n              <div class=\"cycle-info\">\n                <span>周期时长: {{ signalTimingData.afterCycle }}秒</span>\n                <el-tag v-if=\"signalTimingData.cycleImprovement\" type=\"success\" size=\"small\">\n                  优化 {{ formatPercentage(signalTimingData.cycleImprovement) }}\n                </el-tag>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 优化建议 -->\n      <div class=\"optimization-recommendations\">\n        <h4><i class=\"el-icon-document\"></i> 优化建议</h4>\n        <div class=\"recommendations-list\">\n          <div \n            v-for=\"(recommendation, index) in recommendations\" \n            :key=\"index\"\n            class=\"recommendation-item\">\n            <div class=\"recommendation-header\">\n              <el-tag :type=\"getPriorityTagType(recommendation.priority)\" size=\"small\">\n                {{ getPriorityText(recommendation.priority) }}\n              </el-tag>\n              <span class=\"recommendation-title\">{{ recommendation.title }}</span>\n            </div>\n            <div class=\"recommendation-content\">\n              <p>{{ recommendation.description }}</p>\n              <div class=\"recommendation-actions\">\n                <span class=\"action-label\">建议措施:</span>\n                <span class=\"action-text\">{{ recommendation.action }}</span>\n              </div>\n              <div class=\"recommendation-effect\" v-if=\"recommendation.expectedEffect\">\n                <span class=\"effect-label\">预期效果:</span>\n                <span class=\"effect-text\">{{ recommendation.expectedEffect }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'OptimizationComparison',\n  props: {\n    optimizationResult: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['refresh-requested'],\n  setup(props, { emit }) {\n    // 图表引用\n    const performanceChartRef = ref(null)\n    const improvementChartRef = ref(null)\n    let performanceChart = null\n    let improvementChart = null\n    \n    // 改善指标\n    const delayReduction = ref(15.6)\n    const throughputIncrease = ref(12.3)\n    const speedImprovement = ref(8.9)\n    const fuelSaving = ref(11.2)\n    \n    // 对比数据\n    const comparisonData = reactive([\n      {\n        metric: '平均延误时间',\n        unit: '秒',\n        before: 45.2,\n        after: 38.1,\n        improvement: 15.7,\n        description: '车辆在交叉口的平均等待时间'\n      },\n      {\n        metric: '通行能力',\n        unit: '辆/小时',\n        before: 1850,\n        after: 2078,\n        improvement: 12.3,\n        description: '单位时间内通过交叉口的车辆数量'\n      },\n      {\n        metric: '平均行程速度',\n        unit: 'km/h',\n        before: 28.5,\n        after: 31.0,\n        improvement: 8.8,\n        description: '车辆通过交叉口区域的平均速度'\n      },\n      {\n        metric: '燃油消耗',\n        unit: '升/百公里',\n        before: 8.9,\n        after: 7.9,\n        improvement: 11.2,\n        description: '车辆通过交叉口的燃油消耗'\n      },\n      {\n        metric: '排队长度',\n        unit: '米',\n        before: 85.3,\n        after: 68.7,\n        improvement: 19.5,\n        description: '各方向最大排队长度'\n      }\n    ])\n    \n    // 信号配时对比数据\n    const signalTimingData = reactive({\n      beforeCycle: 120,\n      afterCycle: 105,\n      cycleImprovement: 12.5,\n      before: [\n        { phase: '相位1', duration: 30, state: 'GrGr', description: '东西绿灯' },\n        { phase: '相位2', duration: 5, state: 'yryr', description: '东西黄灯' },\n        { phase: '相位3', duration: 30, state: 'rGrG', description: '南北绿灯' },\n        { phase: '相位4', duration: 5, state: 'ryry', description: '南北黄灯' }\n      ],\n      after: [\n        { phase: '相位1', duration: 35, state: 'GrGr', description: '东西绿灯', improved: true },\n        { phase: '相位2', duration: 4, state: 'yryr', description: '东西黄灯' },\n        { phase: '相位3', duration: 25, state: 'rGrG', description: '南北绿灯', improved: true },\n        { phase: '相位4', duration: 4, state: 'ryry', description: '南北黄灯' }\n      ]\n    })\n    \n    // 优化建议\n    const recommendations = reactive([\n      {\n        priority: 'high',\n        title: '应用优化信号配时方案',\n        description: '建议立即应用优化后的信号配时方案，可显著提升交叉口通行效率。',\n        action: '调整信号灯控制器参数，实施新的配时方案',\n        expectedEffect: '减少15.6%的延误时间，提升12.3%的通行能力'\n      },\n      {\n        priority: 'medium',\n        title: '优化车道功能配置',\n        description: '根据各方向流量特点，建议调整车道功能配置以进一步平衡流量。',\n        action: '增设左转专用车道或调整车道标线',\n        expectedEffect: '进一步提升5-8%的通行效率'\n      },\n      {\n        priority: 'low',\n        title: '加强交通引导',\n        description: '在高峰时段增加交通引导，帮助车辆选择最优路径。',\n        action: '部署智能交通引导系统或增加人工引导',\n        expectedEffect: '减少局部拥堵，提升整体通行体验'\n      }\n    ])\n    \n    // 方法\n    const formatPercentage = (value) => {\n      return `${value.toFixed(1)}%`\n    }\n    \n    const formatChange = (value) => {\n      return value > 0 ? `+${value.toFixed(1)}%` : `${value.toFixed(1)}%`\n    }\n    \n    const formatValue = (value, unit) => {\n      if (typeof value === 'number') {\n        return `${value.toFixed(1)} ${unit}`\n      }\n      return `${value} ${unit}`\n    }\n    \n    const getTrendClass = (value) => {\n      return value > 0 ? 'trend-up' : value < 0 ? 'trend-down' : 'trend-neutral'\n    }\n    \n    const getTrendIcon = (value) => {\n      return value > 0 ? 'el-icon-top' : value < 0 ? 'el-icon-bottom' : 'el-icon-minus'\n    }\n    \n    const getImprovementTagType = (improvement) => {\n      if (improvement > 15) return 'success'\n      if (improvement > 5) return 'warning'\n      return 'info'\n    }\n    \n    const getPriorityTagType = (priority) => {\n      const typeMap = {\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info'\n      }\n      return typeMap[priority] || 'info'\n    }\n    \n    const getPriorityText = (priority) => {\n      const textMap = {\n        'high': '高优先级',\n        'medium': '中优先级',\n        'low': '低优先级'\n      }\n      return textMap[priority] || '未知'\n    }\n    \n    const refreshData = () => {\n      emit('refresh-requested')\n      // 这里可以添加数据刷新逻辑\n    }\n    \n    const initCharts = () => {\n      nextTick(() => {\n        // 初始化性能对比图表\n        if (performanceChartRef.value) {\n          performanceChart = echarts.init(performanceChartRef.value)\n          updatePerformanceChart()\n        }\n        \n        // 初始化改善效果图表\n        if (improvementChartRef.value) {\n          improvementChart = echarts.init(improvementChartRef.value)\n          updateImprovementChart()\n        }\n      })\n    }\n    \n    const updatePerformanceChart = () => {\n      if (!performanceChart) return\n      \n      const metrics = comparisonData.map(item => item.metric)\n      const beforeData = comparisonData.map(item => item.before)\n      const afterData = comparisonData.map(item => item.after)\n      \n      const option = {\n        title: {\n          text: '性能指标对比',\n          textStyle: { fontSize: 14 }\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: ['优化前', '优化后']\n        },\n        xAxis: {\n          type: 'category',\n          data: metrics,\n          axisLabel: {\n            interval: 0,\n            rotate: 45\n          }\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [\n          {\n            name: '优化前',\n            type: 'bar',\n            data: beforeData,\n            itemStyle: {\n              color: '#f56c6c'\n            }\n          },\n          {\n            name: '优化后',\n            type: 'bar',\n            data: afterData,\n            itemStyle: {\n              color: '#67c23a'\n            }\n          }\n        ]\n      }\n      \n      performanceChart.setOption(option)\n    }\n    \n    const updateImprovementChart = () => {\n      if (!improvementChart) return\n      \n      const data = [\n        { name: '延误减少', value: delayReduction.value },\n        { name: '通行能力提升', value: throughputIncrease.value },\n        { name: '速度提升', value: speedImprovement.value },\n        { name: '燃油节省', value: fuelSaving.value }\n      ]\n      \n      const option = {\n        title: {\n          text: '改善效果分布',\n          textStyle: { fontSize: 14 }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c}%'\n        },\n        series: [{\n          name: '改善效果',\n          type: 'pie',\n          radius: '60%',\n          data: data,\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }]\n      }\n      \n      improvementChart.setOption(option)\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      initCharts()\n    })\n    \n    return {\n      // 响应式数据\n      delayReduction,\n      throughputIncrease,\n      speedImprovement,\n      fuelSaving,\n      comparisonData,\n      signalTimingData,\n      recommendations,\n      \n      // 图表引用\n      performanceChartRef,\n      improvementChartRef,\n      \n      // 方法\n      formatPercentage,\n      formatChange,\n      formatValue,\n      getTrendClass,\n      getTrendIcon,\n      getImprovementTagType,\n      getPriorityTagType,\n      getPriorityText,\n      refreshData\n    }\n  }\n}\n</script>\n\n<style scoped>\n.optimization-comparison {\n  margin-bottom: 20px;\n}\n\n.comparison-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  color: #303133;\n}\n\n.improvement-overview,\n.comparison-charts,\n.comparison-table,\n.signal-timing-comparison,\n.optimization-recommendations {\n  margin-bottom: 30px;\n}\n\n.improvement-overview h4,\n.comparison-charts h4,\n.comparison-table h4,\n.signal-timing-comparison h4,\n.optimization-recommendations h4 {\n  margin-bottom: 20px;\n  color: #606266;\n  font-size: 16px;\n}\n\n.improvement-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 8px;\n  border-left: 4px solid #409eff;\n  transition: transform 0.2s;\n}\n\n.improvement-card:hover {\n  transform: translateY(-2px);\n}\n\n.improvement-icon {\n  font-size: 28px;\n  margin-right: 15px;\n}\n\n.improvement-content {\n  flex: 1;\n}\n\n.improvement-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.improvement-label {\n  font-size: 12px;\n  color: #909399;\n  margin: 4px 0;\n}\n\n.improvement-trend {\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.trend-up {\n  color: #67c23a;\n}\n\n.trend-down {\n  color: #f56c6c;\n}\n\n.trend-neutral {\n  color: #909399;\n}\n\n.chart-section {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n}\n\n.chart-section h4 {\n  margin-bottom: 15px;\n  color: #303133;\n  font-size: 14px;\n}\n\n.chart-container {\n  height: 300px;\n  width: 100%;\n}\n\n.value-before {\n  color: #f56c6c;\n}\n\n.value-after {\n  color: #67c23a;\n  font-weight: bold;\n}\n\n.timing-section {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n}\n\n.timing-section h5 {\n  margin-bottom: 15px;\n  color: #303133;\n}\n\n.cycle-info {\n  margin-top: 10px;\n  font-size: 12px;\n  color: #606266;\n}\n\n.improved-value {\n  color: #67c23a;\n  font-weight: bold;\n}\n\n.recommendations-list {\n  space-y: 15px;\n}\n\n.recommendation-item {\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border-left: 4px solid #409eff;\n  margin-bottom: 15px;\n}\n\n.recommendation-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.recommendation-title {\n  margin-left: 10px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.recommendation-content p {\n  margin-bottom: 10px;\n  color: #606266;\n  line-height: 1.5;\n}\n\n.recommendation-actions,\n.recommendation-effect {\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n\n.action-label,\n.effect-label {\n  color: #909399;\n  margin-right: 8px;\n}\n\n.action-text,\n.effect-text {\n  color: #303133;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EAGzBA,KAAK,EAAC;AAAa;;EASrBA,KAAK,EAAC;AAAsB;;EAItBA,KAAK,EAAC;AAAkB;;EAItBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAmB;;EAW7BA,KAAK,EAAC;AAAkB;;EAItBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAmB;;EAW7BA,KAAK,EAAC;AAAkB;;EAItBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAmB;;EAW7BA,KAAK,EAAC;AAAkB;;EAItBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAmB;;EAanCA,KAAK,EAAC;AAAmB;;EAGnBA,KAAK,EAAC;AAAe;;EAEnBC,GAAG,EAAC,qBAAqB;EAACD,KAAK,EAAC;;;EAKlCA,KAAK,EAAC;AAAe;;EAEnBC,GAAG,EAAC,qBAAqB;EAACD,KAAK,EAAC;;;EAOxCA,KAAK,EAAC;AAAkB;;EAWfA,KAAK,EAAC;AAAc;;EAKpBA,KAAK,EAAC;AAAa;;EAtHvCE,GAAA;EAqIWF,KAAK,EAAC;;;EAIAA,KAAK,EAAC;AAAgB;;EAYpBA,KAAK,EAAC;AAAY;;EAOpBA,KAAK,EAAC;AAAgB;;EAkBpBA,KAAK,EAAC;AAAY;;EAY1BA,KAAK,EAAC;AAA8B;;EAElCA,KAAK,EAAC;AAAsB;;EAKxBA,KAAK,EAAC;AAAuB;;EAI1BA,KAAK,EAAC;AAAsB;;EAE/BA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAwB;;EAE3BA,KAAK,EAAC;AAAa;;EA3MzCE,GAAA;EA6MmBF,KAAK,EAAC;;;EAEHA,KAAK,EAAC;AAAa;;;;;;;;;uBA9MvCG,mBAAA,CAqNM,OArNNC,UAqNM,GApNJC,YAAA,CAmNUC,kBAAA;IAnNDN,KAAK,EAAC,iBAAiB;IAACO,MAAM,EAAC;;IAC3BC,MAAM,EAAAC,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALNC,UAKM,G,0BAJJD,mBAAA,CAAuD,aAAnDA,mBAAA,CAAqC;MAAlCV,KAAK,EAAC;IAAuB,IAL9CY,gBAAA,CAKmD,WAAS,E,sBAClDP,YAAA,CAEYQ,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAACC,IAAI,EAAC,OAAO;MAAEC,OAAK,EAAEC,MAAA,CAAAC;;MANzDC,OAAA,EAAAV,QAAA,CAOY,MAA+BW,MAAA,QAAAA,MAAA,OAA/BV,mBAAA,CAA+B;QAA5BV,KAAK,EAAC;MAAiB,4BAPtCY,gBAAA,CAO2C,QACjC,E;MARVS,CAAA;;IAAAF,OAAA,EAAAV,QAAA,CAaM,MAmEM,CAnENC,mBAAA,CAmEM,OAnENY,UAmEM,G,4BAlEJZ,mBAAA,CAAoD,aAAhDA,mBAAA,CAAoC;MAAjCV,KAAK,EAAC;IAAsB,IAd3CY,gBAAA,CAcgD,SAAO,E,sBAC/CP,YAAA,CAgESkB,iBAAA;MAhEAC,MAAM,EAAE;IAAE;MAf3BL,OAAA,EAAAV,QAAA,CAgBU,MAcS,CAdTJ,YAAA,CAcSoB,iBAAA;QAdAC,IAAI,EAAE;MAAC;QAhB1BP,OAAA,EAAAV,QAAA,CAiBY,MAYM,CAZNC,mBAAA,CAYM,OAZNiB,UAYM,G,0BAXJjB,mBAAA,CAEM;UAFDV,KAAK,EAAC;QAAkB,IAC3BU,mBAAA,CAAoD;UAAjDV,KAAK,EAAC,eAAe;UAAC4B,KAAsB,EAAtB;YAAA;UAAA;iCAE3BlB,mBAAA,CAOM,OAPNmB,UAOM,GANJnB,mBAAA,CAA2E,OAA3EoB,UAA2E,EAAAC,gBAAA,CAAzCd,MAAA,CAAAe,gBAAgB,CAACf,MAAA,CAAAgB,cAAc,mB,0BACjEvB,mBAAA,CAAyC;UAApCV,KAAK,EAAC;QAAmB,GAAC,MAAI,sBACnCU,mBAAA,CAGM;UAHDV,KAAK,EAxB1BkC,eAAA,EAwB2B,mBAAmB,EAASjB,MAAA,CAAAkB,aAAa,CAAClB,MAAA,CAAAgB,cAAc;YACjEvB,mBAAA,CAA6C;UAAzCV,KAAK,EAzB3BkC,eAAA,CAyB6BjB,MAAA,CAAAmB,YAAY,CAACnB,MAAA,CAAAgB,cAAc;iCAzBxDrB,gBAAA,CAyB+D,GAC7C,GAAAmB,gBAAA,CAAGd,MAAA,CAAAoB,YAAY,CAACpB,MAAA,CAAAgB,cAAc,kB;QA1BhDZ,CAAA;UAgCUhB,YAAA,CAcSoB,iBAAA;QAdAC,IAAI,EAAE;MAAC;QAhC1BP,OAAA,EAAAV,QAAA,CAiCY,MAYM,CAZNC,mBAAA,CAYM,OAZN4B,UAYM,G,0BAXJ5B,mBAAA,CAEM;UAFDV,KAAK,EAAC;QAAkB,IAC3BU,mBAAA,CAAoD;UAAjDV,KAAK,EAAC,eAAe;UAAC4B,KAAsB,EAAtB;YAAA;UAAA;iCAE3BlB,mBAAA,CAOM,OAPN6B,UAOM,GANJ7B,mBAAA,CAA+E,OAA/E8B,UAA+E,EAAAT,gBAAA,CAA7Cd,MAAA,CAAAe,gBAAgB,CAACf,MAAA,CAAAwB,kBAAkB,mB,0BACrE/B,mBAAA,CAA2C;UAAtCV,KAAK,EAAC;QAAmB,GAAC,QAAM,sBACrCU,mBAAA,CAGM;UAHDV,KAAK,EAxC1BkC,eAAA,EAwC2B,mBAAmB,EAASjB,MAAA,CAAAkB,aAAa,CAAClB,MAAA,CAAAwB,kBAAkB;YACrE/B,mBAAA,CAAiD;UAA7CV,KAAK,EAzC3BkC,eAAA,CAyC6BjB,MAAA,CAAAmB,YAAY,CAACnB,MAAA,CAAAwB,kBAAkB;iCAzC5D7B,gBAAA,CAyCmE,GACjD,GAAAmB,gBAAA,CAAGd,MAAA,CAAAoB,YAAY,CAACpB,MAAA,CAAAwB,kBAAkB,kB;QA1CpDpB,CAAA;UAgDUhB,YAAA,CAcSoB,iBAAA;QAdAC,IAAI,EAAE;MAAC;QAhD1BP,OAAA,EAAAV,QAAA,CAiDY,MAYM,CAZNC,mBAAA,CAYM,OAZNgC,WAYM,G,0BAXJhC,mBAAA,CAEM;UAFDV,KAAK,EAAC;QAAkB,IAC3BU,mBAAA,CAAuD;UAApDV,KAAK,EAAC,kBAAkB;UAAC4B,KAAsB,EAAtB;YAAA;UAAA;iCAE9BlB,mBAAA,CAOM,OAPNiC,WAOM,GANJjC,mBAAA,CAA6E,OAA7EkC,WAA6E,EAAAb,gBAAA,CAA3Cd,MAAA,CAAAe,gBAAgB,CAACf,MAAA,CAAA4B,gBAAgB,mB,0BACnEnC,mBAAA,CAA2C;UAAtCV,KAAK,EAAC;QAAmB,GAAC,QAAM,sBACrCU,mBAAA,CAGM;UAHDV,KAAK,EAxD1BkC,eAAA,EAwD2B,mBAAmB,EAASjB,MAAA,CAAAkB,aAAa,CAAClB,MAAA,CAAA4B,gBAAgB;YACnEnC,mBAAA,CAA+C;UAA3CV,KAAK,EAzD3BkC,eAAA,CAyD6BjB,MAAA,CAAAmB,YAAY,CAACnB,MAAA,CAAA4B,gBAAgB;iCAzD1DjC,gBAAA,CAyDiE,GAC/C,GAAAmB,gBAAA,CAAGd,MAAA,CAAAoB,YAAY,CAACpB,MAAA,CAAA4B,gBAAgB,kB;QA1DlDxB,CAAA;UAgEUhB,YAAA,CAcSoB,iBAAA;QAdAC,IAAI,EAAE;MAAC;QAhE1BP,OAAA,EAAAV,QAAA,CAiEY,MAYM,CAZNC,mBAAA,CAYM,OAZNoC,WAYM,G,0BAXJpC,mBAAA,CAEM;UAFDV,KAAK,EAAC;QAAkB,IAC3BU,mBAAA,CAAkD;UAA/CV,KAAK,EAAC,aAAa;UAAC4B,KAAsB,EAAtB;YAAA;UAAA;iCAEzBlB,mBAAA,CAOM,OAPNqC,WAOM,GANJrC,mBAAA,CAAuE,OAAvEsC,WAAuE,EAAAjB,gBAAA,CAArCd,MAAA,CAAAe,gBAAgB,CAACf,MAAA,CAAAgC,UAAU,mB,0BAC7DvC,mBAAA,CAAyC;UAApCV,KAAK,EAAC;QAAmB,GAAC,MAAI,sBACnCU,mBAAA,CAGM;UAHDV,KAAK,EAxE1BkC,eAAA,EAwE2B,mBAAmB,EAASjB,MAAA,CAAAkB,aAAa,CAAClB,MAAA,CAAAgC,UAAU;YAC7DvC,mBAAA,CAAyC;UAArCV,KAAK,EAzE3BkC,eAAA,CAyE6BjB,MAAA,CAAAmB,YAAY,CAACnB,MAAA,CAAAgC,UAAU;iCAzEpDrC,gBAAA,CAyE2D,GACzC,GAAAmB,gBAAA,CAAGd,MAAA,CAAAoB,YAAY,CAACpB,MAAA,CAAAgC,UAAU,kB;QA1E5C5B,CAAA;;MAAAA,CAAA;UAmFMX,mBAAA,CAgBM,OAhBNwC,WAgBM,GAfJ7C,YAAA,CAcSkB,iBAAA;MAdAC,MAAM,EAAE;IAAE;MApF3BL,OAAA,EAAAV,QAAA,CAqFU,MAKS,CALTJ,YAAA,CAKSoB,iBAAA;QALAC,IAAI,EAAE;MAAE;QArF3BP,OAAA,EAAAV,QAAA,CAsFY,MAGM,CAHNC,mBAAA,CAGM,OAHNyC,WAGM,G,4BAFJzC,mBAAA,CAAiD,aAA7CA,mBAAA,CAAiC;UAA9BV,KAAK,EAAC;QAAmB,IAvF9CY,gBAAA,CAuFmD,SAAO,E,sBAC5CF,mBAAA,CAA6D,OAA7D0C,WAA6D,8B;QAxF3E/B,CAAA;UA4FUhB,YAAA,CAKSoB,iBAAA;QALAC,IAAI,EAAE;MAAE;QA5F3BP,OAAA,EAAAV,QAAA,CA6FY,MAGM,CAHNC,mBAAA,CAGM,OAHN2C,WAGM,G,4BAFJ3C,mBAAA,CAAiD,aAA7CA,mBAAA,CAAiC;UAA9BV,KAAK,EAAC;QAAmB,IA9F9CY,gBAAA,CA8FmD,SAAO,E,sBAC5CF,mBAAA,CAA6D,OAA7D4C,WAA6D,8B;QA/F3EjC,CAAA;;MAAAA,CAAA;UAsGMX,mBAAA,CA4BM,OA5BN6C,WA4BM,G,4BA3BJ7C,mBAAA,CAA8C,aAA1CA,mBAAA,CAA8B;MAA3BV,KAAK,EAAC;IAAgB,IAvGrCY,gBAAA,CAuG0C,SAAO,E,sBACzCP,YAAA,CAyBWmD,mBAAA;MAzBAC,IAAI,EAAExC,MAAA,CAAAyC,cAAc;MAAEC,MAAM,EAAN,EAAM;MAAC/B,KAAmB,EAAnB;QAAA;MAAA;;MAxGhDT,OAAA,EAAAV,QAAA,CAyGU,MAIkB,CAJlBJ,YAAA,CAIkBuD,0BAAA;QAJDC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;QAC/C7C,OAAO,EAAAV,QAAA,CACuBwD,KADhB,KACvBvD,mBAAA,CAAuC,gBAAAqB,gBAAA,CAA5BkC,KAAK,CAACC,GAAG,CAACC,MAAM,iB;QA3GzC9C,CAAA;UA8GUhB,YAAA,CAAqEuD,0BAAA;QAApDC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UAC9C1D,YAAA,CAIkBuD,0BAAA;QAJDC,IAAI,EAAC,QAAQ;QAACC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;QACpC5C,OAAO,EAAAV,QAAA,CACqEwD,KAD9D,KACvBvD,mBAAA,CAAqF,QAArF0D,WAAqF,EAAArC,gBAAA,CAAvDd,MAAA,CAAAoD,WAAW,CAACJ,KAAK,CAACC,GAAG,CAACI,MAAM,EAAEL,KAAK,CAACC,GAAG,CAACK,IAAI,kB;QAjHxFlD,CAAA;UAoHUhB,YAAA,CAIkBuD,0BAAA;QAJDC,IAAI,EAAC,OAAO;QAACC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;QACnC5C,OAAO,EAAAV,QAAA,CACmEwD,KAD5D,KACvBvD,mBAAA,CAAmF,QAAnF8D,WAAmF,EAAAzC,gBAAA,CAAtDd,MAAA,CAAAoD,WAAW,CAACJ,KAAK,CAACC,GAAG,CAACO,KAAK,EAAER,KAAK,CAACC,GAAG,CAACK,IAAI,kB;QAtHtFlD,CAAA;UAyHUhB,YAAA,CAMkBuD,0BAAA;QANDC,IAAI,EAAC,aAAa;QAACC,KAAK,EAAC,MAAM;QAACC,KAAK,EAAC;;QAC1C5C,OAAO,EAAAV,QAAA,CAGPwD,KAHc,KACvB5D,YAAA,CAESqE,iBAAA;UAFA5D,IAAI,EAAEG,MAAA,CAAA0D,qBAAqB,CAACV,KAAK,CAACC,GAAG,CAACU,WAAW;UAAG7D,IAAI,EAAC;;UA3HhFI,OAAA,EAAAV,QAAA,CA4HgB,MAA6C,CA5H7DG,gBAAA,CAAAmB,gBAAA,CA4HmBd,MAAA,CAAAe,gBAAgB,CAACiC,KAAK,CAACC,GAAG,CAACU,WAAW,kB;UA5HzDvD,CAAA;;QAAAA,CAAA;UAgIUhB,YAAA,CAAiFuD,0BAAA;QAAhEC,IAAI,EAAC,aAAa;QAACC,KAAK,EAAC,IAAI;QAAC,WAAS,EAAC;;MAhInEzC,CAAA;mCAqIkDJ,MAAA,CAAA4D,gBAAgB,I,cAA5D1E,mBAAA,CAkDM,OAlDN2E,WAkDM,G,4BAjDJpE,mBAAA,CAAiD,aAA7CA,mBAAA,CAA+B;MAA5BV,KAAK,EAAC;IAAiB,IAtItCY,gBAAA,CAsI2C,WAAS,E,sBAC5CP,YAAA,CA+CSkB,iBAAA;MA/CAC,MAAM,EAAE;IAAE;MAvI3BL,OAAA,EAAAV,QAAA,CAwIU,MAiBS,CAjBTJ,YAAA,CAiBSoB,iBAAA;QAjBAC,IAAI,EAAE;MAAE;QAxI3BP,OAAA,EAAAV,QAAA,CAyIY,MAeM,CAfNC,mBAAA,CAeM,OAfNqE,WAeM,G,4BAdJrE,mBAAA,CAAc,YAAV,OAAK,sBACTL,YAAA,CASWmD,mBAAA;UATAC,IAAI,EAAExC,MAAA,CAAA4D,gBAAgB,CAACP,MAAM;UAAEvD,IAAI,EAAC;;UA3I7DI,OAAA,EAAAV,QAAA,CA4IgB,MAAsE,CAAtEJ,YAAA,CAAsEuD,0BAAA;YAArDC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC/C1D,YAAA,CAA6EuD,0BAAA;YAA5DC,IAAI,EAAC,UAAU;YAACC,KAAK,EAAC,OAAO;YAACC,KAAK,EAAC;cACrD1D,YAAA,CAIkBuD,0BAAA;YAJDC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;;YAClC5C,OAAO,EAAAV,QAAA,CACmCwD,KAD5B,KACvB5D,YAAA,CAAmDqE,iBAAA;cAA3C3D,IAAI,EAAC;YAAO;cAhJxCI,OAAA,EAAAV,QAAA,CAgJyC,MAAqB,CAhJ9DG,gBAAA,CAAAmB,gBAAA,CAgJ4CkC,KAAK,CAACC,GAAG,CAACc,KAAK,iB;cAhJ3D3D,CAAA;;YAAAA,CAAA;cAmJgBhB,YAAA,CAAiEuD,0BAAA;YAAhDC,IAAI,EAAC,aAAa;YAACC,KAAK,EAAC;;UAnJ1DzC,CAAA;qCAqJcX,mBAAA,CAEM,OAFNuE,WAEM,GADJvE,mBAAA,CAAsD,cAAhD,QAAM,GAAAqB,gBAAA,CAAGd,MAAA,CAAA4D,gBAAgB,CAACK,WAAW,IAAG,GAAC,gB;QAtJ/D7D,CAAA;UA2JUhB,YAAA,CA0BSoB,iBAAA;QA1BAC,IAAI,EAAE;MAAE;QA3J3BP,OAAA,EAAAV,QAAA,CA4JY,MAwBM,CAxBNC,mBAAA,CAwBM,OAxBNyE,WAwBM,G,4BAvBJzE,mBAAA,CAAc,YAAV,OAAK,sBACTL,YAAA,CAeWmD,mBAAA;UAfAC,IAAI,EAAExC,MAAA,CAAA4D,gBAAgB,CAACJ,KAAK;UAAE1D,IAAI,EAAC;;UA9J5DI,OAAA,EAAAV,QAAA,CA+JgB,MAAsE,CAAtEJ,YAAA,CAAsEuD,0BAAA;YAArDC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC/C1D,YAAA,CAMkBuD,0BAAA;YANDC,IAAI,EAAC,UAAU;YAACC,KAAK,EAAC,OAAO;YAACC,KAAK,EAAC;;YACxC5C,OAAO,EAAAV,QAAA,CAGTwD,KAHgB,KACvBvD,mBAAA,CAEO;cAFAV,KAAK,EAlKhCkC,eAAA;gBAAA,kBAkKsD+B,KAAK,CAACC,GAAG,CAACkB;cAAQ;gCAC/CnB,KAAK,CAACC,GAAG,CAACmB,QAAQ,wB;YAnK3ChE,CAAA;cAuKgBhB,YAAA,CAIkBuD,0BAAA;YAJDC,IAAI,EAAC,OAAO;YAACC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;;YAClC5C,OAAO,EAAAV,QAAA,CACmCwD,KAD5B,KACvB5D,YAAA,CAAmDqE,iBAAA;cAA3C3D,IAAI,EAAC;YAAO;cAzKxCI,OAAA,EAAAV,QAAA,CAyKyC,MAAqB,CAzK9DG,gBAAA,CAAAmB,gBAAA,CAyK4CkC,KAAK,CAACC,GAAG,CAACc,KAAK,iB;cAzK3D3D,CAAA;;YAAAA,CAAA;cA4KgBhB,YAAA,CAAiEuD,0BAAA;YAAhDC,IAAI,EAAC,aAAa;YAACC,KAAK,EAAC;;UA5K1DzC,CAAA;qCA8KcX,mBAAA,CAKM,OALN4E,WAKM,GAJJ5E,mBAAA,CAAqD,cAA/C,QAAM,GAAAqB,gBAAA,CAAGd,MAAA,CAAA4D,gBAAgB,CAACU,UAAU,IAAG,GAAC,iBAChCtE,MAAA,CAAA4D,gBAAgB,CAACW,gBAAgB,I,cAA/CC,YAAA,CAESf,iBAAA;UAlLzBxE,GAAA;UAgLiEY,IAAI,EAAC,SAAS;UAACC,IAAI,EAAC;;UAhLrFI,OAAA,EAAAV,QAAA,CAgL6F,MACxE,CAjLrBG,gBAAA,CAgL6F,MACxE,GAAAmB,gBAAA,CAAGd,MAAA,CAAAe,gBAAgB,CAACf,MAAA,CAAA4D,gBAAgB,CAACW,gBAAgB,kB;UAjL1EnE,CAAA;cAAAqE,mBAAA,e;QAAArE,CAAA;;MAAAA,CAAA;YAAAqE,mBAAA,gBA0LMhF,mBAAA,CA0BM,OA1BNiF,WA0BM,G,4BAzBJjF,mBAAA,CAA8C,aAA1CA,mBAAA,CAAgC;MAA7BV,KAAK,EAAC;IAAkB,IA3LvCY,gBAAA,CA2L4C,OAAK,E,sBACzCF,mBAAA,CAuBM,OAvBNkF,WAuBM,I,kBAtBJzF,mBAAA,CAqBM0F,SAAA,QAlNhBC,WAAA,CA8L8C7E,MAAA,CAAA8E,eAAe,EA9L7D,CA8LoBC,cAAc,EAAEC,KAAK;2BAD/B9F,mBAAA,CAqBM;QAnBHD,GAAG,EAAE+F,KAAK;QACXjG,KAAK,EAAC;UACNU,mBAAA,CAKM,OALNwF,WAKM,GAJJ7F,YAAA,CAESqE,iBAAA;QAFA5D,IAAI,EAAEG,MAAA,CAAAkF,kBAAkB,CAACH,cAAc,CAACI,QAAQ;QAAGrF,IAAI,EAAC;;QAlM/EI,OAAA,EAAAV,QAAA,CAmMgB,MAA8C,CAnM9DG,gBAAA,CAAAmB,gBAAA,CAmMmBd,MAAA,CAAAoF,eAAe,CAACL,cAAc,CAACI,QAAQ,kB;QAnM1D/E,CAAA;qDAqMcX,mBAAA,CAAoE,QAApE4F,WAAoE,EAAAvE,gBAAA,CAA9BiE,cAAc,CAACO,KAAK,iB,GAE5D7F,mBAAA,CAUM,OAVN8F,WAUM,GATJ9F,mBAAA,CAAuC,WAAAqB,gBAAA,CAAjCiE,cAAc,CAACS,WAAW,kBAChC/F,mBAAA,CAGM,OAHNgG,WAGM,G,4BAFJhG,mBAAA,CAAuC;QAAjCV,KAAK,EAAC;MAAc,GAAC,OAAK,sBAChCU,mBAAA,CAA4D,QAA5DiG,WAA4D,EAAA5E,gBAAA,CAA/BiE,cAAc,CAACY,MAAM,iB,GAEXZ,cAAc,CAACa,cAAc,I,cAAtE1G,mBAAA,CAGM,OAHN2G,WAGM,G,4BAFJpG,mBAAA,CAAuC;QAAjCV,KAAK,EAAC;MAAc,GAAC,OAAK,sBAChCU,mBAAA,CAAoE,QAApEqG,WAAoE,EAAAhF,gBAAA,CAAvCiE,cAAc,CAACa,cAAc,iB,KA/M1EnB,mBAAA,e;;IAAArE,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}