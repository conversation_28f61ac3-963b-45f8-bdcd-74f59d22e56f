<template>
  <div class="optimization-comparison">
    <el-card class="comparison-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3><i class="el-icon-data-analysis"></i> 优化效果对比分析</h3>
          <el-button type="primary" size="small" @click="refreshData">
            <i class="el-icon-refresh"></i> 刷新数据
          </el-button>
        </div>
      </template>

      <!-- 总体改善指标 -->
      <div class="improvement-overview">
        <h4><i class="el-icon-trend-charts"></i> 总体改善效果</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="improvement-card">
              <div class="improvement-icon">
                <i class="el-icon-timer" style="color: #409eff"></i>
              </div>
              <div class="improvement-content">
                <div class="improvement-value">{{ formatPercentage(delayReduction) }}</div>
                <div class="improvement-label">延误减少</div>
                <div class="improvement-trend" :class="getTrendClass(delayReduction)">
                  <i :class="getTrendIcon(delayReduction)"></i>
                  {{ formatChange(delayReduction) }}
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="improvement-card">
              <div class="improvement-icon">
                <i class="el-icon-right" style="color: #67c23a"></i>
              </div>
              <div class="improvement-content">
                <div class="improvement-value">{{ formatPercentage(throughputIncrease) }}</div>
                <div class="improvement-label">通行能力提升</div>
                <div class="improvement-trend" :class="getTrendClass(throughputIncrease)">
                  <i :class="getTrendIcon(throughputIncrease)"></i>
                  {{ formatChange(throughputIncrease) }}
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="improvement-card">
              <div class="improvement-icon">
                <i class="el-icon-odometer" style="color: #e6a23c"></i>
              </div>
              <div class="improvement-content">
                <div class="improvement-value">{{ formatPercentage(speedImprovement) }}</div>
                <div class="improvement-label">平均速度提升</div>
                <div class="improvement-trend" :class="getTrendClass(speedImprovement)">
                  <i :class="getTrendIcon(speedImprovement)"></i>
                  {{ formatChange(speedImprovement) }}
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="improvement-card">
              <div class="improvement-icon">
                <i class="el-icon-cpu" style="color: #f56c6c"></i>
              </div>
              <div class="improvement-content">
                <div class="improvement-value">{{ formatPercentage(fuelSaving) }}</div>
                <div class="improvement-label">燃油节省</div>
                <div class="improvement-trend" :class="getTrendClass(fuelSaving)">
                  <i :class="getTrendIcon(fuelSaving)"></i>
                  {{ formatChange(fuelSaving) }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 对比图表 -->
      <div class="comparison-charts">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="chart-section">
              <h4><i class="el-icon-data-line"></i> 性能指标对比</h4>
              <div ref="performanceChartRef" class="chart-container"></div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="chart-section">
              <h4><i class="el-icon-pie-chart"></i> 改善效果分布</h4>
              <div ref="improvementChartRef" class="chart-container"></div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 详细对比表格 -->
      <div class="comparison-table">
        <h4><i class="el-icon-s-data"></i> 详细数据对比</h4>
        <el-table :data="comparisonData" border style="width: 100%">
          <el-table-column prop="metric" label="指标" width="150" fixed="left">
            <template #default="scope">
              <strong>{{ scope.row.metric }}</strong>
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位" width="80"></el-table-column>
          <el-table-column prop="before" label="优化前" width="120">
            <template #default="scope">
              <span class="value-before">{{ formatValue(scope.row.before, scope.row.unit) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="after" label="优化后" width="120">
            <template #default="scope">
              <span class="value-after">{{ formatValue(scope.row.after, scope.row.unit) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="improvement" label="改善幅度" width="120">
            <template #default="scope">
              <el-tag :type="getImprovementTagType(scope.row.improvement)" size="small">
                {{ formatPercentage(scope.row.improvement) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="说明" min-width="200"></el-table-column>
        </el-table>
      </div>

      <!-- 信号配时对比 -->
      <div class="signal-timing-comparison" v-if="signalTimingData">
        <h4><i class="el-icon-warning"></i> 信号配时方案对比</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="timing-section">
              <h5>优化前配时</h5>
              <el-table :data="signalTimingData.before" size="small">
                <el-table-column prop="phase" label="相位" width="80"></el-table-column>
                <el-table-column prop="duration" label="时长(秒)" width="100"></el-table-column>
                <el-table-column prop="state" label="状态" width="100">
                  <template #default="scope">
                    <el-tag size="small">{{ scope.row.state }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述"></el-table-column>
              </el-table>
              <div class="cycle-info">
                <span>周期时长: {{ signalTimingData.beforeCycle }}秒</span>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="timing-section">
              <h5>优化后配时</h5>
              <el-table :data="signalTimingData.after" size="small">
                <el-table-column prop="phase" label="相位" width="80"></el-table-column>
                <el-table-column prop="duration" label="时长(秒)" width="100">
                  <template #default="scope">
                    <span :class="{ 'improved-value': scope.row.improved }">
                      {{ scope.row.duration }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="state" label="状态" width="100">
                  <template #default="scope">
                    <el-tag size="small">{{ scope.row.state }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述"></el-table-column>
              </el-table>
              <div class="cycle-info">
                <span>周期时长: {{ signalTimingData.afterCycle }}秒</span>
                <el-tag v-if="signalTimingData.cycleImprovement" type="success" size="small">
                  优化 {{ formatPercentage(signalTimingData.cycleImprovement) }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 优化建议 -->
      <div class="optimization-recommendations">
        <h4><i class="el-icon-document"></i> 优化建议</h4>
        <div class="recommendations-list">
          <div 
            v-for="(recommendation, index) in recommendations" 
            :key="index"
            class="recommendation-item">
            <div class="recommendation-header">
              <el-tag :type="getPriorityTagType(recommendation.priority)" size="small">
                {{ getPriorityText(recommendation.priority) }}
              </el-tag>
              <span class="recommendation-title">{{ recommendation.title }}</span>
            </div>
            <div class="recommendation-content">
              <p>{{ recommendation.description }}</p>
              <div class="recommendation-actions">
                <span class="action-label">建议措施:</span>
                <span class="action-text">{{ recommendation.action }}</span>
              </div>
              <div class="recommendation-effect" v-if="recommendation.expectedEffect">
                <span class="effect-label">预期效果:</span>
                <span class="effect-text">{{ recommendation.expectedEffect }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'OptimizationComparison',
  props: {
    optimizationResult: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['refresh-requested'],
  setup(props, { emit }) {
    // 图表引用
    const performanceChartRef = ref(null)
    const improvementChartRef = ref(null)
    let performanceChart = null
    let improvementChart = null
    
    // 改善指标
    const delayReduction = ref(15.6)
    const throughputIncrease = ref(12.3)
    const speedImprovement = ref(8.9)
    const fuelSaving = ref(11.2)
    
    // 对比数据
    const comparisonData = reactive([
      {
        metric: '平均延误时间',
        unit: '秒',
        before: 45.2,
        after: 38.1,
        improvement: 15.7,
        description: '车辆在交叉口的平均等待时间'
      },
      {
        metric: '通行能力',
        unit: '辆/小时',
        before: 1850,
        after: 2078,
        improvement: 12.3,
        description: '单位时间内通过交叉口的车辆数量'
      },
      {
        metric: '平均行程速度',
        unit: 'km/h',
        before: 28.5,
        after: 31.0,
        improvement: 8.8,
        description: '车辆通过交叉口区域的平均速度'
      },
      {
        metric: '燃油消耗',
        unit: '升/百公里',
        before: 8.9,
        after: 7.9,
        improvement: 11.2,
        description: '车辆通过交叉口的燃油消耗'
      },
      {
        metric: '排队长度',
        unit: '米',
        before: 85.3,
        after: 68.7,
        improvement: 19.5,
        description: '各方向最大排队长度'
      }
    ])
    
    // 信号配时对比数据
    const signalTimingData = reactive({
      beforeCycle: 120,
      afterCycle: 105,
      cycleImprovement: 12.5,
      before: [
        { phase: '相位1', duration: 30, state: 'GrGr', description: '东西绿灯' },
        { phase: '相位2', duration: 5, state: 'yryr', description: '东西黄灯' },
        { phase: '相位3', duration: 30, state: 'rGrG', description: '南北绿灯' },
        { phase: '相位4', duration: 5, state: 'ryry', description: '南北黄灯' }
      ],
      after: [
        { phase: '相位1', duration: 35, state: 'GrGr', description: '东西绿灯', improved: true },
        { phase: '相位2', duration: 4, state: 'yryr', description: '东西黄灯' },
        { phase: '相位3', duration: 25, state: 'rGrG', description: '南北绿灯', improved: true },
        { phase: '相位4', duration: 4, state: 'ryry', description: '南北黄灯' }
      ]
    })
    
    // 优化建议
    const recommendations = reactive([
      {
        priority: 'high',
        title: '应用优化信号配时方案',
        description: '建议立即应用优化后的信号配时方案，可显著提升交叉口通行效率。',
        action: '调整信号灯控制器参数，实施新的配时方案',
        expectedEffect: '减少15.6%的延误时间，提升12.3%的通行能力'
      },
      {
        priority: 'medium',
        title: '优化车道功能配置',
        description: '根据各方向流量特点，建议调整车道功能配置以进一步平衡流量。',
        action: '增设左转专用车道或调整车道标线',
        expectedEffect: '进一步提升5-8%的通行效率'
      },
      {
        priority: 'low',
        title: '加强交通引导',
        description: '在高峰时段增加交通引导，帮助车辆选择最优路径。',
        action: '部署智能交通引导系统或增加人工引导',
        expectedEffect: '减少局部拥堵，提升整体通行体验'
      }
    ])
    
    // 方法
    const formatPercentage = (value) => {
      return `${value.toFixed(1)}%`
    }
    
    const formatChange = (value) => {
      return value > 0 ? `+${value.toFixed(1)}%` : `${value.toFixed(1)}%`
    }
    
    const formatValue = (value, unit) => {
      if (typeof value === 'number') {
        return `${value.toFixed(1)} ${unit}`
      }
      return `${value} ${unit}`
    }
    
    const getTrendClass = (value) => {
      return value > 0 ? 'trend-up' : value < 0 ? 'trend-down' : 'trend-neutral'
    }
    
    const getTrendIcon = (value) => {
      return value > 0 ? 'el-icon-top' : value < 0 ? 'el-icon-bottom' : 'el-icon-minus'
    }
    
    const getImprovementTagType = (improvement) => {
      if (improvement > 15) return 'success'
      if (improvement > 5) return 'warning'
      return 'info'
    }
    
    const getPriorityTagType = (priority) => {
      const typeMap = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'info'
      }
      return typeMap[priority] || 'info'
    }
    
    const getPriorityText = (priority) => {
      const textMap = {
        'high': '高优先级',
        'medium': '中优先级',
        'low': '低优先级'
      }
      return textMap[priority] || '未知'
    }
    
    const refreshData = () => {
      emit('refresh-requested')
      // 这里可以添加数据刷新逻辑
    }
    
    const initCharts = () => {
      nextTick(() => {
        // 初始化性能对比图表
        if (performanceChartRef.value) {
          performanceChart = echarts.init(performanceChartRef.value)
          updatePerformanceChart()
        }
        
        // 初始化改善效果图表
        if (improvementChartRef.value) {
          improvementChart = echarts.init(improvementChartRef.value)
          updateImprovementChart()
        }
      })
    }
    
    const updatePerformanceChart = () => {
      if (!performanceChart) return
      
      const metrics = comparisonData.map(item => item.metric)
      const beforeData = comparisonData.map(item => item.before)
      const afterData = comparisonData.map(item => item.after)
      
      const option = {
        title: {
          text: '性能指标对比',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['优化前', '优化后']
        },
        xAxis: {
          type: 'category',
          data: metrics,
          axisLabel: {
            interval: 0,
            rotate: 45
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '优化前',
            type: 'bar',
            data: beforeData,
            itemStyle: {
              color: '#f56c6c'
            }
          },
          {
            name: '优化后',
            type: 'bar',
            data: afterData,
            itemStyle: {
              color: '#67c23a'
            }
          }
        ]
      }
      
      performanceChart.setOption(option)
    }
    
    const updateImprovementChart = () => {
      if (!improvementChart) return
      
      const data = [
        { name: '延误减少', value: delayReduction.value },
        { name: '通行能力提升', value: throughputIncrease.value },
        { name: '速度提升', value: speedImprovement.value },
        { name: '燃油节省', value: fuelSaving.value }
      ]
      
      const option = {
        title: {
          text: '改善效果分布',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}%'
        },
        series: [{
          name: '改善效果',
          type: 'pie',
          radius: '60%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      
      improvementChart.setOption(option)
    }
    
    // 生命周期
    onMounted(() => {
      initCharts()
    })
    
    return {
      // 响应式数据
      delayReduction,
      throughputIncrease,
      speedImprovement,
      fuelSaving,
      comparisonData,
      signalTimingData,
      recommendations,
      
      // 图表引用
      performanceChartRef,
      improvementChartRef,
      
      // 方法
      formatPercentage,
      formatChange,
      formatValue,
      getTrendClass,
      getTrendIcon,
      getImprovementTagType,
      getPriorityTagType,
      getPriorityText,
      refreshData
    }
  }
}
</script>

<style scoped>
.optimization-comparison {
  margin-bottom: 20px;
}

.comparison-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.improvement-overview,
.comparison-charts,
.comparison-table,
.signal-timing-comparison,
.optimization-recommendations {
  margin-bottom: 30px;
}

.improvement-overview h4,
.comparison-charts h4,
.comparison-table h4,
.signal-timing-comparison h4,
.optimization-recommendations h4 {
  margin-bottom: 20px;
  color: #606266;
  font-size: 16px;
}

.improvement-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid #409eff;
  transition: transform 0.2s;
}

.improvement-card:hover {
  transform: translateY(-2px);
}

.improvement-icon {
  font-size: 28px;
  margin-right: 15px;
}

.improvement-content {
  flex: 1;
}

.improvement-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.improvement-label {
  font-size: 12px;
  color: #909399;
  margin: 4px 0;
}

.improvement-trend {
  font-size: 12px;
  font-weight: bold;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-neutral {
  color: #909399;
}

.chart-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.chart-section h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 14px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.value-before {
  color: #f56c6c;
}

.value-after {
  color: #67c23a;
  font-weight: bold;
}

.timing-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.timing-section h5 {
  margin-bottom: 15px;
  color: #303133;
}

.cycle-info {
  margin-top: 10px;
  font-size: 12px;
  color: #606266;
}

.improved-value {
  color: #67c23a;
  font-weight: bold;
}

.recommendations-list {
  space-y: 15px;
}

.recommendation-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
  margin-bottom: 15px;
}

.recommendation-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.recommendation-title {
  margin-left: 10px;
  font-weight: bold;
  color: #303133;
}

.recommendation-content p {
  margin-bottom: 10px;
  color: #606266;
  line-height: 1.5;
}

.recommendation-actions,
.recommendation-effect {
  margin-bottom: 5px;
  font-size: 12px;
}

.action-label,
.effect-label {
  color: #909399;
  margin-right: 8px;
}

.action-text,
.effect-text {
  color: #303133;
}
</style>
