{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"simulation-detail\"\n};\nconst _hoisted_2 = {\n  class: \"detail-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  class: \"card-header\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"error-message\"\n};\nconst _hoisted_6 = {\n  class: \"card-header\"\n};\nconst _hoisted_7 = {\n  class: \"log-container\"\n};\nconst _hoisted_8 = {\n  class: \"log-time\"\n};\nconst _hoisted_9 = {\n  class: \"log-message\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"no-logs\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  const _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_SimulationControlPanel = _resolveComponent(\"SimulationControlPanel\");\n  const _component_SimulationMonitor = _resolveComponent(\"SimulationMonitor\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_TrafficLightController = _resolveComponent(\"TrafficLightController\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_DecisionSupportPanel = _resolveComponent(\"DecisionSupportPanel\");\n  const _component_OptimizationComparison = _resolveComponent(\"OptimizationComparison\");\n  const _component_el_statistic = _resolveComponent(\"el-statistic\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面头部 \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_breadcrumb, {\n    separator: \"/\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_breadcrumb_item, {\n      to: {\n        path: '/'\n      }\n    }, {\n      default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"首页\")])),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_breadcrumb_item, {\n      to: {\n        path: '/simulation'\n      }\n    }, {\n      default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"仿真分析\")])),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_breadcrumb_item, null, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"仿真详情\")])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.go(-1))\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createElementVNode(\"i\", {\n      class: \"el-icon-back\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" 返回 \")])),\n    _: 1 /* STABLE */\n  })])]), _createCommentVNode(\" 仿真基本信息 \"), $setup.simulationTask ? (_openBlock(), _createBlock(_component_el_card, {\n    key: 0,\n    class: \"info-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"h3\", null, _toDisplayString($setup.simulationTask.taskName), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      type: $setup.getStatusTagType($setup.simulationTask.status),\n      size: \"large\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText($setup.simulationTask.status)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_descriptions, {\n      column: 3,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"仿真ID\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.simulationTask.simulationId), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"仿真类型\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getTypeTagType($setup.simulationTask.simulationType),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getTypeText($setup.simulationTask.simulationType)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"创建时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatTime($setup.simulationTask.createTime)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"开始时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatTime($setup.simulationTask.startTime)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"结束时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatTime($setup.simulationTask.endTime)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"运行时长\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDuration($setup.simulationTask.duration)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"创建用户\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.simulationTask.username), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"关联分析\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.simulationTask.analysisTaskId || '-'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"进度\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_progress, {\n          percentage: $setup.simulationTask.progress || 0,\n          status: $setup.getProgressStatus($setup.simulationTask.status),\n          \"stroke-width\": 8\n        }, null, 8 /* PROPS */, [\"percentage\", \"status\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), $setup.simulationTask.errorMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_alert, {\n      title: $setup.simulationTask.errorMessage,\n      type: \"error\",\n      closable: false,\n      \"show-icon\": \"\"\n    }, null, 8 /* PROPS */, [\"title\"])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 仿真控制面板 \"), $setup.simulationId ? (_openBlock(), _createBlock(_component_SimulationControlPanel, {\n    key: 1,\n    analysisTaskId: $setup.simulationTask?.analysisTaskId || '',\n    trafficData: $setup.simulationTask?.trafficData || {},\n    onSimulationStarted: $setup.handleSimulationStarted,\n    onSimulationStopped: $setup.handleSimulationStopped,\n    onSimulationCompleted: $setup.handleSimulationCompleted,\n    onStatusUpdated: $setup.handleStatusUpdated\n  }, null, 8 /* PROPS */, [\"analysisTaskId\", \"trafficData\", \"onSimulationStarted\", \"onSimulationStopped\", \"onSimulationCompleted\", \"onStatusUpdated\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 仿真监控和信号灯控制 \"), $setup.simulationId && $setup.isSimulationRunning ? (_openBlock(), _createBlock(_component_el_row, {\n    key: 2,\n    gutter: 20\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 16\n    }, {\n      default: _withCtx(() => [_createVNode(_component_SimulationMonitor, {\n        simulationId: $setup.simulationId,\n        isRunning: $setup.isSimulationRunning,\n        onDataUpdated: $setup.handleMonitorDataUpdated\n      }, null, 8 /* PROPS */, [\"simulationId\", \"isRunning\", \"onDataUpdated\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 8\n    }, {\n      default: _withCtx(() => [_createVNode(_component_TrafficLightController, {\n        simulationId: $setup.simulationId,\n        onStatusChange: _ctx.handleTrafficLightStatusChange,\n        onControlChange: _ctx.handleTrafficLightControlChange\n      }, null, 8 /* PROPS */, [\"simulationId\", \"onStatusChange\", \"onControlChange\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 决策支持面板 \"), $setup.simulationId && $setup.isSimulationRunning ? (_openBlock(), _createBlock(_component_DecisionSupportPanel, {\n    key: 3,\n    simulationId: $setup.simulationId,\n    onSuggestionApplied: _ctx.handleSuggestionApplied,\n    onDecisionMade: _ctx.handleDecisionMade\n  }, null, 8 /* PROPS */, [\"simulationId\", \"onSuggestionApplied\", \"onDecisionMade\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 优化结果对比 \"), $setup.simulationId && $setup.hasOptimizationResult ? (_openBlock(), _createBlock(_component_OptimizationComparison, {\n    key: 4,\n    optimizationResult: $setup.optimizationResult,\n    onRefreshRequested: $setup.loadOptimizationResult\n  }, null, 8 /* PROPS */, [\"optimizationResult\", \"onRefreshRequested\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 性能指标 \"), $setup.simulationTask?.performanceMetrics ? (_openBlock(), _createBlock(_component_el_card, {\n    key: 5,\n    class: \"performance-card\"\n  }, {\n    header: _withCtx(() => _cache[5] || (_cache[5] = [_createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-data-analysis\"\n    }), _createTextVNode(\" 性能指标\")], -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"总车辆数\",\n          value: $setup.simulationTask.performanceMetrics.totalVehicles || 0,\n          suffix: \"辆\"\n        }, {\n          prefix: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"i\", {\n            class: \"el-icon-truck\",\n            style: {\n              \"color\": \"#409eff\"\n            }\n          }, null, -1 /* HOISTED */)])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"平均速度\",\n          value: ($setup.simulationTask.performanceMetrics.averageSpeed || 0).toFixed(1),\n          suffix: \"m/s\"\n        }, {\n          prefix: _withCtx(() => _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n            class: \"el-icon-odometer\",\n            style: {\n              \"color\": \"#67c23a\"\n            }\n          }, null, -1 /* HOISTED */)])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"通行能力\",\n          value: ($setup.simulationTask.performanceMetrics.throughput || 0).toFixed(0),\n          suffix: \"辆/h\"\n        }, {\n          prefix: _withCtx(() => _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n            class: \"el-icon-right\",\n            style: {\n              \"color\": \"#e6a23c\"\n            }\n          }, null, -1 /* HOISTED */)])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"改善效果\",\n          value: ($setup.simulationTask.performanceMetrics.improvementPercentage || 0).toFixed(1),\n          suffix: \"%\"\n        }, {\n          prefix: _withCtx(() => _cache[9] || (_cache[9] = [_createElementVNode(\"i\", {\n            class: \"el-icon-trend-charts\",\n            style: {\n              \"color\": \"#f56c6c\"\n            }\n          }, null, -1 /* HOISTED */)])),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 仿真日志 \"), _createVNode(_component_el_card, {\n    class: \"log-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_cache[11] || (_cache[11] = _createElementVNode(\"h4\", null, [_createElementVNode(\"i\", {\n      class: \"el-icon-document\"\n    }), _createTextVNode(\" 仿真日志\")], -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.refreshLogs\n    }, {\n      default: _withCtx(() => _cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n        class: \"el-icon-refresh\"\n      }, null, -1 /* HOISTED */), _createTextVNode(\" 刷新 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.simulationLogs, (log, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: \"log-entry\"\n      }, [_createElementVNode(\"span\", _hoisted_8, _toDisplayString($setup.formatTime(log.timestamp)), 1 /* TEXT */), _createElementVNode(\"span\", {\n        class: _normalizeClass(['log-level', `log-${log.level}`])\n      }, _toDisplayString(log.level.toUpperCase()), 3 /* TEXT, CLASS */), _createElementVNode(\"span\", _hoisted_9, _toDisplayString(log.message), 1 /* TEXT */)]);\n    }), 128 /* KEYED_FRAGMENT */)), $setup.simulationLogs.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \" 暂无日志记录 \")) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_breadcrumb", "separator", "default", "_withCtx", "_component_el_breadcrumb_item", "to", "path", "_cache", "_createTextVNode", "_", "_hoisted_3", "_component_el_button", "onClick", "$event", "_ctx", "$router", "go", "$setup", "simulationTask", "_createBlock", "_component_el_card", "header", "_hoisted_4", "_toDisplayString", "taskName", "_component_el_tag", "type", "getStatusTagType", "status", "size", "getStatusText", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "label", "simulationId", "getTypeTagType", "simulationType", "getTypeText", "formatTime", "createTime", "startTime", "endTime", "formatDuration", "duration", "username", "analysisTaskId", "_component_el_progress", "percentage", "progress", "getProgressStatus", "errorMessage", "_hoisted_5", "_component_el_alert", "title", "closable", "_component_SimulationControlPanel", "trafficData", "onSimulationStarted", "handleSimulationStarted", "onSimulationStopped", "handleSimulationStopped", "onSimulationCompleted", "handleSimulationCompleted", "onStatusUpdated", "handleStatusUpdated", "isSimulationRunning", "_component_el_row", "gutter", "_component_el_col", "span", "_component_SimulationMonitor", "isRunning", "onDataUpdated", "handleMonitorDataUpdated", "_component_TrafficLightController", "onStatusChange", "handleTrafficLightStatusChange", "onControlChange", "handleTrafficLightControlChange", "_component_DecisionSupportPanel", "onSuggestionApplied", "handleSuggestionApplied", "onDecisionMade", "handleDecisionMade", "hasOptimizationResult", "_component_OptimizationComparison", "optimizationResult", "onRefreshRequested", "loadOptimizationResult", "performanceMetrics", "_component_el_statistic", "value", "totalVehicles", "suffix", "prefix", "style", "averageSpeed", "toFixed", "throughput", "improvementPercentage", "_hoisted_6", "refreshLogs", "_hoisted_7", "_Fragment", "_renderList", "simulationLogs", "log", "index", "_hoisted_8", "timestamp", "_normalizeClass", "level", "toUpperCase", "_hoisted_9", "message", "length", "_hoisted_10"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\simulation\\SimulationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"simulation-detail\">\n    <!-- 页面头部 -->\n    <div class=\"detail-header\">\n      <el-breadcrumb separator=\"/\">\n        <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\n        <el-breadcrumb-item :to=\"{ path: '/simulation' }\">仿真分析</el-breadcrumb-item>\n        <el-breadcrumb-item>仿真详情</el-breadcrumb-item>\n      </el-breadcrumb>\n      \n      <div class=\"header-actions\">\n        <el-button @click=\"$router.go(-1)\">\n          <i class=\"el-icon-back\"></i> 返回\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 仿真基本信息 -->\n    <el-card class=\"info-card\" v-if=\"simulationTask\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>{{ simulationTask.taskName }}</h3>\n          <el-tag :type=\"getStatusTagType(simulationTask.status)\" size=\"large\">\n            {{ getStatusText(simulationTask.status) }}\n          </el-tag>\n        </div>\n      </template>\n\n      <el-descriptions :column=\"3\" border>\n        <el-descriptions-item label=\"仿真ID\">{{ simulationTask.simulationId }}</el-descriptions-item>\n        <el-descriptions-item label=\"仿真类型\">\n          <el-tag :type=\"getTypeTagType(simulationTask.simulationType)\" size=\"small\">\n            {{ getTypeText(simulationTask.simulationType) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"创建时间\">{{ formatTime(simulationTask.createTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"开始时间\">{{ formatTime(simulationTask.startTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"结束时间\">{{ formatTime(simulationTask.endTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"运行时长\">{{ formatDuration(simulationTask.duration) }}</el-descriptions-item>\n        <el-descriptions-item label=\"创建用户\">{{ simulationTask.username }}</el-descriptions-item>\n        <el-descriptions-item label=\"关联分析\">{{ simulationTask.analysisTaskId || '-' }}</el-descriptions-item>\n        <el-descriptions-item label=\"进度\">\n          <el-progress \n            :percentage=\"simulationTask.progress || 0\" \n            :status=\"getProgressStatus(simulationTask.status)\"\n            :stroke-width=\"8\">\n          </el-progress>\n        </el-descriptions-item>\n      </el-descriptions>\n\n      <div v-if=\"simulationTask.errorMessage\" class=\"error-message\">\n        <el-alert\n          :title=\"simulationTask.errorMessage\"\n          type=\"error\"\n          :closable=\"false\"\n          show-icon>\n        </el-alert>\n      </div>\n    </el-card>\n\n    <!-- 仿真控制面板 -->\n    <SimulationControlPanel\n      v-if=\"simulationId\"\n      :analysisTaskId=\"simulationTask?.analysisTaskId || ''\"\n      :trafficData=\"simulationTask?.trafficData || {}\"\n      @simulation-started=\"handleSimulationStarted\"\n      @simulation-stopped=\"handleSimulationStopped\"\n      @simulation-completed=\"handleSimulationCompleted\"\n      @status-updated=\"handleStatusUpdated\"\n    />\n\n    <!-- 仿真监控和信号灯控制 -->\n    <el-row :gutter=\"20\" v-if=\"simulationId && isSimulationRunning\">\n      <el-col :span=\"16\">\n        <SimulationMonitor\n          :simulationId=\"simulationId\"\n          :isRunning=\"isSimulationRunning\"\n          @data-updated=\"handleMonitorDataUpdated\"\n        />\n      </el-col>\n      <el-col :span=\"8\">\n        <TrafficLightController\n          :simulationId=\"simulationId\"\n          @status-change=\"handleTrafficLightStatusChange\"\n          @control-change=\"handleTrafficLightControlChange\"\n        />\n      </el-col>\n    </el-row>\n\n    <!-- 决策支持面板 -->\n    <DecisionSupportPanel\n      v-if=\"simulationId && isSimulationRunning\"\n      :simulationId=\"simulationId\"\n      @suggestion-applied=\"handleSuggestionApplied\"\n      @decision-made=\"handleDecisionMade\"\n    />\n\n    <!-- 优化结果对比 -->\n    <OptimizationComparison\n      v-if=\"simulationId && hasOptimizationResult\"\n      :optimizationResult=\"optimizationResult\"\n      @refresh-requested=\"loadOptimizationResult\"\n    />\n\n    <!-- 性能指标 -->\n    <el-card class=\"performance-card\" v-if=\"simulationTask?.performanceMetrics\">\n      <template #header>\n        <h4><i class=\"el-icon-data-analysis\"></i> 性能指标</h4>\n      </template>\n\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <el-statistic title=\"总车辆数\" :value=\"simulationTask.performanceMetrics.totalVehicles || 0\" suffix=\"辆\">\n            <template #prefix>\n              <i class=\"el-icon-truck\" style=\"color: #409eff\"></i>\n            </template>\n          </el-statistic>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-statistic title=\"平均速度\" :value=\"(simulationTask.performanceMetrics.averageSpeed || 0).toFixed(1)\" suffix=\"m/s\">\n            <template #prefix>\n              <i class=\"el-icon-odometer\" style=\"color: #67c23a\"></i>\n            </template>\n          </el-statistic>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-statistic title=\"通行能力\" :value=\"(simulationTask.performanceMetrics.throughput || 0).toFixed(0)\" suffix=\"辆/h\">\n            <template #prefix>\n              <i class=\"el-icon-right\" style=\"color: #e6a23c\"></i>\n            </template>\n          </el-statistic>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-statistic title=\"改善效果\" :value=\"(simulationTask.performanceMetrics.improvementPercentage || 0).toFixed(1)\" suffix=\"%\">\n            <template #prefix>\n              <i class=\"el-icon-trend-charts\" style=\"color: #f56c6c\"></i>\n            </template>\n          </el-statistic>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 仿真日志 -->\n    <el-card class=\"log-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h4><i class=\"el-icon-document\"></i> 仿真日志</h4>\n          <el-button size=\"small\" @click=\"refreshLogs\">\n            <i class=\"el-icon-refresh\"></i> 刷新\n          </el-button>\n        </div>\n      </template>\n\n      <div class=\"log-container\">\n        <div v-for=\"(log, index) in simulationLogs\" :key=\"index\" class=\"log-entry\">\n          <span class=\"log-time\">{{ formatTime(log.timestamp) }}</span>\n          <span :class=\"['log-level', `log-${log.level}`]\">{{ log.level.toUpperCase() }}</span>\n          <span class=\"log-message\">{{ log.message }}</span>\n        </div>\n        \n        <div v-if=\"simulationLogs.length === 0\" class=\"no-logs\">\n          暂无日志记录\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport simulationApi from '@/api/simulation'\nimport stompService from '@/utils/stomp-service'\nimport SimulationControlPanel from '@/components/simulation/SimulationControlPanel.vue'\nimport SimulationMonitor from '@/components/simulation/SimulationMonitor.vue'\nimport OptimizationComparison from '@/components/simulation/OptimizationComparison.vue'\nimport TrafficLightController from '@/components/simulation/TrafficLightController.vue'\nimport DecisionSupportPanel from '@/components/simulation/DecisionSupportPanel.vue'\n\nexport default {\n  name: 'SimulationDetail',\n  components: {\n    SimulationControlPanel,\n    SimulationMonitor,\n    OptimizationComparison\n  },\n  setup() {\n    const route = useRoute()\n    const simulationId = ref(route.params.id)\n    \n    // 响应式数据\n    const simulationTask = ref(null)\n    const optimizationResult = ref(null)\n    const simulationLogs = ref([])\n    const loading = ref(false)\n    \n    // WebSocket订阅\n    let simulationSubscriptions = null\n    \n    // 计算属性\n    const isSimulationRunning = computed(() => \n      simulationTask.value?.status === 'running'\n    )\n    \n    const hasOptimizationResult = computed(() => \n      optimizationResult.value && Object.keys(optimizationResult.value).length > 0\n    )\n    \n    // 方法\n    const getStatusTagType = (status) => {\n      const statusMap = {\n        'created': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      }\n      return statusMap[status] || 'info'\n    }\n    \n    const getStatusText = (status) => {\n      const statusMap = {\n        'created': '已创建',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      }\n      return statusMap[status] || '未知'\n    }\n    \n    const getTypeTagType = (type) => {\n      const typeMap = {\n        'signal_timing': 'primary',\n        'flow_balance': 'success',\n        'comprehensive': 'warning'\n      }\n      return typeMap[type] || 'info'\n    }\n    \n    const getTypeText = (type) => {\n      const typeMap = {\n        'signal_timing': '信号配时优化',\n        'flow_balance': '流量平衡优化',\n        'comprehensive': '综合优化分析'\n      }\n      return typeMap[type] || '未知'\n    }\n    \n    const getProgressStatus = (status) => {\n      if (status === 'failed') return 'exception'\n      if (status === 'completed') return 'success'\n      return null\n    }\n    \n    const formatTime = (timeStr) => {\n      if (!timeStr) return '-'\n      const date = new Date(timeStr)\n      return date.toLocaleString('zh-CN')\n    }\n    \n    const formatDuration = (duration) => {\n      if (!duration) return '-'\n      const hours = Math.floor(duration / 3600)\n      const minutes = Math.floor((duration % 3600) / 60)\n      const seconds = duration % 60\n      \n      if (hours > 0) {\n        return `${hours}h ${minutes}m ${seconds}s`\n      } else if (minutes > 0) {\n        return `${minutes}m ${seconds}s`\n      } else {\n        return `${seconds}s`\n      }\n    }\n    \n    const loadSimulationTask = async () => {\n      try {\n        loading.value = true\n        const response = await simulationApi.getSimulationTask(simulationId.value)\n        \n        if (response.status === 'success') {\n          simulationTask.value = response.simulation_task\n        } else {\n          ElMessage.error('仿真任务不存在')\n        }\n      } catch (error) {\n        console.error('加载仿真任务失败:', error)\n        ElMessage.error('加载仿真任务失败')\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    const loadOptimizationResult = async () => {\n      try {\n        const response = await simulationApi.getOptimizationResult(simulationId.value)\n        \n        if (response.status === 'success') {\n          optimizationResult.value = response.optimization_result\n        }\n      } catch (error) {\n        console.error('加载优化结果失败:', error)\n      }\n    }\n    \n    const setupWebSocketSubscriptions = async () => {\n      try {\n        // 确保WebSocket连接\n        await stompService.connect()\n        \n        // 订阅仿真相关主题\n        simulationSubscriptions = await stompService.subscribeSimulationAll(simulationId.value, {\n          onStatusUpdate: handleWebSocketStatusUpdate,\n          onRealtimeData: handleWebSocketRealtimeData,\n          onFlowData: handleWebSocketFlowData,\n          onTrafficLights: handleWebSocketTrafficLights,\n          onOptimizationResult: handleWebSocketOptimizationResult\n        })\n        \n        console.log('WebSocket订阅设置完成')\n        \n      } catch (error) {\n        console.error('设置WebSocket订阅失败:', error)\n      }\n    }\n    \n    const cleanupWebSocketSubscriptions = () => {\n      if (simulationSubscriptions) {\n        stompService.unsubscribeSimulation(simulationSubscriptions)\n        simulationSubscriptions = null\n      }\n    }\n    \n    // WebSocket事件处理\n    const handleWebSocketStatusUpdate = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 更新仿真状态\n        if (simulationTask.value) {\n          simulationTask.value.status = data.status\n          simulationTask.value.progress = data.progress\n        }\n        \n        // 添加日志\n        addLog('info', data.message || `状态更新: ${data.status}`)\n      }\n    }\n    \n    const handleWebSocketRealtimeData = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 处理实时数据\n        addLog('debug', `实时数据: 时间${data.simulationTime}s, 车辆${data.vehicleCount}辆`)\n      }\n    }\n    \n    const handleWebSocketFlowData = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 处理流量数据\n        addLog('debug', '收到方向流量数据更新')\n      }\n    }\n    \n    const handleWebSocketTrafficLights = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 处理信号灯数据\n        addLog('debug', `信号灯更新: 相位${data.currentPhase}, 剩余${data.phaseRemaining}s`)\n      }\n    }\n    \n    const handleWebSocketOptimizationResult = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 处理优化结果\n        optimizationResult.value = data.optimizationResult\n        addLog('info', '收到优化结果')\n      }\n    }\n    \n    // 组件事件处理\n    const handleSimulationStarted = (simId) => {\n      addLog('info', '仿真已启动')\n      loadSimulationTask()\n    }\n    \n    const handleSimulationStopped = (simId) => {\n      addLog('info', '仿真已停止')\n      loadSimulationTask()\n    }\n    \n    const handleSimulationCompleted = (simId) => {\n      addLog('info', '仿真已完成')\n      loadSimulationTask()\n      loadOptimizationResult()\n    }\n    \n    const handleStatusUpdated = (status) => {\n      // 状态更新已通过WebSocket处理\n    }\n    \n    const handleMonitorDataUpdated = (data) => {\n      // 监控数据更新\n    }\n    \n    const addLog = (level, message) => {\n      simulationLogs.value.unshift({\n        timestamp: new Date().toISOString(),\n        level,\n        message\n      })\n      \n      // 保持最近100条日志\n      if (simulationLogs.value.length > 100) {\n        simulationLogs.value = simulationLogs.value.slice(0, 100)\n      }\n    }\n    \n    const refreshLogs = () => {\n      // 这里可以从服务器获取日志\n      addLog('info', '日志已刷新')\n    }\n    \n    // 生命周期\n    onMounted(async () => {\n      await loadSimulationTask()\n      await loadOptimizationResult()\n      await setupWebSocketSubscriptions()\n      \n      addLog('info', '页面加载完成')\n    })\n    \n    onUnmounted(() => {\n      cleanupWebSocketSubscriptions()\n    })\n    \n    return {\n      // 响应式数据\n      simulationId,\n      simulationTask,\n      optimizationResult,\n      simulationLogs,\n      loading,\n      \n      // 计算属性\n      isSimulationRunning,\n      hasOptimizationResult,\n      \n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getTypeTagType,\n      getTypeText,\n      getProgressStatus,\n      formatTime,\n      formatDuration,\n      loadOptimizationResult,\n      refreshLogs,\n      \n      // 事件处理\n      handleSimulationStarted,\n      handleSimulationStopped,\n      handleSimulationCompleted,\n      handleStatusUpdated,\n      handleMonitorDataUpdated\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simulation-detail {\n  padding: 20px;\n}\n\n.detail-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.info-card,\n.performance-card,\n.log-card {\n  margin-bottom: 20px;\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3,\n.card-header h4 {\n  margin: 0;\n  color: #303133;\n}\n\n.error-message {\n  margin-top: 15px;\n}\n\n.log-container {\n  max-height: 400px;\n  overflow-y: auto;\n  background: #f8f9fa;\n  border-radius: 4px;\n  padding: 10px;\n}\n\n.log-entry {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n}\n\n.log-time {\n  color: #909399;\n  margin-right: 10px;\n  min-width: 150px;\n}\n\n.log-level {\n  margin-right: 10px;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-weight: bold;\n  min-width: 50px;\n  text-align: center;\n}\n\n.log-info {\n  background: #e1f3d8;\n  color: #67c23a;\n}\n\n.log-debug {\n  background: #e6f7ff;\n  color: #409eff;\n}\n\n.log-warn {\n  background: #fdf6ec;\n  color: #e6a23c;\n}\n\n.log-error {\n  background: #fef0f0;\n  color: #f56c6c;\n}\n\n.log-message {\n  flex: 1;\n  color: #303133;\n}\n\n.no-logs {\n  text-align: center;\n  color: #909399;\n  padding: 20px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAe;;EAOnBA,KAAK,EAAC;AAAgB;;EAUpBA,KAAK,EAAC;AAAa;;EApBhCC,GAAA;EAkD8CD,KAAK,EAAC;;;EAkGvCA,KAAK,EAAC;AAAa;;EAQrBA,KAAK,EAAC;AAAe;;EAEhBA,KAAK,EAAC;AAAU;;EAEhBA,KAAK,EAAC;AAAa;;EAhKnCC,GAAA;EAmKgDD,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;uBAlKpDE,mBAAA,CAuKM,OAvKNC,UAuKM,GAtKJC,mBAAA,UAAa,EACbC,mBAAA,CAYM,OAZNC,UAYM,GAXJC,YAAA,CAIgBC,wBAAA;IAJDC,SAAS,EAAC;EAAG;IAJlCC,OAAA,EAAAC,QAAA,CAKQ,MAA+D,CAA/DJ,YAAA,CAA+DK,6BAAA;MAA1CC,EAAE,EAAE;QAAAC,IAAA;MAAA;IAAa;MAL9CJ,OAAA,EAAAC,QAAA,CAKgD,MAAEI,MAAA,QAAAA,MAAA,OALlDC,gBAAA,CAKgD,IAAE,E;MALlDC,CAAA;QAMQV,YAAA,CAA2EK,6BAAA;MAAtDC,EAAE,EAAE;QAAAC,IAAA;MAAA;IAAuB;MANxDJ,OAAA,EAAAC,QAAA,CAM0D,MAAII,MAAA,QAAAA,MAAA,OAN9DC,gBAAA,CAM0D,MAAI,E;MAN9DC,CAAA;QAOQV,YAAA,CAA6CK,6BAAA;MAPrDF,OAAA,EAAAC,QAAA,CAO4B,MAAII,MAAA,QAAAA,MAAA,OAPhCC,gBAAA,CAO4B,MAAI,E;MAPhCC,CAAA;;IAAAA,CAAA;MAUMZ,mBAAA,CAIM,OAJNa,UAIM,GAHJX,YAAA,CAEYY,oBAAA;IAFAC,OAAK,EAAAL,MAAA,QAAAA,MAAA,MAAAM,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,EAAE;;IAXrCd,OAAA,EAAAC,QAAA,CAYU,MAA4BI,MAAA,QAAAA,MAAA,OAA5BV,mBAAA,CAA4B;MAAzBL,KAAK,EAAC;IAAc,4BAZjCgB,gBAAA,CAYsC,MAC9B,E;IAbRC,CAAA;UAiBIb,mBAAA,YAAe,EACkBqB,MAAA,CAAAC,cAAc,I,cAA/CC,YAAA,CAwCUC,kBAAA;IA1Dd3B,GAAA;IAkBaD,KAAK,EAAC;;IACF6B,MAAM,EAAAlB,QAAA,CACf,MAKM,CALNN,mBAAA,CAKM,OALNyB,UAKM,GAJJzB,mBAAA,CAAsC,YAAA0B,gBAAA,CAA/BN,MAAA,CAAAC,cAAc,CAACM,QAAQ,kBAC9BzB,YAAA,CAES0B,iBAAA;MAFAC,IAAI,EAAET,MAAA,CAAAU,gBAAgB,CAACV,MAAA,CAAAC,cAAc,CAACU,MAAM;MAAGC,IAAI,EAAC;;MAtBvE3B,OAAA,EAAAC,QAAA,CAuBY,MAA0C,CAvBtDK,gBAAA,CAAAe,gBAAA,CAuBeN,MAAA,CAAAa,aAAa,CAACb,MAAA,CAAAC,cAAc,CAACU,MAAM,kB;MAvBlDnB,CAAA;;IAAAP,OAAA,EAAAC,QAAA,CA4BM,MAoBkB,CApBlBJ,YAAA,CAoBkBgC,0BAAA;MApBAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MA5BnC/B,OAAA,EAAAC,QAAA,CA6BQ,MAA2F,CAA3FJ,YAAA,CAA2FmC,+BAAA;QAArEC,KAAK,EAAC;MAAM;QA7B1CjC,OAAA,EAAAC,QAAA,CA6B2C,MAAiC,CA7B5EK,gBAAA,CAAAe,gBAAA,CA6B8CN,MAAA,CAAAC,cAAc,CAACkB,YAAY,iB;QA7BzE3B,CAAA;UA8BQV,YAAA,CAIuBmC,+BAAA;QAJDC,KAAK,EAAC;MAAM;QA9B1CjC,OAAA,EAAAC,QAAA,CA+BU,MAES,CAFTJ,YAAA,CAES0B,iBAAA;UAFAC,IAAI,EAAET,MAAA,CAAAoB,cAAc,CAACpB,MAAA,CAAAC,cAAc,CAACoB,cAAc;UAAGT,IAAI,EAAC;;UA/B7E3B,OAAA,EAAAC,QAAA,CAgCY,MAAgD,CAhC5DK,gBAAA,CAAAe,gBAAA,CAgCeN,MAAA,CAAAsB,WAAW,CAACtB,MAAA,CAAAC,cAAc,CAACoB,cAAc,kB;UAhCxD7B,CAAA;;QAAAA,CAAA;UAmCQV,YAAA,CAAqGmC,+BAAA;QAA/EC,KAAK,EAAC;MAAM;QAnC1CjC,OAAA,EAAAC,QAAA,CAmC2C,MAA2C,CAnCtFK,gBAAA,CAAAe,gBAAA,CAmC8CN,MAAA,CAAAuB,UAAU,CAACvB,MAAA,CAAAC,cAAc,CAACuB,UAAU,kB;QAnClFhC,CAAA;UAoCQV,YAAA,CAAoGmC,+BAAA;QAA9EC,KAAK,EAAC;MAAM;QApC1CjC,OAAA,EAAAC,QAAA,CAoC2C,MAA0C,CApCrFK,gBAAA,CAAAe,gBAAA,CAoC8CN,MAAA,CAAAuB,UAAU,CAACvB,MAAA,CAAAC,cAAc,CAACwB,SAAS,kB;QApCjFjC,CAAA;UAqCQV,YAAA,CAAkGmC,+BAAA;QAA5EC,KAAK,EAAC;MAAM;QArC1CjC,OAAA,EAAAC,QAAA,CAqC2C,MAAwC,CArCnFK,gBAAA,CAAAe,gBAAA,CAqC8CN,MAAA,CAAAuB,UAAU,CAACvB,MAAA,CAAAC,cAAc,CAACyB,OAAO,kB;QArC/ElC,CAAA;UAsCQV,YAAA,CAAuGmC,+BAAA;QAAjFC,KAAK,EAAC;MAAM;QAtC1CjC,OAAA,EAAAC,QAAA,CAsC2C,MAA6C,CAtCxFK,gBAAA,CAAAe,gBAAA,CAsC8CN,MAAA,CAAA2B,cAAc,CAAC3B,MAAA,CAAAC,cAAc,CAAC2B,QAAQ,kB;QAtCpFpC,CAAA;UAuCQV,YAAA,CAAuFmC,+BAAA;QAAjEC,KAAK,EAAC;MAAM;QAvC1CjC,OAAA,EAAAC,QAAA,CAuC2C,MAA6B,CAvCxEK,gBAAA,CAAAe,gBAAA,CAuC8CN,MAAA,CAAAC,cAAc,CAAC4B,QAAQ,iB;QAvCrErC,CAAA;UAwCQV,YAAA,CAAoGmC,+BAAA;QAA9EC,KAAK,EAAC;MAAM;QAxC1CjC,OAAA,EAAAC,QAAA,CAwC2C,MAA0C,CAxCrFK,gBAAA,CAAAe,gBAAA,CAwC8CN,MAAA,CAAAC,cAAc,CAAC6B,cAAc,wB;QAxC3EtC,CAAA;UAyCQV,YAAA,CAMuBmC,+BAAA;QANDC,KAAK,EAAC;MAAI;QAzCxCjC,OAAA,EAAAC,QAAA,CA0CU,MAIc,CAJdJ,YAAA,CAIciD,sBAAA;UAHXC,UAAU,EAAEhC,MAAA,CAAAC,cAAc,CAACgC,QAAQ;UACnCtB,MAAM,EAAEX,MAAA,CAAAkC,iBAAiB,CAAClC,MAAA,CAAAC,cAAc,CAACU,MAAM;UAC/C,cAAY,EAAE;;QA7C3BnB,CAAA;;MAAAA,CAAA;QAkDiBQ,MAAA,CAAAC,cAAc,CAACkC,YAAY,I,cAAtC1D,mBAAA,CAOM,OAPN2D,UAOM,GANJtD,YAAA,CAKWuD,mBAAA;MAJRC,KAAK,EAAEtC,MAAA,CAAAC,cAAc,CAACkC,YAAY;MACnC1B,IAAI,EAAC,OAAO;MACX8B,QAAQ,EAAE,KAAK;MAChB,WAAS,EAAT;4CAvDV5D,mBAAA,e;IAAAa,CAAA;QAAAb,mBAAA,gBA4DIA,mBAAA,YAAe,EAEPqB,MAAA,CAAAmB,YAAY,I,cADpBjB,YAAA,CAQEsC,iCAAA;IArENhE,GAAA;IA+DOsD,cAAc,EAAE9B,MAAA,CAAAC,cAAc,EAAE6B,cAAc;IAC9CW,WAAW,EAAEzC,MAAA,CAAAC,cAAc,EAAEwC,WAAW;IACxCC,mBAAkB,EAAE1C,MAAA,CAAA2C,uBAAuB;IAC3CC,mBAAkB,EAAE5C,MAAA,CAAA6C,uBAAuB;IAC3CC,qBAAoB,EAAE9C,MAAA,CAAA+C,yBAAyB;IAC/CC,eAAc,EAAEhD,MAAA,CAAAiD;0JApEvBtE,mBAAA,gBAuEIA,mBAAA,gBAAmB,EACQqB,MAAA,CAAAmB,YAAY,IAAInB,MAAA,CAAAkD,mBAAmB,I,cAA9DhD,YAAA,CAeSiD,iBAAA;IAvFb3E,GAAA;IAwEa4E,MAAM,EAAE;;IAxErBnE,OAAA,EAAAC,QAAA,CAyEM,MAMS,CANTJ,YAAA,CAMSuE,iBAAA;MANAC,IAAI,EAAE;IAAE;MAzEvBrE,OAAA,EAAAC,QAAA,CA0EQ,MAIE,CAJFJ,YAAA,CAIEyE,4BAAA;QAHCpC,YAAY,EAAEnB,MAAA,CAAAmB,YAAY;QAC1BqC,SAAS,EAAExD,MAAA,CAAAkD,mBAAmB;QAC9BO,aAAY,EAAEzD,MAAA,CAAA0D;;MA7EzBlE,CAAA;QAgFMV,YAAA,CAMSuE,iBAAA;MANAC,IAAI,EAAE;IAAC;MAhFtBrE,OAAA,EAAAC,QAAA,CAiFQ,MAIE,CAJFJ,YAAA,CAIE6E,iCAAA;QAHCxC,YAAY,EAAEnB,MAAA,CAAAmB,YAAY;QAC1ByC,cAAa,EAAE/D,IAAA,CAAAgE,8BAA8B;QAC7CC,eAAc,EAAEjE,IAAA,CAAAkE;;MApF3BvE,CAAA;;IAAAA,CAAA;QAAAb,mBAAA,gBAyFIA,mBAAA,YAAe,EAEPqB,MAAA,CAAAmB,YAAY,IAAInB,MAAA,CAAAkD,mBAAmB,I,cAD3ChD,YAAA,CAKE8D,+BAAA;IA/FNxF,GAAA;IA4FO2C,YAAY,EAAEnB,MAAA,CAAAmB,YAAY;IAC1B8C,mBAAkB,EAAEpE,IAAA,CAAAqE,uBAAuB;IAC3CC,cAAa,EAAEtE,IAAA,CAAAuE;wFA9FtBzF,mBAAA,gBAiGIA,mBAAA,YAAe,EAEPqB,MAAA,CAAAmB,YAAY,IAAInB,MAAA,CAAAqE,qBAAqB,I,cAD7CnE,YAAA,CAIEoE,iCAAA;IAtGN9F,GAAA;IAoGO+F,kBAAkB,EAAEvE,MAAA,CAAAuE,kBAAkB;IACtCC,kBAAiB,EAAExE,MAAA,CAAAyE;2EArG1B9F,mBAAA,gBAwGIA,mBAAA,UAAa,EAC2BqB,MAAA,CAAAC,cAAc,EAAEyE,kBAAkB,I,cAA1ExE,YAAA,CAsCUC,kBAAA;IA/Id3B,GAAA;IAyGaD,KAAK,EAAC;;IACF6B,MAAM,EAAAlB,QAAA,CACf,MAAmDI,MAAA,QAAAA,MAAA,OAAnDV,mBAAA,CAAmD,aAA/CA,mBAAA,CAAqC;MAAlCL,KAAK,EAAC;IAAuB,IA3G5CgB,gBAAA,CA2GiD,OAAK,E;IA3GtDN,OAAA,EAAAC,QAAA,CA8GM,MAgCS,CAhCTJ,YAAA,CAgCSqE,iBAAA;MAhCAC,MAAM,EAAE;IAAE;MA9GzBnE,OAAA,EAAAC,QAAA,CA+GQ,MAMS,CANTJ,YAAA,CAMSuE,iBAAA;QANAC,IAAI,EAAE;MAAC;QA/GxBrE,OAAA,EAAAC,QAAA,CAgHU,MAIe,CAJfJ,YAAA,CAIe6F,uBAAA;UAJDrC,KAAK,EAAC,MAAM;UAAEsC,KAAK,EAAE5E,MAAA,CAAAC,cAAc,CAACyE,kBAAkB,CAACG,aAAa;UAAOC,MAAM,EAAC;;UACnFC,MAAM,EAAA7F,QAAA,CACf,MAAoDI,MAAA,QAAAA,MAAA,OAApDV,mBAAA,CAAoD;YAAjDL,KAAK,EAAC,eAAe;YAACyG,KAAsB,EAAtB;cAAA;YAAA;;UAlHvCxF,CAAA;;QAAAA,CAAA;UAuHQV,YAAA,CAMSuE,iBAAA;QANAC,IAAI,EAAE;MAAC;QAvHxBrE,OAAA,EAAAC,QAAA,CAwHU,MAIe,CAJfJ,YAAA,CAIe6F,uBAAA;UAJDrC,KAAK,EAAC,MAAM;UAAEsC,KAAK,GAAG5E,MAAA,CAAAC,cAAc,CAACyE,kBAAkB,CAACO,YAAY,OAAOC,OAAO;UAAKJ,MAAM,EAAC;;UAC/FC,MAAM,EAAA7F,QAAA,CACf,MAAuDI,MAAA,QAAAA,MAAA,OAAvDV,mBAAA,CAAuD;YAApDL,KAAK,EAAC,kBAAkB;YAACyG,KAAsB,EAAtB;cAAA;YAAA;;UA1H1CxF,CAAA;;QAAAA,CAAA;UA+HQV,YAAA,CAMSuE,iBAAA;QANAC,IAAI,EAAE;MAAC;QA/HxBrE,OAAA,EAAAC,QAAA,CAgIU,MAIe,CAJfJ,YAAA,CAIe6F,uBAAA;UAJDrC,KAAK,EAAC,MAAM;UAAEsC,KAAK,GAAG5E,MAAA,CAAAC,cAAc,CAACyE,kBAAkB,CAACS,UAAU,OAAOD,OAAO;UAAKJ,MAAM,EAAC;;UAC7FC,MAAM,EAAA7F,QAAA,CACf,MAAoDI,MAAA,QAAAA,MAAA,OAApDV,mBAAA,CAAoD;YAAjDL,KAAK,EAAC,eAAe;YAACyG,KAAsB,EAAtB;cAAA;YAAA;;UAlIvCxF,CAAA;;QAAAA,CAAA;UAuIQV,YAAA,CAMSuE,iBAAA;QANAC,IAAI,EAAE;MAAC;QAvIxBrE,OAAA,EAAAC,QAAA,CAwIU,MAIe,CAJfJ,YAAA,CAIe6F,uBAAA;UAJDrC,KAAK,EAAC,MAAM;UAAEsC,KAAK,GAAG5E,MAAA,CAAAC,cAAc,CAACyE,kBAAkB,CAACU,qBAAqB,OAAOF,OAAO;UAAKJ,MAAM,EAAC;;UACxGC,MAAM,EAAA7F,QAAA,CACf,MAA2DI,MAAA,QAAAA,MAAA,OAA3DV,mBAAA,CAA2D;YAAxDL,KAAK,EAAC,sBAAsB;YAACyG,KAAsB,EAAtB;cAAA;YAAA;;UA1I9CxF,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;QAAAb,mBAAA,gBAiJIA,mBAAA,UAAa,EACbG,YAAA,CAqBUqB,kBAAA;IArBD5B,KAAK,EAAC;EAAU;IACZ6B,MAAM,EAAAlB,QAAA,CACf,MAKM,CALNN,mBAAA,CAKM,OALNyG,UAKM,G,4BAJJzG,mBAAA,CAA8C,aAA1CA,mBAAA,CAAgC;MAA7BL,KAAK,EAAC;IAAkB,IArJzCgB,gBAAA,CAqJ8C,OAAK,E,sBACzCT,YAAA,CAEYY,oBAAA;MAFDkB,IAAI,EAAC,OAAO;MAAEjB,OAAK,EAAEK,MAAA,CAAAsF;;MAtJ1CrG,OAAA,EAAAC,QAAA,CAuJY,MAA+BI,MAAA,SAAAA,MAAA,QAA/BV,mBAAA,CAA+B;QAA5BL,KAAK,EAAC;MAAiB,4BAvJtCgB,gBAAA,CAuJ2C,MACjC,E;MAxJVC,CAAA;;IAAAP,OAAA,EAAAC,QAAA,CA4JM,MAUM,CAVNN,mBAAA,CAUM,OAVN2G,UAUM,I,kBATJ9G,mBAAA,CAIM+G,SAAA,QAjKdC,WAAA,CA6JoCzF,MAAA,CAAA0F,cAAc,EA7JlD,CA6JqBC,GAAG,EAAEC,KAAK;2BAAvBnH,mBAAA,CAIM;QAJuCD,GAAG,EAAEoH,KAAK;QAAErH,KAAK,EAAC;UAC7DK,mBAAA,CAA6D,QAA7DiH,UAA6D,EAAAvF,gBAAA,CAAnCN,MAAA,CAAAuB,UAAU,CAACoE,GAAG,CAACG,SAAS,mBAClDlH,mBAAA,CAAqF;QAA9EL,KAAK,EA/JtBwH,eAAA,sBA+J6CJ,GAAG,CAACK,KAAK;0BAAQL,GAAG,CAACK,KAAK,CAACC,WAAW,2BACzErH,mBAAA,CAAkD,QAAlDsH,UAAkD,EAAA5F,gBAAA,CAArBqF,GAAG,CAACQ,OAAO,iB;oCAG/BnG,MAAA,CAAA0F,cAAc,CAACU,MAAM,U,cAAhC3H,mBAAA,CAEM,OAFN4H,WAEM,EAFkD,UAExD,KArKR1H,mBAAA,e;IAAAa,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}