package com.traffic.analysis.repository;

import com.traffic.analysis.model.OptimizationResult;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 优化结果数据访问接口
 */
@Repository
public interface OptimizationResultRepository extends MongoRepository<OptimizationResult, String> {
    
    /**
     * 根据仿真任务ID查找优化结果
     * @param simulationTaskId 仿真任务ID
     * @return 优化结果
     */
    Optional<OptimizationResult> findBySimulationTaskId(String simulationTaskId);
    
    /**
     * 根据优化类型查询结果列表
     * @param optimizationType 优化类型
     * @return 优化结果列表
     */
    List<OptimizationResult> findByOptimizationType(String optimizationType);
    
    /**
     * 根据优化方法查询结果列表
     * @param optimizationMethod 优化方法
     * @return 优化结果列表
     */
    List<OptimizationResult> findByOptimizationMethod(String optimizationMethod);
    
    /**
     * 查询指定时间范围内的优化结果
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 优化结果列表
     */
    List<OptimizationResult> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查询最近的优化结果（按创建时间降序）
     * @param limit 限制数量
     * @return 优化结果列表
     */
    @Query(value = "{}", sort = "{'createTime': -1}")
    List<OptimizationResult> findRecentResults(int limit);
    
    /**
     * 查询高改善效果的优化结果
     * @param improvementThreshold 改善阈值
     * @return 优化结果列表
     */
    @Query("{'improvementMetrics.overallImprovement': {'$gte': ?0}}")
    List<OptimizationResult> findHighImprovementResults(Double improvementThreshold);
    
    /**
     * 根据优化类型和改善阈值查询结果
     * @param optimizationType 优化类型
     * @param improvementThreshold 改善阈值
     * @return 优化结果列表
     */
    @Query("{'optimizationType': ?0, 'improvementMetrics.overallImprovement': {'$gte': ?1}}")
    List<OptimizationResult> findByTypeAndImprovement(String optimizationType, Double improvementThreshold);
    
    /**
     * 统计各优化类型的数量
     * @return 统计结果
     */
    @Query(value = "{}", fields = "{'optimizationType': 1}")
    List<OptimizationResult> findAllOptimizationTypes();
    
    /**
     * 查询信号灯配时优化结果
     * @return 信号灯配时优化结果列表
     */
    @Query("{'optimizationType': 'signal_timing'}")
    List<OptimizationResult> findSignalTimingResults();
    
    /**
     * 查询流量平衡优化结果
     * @return 流量平衡优化结果列表
     */
    @Query("{'optimizationType': 'flow_balance'}")
    List<OptimizationResult> findFlowBalanceResults();
    
    /**
     * 查询综合优化结果
     * @return 综合优化结果列表
     */
    @Query("{'optimizationType': 'comprehensive'}")
    List<OptimizationResult> findComprehensiveResults();
    
    /**
     * 删除指定时间之前的优化结果（清理历史数据）
     * @param cutoffTime 截止时间
     * @return 删除的结果数量
     */
    @Query(value = "{'createTime': {'$lt': ?0}}", delete = true)
    long deleteOldResults(LocalDateTime cutoffTime);
}
