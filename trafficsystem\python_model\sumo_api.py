#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SUMO仿真API服务

提供SUMO仿真的REST API接口
"""

import os
import sys
import logging
import traceback
import json
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入SUMO集成模块
try:
    from sumo_integration import SumoService, DataConverter, OptimizationEngine
    SUMO_INTEGRATION_AVAILABLE = True
except ImportError as e:
    SUMO_INTEGRATION_AVAILABLE = False
    logging.warning(f"SUMO集成模块导入失败: {e}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*", "supports_credentials": True}})

# 全局变量
sumo_service = None
data_converter = None
optimization_engine = None

def init_sumo_services():
    """初始化SUMO服务"""
    global sumo_service, data_converter, optimization_engine
    
    try:
        if SUMO_INTEGRATION_AVAILABLE:
            sumo_service = SumoService()
            data_converter = DataConverter()
            optimization_engine = OptimizationEngine()
            logger.info("SUMO服务初始化成功")
            return True
        else:
            logger.error("SUMO集成模块不可用")
            return False
    except Exception as e:
        logger.error(f"SUMO服务初始化失败: {e}")
        return False

@app.route('/api/sumo/status', methods=['GET'])
def get_sumo_status():
    """获取SUMO服务状态"""
    try:
        if not SUMO_INTEGRATION_AVAILABLE:
            return jsonify({
                "status": "error",
                "message": "SUMO集成模块不可用",
                "available": False
            }), 503
        
        if sumo_service:
            status = sumo_service.get_simulation_status()
            status["available"] = True
            status["service_status"] = "running"
            return jsonify(status)
        else:
            return jsonify({
                "status": "error",
                "message": "SUMO服务未初始化",
                "available": False
            }), 503
            
    except Exception as e:
        logger.error(f"获取SUMO状态失败: {e}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "available": False
        }), 500

@app.route('/api/sumo/simulation/create', methods=['POST'])
def create_simulation():
    """创建新的仿真任务"""
    try:
        if not sumo_service:
            return jsonify({
                "status": "error",
                "message": "SUMO服务未初始化"
            }), 503
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "缺少请求数据"
            }), 400
        
        traffic_data = data.get('traffic_data')
        simulation_config = data.get('simulation_config', {})
        
        if not traffic_data:
            return jsonify({
                "status": "error",
                "message": "缺少交通数据"
            }), 400
        
        logger.info("创建SUMO仿真任务")
        
        # 创建仿真
        simulation_id = sumo_service.create_simulation(traffic_data, simulation_config)
        
        return jsonify({
            "status": "success",
            "simulation_id": simulation_id,
            "message": "仿真任务创建成功",
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"创建仿真任务失败: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/sumo/simulation/<simulation_id>/start', methods=['POST'])
def start_simulation(simulation_id):
    """启动仿真"""
    try:
        if not sumo_service:
            return jsonify({
                "status": "error",
                "message": "SUMO服务未初始化"
            }), 503
        
        # 获取请求参数
        data = request.get_json() or {}
        use_gui = data.get('use_gui', False)
        
        logger.info(f"启动仿真: {simulation_id}")
        
        # 启动仿真
        success = sumo_service.start_simulation(simulation_id, use_gui)
        
        if success:
            return jsonify({
                "status": "success",
                "simulation_id": simulation_id,
                "message": "仿真启动成功",
                "timestamp": datetime.now().isoformat()
            })
        else:
            return jsonify({
                "status": "error",
                "message": "仿真启动失败"
            }), 500
        
    except Exception as e:
        logger.error(f"启动仿真失败: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/sumo/simulation/stop', methods=['POST'])
def stop_simulation():
    """停止当前仿真"""
    try:
        if not sumo_service:
            return jsonify({
                "status": "error",
                "message": "SUMO服务未初始化"
            }), 503
        
        logger.info("停止仿真")
        
        # 停止仿真
        success = sumo_service.stop_simulation()
        
        if success:
            return jsonify({
                "status": "success",
                "message": "仿真停止成功",
                "timestamp": datetime.now().isoformat()
            })
        else:
            return jsonify({
                "status": "error",
                "message": "仿真停止失败"
            }), 500
        
    except Exception as e:
        logger.error(f"停止仿真失败: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/sumo/optimization/signal', methods=['POST'])
def optimize_signal_timing():
    """优化信号灯配时"""
    try:
        if not optimization_engine:
            return jsonify({
                "status": "error",
                "message": "优化引擎未初始化"
            }), 503
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "缺少请求数据"
            }), 400
        
        traffic_data = data.get('traffic_data')
        current_timing = data.get('current_timing')
        
        if not traffic_data:
            return jsonify({
                "status": "error",
                "message": "缺少交通数据"
            }), 400
        
        logger.info("执行信号灯配时优化")
        
        # 执行优化
        optimization_result = optimization_engine.optimize_signal_timing(traffic_data, current_timing)
        
        return jsonify({
            "status": "success",
            "optimization_result": optimization_result,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"信号灯配时优化失败: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/sumo/optimization/flow', methods=['POST'])
def optimize_flow_balance():
    """优化流量平衡"""
    try:
        if not optimization_engine:
            return jsonify({
                "status": "error",
                "message": "优化引擎未初始化"
            }), 503
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "缺少请求数据"
            }), 400
        
        traffic_data = data.get('traffic_data')
        
        if not traffic_data:
            return jsonify({
                "status": "error",
                "message": "缺少交通数据"
            }), 400
        
        logger.info("执行流量平衡优化")
        
        # 执行优化
        optimization_result = optimization_engine.optimize_flow_balance(traffic_data)
        
        return jsonify({
            "status": "success",
            "optimization_result": optimization_result,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"流量平衡优化失败: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/sumo/optimization/comprehensive', methods=['POST'])
def comprehensive_optimization():
    """综合优化分析"""
    try:
        if not optimization_engine:
            return jsonify({
                "status": "error",
                "message": "优化引擎未初始化"
            }), 503
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "缺少请求数据"
            }), 400
        
        traffic_data = data.get('traffic_data')
        
        if not traffic_data:
            return jsonify({
                "status": "error",
                "message": "缺少交通数据"
            }), 400
        
        logger.info("执行综合优化分析")
        
        # 执行综合优化
        optimization_result = optimization_engine.generate_comprehensive_optimization(traffic_data)
        
        return jsonify({
            "status": "success",
            "optimization_result": optimization_result,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"综合优化分析失败: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/sumo/data/convert', methods=['POST'])
def convert_data():
    """数据格式转换"""
    try:
        if not data_converter:
            return jsonify({
                "status": "error",
                "message": "数据转换器未初始化"
            }), 503
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "缺少请求数据"
            }), 400
        
        traffic_data = data.get('traffic_data')
        conversion_type = data.get('conversion_type', 'to_sumo')
        
        if not traffic_data:
            return jsonify({
                "status": "error",
                "message": "缺少交通数据"
            }), 400
        
        logger.info(f"执行数据转换: {conversion_type}")
        
        # 执行数据转换
        if conversion_type == 'to_sumo':
            converted_data = data_converter.convert_traffic_data_to_sumo(traffic_data)
        elif conversion_type == 'to_frontend':
            converted_data = data_converter.convert_sumo_results_to_frontend(traffic_data)
        else:
            return jsonify({
                "status": "error",
                "message": f"不支持的转换类型: {conversion_type}"
            }), 400
        
        return jsonify({
            "status": "success",
            "converted_data": converted_data,
            "conversion_type": conversion_type,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"数据转换失败: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/sumo/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "sumo_integration_available": SUMO_INTEGRATION_AVAILABLE,
            "services": {
                "sumo_service": sumo_service is not None,
                "data_converter": data_converter is not None,
                "optimization_engine": optimization_engine is not None
            }
        }
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    # 初始化SUMO服务
    init_success = init_sumo_services()
    
    if init_success:
        logger.info("SUMO API服务启动成功")
        app.run(host='0.0.0.0', port=5002, debug=True)
    else:
        logger.error("SUMO API服务启动失败")
        sys.exit(1)
