{"ast": null, "code": "import request from '@/utils/request';\n\n/**\n * 仿真相关API\n */\nconst simulationApi = {\n  /**\n   * 创建仿真任务\n   * @param {Object} data 仿真任务数据\n   * @returns {Promise}\n   */\n  createSimulation(data) {\n    return request({\n      url: '/api/simulation/create',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 启动仿真\n   * @param {string} simulationId 仿真ID\n   * @param {Object} config 启动配置\n   * @returns {Promise}\n   */\n  startSimulation(simulationId, config = {}) {\n    return request({\n      url: `/api/simulation/${simulationId}/start`,\n      method: 'post',\n      data: config\n    });\n  },\n  /**\n   * 停止仿真\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  stopSimulation(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/stop`,\n      method: 'post'\n    });\n  },\n  /**\n   * 获取仿真状态\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getSimulationStatus(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/status`,\n      method: 'get'\n    });\n  },\n  /**\n   * 获取用户的仿真任务列表\n   * @param {string} userId 用户ID\n   * @returns {Promise}\n   */\n  getUserSimulations(userId) {\n    return request({\n      url: `/api/simulation/user/${userId}`,\n      method: 'get'\n    });\n  },\n  /**\n   * 获取仿真任务详情\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getSimulationTask(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}`,\n      method: 'get'\n    });\n  },\n  /**\n   * 删除仿真任务\n   * @param {string} simulationId 仿真ID\n   * @param {string} userId 用户ID\n   * @returns {Promise}\n   */\n  deleteSimulation(simulationId, userId) {\n    return request({\n      url: `/api/simulation/${simulationId}`,\n      method: 'delete',\n      params: {\n        userId\n      }\n    });\n  },\n  /**\n   * 信号灯配时优化\n   * @param {Object} data 优化数据\n   * @returns {Promise}\n   */\n  optimizeSignalTiming(data) {\n    return request({\n      url: '/api/simulation/optimization/signal',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 流量平衡优化\n   * @param {Object} data 优化数据\n   * @returns {Promise}\n   */\n  optimizeFlowBalance(data) {\n    return request({\n      url: '/api/simulation/optimization/flow',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 综合优化分析\n   * @param {Object} data 优化数据\n   * @returns {Promise}\n   */\n  comprehensiveOptimization(data) {\n    return request({\n      url: '/api/simulation/optimization/comprehensive',\n      method: 'post',\n      data\n    });\n  },\n  /**\n   * 获取优化结果\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getOptimizationResult(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/optimization`,\n      method: 'get'\n    });\n  },\n  /**\n   * 获取仿真统计信息\n   * @param {string} userId 用户ID（可选）\n   * @returns {Promise}\n   */\n  getStatistics(userId = null) {\n    return request({\n      url: '/api/simulation/statistics',\n      method: 'get',\n      params: userId ? {\n        userId\n      } : {}\n    });\n  },\n  /**\n   * 检查SUMO服务状态\n   * @returns {Promise}\n   */\n  checkServiceStatus() {\n    return request({\n      url: '/api/simulation/service/status',\n      method: 'get'\n    });\n  },\n  /**\n   * 导出仿真结果\n   * @param {string} simulationId 仿真ID\n   * @param {string} format 导出格式\n   * @returns {Promise}\n   */\n  exportResults(simulationId, format = 'json') {\n    return request({\n      url: `/api/simulation/${simulationId}/export`,\n      method: 'get',\n      params: {\n        format\n      },\n      responseType: format === 'json' ? 'json' : 'blob'\n    });\n  },\n  /**\n   * 重启仿真\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  restartSimulation(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/restart`,\n      method: 'post'\n    });\n  },\n  /**\n   * 比较多个仿真结果\n   * @param {Array} simulationIds 仿真ID列表\n   * @returns {Promise}\n   */\n  compareResults(simulationIds) {\n    return request({\n      url: '/api/simulation/compare',\n      method: 'post',\n      data: {\n        simulation_ids: simulationIds\n      }\n    });\n  },\n  /**\n   * 获取性能报告\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getPerformanceReport(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/performance`,\n      method: 'get'\n    });\n  }\n};\n\n/**\n * 数据集成相关API\n */\nconst integrationApi = {\n  /**\n   * 转换分析数据为SUMO格式\n   * @param {string} analysisTaskId 分析任务ID\n   * @returns {Promise}\n   */\n  convertAnalysisData(analysisTaskId) {\n    return request({\n      url: '/api/integration/convert-analysis-data',\n      method: 'post',\n      data: {\n        analysis_task_id: analysisTaskId\n      },\n      baseURL: 'http://localhost:5003'\n    });\n  },\n  /**\n   * 基于分析结果创建仿真任务\n   * @param {Object} data 创建数据\n   * @returns {Promise}\n   */\n  createSimulationFromAnalysis(data) {\n    return request({\n      url: '/api/integration/create-simulation',\n      method: 'post',\n      data,\n      baseURL: 'http://localhost:5003'\n    });\n  },\n  /**\n   * 启动仿真任务\n   * @param {string} simulationId 仿真ID\n   * @param {boolean} useGui 是否使用GUI\n   * @returns {Promise}\n   */\n  startSimulationTask(simulationId, useGui = false) {\n    return request({\n      url: '/api/integration/start-simulation',\n      method: 'post',\n      data: {\n        simulation_id: simulationId,\n        use_gui: useGui\n      },\n      baseURL: 'http://localhost:5003'\n    });\n  },\n  /**\n   * 获取仿真状态\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getSimulationStatus(simulationId) {\n    return request({\n      url: `/api/integration/simulation-status/${simulationId}`,\n      method: 'get',\n      baseURL: 'http://localhost:5003'\n    });\n  },\n  /**\n   * 健康检查\n   * @returns {Promise}\n   */\n  healthCheck() {\n    return request({\n      url: '/api/integration/health',\n      method: 'get',\n      baseURL: 'http://localhost:5003'\n    });\n  }\n};\n\n/**\n * SUMO API相关\n */\nconst sumoApi = {\n  /**\n   * 获取SUMO服务状态\n   * @returns {Promise}\n   */\n  getStatus() {\n    return request({\n      url: '/api/sumo/status',\n      method: 'get',\n      baseURL: 'http://localhost:5002'\n    });\n  },\n  /**\n   * 创建SUMO仿真\n   * @param {Object} data 仿真数据\n   * @returns {Promise}\n   */\n  createSimulation(data) {\n    return request({\n      url: '/api/sumo/simulation/create',\n      method: 'post',\n      data,\n      baseURL: 'http://localhost:5002'\n    });\n  },\n  /**\n   * 启动SUMO仿真\n   * @param {string} simulationId 仿真ID\n   * @param {Object} config 配置\n   * @returns {Promise}\n   */\n  startSimulation(simulationId, config) {\n    return request({\n      url: `/api/sumo/simulation/${simulationId}/start`,\n      method: 'post',\n      data: config,\n      baseURL: 'http://localhost:5002'\n    });\n  },\n  /**\n   * 停止SUMO仿真\n   * @returns {Promise}\n   */\n  stopSimulation() {\n    return request({\n      url: '/api/sumo/simulation/stop',\n      method: 'post',\n      baseURL: 'http://localhost:5002'\n    });\n  },\n  /**\n   * 数据转换\n   * @param {Object} data 转换数据\n   * @returns {Promise}\n   */\n  convertData(data) {\n    return request({\n      url: '/api/sumo/data/convert',\n      method: 'post',\n      data,\n      baseURL: 'http://localhost:5002'\n    });\n  },\n  /**\n   * 健康检查\n   * @returns {Promise}\n   */\n  healthCheck() {\n    return request({\n      url: '/api/sumo/health',\n      method: 'get',\n      baseURL: 'http://localhost:5002'\n    });\n  }\n};\nexport default simulationApi;\nexport { integrationApi, sumoApi };", "map": {"version": 3, "names": ["request", "simulationApi", "createSimulation", "data", "url", "method", "startSimulation", "simulationId", "config", "stopSimulation", "getSimulationStatus", "getUserSimulations", "userId", "getSimulationTask", "deleteSimulation", "params", "optimizeSignalTiming", "optimizeFlowBalance", "comprehensiveOptimization", "getOptimizationResult", "getStatistics", "checkServiceStatus", "exportResults", "format", "responseType", "restartSimulation", "compareResults", "simulationIds", "simulation_ids", "getPerformanceReport", "integrationApi", "convertAnalysisData", "analysisTaskId", "analysis_task_id", "baseURL", "createSimulationFromAnalysis", "startSimulationTask", "useGui", "simulation_id", "use_gui", "healthCheck", "sumoApi", "getStatus", "convertData"], "sources": ["D:/code/nvm/trafficsystem/src/api/simulation.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * 仿真相关API\n */\nconst simulationApi = {\n  /**\n   * 创建仿真任务\n   * @param {Object} data 仿真任务数据\n   * @returns {Promise}\n   */\n  createSimulation(data) {\n    return request({\n      url: '/api/simulation/create',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 启动仿真\n   * @param {string} simulationId 仿真ID\n   * @param {Object} config 启动配置\n   * @returns {Promise}\n   */\n  startSimulation(simulationId, config = {}) {\n    return request({\n      url: `/api/simulation/${simulationId}/start`,\n      method: 'post',\n      data: config\n    })\n  },\n\n  /**\n   * 停止仿真\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  stopSimulation(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/stop`,\n      method: 'post'\n    })\n  },\n\n  /**\n   * 获取仿真状态\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getSimulationStatus(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/status`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 获取用户的仿真任务列表\n   * @param {string} userId 用户ID\n   * @returns {Promise}\n   */\n  getUserSimulations(userId) {\n    return request({\n      url: `/api/simulation/user/${userId}`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 获取仿真任务详情\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getSimulationTask(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 删除仿真任务\n   * @param {string} simulationId 仿真ID\n   * @param {string} userId 用户ID\n   * @returns {Promise}\n   */\n  deleteSimulation(simulationId, userId) {\n    return request({\n      url: `/api/simulation/${simulationId}`,\n      method: 'delete',\n      params: { userId }\n    })\n  },\n\n  /**\n   * 信号灯配时优化\n   * @param {Object} data 优化数据\n   * @returns {Promise}\n   */\n  optimizeSignalTiming(data) {\n    return request({\n      url: '/api/simulation/optimization/signal',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 流量平衡优化\n   * @param {Object} data 优化数据\n   * @returns {Promise}\n   */\n  optimizeFlowBalance(data) {\n    return request({\n      url: '/api/simulation/optimization/flow',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 综合优化分析\n   * @param {Object} data 优化数据\n   * @returns {Promise}\n   */\n  comprehensiveOptimization(data) {\n    return request({\n      url: '/api/simulation/optimization/comprehensive',\n      method: 'post',\n      data\n    })\n  },\n\n  /**\n   * 获取优化结果\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getOptimizationResult(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/optimization`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 获取仿真统计信息\n   * @param {string} userId 用户ID（可选）\n   * @returns {Promise}\n   */\n  getStatistics(userId = null) {\n    return request({\n      url: '/api/simulation/statistics',\n      method: 'get',\n      params: userId ? { userId } : {}\n    })\n  },\n\n  /**\n   * 检查SUMO服务状态\n   * @returns {Promise}\n   */\n  checkServiceStatus() {\n    return request({\n      url: '/api/simulation/service/status',\n      method: 'get'\n    })\n  },\n\n  /**\n   * 导出仿真结果\n   * @param {string} simulationId 仿真ID\n   * @param {string} format 导出格式\n   * @returns {Promise}\n   */\n  exportResults(simulationId, format = 'json') {\n    return request({\n      url: `/api/simulation/${simulationId}/export`,\n      method: 'get',\n      params: { format },\n      responseType: format === 'json' ? 'json' : 'blob'\n    })\n  },\n\n  /**\n   * 重启仿真\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  restartSimulation(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/restart`,\n      method: 'post'\n    })\n  },\n\n  /**\n   * 比较多个仿真结果\n   * @param {Array} simulationIds 仿真ID列表\n   * @returns {Promise}\n   */\n  compareResults(simulationIds) {\n    return request({\n      url: '/api/simulation/compare',\n      method: 'post',\n      data: { simulation_ids: simulationIds }\n    })\n  },\n\n  /**\n   * 获取性能报告\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getPerformanceReport(simulationId) {\n    return request({\n      url: `/api/simulation/${simulationId}/performance`,\n      method: 'get'\n    })\n  }\n}\n\n/**\n * 数据集成相关API\n */\nconst integrationApi = {\n  /**\n   * 转换分析数据为SUMO格式\n   * @param {string} analysisTaskId 分析任务ID\n   * @returns {Promise}\n   */\n  convertAnalysisData(analysisTaskId) {\n    return request({\n      url: '/api/integration/convert-analysis-data',\n      method: 'post',\n      data: { analysis_task_id: analysisTaskId },\n      baseURL: 'http://localhost:5003'\n    })\n  },\n\n  /**\n   * 基于分析结果创建仿真任务\n   * @param {Object} data 创建数据\n   * @returns {Promise}\n   */\n  createSimulationFromAnalysis(data) {\n    return request({\n      url: '/api/integration/create-simulation',\n      method: 'post',\n      data,\n      baseURL: 'http://localhost:5003'\n    })\n  },\n\n  /**\n   * 启动仿真任务\n   * @param {string} simulationId 仿真ID\n   * @param {boolean} useGui 是否使用GUI\n   * @returns {Promise}\n   */\n  startSimulationTask(simulationId, useGui = false) {\n    return request({\n      url: '/api/integration/start-simulation',\n      method: 'post',\n      data: { simulation_id: simulationId, use_gui: useGui },\n      baseURL: 'http://localhost:5003'\n    })\n  },\n\n  /**\n   * 获取仿真状态\n   * @param {string} simulationId 仿真ID\n   * @returns {Promise}\n   */\n  getSimulationStatus(simulationId) {\n    return request({\n      url: `/api/integration/simulation-status/${simulationId}`,\n      method: 'get',\n      baseURL: 'http://localhost:5003'\n    })\n  },\n\n  /**\n   * 健康检查\n   * @returns {Promise}\n   */\n  healthCheck() {\n    return request({\n      url: '/api/integration/health',\n      method: 'get',\n      baseURL: 'http://localhost:5003'\n    })\n  }\n}\n\n/**\n * SUMO API相关\n */\nconst sumoApi = {\n  /**\n   * 获取SUMO服务状态\n   * @returns {Promise}\n   */\n  getStatus() {\n    return request({\n      url: '/api/sumo/status',\n      method: 'get',\n      baseURL: 'http://localhost:5002'\n    })\n  },\n\n  /**\n   * 创建SUMO仿真\n   * @param {Object} data 仿真数据\n   * @returns {Promise}\n   */\n  createSimulation(data) {\n    return request({\n      url: '/api/sumo/simulation/create',\n      method: 'post',\n      data,\n      baseURL: 'http://localhost:5002'\n    })\n  },\n\n  /**\n   * 启动SUMO仿真\n   * @param {string} simulationId 仿真ID\n   * @param {Object} config 配置\n   * @returns {Promise}\n   */\n  startSimulation(simulationId, config) {\n    return request({\n      url: `/api/sumo/simulation/${simulationId}/start`,\n      method: 'post',\n      data: config,\n      baseURL: 'http://localhost:5002'\n    })\n  },\n\n  /**\n   * 停止SUMO仿真\n   * @returns {Promise}\n   */\n  stopSimulation() {\n    return request({\n      url: '/api/sumo/simulation/stop',\n      method: 'post',\n      baseURL: 'http://localhost:5002'\n    })\n  },\n\n  /**\n   * 数据转换\n   * @param {Object} data 转换数据\n   * @returns {Promise}\n   */\n  convertData(data) {\n    return request({\n      url: '/api/sumo/data/convert',\n      method: 'post',\n      data,\n      baseURL: 'http://localhost:5002'\n    })\n  },\n\n  /**\n   * 健康检查\n   * @returns {Promise}\n   */\n  healthCheck() {\n    return request({\n      url: '/api/sumo/health',\n      method: 'get',\n      baseURL: 'http://localhost:5002'\n    })\n  }\n}\n\nexport default simulationApi\nexport { integrationApi, sumoApi }\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA,MAAMC,aAAa,GAAG;EACpB;AACF;AACA;AACA;AACA;EACEC,gBAAgBA,CAACC,IAAI,EAAE;IACrB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,wBAAwB;MAC7BC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEG,eAAeA,CAACC,YAAY,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;IACzC,OAAOR,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,QAAQ;MAC5CF,MAAM,EAAE,MAAM;MACdF,IAAI,EAAEK;IACR,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAACF,YAAY,EAAE;IAC3B,OAAOP,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,OAAO;MAC3CF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEK,mBAAmBA,CAACH,YAAY,EAAE;IAChC,OAAOP,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,SAAS;MAC7CF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEM,kBAAkBA,CAACC,MAAM,EAAE;IACzB,OAAOZ,OAAO,CAAC;MACbI,GAAG,EAAE,wBAAwBQ,MAAM,EAAE;MACrCP,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEQ,iBAAiBA,CAACN,YAAY,EAAE;IAC9B,OAAOP,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,EAAE;MACtCF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACES,gBAAgBA,CAACP,YAAY,EAAEK,MAAM,EAAE;IACrC,OAAOZ,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,EAAE;MACtCF,MAAM,EAAE,QAAQ;MAChBU,MAAM,EAAE;QAAEH;MAAO;IACnB,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEI,oBAAoBA,CAACb,IAAI,EAAE;IACzB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,qCAAqC;MAC1CC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEc,mBAAmBA,CAACd,IAAI,EAAE;IACxB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,mCAAmC;MACxCC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEe,yBAAyBA,CAACf,IAAI,EAAE;IAC9B,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,4CAA4C;MACjDC,MAAM,EAAE,MAAM;MACdF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEgB,qBAAqBA,CAACZ,YAAY,EAAE;IAClC,OAAOP,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,eAAe;MACnDF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEe,aAAaA,CAACR,MAAM,GAAG,IAAI,EAAE;IAC3B,OAAOZ,OAAO,CAAC;MACbI,GAAG,EAAE,4BAA4B;MACjCC,MAAM,EAAE,KAAK;MACbU,MAAM,EAAEH,MAAM,GAAG;QAAEA;MAAO,CAAC,GAAG,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACES,kBAAkBA,CAAA,EAAG;IACnB,OAAOrB,OAAO,CAAC;MACbI,GAAG,EAAE,gCAAgC;MACrCC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEiB,aAAaA,CAACf,YAAY,EAAEgB,MAAM,GAAG,MAAM,EAAE;IAC3C,OAAOvB,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,SAAS;MAC7CF,MAAM,EAAE,KAAK;MACbU,MAAM,EAAE;QAAEQ;MAAO,CAAC;MAClBC,YAAY,EAAED,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG;IAC7C,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEE,iBAAiBA,CAAClB,YAAY,EAAE;IAC9B,OAAOP,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,UAAU;MAC9CF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEqB,cAAcA,CAACC,aAAa,EAAE;IAC5B,OAAO3B,OAAO,CAAC;MACbI,GAAG,EAAE,yBAAyB;MAC9BC,MAAM,EAAE,MAAM;MACdF,IAAI,EAAE;QAAEyB,cAAc,EAAED;MAAc;IACxC,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEE,oBAAoBA,CAACtB,YAAY,EAAE;IACjC,OAAOP,OAAO,CAAC;MACbI,GAAG,EAAE,mBAAmBG,YAAY,cAAc;MAClDF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMyB,cAAc,GAAG;EACrB;AACF;AACA;AACA;AACA;EACEC,mBAAmBA,CAACC,cAAc,EAAE;IAClC,OAAOhC,OAAO,CAAC;MACbI,GAAG,EAAE,wCAAwC;MAC7CC,MAAM,EAAE,MAAM;MACdF,IAAI,EAAE;QAAE8B,gBAAgB,EAAED;MAAe,CAAC;MAC1CE,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,4BAA4BA,CAAChC,IAAI,EAAE;IACjC,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,oCAAoC;MACzCC,MAAM,EAAE,MAAM;MACdF,IAAI;MACJ+B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEE,mBAAmBA,CAAC7B,YAAY,EAAE8B,MAAM,GAAG,KAAK,EAAE;IAChD,OAAOrC,OAAO,CAAC;MACbI,GAAG,EAAE,mCAAmC;MACxCC,MAAM,EAAE,MAAM;MACdF,IAAI,EAAE;QAAEmC,aAAa,EAAE/B,YAAY;QAAEgC,OAAO,EAAEF;MAAO,CAAC;MACtDH,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACExB,mBAAmBA,CAACH,YAAY,EAAE;IAChC,OAAOP,OAAO,CAAC;MACbI,GAAG,EAAE,sCAAsCG,YAAY,EAAE;MACzDF,MAAM,EAAE,KAAK;MACb6B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEM,WAAWA,CAAA,EAAG;IACZ,OAAOxC,OAAO,CAAC;MACbI,GAAG,EAAE,yBAAyB;MAC9BC,MAAM,EAAE,KAAK;MACb6B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMO,OAAO,GAAG;EACd;AACF;AACA;AACA;EACEC,SAASA,CAAA,EAAG;IACV,OAAO1C,OAAO,CAAC;MACbI,GAAG,EAAE,kBAAkB;MACvBC,MAAM,EAAE,KAAK;MACb6B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEhC,gBAAgBA,CAACC,IAAI,EAAE;IACrB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,6BAA6B;MAClCC,MAAM,EAAE,MAAM;MACdF,IAAI;MACJ+B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE5B,eAAeA,CAACC,YAAY,EAAEC,MAAM,EAAE;IACpC,OAAOR,OAAO,CAAC;MACbI,GAAG,EAAE,wBAAwBG,YAAY,QAAQ;MACjDF,MAAM,EAAE,MAAM;MACdF,IAAI,EAAEK,MAAM;MACZ0B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEzB,cAAcA,CAAA,EAAG;IACf,OAAOT,OAAO,CAAC;MACbI,GAAG,EAAE,2BAA2B;MAChCC,MAAM,EAAE,MAAM;MACd6B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACES,WAAWA,CAACxC,IAAI,EAAE;IAChB,OAAOH,OAAO,CAAC;MACbI,GAAG,EAAE,wBAAwB;MAC7BC,MAAM,EAAE,MAAM;MACdF,IAAI;MACJ+B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEM,WAAWA,CAAA,EAAG;IACZ,OAAOxC,OAAO,CAAC;MACbI,GAAG,EAAE,kBAAkB;MACvBC,MAAM,EAAE,KAAK;MACb6B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAejC,aAAa;AAC5B,SAAS6B,cAAc,EAAEW,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}