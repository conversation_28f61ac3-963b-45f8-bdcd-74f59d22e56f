{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"traffic-light-controller\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"title\"\n};\nconst _hoisted_4 = {\n  class: \"control-mode\"\n};\nconst _hoisted_5 = {\n  class: \"connection-status\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"last-update\"\n};\nconst _hoisted_7 = {\n  class: \"intersection-layout\"\n};\nconst _hoisted_8 = {\n  class: \"direction north\"\n};\nconst _hoisted_9 = {\n  class: \"direction west\"\n};\nconst _hoisted_10 = {\n  class: \"intersection-center\"\n};\nconst _hoisted_11 = {\n  class: \"direction east\"\n};\nconst _hoisted_12 = {\n  class: \"direction south\"\n};\nconst _hoisted_13 = {\n  class: \"phase-info\"\n};\nconst _hoisted_14 = {\n  key: 0,\n  class: \"control-buttons\"\n};\nconst _hoisted_15 = {\n  class: \"operation-log\"\n};\nconst _hoisted_16 = {\n  class: \"log-container\"\n};\nconst _hoisted_17 = {\n  class: \"log-time\"\n};\nconst _hoisted_18 = {\n  class: \"log-message\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Traffic = _resolveComponent(\"Traffic\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_Connection = _resolveComponent(\"Connection\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_traffic_light_display = _resolveComponent(\"traffic-light-display\");\n  const _component_Grid = _resolveComponent(\"Grid\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_Check = _resolveComponent(\"Check\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_Warning = _resolveComponent(\"Warning\");\n  const _component_el_button_group = _resolveComponent(\"el-button-group\");\n  const _component_el_collapse_item = _resolveComponent(\"el-collapse-item\");\n  const _component_el_collapse = _resolveComponent(\"el-collapse\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"controller-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"span\", _hoisted_3, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Traffic)]),\n      _: 1 /* STABLE */\n    }), _cache[2] || (_cache[2] = _createTextVNode(\" 信号灯控制器 \"))]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_switch, {\n      modelValue: $setup.isManualMode,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.isManualMode = $event),\n      disabled: !$setup.isConnected,\n      \"active-text\": \"手动\",\n      \"inactive-text\": \"自动\",\n      onChange: $setup.onModeChange\n    }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\", \"onChange\"])])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_tag, {\n      type: $setup.isConnected ? 'success' : 'danger',\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Connection)]),\n        _: 1 /* STABLE */\n      }), _createTextVNode(\" \" + _toDisplayString($setup.isConnected ? '已连接' : '未连接'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"]), $setup.lastUpdateTime ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, \" 最后更新: \" + _toDisplayString($setup.formatTime($setup.lastUpdateTime)), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" 北方向 \"), _createElementVNode(\"div\", _hoisted_8, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n      class: \"direction-label\"\n    }, \"北\", -1 /* HOISTED */)), _createVNode(_component_traffic_light_display, {\n      state: $setup.trafficLights.north,\n      \"is-manual\": $setup.isManualMode,\n      direction: \"north\",\n      onManualChange: $setup.onManualLightChange\n    }, null, 8 /* PROPS */, [\"state\", \"is-manual\", \"onManualChange\"])]), _createCommentVNode(\" 西方向 \"), _createElementVNode(\"div\", _hoisted_9, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n      class: \"direction-label\"\n    }, \"西\", -1 /* HOISTED */)), _createVNode(_component_traffic_light_display, {\n      state: $setup.trafficLights.west,\n      \"is-manual\": $setup.isManualMode,\n      direction: \"west\",\n      onManualChange: $setup.onManualLightChange\n    }, null, 8 /* PROPS */, [\"state\", \"is-manual\", \"onManualChange\"])]), _createCommentVNode(\" 中央路口 \"), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_icon, {\n      class: \"intersection-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_Grid)]),\n      _: 1 /* STABLE */\n    })]), _createCommentVNode(\" 东方向 \"), _createElementVNode(\"div\", _hoisted_11, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n      class: \"direction-label\"\n    }, \"东\", -1 /* HOISTED */)), _createVNode(_component_traffic_light_display, {\n      state: $setup.trafficLights.east,\n      \"is-manual\": $setup.isManualMode,\n      direction: \"east\",\n      onManualChange: $setup.onManualLightChange\n    }, null, 8 /* PROPS */, [\"state\", \"is-manual\", \"onManualChange\"])]), _createCommentVNode(\" 南方向 \"), _createElementVNode(\"div\", _hoisted_12, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n      class: \"direction-label\"\n    }, \"南\", -1 /* HOISTED */)), _createVNode(_component_traffic_light_display, {\n      state: $setup.trafficLights.south,\n      \"is-manual\": $setup.isManualMode,\n      direction: \"south\",\n      onManualChange: $setup.onManualLightChange\n    }, null, 8 /* PROPS */, [\"state\", \"is-manual\", \"onManualChange\"])])]), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_descriptions, {\n      title: \"当前相位信息\",\n      column: 2,\n      size: \"small\",\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"相位ID\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentPhase.phaseId || '-'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"剩余时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentPhase.remainingTime || 0) + \"秒 \", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"相位描述\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentPhase.description || '-'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"下一相位\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentPhase.nextPhase || '-'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]), $setup.isManualMode ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createVNode(_component_el_button_group, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"success\",\n        disabled: !$setup.isConnected,\n        onClick: $setup.applyManualControl\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Check)]),\n          _: 1 /* STABLE */\n        }), _cache[7] || (_cache[7] = _createTextVNode(\" 应用设置 \"))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n        type: \"warning\",\n        disabled: !$setup.isConnected,\n        onClick: $setup.resetToAuto\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Refresh)]),\n          _: 1 /* STABLE */\n        }), _cache[8] || (_cache[8] = _createTextVNode(\" 恢复自动 \"))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n        type: \"danger\",\n        disabled: !$setup.isConnected,\n        onClick: $setup.emergencyStop\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Warning)]),\n          _: 1 /* STABLE */\n        }), _cache[9] || (_cache[9] = _createTextVNode(\" 紧急停止 \"))]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"disabled\", \"onClick\"])]),\n      _: 1 /* STABLE */\n    })])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_collapse, {\n      modelValue: $setup.logCollapsed,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.logCollapsed = $event)\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_collapse_item, {\n        title: \"操作日志\",\n        name: \"log\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.operationLogs, (log, index) => {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: index,\n            class: _normalizeClass([\"log-item\", log.type])\n          }, [_createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.formatTime(log.timestamp)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_18, _toDisplayString(log.message), 1 /* TEXT */)], 2 /* CLASS */);\n        }), 128 /* KEYED_FRAGMENT */))])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_component_el_icon", "default", "_component_Traffic", "_", "_createTextVNode", "_hoisted_4", "_component_el_switch", "modelValue", "$setup", "isManualMode", "_cache", "$event", "disabled", "isConnected", "onChange", "onModeChange", "_hoisted_5", "_component_el_tag", "type", "size", "_component_Connection", "_toDisplayString", "lastUpdateTime", "_hoisted_6", "formatTime", "_createCommentVNode", "_hoisted_7", "_hoisted_8", "_component_traffic_light_display", "state", "trafficLights", "north", "direction", "onManualChange", "onManualLightChange", "_hoisted_9", "west", "_hoisted_10", "_component_Grid", "_hoisted_11", "east", "_hoisted_12", "south", "_hoisted_13", "_component_el_descriptions", "title", "column", "border", "_component_el_descriptions_item", "label", "currentPhase", "phaseId", "remainingTime", "description", "nextPhase", "_hoisted_14", "_component_el_button_group", "_component_el_button", "onClick", "applyManualControl", "_component_Check", "resetToAuto", "_component_Refresh", "emergencyStop", "_component_Warning", "_hoisted_15", "_component_el_collapse", "logCollapsed", "_component_el_collapse_item", "name", "_hoisted_16", "_Fragment", "_renderList", "operationLogs", "log", "index", "_normalizeClass", "_hoisted_17", "timestamp", "_hoisted_18", "message"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\TrafficLightController.vue"], "sourcesContent": ["<template>\n  <div class=\"traffic-light-controller\">\n    <el-card class=\"controller-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <span class=\"title\">\n            <el-icon><Traffic /></el-icon>\n            信号灯控制器\n          </span>\n          <div class=\"control-mode\">\n            <el-switch\n              v-model=\"isManualMode\"\n              :disabled=\"!isConnected\"\n              active-text=\"手动\"\n              inactive-text=\"自动\"\n              @change=\"onModeChange\"\n            />\n          </div>\n        </div>\n      </template>\n\n      <!-- 连接状态 -->\n      <div class=\"connection-status\">\n        <el-tag :type=\"isConnected ? 'success' : 'danger'\" size=\"small\">\n          <el-icon><Connection /></el-icon>\n          {{ isConnected ? '已连接' : '未连接' }}\n        </el-tag>\n        <span class=\"last-update\" v-if=\"lastUpdateTime\">\n          最后更新: {{ formatTime(lastUpdateTime) }}\n        </span>\n      </div>\n\n      <!-- 十字路口信号灯状态 -->\n      <div class=\"intersection-layout\">\n        <!-- 北方向 -->\n        <div class=\"direction north\">\n          <div class=\"direction-label\">北</div>\n          <traffic-light-display\n            :state=\"trafficLights.north\"\n            :is-manual=\"isManualMode\"\n            direction=\"north\"\n            @manual-change=\"onManualLightChange\"\n          />\n        </div>\n\n        <!-- 西方向 -->\n        <div class=\"direction west\">\n          <div class=\"direction-label\">西</div>\n          <traffic-light-display\n            :state=\"trafficLights.west\"\n            :is-manual=\"isManualMode\"\n            direction=\"west\"\n            @manual-change=\"onManualLightChange\"\n          />\n        </div>\n\n        <!-- 中央路口 -->\n        <div class=\"intersection-center\">\n          <el-icon class=\"intersection-icon\"><Grid /></el-icon>\n        </div>\n\n        <!-- 东方向 -->\n        <div class=\"direction east\">\n          <div class=\"direction-label\">东</div>\n          <traffic-light-display\n            :state=\"trafficLights.east\"\n            :is-manual=\"isManualMode\"\n            direction=\"east\"\n            @manual-change=\"onManualLightChange\"\n          />\n        </div>\n\n        <!-- 南方向 -->\n        <div class=\"direction south\">\n          <div class=\"direction-label\">南</div>\n          <traffic-light-display\n            :state=\"trafficLights.south\"\n            :is-manual=\"isManualMode\"\n            direction=\"south\"\n            @manual-change=\"onManualLightChange\"\n          />\n        </div>\n      </div>\n\n      <!-- 当前相位信息 -->\n      <div class=\"phase-info\">\n        <el-descriptions title=\"当前相位信息\" :column=\"2\" size=\"small\" border>\n          <el-descriptions-item label=\"相位ID\">\n            {{ currentPhase.phaseId || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"剩余时间\">\n            {{ currentPhase.remainingTime || 0 }}秒\n          </el-descriptions-item>\n          <el-descriptions-item label=\"相位描述\">\n            {{ currentPhase.description || '-' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"下一相位\">\n            {{ currentPhase.nextPhase || '-' }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n\n      <!-- 控制按钮 -->\n      <div class=\"control-buttons\" v-if=\"isManualMode\">\n        <el-button-group>\n          <el-button \n            type=\"success\" \n            :disabled=\"!isConnected\"\n            @click=\"applyManualControl\"\n          >\n            <el-icon><Check /></el-icon>\n            应用设置\n          </el-button>\n          <el-button \n            type=\"warning\" \n            :disabled=\"!isConnected\"\n            @click=\"resetToAuto\"\n          >\n            <el-icon><Refresh /></el-icon>\n            恢复自动\n          </el-button>\n          <el-button \n            type=\"danger\" \n            :disabled=\"!isConnected\"\n            @click=\"emergencyStop\"\n          >\n            <el-icon><Warning /></el-icon>\n            紧急停止\n          </el-button>\n        </el-button-group>\n      </div>\n\n      <!-- 操作日志 -->\n      <div class=\"operation-log\">\n        <el-collapse v-model=\"logCollapsed\">\n          <el-collapse-item title=\"操作日志\" name=\"log\">\n            <div class=\"log-container\">\n              <div \n                v-for=\"(log, index) in operationLogs\" \n                :key=\"index\"\n                class=\"log-item\"\n                :class=\"log.type\"\n              >\n                <span class=\"log-time\">{{ formatTime(log.timestamp) }}</span>\n                <span class=\"log-message\">{{ log.message }}</span>\n              </div>\n            </div>\n          </el-collapse-item>\n        </el-collapse>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { \n  Traffic, \n  Connection, \n  Grid, \n  Check, \n  Refresh, \n  Warning \n} from '@element-plus/icons-vue'\nimport stompService from '@/utils/stomp-service'\nimport TrafficLightDisplay from './TrafficLightDisplay.vue'\n\nexport default {\n  name: 'TrafficLightController',\n  components: {\n    TrafficLightDisplay,\n    Traffic,\n    Connection,\n    Grid,\n    Check,\n    Refresh,\n    Warning\n  },\n  props: {\n    simulationId: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['status-change', 'control-change'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isConnected = ref(false)\n    const isManualMode = ref(false)\n    const lastUpdateTime = ref(null)\n    const logCollapsed = ref(['log'])\n    \n    // 信号灯状态\n    const trafficLights = reactive({\n      north: { red: true, yellow: false, green: false },\n      south: { red: true, yellow: false, green: false },\n      east: { red: false, yellow: false, green: true },\n      west: { red: false, yellow: false, green: true }\n    })\n    \n    // 当前相位信息\n    const currentPhase = reactive({\n      phaseId: '',\n      remainingTime: 0,\n      description: '',\n      nextPhase: ''\n    })\n    \n    // 操作日志\n    const operationLogs = ref([])\n    \n    // WebSocket订阅\n    let trafficLightSubscription = null\n    \n    // 计算属性\n    const formatTime = computed(() => {\n      return (timestamp) => {\n        if (!timestamp) return ''\n        return new Date(timestamp).toLocaleTimeString()\n      }\n    })\n    \n    // 方法\n    const addLog = (message, type = 'info') => {\n      operationLogs.value.unshift({\n        timestamp: new Date(),\n        message,\n        type\n      })\n      \n      // 保持日志数量在合理范围内\n      if (operationLogs.value.length > 50) {\n        operationLogs.value = operationLogs.value.slice(0, 50)\n      }\n    }\n    \n    const onModeChange = async (manual) => {\n      try {\n        addLog(`切换到${manual ? '手动' : '自动'}模式`, 'info')\n        \n        if (!manual) {\n          // 切换到自动模式时重置所有手动设置\n          await resetToAuto()\n        }\n        \n        emit('control-change', { mode: manual ? 'manual' : 'auto' })\n        \n      } catch (error) {\n        console.error('模式切换失败:', error)\n        ElMessage.error('模式切换失败')\n        addLog(`模式切换失败: ${error.message}`, 'error')\n      }\n    }\n    \n    const onManualLightChange = (direction, state) => {\n      if (!isManualMode.value) return\n      \n      trafficLights[direction] = { ...state }\n      addLog(`手动设置${direction}方向信号灯`, 'warning')\n    }\n    \n    const applyManualControl = async () => {\n      try {\n        // 发送手动控制指令到后端\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/manual`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            lights: trafficLights,\n            timestamp: new Date().toISOString()\n          })\n        })\n        \n        if (response.ok) {\n          ElMessage.success('手动控制设置已应用')\n          addLog('手动控制设置已应用', 'success')\n        } else {\n          throw new Error('应用手动控制失败')\n        }\n        \n      } catch (error) {\n        console.error('应用手动控制失败:', error)\n        ElMessage.error('应用手动控制失败')\n        addLog(`应用手动控制失败: ${error.message}`, 'error')\n      }\n    }\n    \n    const resetToAuto = async () => {\n      try {\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/auto`, {\n          method: 'POST'\n        })\n        \n        if (response.ok) {\n          isManualMode.value = false\n          ElMessage.success('已恢复自动控制')\n          addLog('已恢复自动控制', 'success')\n        } else {\n          throw new Error('恢复自动控制失败')\n        }\n        \n      } catch (error) {\n        console.error('恢复自动控制失败:', error)\n        ElMessage.error('恢复自动控制失败')\n        addLog(`恢复自动控制失败: ${error.message}`, 'error')\n      }\n    }\n    \n    const emergencyStop = async () => {\n      try {\n        await ElMessageBox.confirm(\n          '确定要执行紧急停止吗？这将使所有方向显示红灯。',\n          '紧急停止确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await fetch(`/api/simulation/${props.simulationId}/traffic-lights/emergency`, {\n          method: 'POST'\n        })\n\n        if (response.ok) {\n          // 设置所有方向为红灯\n          Object.keys(trafficLights).forEach(direction => {\n            trafficLights[direction] = { red: true, yellow: false, green: false }\n          })\n\n          ElMessage.warning('紧急停止已执行，所有方向红灯')\n          addLog('执行紧急停止，所有方向红灯', 'error')\n        } else {\n          throw new Error('紧急停止执行失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('紧急停止失败:', error)\n          ElMessage.error('紧急停止失败')\n          addLog(`紧急停止失败: ${error.message}`, 'error')\n        }\n      }\n    }\n\n    const setupWebSocketConnection = async () => {\n      try {\n        await stompService.connect()\n        isConnected.value = true\n        addLog('WebSocket连接已建立', 'success')\n\n        // 订阅信号灯状态更新\n        trafficLightSubscription = await stompService.subscribe(\n          `/topic/simulation/${props.simulationId}/traffic-lights`,\n          (message) => {\n            try {\n              const data = JSON.parse(message.body)\n              handleTrafficLightUpdate(data)\n            } catch (error) {\n              console.error('解析信号灯数据失败:', error)\n            }\n          }\n        )\n\n        emit('status-change', { connected: true })\n\n      } catch (error) {\n        console.error('WebSocket连接失败:', error)\n        isConnected.value = false\n        addLog(`WebSocket连接失败: ${error.message}`, 'error')\n        emit('status-change', { connected: false })\n      }\n    }\n\n    const handleTrafficLightUpdate = (data) => {\n      if (!data) return\n\n      // 更新信号灯状态\n      if (data.lights) {\n        Object.keys(data.lights).forEach(direction => {\n          if (trafficLights[direction]) {\n            trafficLights[direction] = { ...data.lights[direction] }\n          }\n        })\n      }\n\n      // 更新相位信息\n      if (data.phase) {\n        Object.assign(currentPhase, data.phase)\n      }\n\n      lastUpdateTime.value = new Date()\n\n      // 如果是自动模式下的更新，记录日志\n      if (!isManualMode.value && data.source === 'auto') {\n        addLog(`自动更新: ${data.phase?.description || '信号灯状态更新'}`, 'info')\n      }\n    }\n\n    const cleanup = () => {\n      if (trafficLightSubscription) {\n        stompService.unsubscribe(trafficLightSubscription)\n        trafficLightSubscription = null\n      }\n\n      if (stompService.isConnected()) {\n        stompService.disconnect()\n      }\n\n      isConnected.value = false\n    }\n\n    // 生命周期钩子\n    onMounted(() => {\n      setupWebSocketConnection()\n      addLog('信号灯控制器已初始化', 'info')\n    })\n\n    onUnmounted(() => {\n      cleanup()\n    })\n\n    return {\n      isConnected,\n      isManualMode,\n      lastUpdateTime,\n      logCollapsed,\n      trafficLights,\n      currentPhase,\n      operationLogs,\n      formatTime,\n      onModeChange,\n      onManualLightChange,\n      applyManualControl,\n      resetToAuto,\n      emergencyStop\n    }\n  }\n}\n</script>\n\n<style scoped>\n.traffic-light-controller {\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.controller-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 18px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.control-mode {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.connection-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.last-update {\n  font-size: 12px;\n  color: #6c757d;\n}\n\n.intersection-layout {\n  position: relative;\n  display: grid;\n  grid-template-columns: 1fr 120px 1fr;\n  grid-template-rows: 1fr 120px 1fr;\n  gap: 20px;\n  margin: 30px 0;\n  min-height: 300px;\n}\n\n.direction {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n}\n\n.direction.north {\n  grid-column: 2;\n  grid-row: 1;\n}\n\n.direction.south {\n  grid-column: 2;\n  grid-row: 3;\n}\n\n.direction.west {\n  grid-column: 1;\n  grid-row: 2;\n}\n\n.direction.east {\n  grid-column: 3;\n  grid-row: 2;\n}\n\n.direction-label {\n  font-weight: 600;\n  font-size: 14px;\n  color: #495057;\n  background: #e9ecef;\n  padding: 4px 12px;\n  border-radius: 20px;\n}\n\n.intersection-center {\n  grid-column: 2;\n  grid-row: 2;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 12px;\n}\n\n.intersection-icon {\n  font-size: 48px;\n  color: #6c757d;\n}\n\n.phase-info {\n  margin: 20px 0;\n}\n\n.control-buttons {\n  margin: 20px 0;\n  text-align: center;\n}\n\n.operation-log {\n  margin-top: 20px;\n}\n\n.log-container {\n  max-height: 200px;\n  overflow-y: auto;\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 10px;\n}\n\n.log-item {\n  display: flex;\n  gap: 10px;\n  padding: 4px 0;\n  border-bottom: 1px solid #e9ecef;\n  font-size: 12px;\n}\n\n.log-item:last-child {\n  border-bottom: none;\n}\n\n.log-time {\n  color: #6c757d;\n  min-width: 80px;\n}\n\n.log-message {\n  flex: 1;\n}\n\n.log-item.success .log-message {\n  color: #28a745;\n}\n\n.log-item.warning .log-message {\n  color: #ffc107;\n}\n\n.log-item.error .log-message {\n  color: #dc3545;\n}\n\n.log-item.info .log-message {\n  color: #17a2b8;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .intersection-layout {\n    grid-template-columns: 80px 100px 80px;\n    grid-template-rows: 80px 100px 80px;\n    gap: 10px;\n    min-height: 260px;\n  }\n\n  .intersection-icon {\n    font-size: 32px;\n  }\n\n  .direction-label {\n    font-size: 12px;\n    padding: 2px 8px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;EAG1BA,KAAK,EAAC;AAAa;;EAChBA,KAAK,EAAC;AAAO;;EAIdA,KAAK,EAAC;AAAc;;EAaxBA,KAAK,EAAC;AAAmB;;EAtBpCC,GAAA;EA2BcD,KAAK,EAAC;;;EAMTA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAiB;;EAWvBA,KAAK,EAAC;AAAgB;;EAWtBA,KAAK,EAAC;AAAqB;;EAK3BA,KAAK,EAAC;AAAgB;;EAWtBA,KAAK,EAAC;AAAiB;;EAYzBA,KAAK,EAAC;AAAY;;EArF7BC,GAAA;EAuGWD,KAAK,EAAC;;;EA8BNA,KAAK,EAAC;AAAe;;EAGfA,KAAK,EAAC;AAAe;;EAOhBA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAa;;;;;;;;;;;;;;;;;;;uBA/IvCE,mBAAA,CAsJM,OAtJNC,UAsJM,GArJJC,YAAA,CAoJUC,kBAAA;IApJDL,KAAK,EAAC;EAAiB;IACnBM,MAAM,EAAAC,QAAA,CACf,MAcM,CAdNC,mBAAA,CAcM,OAdNC,UAcM,GAbJD,mBAAA,CAGO,QAHPE,UAGO,GAFLN,YAAA,CAA8BO,kBAAA;MAN1CC,OAAA,EAAAL,QAAA,CAMqB,MAAW,CAAXH,YAAA,CAAWS,kBAAA,E;MANhCC,CAAA;kCAAAC,gBAAA,CAM0C,UAEhC,G,GACAP,mBAAA,CAQM,OARNQ,UAQM,GAPJZ,YAAA,CAMEa,oBAAA;MAhBdC,UAAA,EAWuBC,MAAA,CAAAC,YAAY;MAXnC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAWuBH,MAAA,CAAAC,YAAY,GAAAE,MAAA;MACpBC,QAAQ,GAAGJ,MAAA,CAAAK,WAAW;MACvB,aAAW,EAAC,IAAI;MAChB,eAAa,EAAC,IAAI;MACjBC,QAAM,EAAEN,MAAA,CAAAO;;IAfvBd,OAAA,EAAAL,QAAA,CAsBM,MAQM,CARNC,mBAAA,CAQM,OARNmB,UAQM,GAPJvB,YAAA,CAGSwB,iBAAA;MAHAC,IAAI,EAAEV,MAAA,CAAAK,WAAW;MAAyBM,IAAI,EAAC;;MAvBhElB,OAAA,EAAAL,QAAA,CAwBU,MAAiC,CAAjCH,YAAA,CAAiCO,kBAAA;QAxB3CC,OAAA,EAAAL,QAAA,CAwBmB,MAAc,CAAdH,YAAA,CAAc2B,qBAAA,E;QAxBjCjB,CAAA;UAAAC,gBAAA,CAwB2C,GACjC,GAAAiB,gBAAA,CAAGb,MAAA,CAAAK,WAAW,iC;MAzBxBV,CAAA;iCA2BwCK,MAAA,CAAAc,cAAc,I,cAA9C/B,mBAAA,CAEO,QAFPgC,UAEO,EAFyC,SACxC,GAAAF,gBAAA,CAAGb,MAAA,CAAAgB,UAAU,CAAChB,MAAA,CAAAc,cAAc,qBA5B5CG,mBAAA,e,GAiCM5B,mBAAA,CAiDM,OAjDN6B,UAiDM,GAhDJD,mBAAA,SAAY,EACZ5B,mBAAA,CAQM,OARN8B,UAQM,G,0BAPJ9B,mBAAA,CAAoC;MAA/BR,KAAK,EAAC;IAAiB,GAAC,GAAC,sBAC9BI,YAAA,CAKEmC,gCAAA;MAJCC,KAAK,EAAErB,MAAA,CAAAsB,aAAa,CAACC,KAAK;MAC1B,WAAS,EAAEvB,MAAA,CAAAC,YAAY;MACxBuB,SAAS,EAAC,OAAO;MAChBC,cAAa,EAAEzB,MAAA,CAAA0B;yEAIpBT,mBAAA,SAAY,EACZ5B,mBAAA,CAQM,OARNsC,UAQM,G,0BAPJtC,mBAAA,CAAoC;MAA/BR,KAAK,EAAC;IAAiB,GAAC,GAAC,sBAC9BI,YAAA,CAKEmC,gCAAA;MAJCC,KAAK,EAAErB,MAAA,CAAAsB,aAAa,CAACM,IAAI;MACzB,WAAS,EAAE5B,MAAA,CAAAC,YAAY;MACxBuB,SAAS,EAAC,MAAM;MACfC,cAAa,EAAEzB,MAAA,CAAA0B;yEAIpBT,mBAAA,UAAa,EACb5B,mBAAA,CAEM,OAFNwC,WAEM,GADJ5C,YAAA,CAAqDO,kBAAA;MAA5CX,KAAK,EAAC;IAAmB;MA1D5CY,OAAA,EAAAL,QAAA,CA0D6C,MAAQ,CAARH,YAAA,CAAQ6C,eAAA,E;MA1DrDnC,CAAA;UA6DQsB,mBAAA,SAAY,EACZ5B,mBAAA,CAQM,OARN0C,WAQM,G,0BAPJ1C,mBAAA,CAAoC;MAA/BR,KAAK,EAAC;IAAiB,GAAC,GAAC,sBAC9BI,YAAA,CAKEmC,gCAAA;MAJCC,KAAK,EAAErB,MAAA,CAAAsB,aAAa,CAACU,IAAI;MACzB,WAAS,EAAEhC,MAAA,CAAAC,YAAY;MACxBuB,SAAS,EAAC,MAAM;MACfC,cAAa,EAAEzB,MAAA,CAAA0B;yEAIpBT,mBAAA,SAAY,EACZ5B,mBAAA,CAQM,OARN4C,WAQM,G,0BAPJ5C,mBAAA,CAAoC;MAA/BR,KAAK,EAAC;IAAiB,GAAC,GAAC,sBAC9BI,YAAA,CAKEmC,gCAAA;MAJCC,KAAK,EAAErB,MAAA,CAAAsB,aAAa,CAACY,KAAK;MAC1B,WAAS,EAAElC,MAAA,CAAAC,YAAY;MACxBuB,SAAS,EAAC,OAAO;MAChBC,cAAa,EAAEzB,MAAA,CAAA0B;2EAMtBrC,mBAAA,CAeM,OAfN8C,WAeM,GAdJlD,YAAA,CAakBmD,0BAAA;MAbDC,KAAK,EAAC,QAAQ;MAAEC,MAAM,EAAE,CAAC;MAAE3B,IAAI,EAAC,OAAO;MAAC4B,MAAM,EAAN;;MAtFjE9C,OAAA,EAAAL,QAAA,CAuFU,MAEuB,CAFvBH,YAAA,CAEuBuD,+BAAA;QAFDC,KAAK,EAAC;MAAM;QAvF5ChD,OAAA,EAAAL,QAAA,CAwFY,MAAiC,CAxF7CQ,gBAAA,CAAAiB,gBAAA,CAwFeb,MAAA,CAAA0C,YAAY,CAACC,OAAO,wB;QAxFnChD,CAAA;UA0FUV,YAAA,CAEuBuD,+BAAA;QAFDC,KAAK,EAAC;MAAM;QA1F5ChD,OAAA,EAAAL,QAAA,CA2FY,MAAqC,CA3FjDQ,gBAAA,CAAAiB,gBAAA,CA2Feb,MAAA,CAAA0C,YAAY,CAACE,aAAa,SAAQ,IACvC,gB;QA5FVjD,CAAA;UA6FUV,YAAA,CAEuBuD,+BAAA;QAFDC,KAAK,EAAC;MAAM;QA7F5ChD,OAAA,EAAAL,QAAA,CA8FY,MAAqC,CA9FjDQ,gBAAA,CAAAiB,gBAAA,CA8Feb,MAAA,CAAA0C,YAAY,CAACG,WAAW,wB;QA9FvClD,CAAA;UAgGUV,YAAA,CAEuBuD,+BAAA;QAFDC,KAAK,EAAC;MAAM;QAhG5ChD,OAAA,EAAAL,QAAA,CAiGY,MAAmC,CAjG/CQ,gBAAA,CAAAiB,gBAAA,CAiGeb,MAAA,CAAA0C,YAAY,CAACI,SAAS,wB;QAjGrCnD,CAAA;;MAAAA,CAAA;UAuGyCK,MAAA,CAAAC,YAAY,I,cAA/ClB,mBAAA,CA2BM,OA3BNgE,WA2BM,GA1BJ9D,YAAA,CAyBkB+D,0BAAA;MAjI1BvD,OAAA,EAAAL,QAAA,CAyGU,MAOY,CAPZH,YAAA,CAOYgE,oBAAA;QANVvC,IAAI,EAAC,SAAS;QACbN,QAAQ,GAAGJ,MAAA,CAAAK,WAAW;QACtB6C,OAAK,EAAElD,MAAA,CAAAmD;;QA5GpB1D,OAAA,EAAAL,QAAA,CA8GY,MAA4B,CAA5BH,YAAA,CAA4BO,kBAAA;UA9GxCC,OAAA,EAAAL,QAAA,CA8GqB,MAAS,CAATH,YAAA,CAASmE,gBAAA,E;UA9G9BzD,CAAA;sCAAAC,gBAAA,CA8GwC,QAE9B,G;QAhHVD,CAAA;kDAiHUV,YAAA,CAOYgE,oBAAA;QANVvC,IAAI,EAAC,SAAS;QACbN,QAAQ,GAAGJ,MAAA,CAAAK,WAAW;QACtB6C,OAAK,EAAElD,MAAA,CAAAqD;;QApHpB5D,OAAA,EAAAL,QAAA,CAsHY,MAA8B,CAA9BH,YAAA,CAA8BO,kBAAA;UAtH1CC,OAAA,EAAAL,QAAA,CAsHqB,MAAW,CAAXH,YAAA,CAAWqE,kBAAA,E;UAtHhC3D,CAAA;sCAAAC,gBAAA,CAsH0C,QAEhC,G;QAxHVD,CAAA;kDAyHUV,YAAA,CAOYgE,oBAAA;QANVvC,IAAI,EAAC,QAAQ;QACZN,QAAQ,GAAGJ,MAAA,CAAAK,WAAW;QACtB6C,OAAK,EAAElD,MAAA,CAAAuD;;QA5HpB9D,OAAA,EAAAL,QAAA,CA8HY,MAA8B,CAA9BH,YAAA,CAA8BO,kBAAA;UA9H1CC,OAAA,EAAAL,QAAA,CA8HqB,MAAW,CAAXH,YAAA,CAAWuE,kBAAA,E;UA9HhC7D,CAAA;sCAAAC,gBAAA,CA8H0C,QAEhC,G;QAhIVD,CAAA;;MAAAA,CAAA;YAAAsB,mBAAA,gBAqIM5B,mBAAA,CAgBM,OAhBNoE,WAgBM,GAfJxE,YAAA,CAccyE,sBAAA;MApJtB3D,UAAA,EAsI8BC,MAAA,CAAA2D,YAAY;MAtI1C,uBAAAzD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAsI8BH,MAAA,CAAA2D,YAAY,GAAAxD,MAAA;;MAtI1CV,OAAA,EAAAL,QAAA,CAuIU,MAYmB,CAZnBH,YAAA,CAYmB2E,2BAAA;QAZDvB,KAAK,EAAC,MAAM;QAACwB,IAAI,EAAC;;QAvI9CpE,OAAA,EAAAL,QAAA,CAwIY,MAUM,CAVNC,mBAAA,CAUM,OAVNyE,WAUM,I,kBATJ/E,mBAAA,CAQMgF,SAAA,QAjJpBC,WAAA,CA0IuChE,MAAA,CAAAiE,aAAa,EA1IpD,CA0IwBC,GAAG,EAAEC,KAAK;+BADpBpF,mBAAA,CAQM;YANHD,GAAG,EAAEqF,KAAK;YACXtF,KAAK,EA5IrBuF,eAAA,EA4IsB,UAAU,EACRF,GAAG,CAACxD,IAAI;cAEhBrB,mBAAA,CAA6D,QAA7DgF,WAA6D,EAAAxD,gBAAA,CAAnCb,MAAA,CAAAgB,UAAU,CAACkD,GAAG,CAACI,SAAS,mBAClDjF,mBAAA,CAAkD,QAAlDkF,WAAkD,EAAA1D,gBAAA,CAArBqD,GAAG,CAACM,OAAO,iB;;QAhJxD7E,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}