{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"traffic-light-display\"\n};\nconst _hoisted_2 = {\n  class: \"traffic-light\"\n};\nconst _hoisted_3 = {\n  class: \"status-indicator\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"manual-hint\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_text = _resolveComponent(\"el-text\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 红灯 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"light red\", {\n      active: $props.state.red,\n      clickable: $props.isManual\n    }]),\n    onClick: _cache[0] || (_cache[0] = $event => $setup.onLightClick('red'))\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n    class: \"light-inner\"\n  }, null, -1 /* HOISTED */)]), 2 /* CLASS */), _createCommentVNode(\" 黄灯 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"light yellow\", {\n      active: $props.state.yellow,\n      clickable: $props.isManual\n    }]),\n    onClick: _cache[1] || (_cache[1] = $event => $setup.onLightClick('yellow'))\n  }, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n    class: \"light-inner\"\n  }, null, -1 /* HOISTED */)]), 2 /* CLASS */), _createCommentVNode(\" 绿灯 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"light green\", {\n      active: $props.state.green,\n      clickable: $props.isManual\n    }]),\n    onClick: _cache[2] || (_cache[2] = $event => $setup.onLightClick('green'))\n  }, _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n    class: \"light-inner\"\n  }, null, -1 /* HOISTED */)]), 2 /* CLASS */)]), _createCommentVNode(\" 状态指示 \"), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_tag, {\n    type: $setup.getStatusType(),\n    size: \"small\",\n    effect: \"plain\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText()), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 手动控制提示 \"), $props.isManual ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_text, {\n    size: \"small\",\n    type: \"info\"\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\" 点击切换状态 \")])),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_normalizeClass", "active", "$props", "state", "red", "clickable", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "_cache", "$event", "$setup", "onLightClick", "yellow", "green", "_hoisted_3", "_createVNode", "_component_el_tag", "type", "getStatusType", "size", "effect", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "getStatusText", "_", "_hoisted_4", "_component_el_text"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\TrafficLightDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"traffic-light-display\">\n    <div class=\"traffic-light\">\n      <!-- 红灯 -->\n      <div \n        class=\"light red\"\n        :class=\"{ active: state.red, clickable: isManual }\"\n        @click=\"onLightClick('red')\"\n      >\n        <div class=\"light-inner\"></div>\n      </div>\n      \n      <!-- 黄灯 -->\n      <div \n        class=\"light yellow\"\n        :class=\"{ active: state.yellow, clickable: isManual }\"\n        @click=\"onLightClick('yellow')\"\n      >\n        <div class=\"light-inner\"></div>\n      </div>\n      \n      <!-- 绿灯 -->\n      <div \n        class=\"light green\"\n        :class=\"{ active: state.green, clickable: isManual }\"\n        @click=\"onLightClick('green')\"\n      >\n        <div class=\"light-inner\"></div>\n      </div>\n    </div>\n    \n    <!-- 状态指示 -->\n    <div class=\"status-indicator\">\n      <el-tag \n        :type=\"getStatusType()\" \n        size=\"small\"\n        effect=\"plain\"\n      >\n        {{ getStatusText() }}\n      </el-tag>\n    </div>\n    \n    <!-- 手动控制提示 -->\n    <div v-if=\"isManual\" class=\"manual-hint\">\n      <el-text size=\"small\" type=\"info\">\n        点击切换状态\n      </el-text>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed } from 'vue'\nimport { ElTag, ElText } from 'element-plus'\n\nexport default {\n  name: 'TrafficLightDisplay',\n  components: {\n    ElTag,\n    ElText\n  },\n  props: {\n    state: {\n      type: Object,\n      required: true,\n      default: () => ({\n        red: false,\n        yellow: false,\n        green: false\n      })\n    },\n    isManual: {\n      type: Boolean,\n      default: false\n    },\n    direction: {\n      type: String,\n      required: true\n    }\n  },\n  emits: ['manual-change'],\n  setup(props, { emit }) {\n    \n    const getStatusType = () => {\n      if (props.state.red) return 'danger'\n      if (props.state.yellow) return 'warning'\n      if (props.state.green) return 'success'\n      return 'info'\n    }\n    \n    const getStatusText = () => {\n      if (props.state.red) return '停止'\n      if (props.state.yellow) return '警告'\n      if (props.state.green) return '通行'\n      return '未知'\n    }\n    \n    const onLightClick = (color) => {\n      if (!props.isManual) return\n      \n      // 创建新的状态对象\n      const newState = {\n        red: false,\n        yellow: false,\n        green: false\n      }\n      \n      // 设置选中的颜色为true\n      newState[color] = true\n      \n      // 发射变更事件\n      emit('manual-change', props.direction, newState)\n    }\n    \n    return {\n      getStatusType,\n      getStatusText,\n      onLightClick\n    }\n  }\n}\n</script>\n\n<style scoped>\n.traffic-light-display {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.traffic-light {\n  display: flex;\n  flex-direction: column;\n  background: #2c3e50;\n  border-radius: 20px;\n  padding: 8px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  gap: 6px;\n}\n\n.light {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  transition: all 0.3s ease;\n  border: 2px solid #34495e;\n}\n\n.light-inner {\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.light.red .light-inner {\n  background: #2c3e50;\n}\n\n.light.red.active .light-inner {\n  background: #e74c3c;\n  box-shadow: 0 0 20px #e74c3c, inset 0 0 10px #c0392b;\n}\n\n.light.yellow .light-inner {\n  background: #2c3e50;\n}\n\n.light.yellow.active .light-inner {\n  background: #f39c12;\n  box-shadow: 0 0 20px #f39c12, inset 0 0 10px #e67e22;\n}\n\n.light.green .light-inner {\n  background: #2c3e50;\n}\n\n.light.green.active .light-inner {\n  background: #27ae60;\n  box-shadow: 0 0 20px #27ae60, inset 0 0 10px #229954;\n}\n\n.light.clickable {\n  cursor: pointer;\n}\n\n.light.clickable:hover {\n  transform: scale(1.05);\n  border-color: #3498db;\n}\n\n.status-indicator {\n  margin-top: 4px;\n}\n\n.manual-hint {\n  margin-top: 4px;\n  text-align: center;\n}\n\n/* 动画效果 */\n.light.active {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.02);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .light {\n    width: 32px;\n    height: 32px;\n  }\n  \n  .light-inner {\n    width: 24px;\n    height: 24px;\n  }\n  \n  .traffic-light {\n    padding: 6px;\n    gap: 4px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAe;;EA8BrBA,KAAK,EAAC;AAAkB;;EAhCjCC,GAAA;EA2CyBD,KAAK,EAAC;;;;;uBA1C7BE,mBAAA,CA+CM,OA/CNC,UA+CM,GA9CJC,mBAAA,CA2BM,OA3BNC,UA2BM,GA1BJC,mBAAA,QAAW,EACXF,mBAAA,CAMM;IALJJ,KAAK,EALbO,eAAA,EAKc,WAAW;MAAAC,MAAA,EACCC,MAAA,CAAAC,KAAK,CAACC,GAAG;MAAAC,SAAA,EAAaH,MAAA,CAAAI;IAAQ;IAC/CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,YAAY;gCAEpBd,mBAAA,CAA+B;IAA1BJ,KAAK,EAAC;EAAa,2B,mBAG1BM,mBAAA,QAAW,EACXF,mBAAA,CAMM;IALJJ,KAAK,EAdbO,eAAA,EAcc,cAAc;MAAAC,MAAA,EACFC,MAAA,CAAAC,KAAK,CAACS,MAAM;MAAAP,SAAA,EAAaH,MAAA,CAAAI;IAAQ;IAClDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,YAAY;gCAEpBd,mBAAA,CAA+B;IAA1BJ,KAAK,EAAC;EAAa,2B,mBAG1BM,mBAAA,QAAW,EACXF,mBAAA,CAMM;IALJJ,KAAK,EAvBbO,eAAA,EAuBc,aAAa;MAAAC,MAAA,EACDC,MAAA,CAAAC,KAAK,CAACU,KAAK;MAAAR,SAAA,EAAaH,MAAA,CAAAI;IAAQ;IACjDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,YAAY;gCAEpBd,mBAAA,CAA+B;IAA1BJ,KAAK,EAAC;EAAa,2B,qBAI5BM,mBAAA,UAAa,EACbF,mBAAA,CAQM,OARNiB,UAQM,GAPJC,YAAA,CAMSC,iBAAA;IALNC,IAAI,EAAEP,MAAA,CAAAQ,aAAa;IACpBC,IAAI,EAAC,OAAO;IACZC,MAAM,EAAC;;IApCfC,OAAA,EAAAC,QAAA,CAsCQ,MAAqB,CAtC7BC,gBAAA,CAAAC,gBAAA,CAsCWd,MAAA,CAAAe,aAAa,mB;IAtCxBC,CAAA;iCA0CI3B,mBAAA,YAAe,EACJG,MAAA,CAAAI,QAAQ,I,cAAnBX,mBAAA,CAIM,OAJNgC,UAIM,GAHJZ,YAAA,CAEUa,kBAAA;IAFDT,IAAI,EAAC,OAAO;IAACF,IAAI,EAAC;;IA5CjCI,OAAA,EAAAC,QAAA,CA4CwC,MAElCd,MAAA,QAAAA,MAAA,OA9CNe,gBAAA,CA4CwC,UAElC,E;IA9CNG,CAAA;UAAA3B,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}