{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { createRouter, createWebHistory } from 'vue-router';\n\n// 改进懒加载方式，添加错误处理和预加载\nconst lazyLoad = viewPath => {\n  return () => import(`@/views/${viewPath}.vue`).catch(err => {\n    // 如果组件加载失败，返回一个简单的错误组件\n    return import('@/views/Error.vue').catch(() => {\n      // 如果连错误页面都无法加载，返回一个内联的错误组件\n      return {\n        template: `\n          <div style=\"padding: 20px; text-align: center;\">\n            <h2>组件加载失败</h2>\n            <p>无法加载 ${viewPath} 组件，请刷新页面或联系管理员</p>\n            <button @click=\"$router.go(-1)\" style=\"padding: 8px 16px;\">返回上一页</button>\n          </div>\n        `\n      };\n    });\n  });\n};\n\n// 路由组件懒加载\nconst LoginView = lazyLoad('Login');\nconst RegisterView = lazyLoad('Register');\nconst HomeView = lazyLoad('Home');\nconst UploadView = lazyLoad('Upload');\nconst ResultView = lazyLoad('Result');\nconst HistoryView = lazyLoad('History');\nconst UserManagementView = lazyLoad('UserManagement');\nconst PersonalInfoView = lazyLoad('PersonalInfo');\nconst SettingsView = lazyLoad('Settings');\nconst VideoUploadView = lazyLoad('VideoUpload');\nconst VideoHistoryView = lazyLoad('VideoHistory');\nconst VideoResultView = lazyLoad('VideoResult');\nconst FourWayResultView = lazyLoad('FourWayResult');\nconst FourWayAnalysisConsoleView = lazyLoad('FourWayAnalysisConsole');\nconst TrafficAnalysisTestView = lazyLoad('TrafficAnalysisTest');\nconst routes = [{\n  path: '/',\n  redirect: '/login'\n}, {\n  path: '/login',\n  name: 'Login',\n  component: LoginView,\n  meta: {\n    requiresAuth: false\n  }\n}, {\n  path: '/register',\n  name: 'Register',\n  component: RegisterView,\n  meta: {\n    requiresAuth: false\n  }\n}, {\n  path: '/home',\n  name: 'Home',\n  component: HomeView,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/upload',\n  name: 'Upload',\n  component: UploadView,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/result/:id',\n  name: 'Result',\n  component: ResultView,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/history',\n  name: 'History',\n  component: HistoryView,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/user/profile',\n  name: 'PersonalInfo',\n  component: PersonalInfoView,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/admin/users',\n  name: 'UserManagement',\n  component: UserManagementView,\n  meta: {\n    requiresAuth: true,\n    requiresAdmin: true\n  }\n}, {\n  path: '/settings',\n  name: 'Settings',\n  component: SettingsView,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/video-upload',\n  name: 'VideoUpload',\n  component: VideoUploadView,\n  meta: {\n    requiresAuth: true,\n    title: '视频上传分析'\n  }\n}, {\n  path: '/video-history',\n  name: 'VideoHistory',\n  component: VideoHistoryView,\n  meta: {\n    requiresAuth: true,\n    title: '视频分析历史'\n  }\n}, {\n  path: '/four-way-history',\n  name: 'FourWayHistory',\n  component: lazyLoad('FourWayHistory'),\n  meta: {\n    requiresAuth: true,\n    title: '四方向分析历史'\n  }\n}, {\n  path: '/video-status/:taskId',\n  name: 'VideoStatus',\n  component: VideoUploadView,\n  meta: {\n    requiresAuth: true,\n    title: '视频处理状态'\n  }\n}, {\n  path: '/video-result/:id',\n  name: 'VideoResult',\n  component: VideoResultView,\n  meta: {\n    requiresAuth: true,\n    title: '视频分析结果'\n  }\n}, {\n  path: '/video-result/smart/:id',\n  name: 'VideoResultSmart',\n  component: VideoResultView,\n  meta: {\n    requiresAuth: true,\n    title: '视频分析结果(智能ID)'\n  }\n}, {\n  path: '/video-result/id/:id',\n  name: 'VideoResultId',\n  component: VideoResultView,\n  meta: {\n    requiresAuth: true,\n    title: '视频分析结果'\n  },\n  props: route => ({\n    idType: 'auto'\n  })\n}, {\n  path: '/video-result/uuid/:id',\n  name: 'VideoResultUuid',\n  component: VideoResultView,\n  meta: {\n    requiresAuth: true,\n    title: '视频分析结果(UUID格式)'\n  },\n  props: route => ({\n    idType: 'uuid'\n  })\n}, {\n  path: '/video-result/mongodb/:id',\n  name: 'VideoResultMongodb',\n  component: VideoResultView,\n  meta: {\n    requiresAuth: true,\n    title: '视频分析结果(MongoDB格式)'\n  },\n  props: route => ({\n    idType: 'mongodb'\n  })\n},\n// ==================== SUMO仿真分析路由 ====================\n{\n  path: '/simulation',\n  name: 'SimulationDashboard',\n  component: () => import('@/views/simulation/SimulationDashboard.vue'),\n  meta: {\n    requiresAuth: true,\n    title: '仿真控制台'\n  }\n}, {\n  path: '/simulation/:id',\n  name: 'SimulationDetail',\n  component: () => import('@/views/simulation/SimulationDetail.vue'),\n  meta: {\n    requiresAuth: true,\n    title: '仿真详情'\n  },\n  props: true\n},\n// ==================== 四方向智能交通分析路由 ====================\n{\n  path: '/four-way-console',\n  name: 'FourWayAnalysisConsole',\n  component: FourWayAnalysisConsoleView,\n  meta: {\n    requiresAuth: true,\n    title: '四方向分析控制台'\n  }\n}, {\n  path: '/four-way-test',\n  name: 'FourWayTestPage',\n  component: () => import('@/views/FourWayTestPage.vue'),\n  meta: {\n    requiresAuth: true,\n    title: '四方向测试页面'\n  }\n}, {\n  path: '/traffic-analysis-test',\n  name: 'TrafficAnalysisTest',\n  component: TrafficAnalysisTestView,\n  meta: {\n    requiresAuth: true,\n    title: '智能交通状态面板测试'\n  }\n}, {\n  path: '/four-way-result/:taskId',\n  name: 'FourWayResult',\n  component: FourWayResultView,\n  meta: {\n    requiresAuth: true,\n    title: '四方向分析结果'\n  },\n  props: true\n}, {\n  path: '/four-way-preview/:taskId',\n  name: 'FourWayPreview',\n  component: FourWayResultView,\n  meta: {\n    requiresAuth: true,\n    title: '四方向实时预览'\n  },\n  props: route => ({\n    taskId: route.params.taskId,\n    mode: 'preview'\n  })\n}, {\n  path: '/four-way-dashboard/:taskId',\n  name: 'FourWayDashboard',\n  component: FourWayResultView,\n  meta: {\n    requiresAuth: true,\n    title: '四方向智能分析仪表板'\n  },\n  props: route => ({\n    taskId: route.params.taskId,\n    mode: 'dashboard'\n  })\n}, {\n  path: '/four-way-report/:taskId',\n  name: 'FourWayReport',\n  component: FourWayResultView,\n  meta: {\n    requiresAuth: true,\n    title: '四方向分析报告'\n  },\n  props: route => ({\n    taskId: route.params.taskId,\n    mode: 'report'\n  })\n},\n// 添加错误路由\n{\n  path: '/:pathMatch(.*)*',\n  name: 'NotFound',\n  component: lazyLoad('Error'),\n  meta: {\n    requiresAuth: false\n  }\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\n\n// 导航守卫\nrouter.beforeEach((to, from, next) => {\n  const token = localStorage.getItem('auth_token');\n  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);\n  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin);\n  const isAuthPage = to.path === '/login' || to.path === '/register';\n\n  // 验证令牌格式是否有效（简单验证非空且长度合适）\n  const isValidToken = token && token.length > 20;\n  if (requiresAuth && !isValidToken) {\n    // 令牌无效时清除所有认证信息\n    if (token) {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      localStorage.removeItem('user_id');\n    }\n\n    // 需要认证但未登录或令牌无效，重定向到登录页\n    next({\n      path: '/login',\n      query: {\n        redirect: to.fullPath\n      }\n    });\n  } else if (requiresAdmin) {\n    // 检查管理员权限\n    const userStr = localStorage.getItem('user');\n    if (userStr) {\n      const user = JSON.parse(userStr);\n      const role = (user.role || '').toLowerCase();\n      const isAdmin = role === 'admin' || role === 'administrator';\n      if (!isAdmin) {\n        // 不是管理员，重定向到首页\n\n        next('/home');\n        return;\n      }\n    } else {\n      // 用户信息不存在，重定向到登录页\n      next({\n        path: '/login',\n        query: {\n          redirect: to.fullPath\n        }\n      });\n      return;\n    }\n    next(); // 有管理员权限，正常访问\n  } else if (isAuthPage && isValidToken) {\n    // 已登录用户访问登录/注册页，重定向到首页\n    next('/home');\n  } else {\n    // 其他情况正常导航\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "lazyLoad", "viewPath", "catch", "err", "template", "<PERSON><PERSON><PERSON>ie<PERSON>", "RegisterView", "HomeView", "UploadView", "ResultView", "HistoryView", "UserManagementView", "PersonalInfoView", "SettingsView", "VideoUploadView", "VideoHistoryView", "VideoResultView", "FourWayResultView", "FourWayAnalysisConsoleView", "TrafficAnalysisTestView", "routes", "path", "redirect", "name", "component", "meta", "requiresAuth", "requiresAdmin", "title", "props", "route", "idType", "taskId", "params", "mode", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "token", "localStorage", "getItem", "matched", "some", "record", "isAuthPage", "isValidToken", "length", "removeItem", "query", "fullPath", "userStr", "user", "JSON", "parse", "role", "toLowerCase", "isAdmin"], "sources": ["D:/code/nvm/trafficsystem/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router';\n\n// 改进懒加载方式，添加错误处理和预加载\nconst lazyLoad = (viewPath) => {\n  return () => import(`@/views/${viewPath}.vue`).catch(err => {\n\n    // 如果组件加载失败，返回一个简单的错误组件\n    return import('@/views/Error.vue').catch(() => {\n      // 如果连错误页面都无法加载，返回一个内联的错误组件\n      return {\n        template: `\n          <div style=\"padding: 20px; text-align: center;\">\n            <h2>组件加载失败</h2>\n            <p>无法加载 ${viewPath} 组件，请刷新页面或联系管理员</p>\n            <button @click=\"$router.go(-1)\" style=\"padding: 8px 16px;\">返回上一页</button>\n          </div>\n        `\n      };\n    });\n  });\n};\n\n// 路由组件懒加载\nconst LoginView = lazyLoad('Login');\nconst RegisterView = lazyLoad('Register');\nconst HomeView = lazyLoad('Home');\nconst UploadView = lazyLoad('Upload');\nconst ResultView = lazyLoad('Result');\nconst HistoryView = lazyLoad('History');\nconst UserManagementView = lazyLoad('UserManagement');\nconst PersonalInfoView = lazyLoad('PersonalInfo');\nconst SettingsView = lazyLoad('Settings');\nconst VideoUploadView = lazyLoad('VideoUpload');\nconst VideoHistoryView = lazyLoad('VideoHistory');\nconst VideoResultView = lazyLoad('VideoResult');\n\nconst FourWayResultView = lazyLoad('FourWayResult');\nconst FourWayAnalysisConsoleView = lazyLoad('FourWayAnalysisConsole');\nconst TrafficAnalysisTestView = lazyLoad('TrafficAnalysisTest');\n\nconst routes = [\n  {\n    path: '/',\n    redirect: '/login'\n  },\n  {\n    path: '/login',\n    name: 'Login',\n    component: LoginView,\n    meta: { requiresAuth: false }\n  },\n  {\n    path: '/register',\n    name: 'Register',\n    component: RegisterView,\n    meta: { requiresAuth: false }\n  },\n  {\n    path: '/home',\n    name: 'Home',\n    component: HomeView,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/upload',\n    name: 'Upload',\n    component: UploadView,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/result/:id',\n    name: 'Result',\n    component: ResultView,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/history',\n    name: 'History',\n    component: HistoryView,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/user/profile',\n    name: 'PersonalInfo',\n    component: PersonalInfoView,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/admin/users',\n    name: 'UserManagement',\n    component: UserManagementView,\n    meta: { \n      requiresAuth: true,\n      requiresAdmin: true\n    }\n  },\n  {\n    path: '/settings',\n    name: 'Settings',\n    component: SettingsView,\n    meta: { requiresAuth: true }\n  },\n  {\n    path: '/video-upload',\n    name: 'VideoUpload',\n    component: VideoUploadView,\n    meta: {\n      requiresAuth: true,\n      title: '视频上传分析'\n    }\n  },\n  {\n    path: '/video-history',\n    name: 'VideoHistory',\n    component: VideoHistoryView,\n    meta: {\n      requiresAuth: true,\n      title: '视频分析历史'\n    }\n  },\n  {\n    path: '/four-way-history',\n    name: 'FourWayHistory',\n    component: lazyLoad('FourWayHistory'),\n    meta: {\n      requiresAuth: true,\n      title: '四方向分析历史'\n    }\n  },\n  {\n    path: '/video-status/:taskId',\n    name: 'VideoStatus',\n    component: VideoUploadView,\n    meta: {\n      requiresAuth: true,\n      title: '视频处理状态'\n    }\n  },\n  {\n    path: '/video-result/:id',\n    name: 'VideoResult',\n    component: VideoResultView,\n    meta: {\n      requiresAuth: true,\n      title: '视频分析结果'\n    }\n  },\n  {\n    path: '/video-result/smart/:id',\n    name: 'VideoResultSmart',\n    component: VideoResultView,\n    meta: {\n      requiresAuth: true,\n      title: '视频分析结果(智能ID)'\n    }\n  },\n  {\n    path: '/video-result/id/:id',\n    name: 'VideoResultId',\n    component: VideoResultView,\n    meta: {\n      requiresAuth: true,\n      title: '视频分析结果'\n    },\n    props: (route) => ({\n      idType: 'auto'\n    })\n  },\n  {\n    path: '/video-result/uuid/:id',\n    name: 'VideoResultUuid',\n    component: VideoResultView,\n    meta: {\n      requiresAuth: true,\n      title: '视频分析结果(UUID格式)'\n    },\n    props: (route) => ({\n      idType: 'uuid'\n    })\n  },\n  {\n    path: '/video-result/mongodb/:id',\n    name: 'VideoResultMongodb',\n    component: VideoResultView,\n    meta: {\n      requiresAuth: true,\n      title: '视频分析结果(MongoDB格式)'\n    },\n    props: (route) => ({\n      idType: 'mongodb'\n    })\n  },\n\n  // ==================== SUMO仿真分析路由 ====================\n  {\n    path: '/simulation',\n    name: 'SimulationDashboard',\n    component: () => import('@/views/simulation/SimulationDashboard.vue'),\n    meta: {\n      requiresAuth: true,\n      title: '仿真控制台'\n    }\n  },\n  {\n    path: '/simulation/:id',\n    name: 'SimulationDetail',\n    component: () => import('@/views/simulation/SimulationDetail.vue'),\n    meta: {\n      requiresAuth: true,\n      title: '仿真详情'\n    },\n    props: true\n  },\n\n  // ==================== 四方向智能交通分析路由 ====================\n  {\n    path: '/four-way-console',\n    name: 'FourWayAnalysisConsole',\n    component: FourWayAnalysisConsoleView,\n    meta: {\n      requiresAuth: true,\n      title: '四方向分析控制台'\n    }\n  },\n\n\n  {\n    path: '/four-way-test',\n    name: 'FourWayTestPage',\n    component: () => import('@/views/FourWayTestPage.vue'),\n    meta: {\n      requiresAuth: true,\n      title: '四方向测试页面'\n    }\n  },\n  {\n    path: '/traffic-analysis-test',\n    name: 'TrafficAnalysisTest',\n    component: TrafficAnalysisTestView,\n    meta: {\n      requiresAuth: true,\n      title: '智能交通状态面板测试'\n    }\n  },\n  {\n    path: '/four-way-result/:taskId',\n    name: 'FourWayResult',\n    component: FourWayResultView,\n    meta: {\n      requiresAuth: true,\n      title: '四方向分析结果'\n    },\n    props: true\n  },\n  {\n    path: '/four-way-preview/:taskId',\n    name: 'FourWayPreview',\n    component: FourWayResultView,\n    meta: {\n      requiresAuth: true,\n      title: '四方向实时预览'\n    },\n    props: (route) => ({\n      taskId: route.params.taskId,\n      mode: 'preview'\n    })\n  },\n  {\n    path: '/four-way-dashboard/:taskId',\n    name: 'FourWayDashboard',\n    component: FourWayResultView,\n    meta: {\n      requiresAuth: true,\n      title: '四方向智能分析仪表板'\n    },\n    props: (route) => ({\n      taskId: route.params.taskId,\n      mode: 'dashboard'\n    })\n  },\n  {\n    path: '/four-way-report/:taskId',\n    name: 'FourWayReport',\n    component: FourWayResultView,\n    meta: {\n      requiresAuth: true,\n      title: '四方向分析报告'\n    },\n    props: (route) => ({\n      taskId: route.params.taskId,\n      mode: 'report'\n    })\n  },\n\n  // 添加错误路由\n  {\n    path: '/:pathMatch(.*)*',\n    name: 'NotFound',\n    component: lazyLoad('Error'),\n    meta: { requiresAuth: false }\n  }\n];\n\n\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\n\n// 导航守卫\nrouter.beforeEach((to, from, next) => {\n  const token = localStorage.getItem('auth_token');\n  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);\n  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin);\n  const isAuthPage = to.path === '/login' || to.path === '/register';\n\n  // 验证令牌格式是否有效（简单验证非空且长度合适）\n  const isValidToken = token && token.length > 20;\n\n  if (requiresAuth && !isValidToken) {\n    // 令牌无效时清除所有认证信息\n    if (token) {\n\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user');\n      localStorage.removeItem('user_id');\n    }\n    \n    // 需要认证但未登录或令牌无效，重定向到登录页\n    next({ path: '/login', query: { redirect: to.fullPath } });\n  } else if (requiresAdmin) {\n    // 检查管理员权限\n    const userStr = localStorage.getItem('user');\n    if (userStr) {\n      const user = JSON.parse(userStr);\n      const role = (user.role || '').toLowerCase();\n      const isAdmin = role === 'admin' || role === 'administrator';\n      \n      if (!isAdmin) {\n        // 不是管理员，重定向到首页\n\n        next('/home');\n        return;\n      }\n    } else {\n      // 用户信息不存在，重定向到登录页\n      next({ path: '/login', query: { redirect: to.fullPath } });\n      return;\n    }\n    \n    next(); // 有管理员权限，正常访问\n  } else if (isAuthPage && isValidToken) {\n    // 已登录用户访问登录/注册页，重定向到首页\n    next('/home');\n  } else {\n    // 其他情况正常导航\n    next();\n  }\n});\n\n\n\nexport default router; "], "mappings": ";;AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;;AAE3D;AACA,MAAMC,QAAQ,GAAIC,QAAQ,IAAK;EAC7B,OAAO,MAAM,MAAM,CAAC,WAAWA,QAAQ,MAAM,CAAC,CAACC,KAAK,CAACC,GAAG,IAAI;IAE1D;IACA,OAAO,MAAM,CAAC,mBAAmB,CAAC,CAACD,KAAK,CAAC,MAAM;MAC7C;MACA,OAAO;QACLE,QAAQ,EAAE;AAClB;AACA;AACA,sBAAsBH,QAAQ;AAC9B;AACA;AACA;MACM,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMI,SAAS,GAAGL,QAAQ,CAAC,OAAO,CAAC;AACnC,MAAMM,YAAY,GAAGN,QAAQ,CAAC,UAAU,CAAC;AACzC,MAAMO,QAAQ,GAAGP,QAAQ,CAAC,MAAM,CAAC;AACjC,MAAMQ,UAAU,GAAGR,QAAQ,CAAC,QAAQ,CAAC;AACrC,MAAMS,UAAU,GAAGT,QAAQ,CAAC,QAAQ,CAAC;AACrC,MAAMU,WAAW,GAAGV,QAAQ,CAAC,SAAS,CAAC;AACvC,MAAMW,kBAAkB,GAAGX,QAAQ,CAAC,gBAAgB,CAAC;AACrD,MAAMY,gBAAgB,GAAGZ,QAAQ,CAAC,cAAc,CAAC;AACjD,MAAMa,YAAY,GAAGb,QAAQ,CAAC,UAAU,CAAC;AACzC,MAAMc,eAAe,GAAGd,QAAQ,CAAC,aAAa,CAAC;AAC/C,MAAMe,gBAAgB,GAAGf,QAAQ,CAAC,cAAc,CAAC;AACjD,MAAMgB,eAAe,GAAGhB,QAAQ,CAAC,aAAa,CAAC;AAE/C,MAAMiB,iBAAiB,GAAGjB,QAAQ,CAAC,eAAe,CAAC;AACnD,MAAMkB,0BAA0B,GAAGlB,QAAQ,CAAC,wBAAwB,CAAC;AACrE,MAAMmB,uBAAuB,GAAGnB,QAAQ,CAAC,qBAAqB,CAAC;AAE/D,MAAMoB,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEnB,SAAS;EACpBoB,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAM;AAC9B,CAAC,EACD;EACEL,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAElB,YAAY;EACvBmB,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAM;AAC9B,CAAC,EACD;EACEL,IAAI,EAAE,OAAO;EACbE,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEjB,QAAQ;EACnBkB,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,SAAS;EACfE,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEhB,UAAU;EACrBiB,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,aAAa;EACnBE,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEf,UAAU;EACrBgB,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEd,WAAW;EACtBe,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,eAAe;EACrBE,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEZ,gBAAgB;EAC3Ba,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,cAAc;EACpBE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEb,kBAAkB;EAC7Bc,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE;EACjB;AACF,CAAC,EACD;EACEN,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEX,YAAY;EACvBY,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,eAAe;EACrBE,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEV,eAAe;EAC1BW,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,gBAAgB;EACtBE,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAET,gBAAgB;EAC3BU,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,mBAAmB;EACzBE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAExB,QAAQ,CAAC,gBAAgB,CAAC;EACrCyB,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,uBAAuB;EAC7BE,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEV,eAAe;EAC1BW,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,mBAAmB;EACzBE,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAER,eAAe;EAC1BS,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,yBAAyB;EAC/BE,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAER,eAAe;EAC1BS,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,sBAAsB;EAC5BE,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAER,eAAe;EAC1BS,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAGC,KAAK,KAAM;IACjBC,MAAM,EAAE;EACV,CAAC;AACH,CAAC,EACD;EACEV,IAAI,EAAE,wBAAwB;EAC9BE,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAER,eAAe;EAC1BS,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAGC,KAAK,KAAM;IACjBC,MAAM,EAAE;EACV,CAAC;AACH,CAAC,EACD;EACEV,IAAI,EAAE,2BAA2B;EACjCE,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAER,eAAe;EAC1BS,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAGC,KAAK,KAAM;IACjBC,MAAM,EAAE;EACV,CAAC;AACH,CAAC;AAED;AACA;EACEV,IAAI,EAAE,aAAa;EACnBE,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4CAA4C,CAAC;EACrEC,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,iBAAiB;EACvBE,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC;EAClEC,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAE;AACT,CAAC;AAED;AACA;EACER,IAAI,EAAE,mBAAmB;EACzBE,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,EAAEN,0BAA0B;EACrCO,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EAGD;EACEP,IAAI,EAAE,gBAAgB;EACtBE,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;EACtDC,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,wBAAwB;EAC9BE,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEL,uBAAuB;EAClCM,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEP,IAAI,EAAE,0BAA0B;EAChCE,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEP,iBAAiB;EAC5BQ,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAE;AACT,CAAC,EACD;EACER,IAAI,EAAE,2BAA2B;EACjCE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEP,iBAAiB;EAC5BQ,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAGC,KAAK,KAAM;IACjBE,MAAM,EAAEF,KAAK,CAACG,MAAM,CAACD,MAAM;IAC3BE,IAAI,EAAE;EACR,CAAC;AACH,CAAC,EACD;EACEb,IAAI,EAAE,6BAA6B;EACnCE,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEP,iBAAiB;EAC5BQ,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAGC,KAAK,KAAM;IACjBE,MAAM,EAAEF,KAAK,CAACG,MAAM,CAACD,MAAM;IAC3BE,IAAI,EAAE;EACR,CAAC;AACH,CAAC,EACD;EACEb,IAAI,EAAE,0BAA0B;EAChCE,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEP,iBAAiB;EAC5BQ,IAAI,EAAE;IACJC,YAAY,EAAE,IAAI;IAClBE,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAGC,KAAK,KAAM;IACjBE,MAAM,EAAEF,KAAK,CAACG,MAAM,CAACD,MAAM;IAC3BE,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AAED;AACA;EACEb,IAAI,EAAE,kBAAkB;EACxBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAExB,QAAQ,CAAC,OAAO,CAAC;EAC5ByB,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAM;AAC9B,CAAC,CACF;AAID,MAAMS,MAAM,GAAGrC,YAAY,CAAC;EAC1BsC,OAAO,EAAErC,gBAAgB,CAACsC,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CnB;AACF,CAAC,CAAC;;AAEF;AACAe,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,MAAMpB,YAAY,GAAGe,EAAE,CAACM,OAAO,CAACC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACxB,IAAI,CAACC,YAAY,CAAC;EACxE,MAAMC,aAAa,GAAGc,EAAE,CAACM,OAAO,CAACC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACxB,IAAI,CAACE,aAAa,CAAC;EAC1E,MAAMuB,UAAU,GAAGT,EAAE,CAACpB,IAAI,KAAK,QAAQ,IAAIoB,EAAE,CAACpB,IAAI,KAAK,WAAW;;EAElE;EACA,MAAM8B,YAAY,GAAGP,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,EAAE;EAE/C,IAAI1B,YAAY,IAAI,CAACyB,YAAY,EAAE;IACjC;IACA,IAAIP,KAAK,EAAE;MAETC,YAAY,CAACQ,UAAU,CAAC,YAAY,CAAC;MACrCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;MAC/BR,YAAY,CAACQ,UAAU,CAAC,SAAS,CAAC;IACpC;;IAEA;IACAV,IAAI,CAAC;MAAEtB,IAAI,EAAE,QAAQ;MAAEiC,KAAK,EAAE;QAAEhC,QAAQ,EAAEmB,EAAE,CAACc;MAAS;IAAE,CAAC,CAAC;EAC5D,CAAC,MAAM,IAAI5B,aAAa,EAAE;IACxB;IACA,MAAM6B,OAAO,GAAGX,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC5C,IAAIU,OAAO,EAAE;MACX,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;MAChC,MAAMI,IAAI,GAAG,CAACH,IAAI,CAACG,IAAI,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;MAC5C,MAAMC,OAAO,GAAGF,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,eAAe;MAE5D,IAAI,CAACE,OAAO,EAAE;QACZ;;QAEAnB,IAAI,CAAC,OAAO,CAAC;QACb;MACF;IACF,CAAC,MAAM;MACL;MACAA,IAAI,CAAC;QAAEtB,IAAI,EAAE,QAAQ;QAAEiC,KAAK,EAAE;UAAEhC,QAAQ,EAAEmB,EAAE,CAACc;QAAS;MAAE,CAAC,CAAC;MAC1D;IACF;IAEAZ,IAAI,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,MAAM,IAAIO,UAAU,IAAIC,YAAY,EAAE;IACrC;IACAR,IAAI,CAAC,OAAO,CAAC;EACf,CAAC,MAAM;IACL;IACAA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAIF,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}