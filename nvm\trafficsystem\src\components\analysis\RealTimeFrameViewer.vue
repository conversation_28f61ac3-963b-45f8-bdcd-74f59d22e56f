<template>
  <div class="real-time-frame-viewer">
    <!-- 视频显示区域 -->
    <div class="video-container" :class="{ 'has-frame': hasCurrentFrame }">
      <div v-if="!hasCurrentFrame" class="no-frame-placeholder">
        <el-icon size="48"><VideoCamera /></el-icon>
        <p>{{ getWaitingMessage() }}</p>
        <el-progress 
          v-if="progress > 0"
          :percentage="progress" 
          :stroke-width="6"
          :show-text="false"
        />
      </div>
      
      <div v-else class="frame-display">
        <img 
          :src="currentFrame.imageData" 
          :alt="`${directionName}方向检测结果`"
          class="frame-image"
          @load="onImageLoad"
          @error="onImageError"
        />
        
        <!-- 检测信息覆盖层 -->
        <div class="detection-overlay">
          <div class="detection-info">
            <span class="vehicle-count">{{ currentFrame.detectionCount || 0 }}</span>
            <span class="vehicle-label">当前帧</span>
          </div>

          <div class="frame-info">
            <span class="frame-number">
              {{ currentFrame.frameNumber || 0 }} / {{ currentFrame.totalFrames || 0 }}
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 方向标识 -->
    <div class="direction-header">
      <div class="direction-icon" :class="direction">
        {{ directionName.substring(0, 1) }}
      </div>
      <div class="direction-info">
        <h4 class="direction-name">{{ directionName }}</h4>
        <p class="direction-status" :class="statusClass">{{ statusText }}</p>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-panel" v-if="hasCurrentFrame && currentFrame.vehicleTypes">
      <h5>车辆类型统计</h5>
      <div class="vehicle-types">
        <div 
          v-for="(count, type) in currentFrame.vehicleTypes" 
          :key="type"
          class="vehicle-type-item"
        >
          <span class="type-name">{{ getVehicleTypeName(type) }}</span>
          <span class="type-count">{{ count }}</span>
        </div>
      </div>
    </div>
    
    <!-- 控制按钮 -->
    <div class="controls" v-if="showControls">
      <el-button 
        size="small" 
        @click="togglePause"
        :icon="isPaused ? VideoPlay : VideoPause"
      >
        {{ isPaused ? '播放' : '暂停' }}
      </el-button>
      
      <el-button 
        size="small" 
        @click="saveFrame"
        :disabled="!hasCurrentFrame"
        icon="Download"
      >
        保存帧
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  VideoCamera, VideoPlay, VideoPause, Download 
} from '@element-plus/icons-vue'

export default {
  name: 'RealTimeFrameViewer',
  components: {
    VideoCamera, VideoPlay, VideoPause, Download
  },
  props: {
    direction: {
      type: String,
      required: true,
      validator: (value) => ['east', 'south', 'west', 'north'].includes(value)
    },
    frameData: {
      type: Object,
      default: null
    },
    status: {
      type: String,
      default: 'waiting',
      validator: (value) => ['waiting', 'processing', 'completed', 'error'].includes(value)
    },
    progress: {
      type: Number,
      default: 0
    },
    showControls: {
      type: Boolean,
      default: true
    }
  },
  emits: ['pause-toggled', 'frame-saved'],
  setup(props, { emit }) {
    // 响应式数据
    const currentFrame = ref(null)
    const isPaused = ref(false)
    const imageLoading = ref(false)
    
    // 计算属性
    const directionName = computed(() => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向',
        north: '北向'
      }
      return names[props.direction] || props.direction
    })
    
    const hasCurrentFrame = computed(() => {
      return currentFrame.value && currentFrame.value.imageData
    })
    
    const statusClass = computed(() => {
      return {
        'status-waiting': props.status === 'waiting',
        'status-processing': props.status === 'processing',
        'status-completed': props.status === 'completed',
        'status-error': props.status === 'error'
      }
    })
    
    const statusText = computed(() => {
      const statusMap = {
        waiting: '等待处理',
        processing: '正在处理',
        completed: '处理完成',
        error: '处理失败'
      }
      return statusMap[props.status] || '未知状态'
    })
    
    // 监听帧数据变化
    watch(() => props.frameData, (newFrameData) => {
      if (newFrameData && !isPaused.value) {
        updateFrame(newFrameData)
      }
    }, { immediate: true })
    
    // 方法
    const updateFrame = (frameData) => {
      if (!frameData) return
      
      // 验证帧数据
      if (!frameData.imageData || !frameData.imageData.startsWith('data:image/')) {
        console.warn('无效的帧数据:', frameData)
        return
      }
      
      currentFrame.value = {
        ...frameData,
        timestamp: new Date().toISOString()
      }
    }
    
    const getVehicleTypeName = (type) => {
      const typeNames = {
        car: '小汽车',
        truck: '卡车',
        bus: '公交车',
        motorcycle: '摩托车'
      }
      return typeNames[type] || type
    }
    
    const togglePause = () => {
      isPaused.value = !isPaused.value
      emit('pause-toggled', {
        direction: props.direction,
        isPaused: isPaused.value
      })
    }
    
    const saveFrame = () => {
      if (!hasCurrentFrame.value) {
        ElMessage.warning('没有可保存的帧数据')
        return
      }
      
      try {
        // 创建下载链接
        const link = document.createElement('a')
        link.href = currentFrame.value.imageData
        link.download = `${props.direction}_frame_${currentFrame.value.frameNumber || 'unknown'}_${Date.now()}.jpg`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        ElMessage.success('帧图像已保存')
        emit('frame-saved', {
          direction: props.direction,
          frameData: currentFrame.value
        })
        
      } catch (error) {
        console.error('保存帧失败:', error)
        ElMessage.error('保存帧失败')
      }
    }
    
    const onImageLoad = () => {
      imageLoading.value = false
    }
    
    const onImageError = () => {
      imageLoading.value = false
      console.error('帧图像加载失败')
    }

    const getWaitingMessage = () => {
      if (props.status === 'processing') {
        return '正在处理视频，请稍候...'
      } else if (props.status === 'waiting') {
        return '等待视频帧数据...'
      } else if (props.status === 'error') {
        return '视频处理失败'
      }
      return '等待视频帧数据...'
    }

    return {
      currentFrame,
      isPaused,
      imageLoading,
      directionName,
      hasCurrentFrame,
      statusClass,
      statusText,
      getVehicleTypeName,
      getWaitingMessage,
      togglePause,
      saveFrame,
      onImageLoad,
      onImageError
    }
  }
}
</script>

<style scoped>
.real-time-frame-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.video-container {
  flex: 1;
  position: relative;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.no-frame-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  text-align: center;
  padding: 20px;
}

.no-frame-placeholder p {
  margin: 12px 0 16px 0;
  font-size: 14px;
}

.frame-display {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.frame-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.detection-overlay {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  pointer-events: none;
}

.detection-info {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.vehicle-count {
  font-size: 18px;
  font-weight: bold;
}

.vehicle-label {
  font-size: 12px;
}

.frame-info {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
}

.direction-header {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.direction-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
  margin-right: 12px;
}

.direction-icon.east {
  background: #409eff;
}

.direction-icon.south {
  background: #67c23a;
}

.direction-icon.west {
  background: #e6a23c;
}

.direction-icon.north {
  background: #f56c6c;
}

.direction-info {
  flex: 1;
}

.direction-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #2c3e50;
}

.direction-status {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.status-waiting {
  color: #909399;
}

.status-processing {
  color: #e6a23c;
}

.status-completed {
  color: #67c23a;
}

.status-error {
  color: #f56c6c;
}

.stats-panel {
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.stats-panel h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.vehicle-types {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vehicle-type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.type-name {
  color: #606266;
}

.type-count {
  color: #2c3e50;
  font-weight: 500;
}

.controls {
  display: flex;
  gap: 8px;
  padding: 12px;
  background: #fafafa;
}

.controls .el-button {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detection-overlay {
    flex-direction: column;
    gap: 8px;
  }
  
  .direction-header {
    padding: 8px;
  }
  
  .direction-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .direction-name {
    font-size: 14px;
  }
}
</style>
