<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="traffic_analysis@localhost">
  <database-model serializer="dbm" dbms="MONGO" family-id="MONGO" format-version="4.52">
    <root id="1"/>
    <schema id="2" parent="1" name="admin"/>
    <schema id="3" parent="1" name="articledb"/>
    <schema id="4" parent="1" name="config"/>
    <schema id="5" parent="1" name="local"/>
    <schema id="6" parent="1" name="school"/>
    <schema id="7" parent="1" name="traffic_analysis">
      <Current>1</Current>
      <LastIntrospectionLocalTimestamp>2025-06-03.14:50:39</LastIntrospectionLocalTimestamp>
    </schema>
    <schema id="8" parent="1" name="traffic_system"/>
    <table id="9" parent="7" name="analysis_history"/>
    <table id="10" parent="7" name="analysis_results"/>
    <table id="11" parent="7" name="analysis_videos"/>
    <table id="12" parent="7" name="system_logs"/>
    <table id="13" parent="7" name="traffic_data"/>
    <table id="14" parent="7" name="users"/>
    <column id="15" parent="9" name="_id">
      <DasType>ObjectId|12s</DasType>
      <Position>1</Position>
    </column>
    <column id="16" parent="9" name="_class">
      <DasType>String|12s</DasType>
      <Position>2</Position>
    </column>
    <column id="17" parent="9" name="analysisEndTime">
      <DasType>ISODate|91s</DasType>
      <Position>3</Position>
    </column>
    <column id="18" parent="9" name="analysisResult">
      <DasType>Object|4999544s</DasType>
      <Position>4</Position>
    </column>
    <column id="19" parent="9" name="analysisResult.analyst">
      <DasType>String|12s</DasType>
      <Position>5</Position>
    </column>
    <column id="20" parent="9" name="analysisResult.detections">
      <DasType>list|4999545s</DasType>
      <Position>6</Position>
    </column>
    <column id="21" parent="9" name="analysisResult.detections._class">
      <DasType>String|12s</DasType>
      <Position>7</Position>
    </column>
    <column id="22" parent="9" name="analysisResult.detections.bbox">
      <DasType>Array|2003s</DasType>
      <Position>8</Position>
    </column>
    <column id="23" parent="9" name="analysisResult.detections.classId">
      <DasType>Int32|4s</DasType>
      <Position>9</Position>
    </column>
    <column id="24" parent="9" name="analysisResult.detections.className">
      <DasType>String|12s</DasType>
      <Position>10</Position>
    </column>
    <column id="25" parent="9" name="analysisResult.detections.confidence">
      <DasType>Double|8s</DasType>
      <Position>11</Position>
    </column>
    <column id="26" parent="9" name="analysisResult.id">
      <DasType>String|12s</DasType>
      <Position>12</Position>
    </column>
    <column id="27" parent="9" name="analysisResult.imageUrl">
      <DasType>String|12s</DasType>
      <Position>13</Position>
    </column>
    <column id="28" parent="9" name="analysisResult.inferenceTime">
      <DasType>Double|8s</DasType>
      <Position>14</Position>
    </column>
    <column id="29" parent="9" name="analysisResult.status">
      <DasType>String|12s</DasType>
      <Position>15</Position>
    </column>
    <column id="30" parent="9" name="analysisResult.timestamp">
      <DasType>ISODate|91s</DasType>
      <Position>16</Position>
    </column>
    <column id="31" parent="9" name="analysisResult.vehicleCount">
      <DasType>Int32|4s</DasType>
      <Position>17</Position>
    </column>
    <column id="32" parent="9" name="analysisResult.vehicleTypeStats">
      <DasType>Object|4999544s</DasType>
      <Position>18</Position>
    </column>
    <column id="33" parent="9" name="analysisResult.vehicleTypeStats.stop sign">
      <DasType>Int32|4s</DasType>
      <Position>19</Position>
    </column>
    <column id="34" parent="9" name="analysisResult.vehicleTypeStats.交通灯">
      <DasType>Int32|4s</DasType>
      <Position>20</Position>
    </column>
    <column id="35" parent="9" name="analysisResult.vehicleTypeStats.人">
      <DasType>Int32|4s</DasType>
      <Position>21</Position>
    </column>
    <column id="36" parent="9" name="analysisResult.vehicleTypeStats.公交车">
      <DasType>Int32|4s</DasType>
      <Position>22</Position>
    </column>
    <column id="37" parent="9" name="analysisResult.vehicleTypeStats.卡车">
      <DasType>Int32|4s</DasType>
      <Position>23</Position>
    </column>
    <column id="38" parent="9" name="analysisResult.vehicleTypeStats.摩托车">
      <DasType>Int32|4s</DasType>
      <Position>24</Position>
    </column>
    <column id="39" parent="9" name="analysisResult.vehicleTypeStats.汽车">
      <DasType>Int32|4s</DasType>
      <Position>25</Position>
    </column>
    <column id="40" parent="9" name="analysisStartTime">
      <DasType>ISODate|91s</DasType>
      <Position>26</Position>
    </column>
    <column id="41" parent="9" name="analyst">
      <DasType>String|12s</DasType>
      <Position>27</Position>
    </column>
    <column id="42" parent="9" name="createTime">
      <DasType>ISODate|91s</DasType>
      <Position>28</Position>
    </column>
    <column id="43" parent="9" name="fileName">
      <DasType>String|12s</DasType>
      <Position>29</Position>
    </column>
    <column id="44" parent="9" name="imageUrl">
      <DasType>String|12s</DasType>
      <Position>30</Position>
    </column>
    <column id="45" parent="9" name="inferenceTime">
      <DasType>Double|8s</DasType>
      <Position>31</Position>
    </column>
    <column id="46" parent="9" name="requestSource">
      <DasType>String|12s</DasType>
      <Position>32</Position>
    </column>
    <column id="47" parent="9" name="resultId">
      <DasType>String|12s</DasType>
      <Position>33</Position>
    </column>
    <column id="48" parent="9" name="status">
      <DasType>String|12s</DasType>
      <Position>34</Position>
    </column>
    <column id="49" parent="9" name="timestamp">
      <DasType>String|12s</DasType>
      <Position>35</Position>
    </column>
    <column id="50" parent="9" name="updateTime">
      <DasType>ISODate|91s</DasType>
      <Position>36</Position>
    </column>
    <column id="51" parent="9" name="userId">
      <DasType>String|12s</DasType>
      <Position>37</Position>
    </column>
    <column id="52" parent="9" name="username">
      <DasType>String|12s</DasType>
      <Position>38</Position>
    </column>
    <column id="53" parent="9" name="vehicleCount">
      <DasType>Int32|4s</DasType>
      <Position>39</Position>
    </column>
    <column id="54" parent="9" name="vehicleTypeStats">
      <DasType>Object|4999544s</DasType>
      <Position>40</Position>
    </column>
    <column id="55" parent="9" name="vehicleTypeStats.stop sign">
      <DasType>Int32|4s</DasType>
      <Position>41</Position>
    </column>
    <column id="56" parent="9" name="vehicleTypeStats.交通灯">
      <DasType>Int32|4s</DasType>
      <Position>42</Position>
    </column>
    <column id="57" parent="9" name="vehicleTypeStats.人">
      <DasType>Int32|4s</DasType>
      <Position>43</Position>
    </column>
    <column id="58" parent="9" name="vehicleTypeStats.公交车">
      <DasType>Int32|4s</DasType>
      <Position>44</Position>
    </column>
    <column id="59" parent="9" name="vehicleTypeStats.卡车">
      <DasType>Int32|4s</DasType>
      <Position>45</Position>
    </column>
    <column id="60" parent="9" name="vehicleTypeStats.摩托车">
      <DasType>Int32|4s</DasType>
      <Position>46</Position>
    </column>
    <column id="61" parent="9" name="vehicleTypeStats.汽车">
      <DasType>Int32|4s</DasType>
      <Position>47</Position>
    </column>
    <column id="62" parent="10" name="_id">
      <DasType>ObjectId|12s</DasType>
      <Position>1</Position>
    </column>
    <column id="63" parent="10" name="_class">
      <DasType>String|12s</DasType>
      <Position>2</Position>
    </column>
    <column id="64" parent="10" name="analysisStartTime">
      <DasType>ISODate|91s</DasType>
      <Position>3</Position>
    </column>
    <column id="65" parent="10" name="analyst">
      <DasType>String|12s</DasType>
      <Position>4</Position>
    </column>
    <column id="66" parent="10" name="detections">
      <DasType>list|4999545s</DasType>
      <Position>5</Position>
    </column>
    <column id="67" parent="10" name="detections.bbox">
      <DasType>Array|2003s</DasType>
      <Position>6</Position>
    </column>
    <column id="68" parent="10" name="detections.classId">
      <DasType>Int32|4s</DasType>
      <Position>7</Position>
    </column>
    <column id="69" parent="10" name="detections.className">
      <DasType>String|12s</DasType>
      <Position>8</Position>
    </column>
    <column id="70" parent="10" name="detections.confidence">
      <DasType>Double|8s</DasType>
      <Position>9</Position>
    </column>
    <column id="71" parent="10" name="imageUrl">
      <DasType>String|12s</DasType>
      <Position>10</Position>
    </column>
    <column id="72" parent="10" name="inferenceTime">
      <DasType>Double|8s</DasType>
      <Position>11</Position>
    </column>
    <column id="73" parent="10" name="requestSource">
      <DasType>String|12s</DasType>
      <Position>12</Position>
    </column>
    <column id="74" parent="10" name="resultImageBase64">
      <DasType>String|12s</DasType>
      <Position>13</Position>
    </column>
    <column id="75" parent="10" name="status">
      <DasType>String|12s</DasType>
      <Position>14</Position>
    </column>
    <column id="76" parent="10" name="timestamp">
      <DasType>ISODate|91s</DasType>
      <Position>15</Position>
    </column>
    <column id="77" parent="10" name="userId">
      <DasType>String|12s</DasType>
      <Position>16</Position>
    </column>
    <column id="78" parent="10" name="username">
      <DasType>String|12s</DasType>
      <Position>17</Position>
    </column>
    <column id="79" parent="10" name="vehicleCount">
      <DasType>Int32|4s</DasType>
      <Position>18</Position>
    </column>
    <column id="80" parent="10" name="vehicleTypeStats">
      <DasType>Object|4999544s</DasType>
      <Position>19</Position>
    </column>
    <column id="81" parent="10" name="vehicleTypeStats.stop sign">
      <DasType>Int32|4s</DasType>
      <Position>20</Position>
    </column>
    <column id="82" parent="10" name="vehicleTypeStats.交通灯">
      <DasType>Int32|4s</DasType>
      <Position>21</Position>
    </column>
    <column id="83" parent="10" name="vehicleTypeStats.人">
      <DasType>Int32|4s</DasType>
      <Position>22</Position>
    </column>
    <column id="84" parent="10" name="vehicleTypeStats.公交车">
      <DasType>Int32|4s</DasType>
      <Position>23</Position>
    </column>
    <column id="85" parent="10" name="vehicleTypeStats.卡车">
      <DasType>Int32|4s</DasType>
      <Position>24</Position>
    </column>
    <column id="86" parent="10" name="vehicleTypeStats.摩托车">
      <DasType>Int32|4s</DasType>
      <Position>25</Position>
    </column>
    <column id="87" parent="10" name="vehicleTypeStats.汽车">
      <DasType>Int32|4s</DasType>
      <Position>26</Position>
    </column>
    <column id="88" parent="11" name="_id">
      <DasType>ObjectId|12s</DasType>
      <Position>1</Position>
    </column>
    <column id="89" parent="11" name="_class">
      <DasType>String|12s</DasType>
      <Position>2</Position>
    </column>
    <column id="90" parent="11" name="completed_at">
      <DasType>ISODate|91s</DasType>
      <Position>3</Position>
    </column>
    <column id="91" parent="11" name="created_at">
      <DasType>ISODate|91s</DasType>
      <Position>4</Position>
    </column>
    <column id="92" parent="11" name="direction">
      <DasType>String|12s</DasType>
      <Position>5</Position>
    </column>
    <column id="93" parent="11" name="message">
      <DasType>String|12s</DasType>
      <Position>6</Position>
    </column>
    <column id="94" parent="11" name="mode">
      <DasType>String|12s</DasType>
      <Position>7</Position>
    </column>
    <column id="95" parent="11" name="processing_time">
      <DasType>Double|8s</DasType>
      <Position>8</Position>
    </column>
    <column id="96" parent="11" name="progress">
      <DasType>Int32|4s</DasType>
      <Position>9</Position>
    </column>
    <column id="97" parent="11" name="result_id">
      <DasType>String|12s</DasType>
      <Position>10</Position>
    </column>
    <column id="98" parent="11" name="result_path">
      <DasType>String|12s</DasType>
      <Position>11</Position>
    </column>
    <column id="99" parent="11" name="role">
      <DasType>String|12s</DasType>
      <Position>12</Position>
    </column>
    <column id="100" parent="11" name="status">
      <DasType>String|12s</DasType>
      <Position>13</Position>
    </column>
    <column id="101" parent="11" name="task_id">
      <DasType>String|12s</DasType>
      <Position>14</Position>
    </column>
    <column id="102" parent="11" name="thumbnail_url">
      <DasType>String|12s</DasType>
      <Position>15</Position>
    </column>
    <column id="103" parent="11" name="updated_at">
      <DasType>ISODate|91s</DasType>
      <Position>16</Position>
    </column>
    <column id="104" parent="11" name="user_id">
      <DasType>String|12s</DasType>
      <Position>17</Position>
    </column>
    <column id="105" parent="11" name="username">
      <DasType>String|12s</DasType>
      <Position>18</Position>
    </column>
    <column id="106" parent="11" name="vehicle_count">
      <DasType>Int32|4s</DasType>
      <Position>19</Position>
    </column>
    <column id="107" parent="11" name="vehicle_type_stats">
      <DasType>Object|4999544s</DasType>
      <Position>20</Position>
    </column>
    <column id="108" parent="11" name="vehicle_type_stats.bicycle">
      <DasType>Int32|4s</DasType>
      <Position>21</Position>
    </column>
    <column id="109" parent="11" name="vehicle_type_stats.bus">
      <DasType>Int32|4s</DasType>
      <Position>22</Position>
    </column>
    <column id="110" parent="11" name="vehicle_type_stats.car">
      <DasType>Int32|4s</DasType>
      <Position>23</Position>
    </column>
    <column id="111" parent="11" name="vehicle_type_stats.motorcycle">
      <DasType>Int32|4s</DasType>
      <Position>24</Position>
    </column>
    <column id="112" parent="11" name="vehicle_type_stats.truck">
      <DasType>Int32|4s</DasType>
      <Position>25</Position>
    </column>
    <column id="113" parent="11" name="video_filename">
      <DasType>String|12s</DasType>
      <Position>26</Position>
    </column>
    <column id="114" parent="11" name="video_path">
      <DasType>String|12s</DasType>
      <Position>27</Position>
    </column>
    <column id="115" parent="12" name="_id">
      <DasType>ObjectId|12s</DasType>
      <Position>1</Position>
    </column>
    <column id="116" parent="12" name="_class">
      <DasType>String|12s</DasType>
      <Position>2</Position>
    </column>
    <column id="117" parent="12" name="level">
      <DasType>String|12s</DasType>
      <Position>3</Position>
    </column>
    <column id="118" parent="12" name="message">
      <DasType>String|12s</DasType>
      <Position>4</Position>
    </column>
    <column id="119" parent="12" name="operationType">
      <DasType>String|12s</DasType>
      <Position>5</Position>
    </column>
    <column id="120" parent="12" name="operatorId">
      <DasType>String|12s</DasType>
      <Position>6</Position>
    </column>
    <column id="121" parent="12" name="operatorName">
      <DasType>String|12s</DasType>
      <Position>7</Position>
    </column>
    <column id="122" parent="12" name="serviceName">
      <DasType>String|12s</DasType>
      <Position>8</Position>
    </column>
    <column id="123" parent="12" name="serviceOperation">
      <DasType>String|12s</DasType>
      <Position>9</Position>
    </column>
    <column id="124" parent="12" name="success">
      <DasType>Boolean|12s</DasType>
      <Position>10</Position>
    </column>
    <column id="125" parent="12" name="timestamp">
      <DasType>ISODate|91s</DasType>
      <Position>11</Position>
    </column>
    <column id="126" parent="14" name="_id">
      <DasType>String|12s</DasType>
      <Position>1</Position>
    </column>
    <column id="127" parent="14" name="_class">
      <DasType>String|12s</DasType>
      <Position>2</Position>
    </column>
    <column id="128" parent="14" name="active">
      <DasType>Boolean|12s</DasType>
      <Position>3</Position>
    </column>
    <column id="129" parent="14" name="createdAt">
      <DasType>ISODate|91s</DasType>
      <Position>4</Position>
    </column>
    <column id="130" parent="14" name="email">
      <DasType>String|12s</DasType>
      <Position>5</Position>
    </column>
    <column id="131" parent="14" name="lastLoginAt">
      <DasType>ISODate|91s</DasType>
      <Position>6</Position>
    </column>
    <column id="132" parent="14" name="password">
      <DasType>String|12s</DasType>
      <Position>7</Position>
    </column>
    <column id="133" parent="14" name="phone">
      <DasType>String|12s</DasType>
      <Position>8</Position>
    </column>
    <column id="134" parent="14" name="role">
      <DasType>String|12s</DasType>
      <Position>9</Position>
    </column>
    <column id="135" parent="14" name="username">
      <DasType>String|12s</DasType>
      <Position>10</Position>
    </column>
  </database-model>
</dataSource>