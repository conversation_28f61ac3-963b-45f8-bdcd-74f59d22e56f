{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, onMounted, nextTick } from 'vue';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'OptimizationComparison',\n  props: {\n    optimizationResult: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['refresh-requested'],\n  setup(props, {\n    emit\n  }) {\n    // 图表引用\n    const performanceChartRef = ref(null);\n    const improvementChartRef = ref(null);\n    let performanceChart = null;\n    let improvementChart = null;\n\n    // 改善指标\n    const delayReduction = ref(15.6);\n    const throughputIncrease = ref(12.3);\n    const speedImprovement = ref(8.9);\n    const fuelSaving = ref(11.2);\n\n    // 对比数据\n    const comparisonData = reactive([{\n      metric: '平均延误时间',\n      unit: '秒',\n      before: 45.2,\n      after: 38.1,\n      improvement: 15.7,\n      description: '车辆在交叉口的平均等待时间'\n    }, {\n      metric: '通行能力',\n      unit: '辆/小时',\n      before: 1850,\n      after: 2078,\n      improvement: 12.3,\n      description: '单位时间内通过交叉口的车辆数量'\n    }, {\n      metric: '平均行程速度',\n      unit: 'km/h',\n      before: 28.5,\n      after: 31.0,\n      improvement: 8.8,\n      description: '车辆通过交叉口区域的平均速度'\n    }, {\n      metric: '燃油消耗',\n      unit: '升/百公里',\n      before: 8.9,\n      after: 7.9,\n      improvement: 11.2,\n      description: '车辆通过交叉口的燃油消耗'\n    }, {\n      metric: '排队长度',\n      unit: '米',\n      before: 85.3,\n      after: 68.7,\n      improvement: 19.5,\n      description: '各方向最大排队长度'\n    }]);\n\n    // 信号配时对比数据\n    const signalTimingData = reactive({\n      beforeCycle: 120,\n      afterCycle: 105,\n      cycleImprovement: 12.5,\n      before: [{\n        phase: '相位1',\n        duration: 30,\n        state: 'GrGr',\n        description: '东西绿灯'\n      }, {\n        phase: '相位2',\n        duration: 5,\n        state: 'yryr',\n        description: '东西黄灯'\n      }, {\n        phase: '相位3',\n        duration: 30,\n        state: 'rGrG',\n        description: '南北绿灯'\n      }, {\n        phase: '相位4',\n        duration: 5,\n        state: 'ryry',\n        description: '南北黄灯'\n      }],\n      after: [{\n        phase: '相位1',\n        duration: 35,\n        state: 'GrGr',\n        description: '东西绿灯',\n        improved: true\n      }, {\n        phase: '相位2',\n        duration: 4,\n        state: 'yryr',\n        description: '东西黄灯'\n      }, {\n        phase: '相位3',\n        duration: 25,\n        state: 'rGrG',\n        description: '南北绿灯',\n        improved: true\n      }, {\n        phase: '相位4',\n        duration: 4,\n        state: 'ryry',\n        description: '南北黄灯'\n      }]\n    });\n\n    // 优化建议\n    const recommendations = reactive([{\n      priority: 'high',\n      title: '应用优化信号配时方案',\n      description: '建议立即应用优化后的信号配时方案，可显著提升交叉口通行效率。',\n      action: '调整信号灯控制器参数，实施新的配时方案',\n      expectedEffect: '减少15.6%的延误时间，提升12.3%的通行能力'\n    }, {\n      priority: 'medium',\n      title: '优化车道功能配置',\n      description: '根据各方向流量特点，建议调整车道功能配置以进一步平衡流量。',\n      action: '增设左转专用车道或调整车道标线',\n      expectedEffect: '进一步提升5-8%的通行效率'\n    }, {\n      priority: 'low',\n      title: '加强交通引导',\n      description: '在高峰时段增加交通引导，帮助车辆选择最优路径。',\n      action: '部署智能交通引导系统或增加人工引导',\n      expectedEffect: '减少局部拥堵，提升整体通行体验'\n    }]);\n\n    // 方法\n    const formatPercentage = value => {\n      return `${value.toFixed(1)}%`;\n    };\n    const formatChange = value => {\n      return value > 0 ? `+${value.toFixed(1)}%` : `${value.toFixed(1)}%`;\n    };\n    const formatValue = (value, unit) => {\n      if (typeof value === 'number') {\n        return `${value.toFixed(1)} ${unit}`;\n      }\n      return `${value} ${unit}`;\n    };\n    const getTrendClass = value => {\n      return value > 0 ? 'trend-up' : value < 0 ? 'trend-down' : 'trend-neutral';\n    };\n    const getTrendIcon = value => {\n      return value > 0 ? 'el-icon-top' : value < 0 ? 'el-icon-bottom' : 'el-icon-minus';\n    };\n    const getImprovementTagType = improvement => {\n      if (improvement > 15) return 'success';\n      if (improvement > 5) return 'warning';\n      return 'info';\n    };\n    const getPriorityTagType = priority => {\n      const typeMap = {\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info'\n      };\n      return typeMap[priority] || 'info';\n    };\n    const getPriorityText = priority => {\n      const textMap = {\n        'high': '高优先级',\n        'medium': '中优先级',\n        'low': '低优先级'\n      };\n      return textMap[priority] || '未知';\n    };\n    const refreshData = () => {\n      emit('refresh-requested');\n      // 这里可以添加数据刷新逻辑\n    };\n    const initCharts = () => {\n      nextTick(() => {\n        // 初始化性能对比图表\n        if (performanceChartRef.value) {\n          performanceChart = echarts.init(performanceChartRef.value);\n          updatePerformanceChart();\n        }\n\n        // 初始化改善效果图表\n        if (improvementChartRef.value) {\n          improvementChart = echarts.init(improvementChartRef.value);\n          updateImprovementChart();\n        }\n      });\n    };\n    const updatePerformanceChart = () => {\n      if (!performanceChart) return;\n      const metrics = comparisonData.map(item => item.metric);\n      const beforeData = comparisonData.map(item => item.before);\n      const afterData = comparisonData.map(item => item.after);\n      const option = {\n        title: {\n          text: '性能指标对比',\n          textStyle: {\n            fontSize: 14\n          }\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: ['优化前', '优化后']\n        },\n        xAxis: {\n          type: 'category',\n          data: metrics,\n          axisLabel: {\n            interval: 0,\n            rotate: 45\n          }\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          name: '优化前',\n          type: 'bar',\n          data: beforeData,\n          itemStyle: {\n            color: '#f56c6c'\n          }\n        }, {\n          name: '优化后',\n          type: 'bar',\n          data: afterData,\n          itemStyle: {\n            color: '#67c23a'\n          }\n        }]\n      };\n      performanceChart.setOption(option);\n    };\n    const updateImprovementChart = () => {\n      if (!improvementChart) return;\n      const data = [{\n        name: '延误减少',\n        value: delayReduction.value\n      }, {\n        name: '通行能力提升',\n        value: throughputIncrease.value\n      }, {\n        name: '速度提升',\n        value: speedImprovement.value\n      }, {\n        name: '燃油节省',\n        value: fuelSaving.value\n      }];\n      const option = {\n        title: {\n          text: '改善效果分布',\n          textStyle: {\n            fontSize: 14\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c}%'\n        },\n        series: [{\n          name: '改善效果',\n          type: 'pie',\n          radius: '60%',\n          data: data,\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }]\n      };\n      improvementChart.setOption(option);\n    };\n\n    // 生命周期\n    onMounted(() => {\n      initCharts();\n    });\n    return {\n      // 响应式数据\n      delayReduction,\n      throughputIncrease,\n      speedImprovement,\n      fuelSaving,\n      comparisonData,\n      signalTimingData,\n      recommendations,\n      // 图表引用\n      performanceChartRef,\n      improvementChartRef,\n      // 方法\n      formatPercentage,\n      formatChange,\n      formatValue,\n      getTrendClass,\n      getTrendIcon,\n      getImprovementTagType,\n      getPriorityTagType,\n      getPriorityText,\n      refreshData\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "nextTick", "echarts", "name", "props", "optimizationResult", "type", "Object", "default", "emits", "setup", "emit", "performanceChartRef", "improvementChartRef", "performanceChart", "improvementChart", "delayReduction", "throughputIncrease", "speedImprovement", "fuelSaving", "comparisonData", "metric", "unit", "before", "after", "improvement", "description", "signalTimingData", "beforeCycle", "afterCycle", "cycleImprovement", "phase", "duration", "state", "improved", "recommendations", "priority", "title", "action", "expectedEffect", "formatPercentage", "value", "toFixed", "formatChange", "formatValue", "getTrendClass", "getTrendIcon", "getImprovementTagType", "getPriorityTagType", "typeMap", "getPriorityText", "textMap", "refreshData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "init", "updatePerformanceChart", "updateImprovementChart", "metrics", "map", "item", "beforeData", "afterData", "option", "text", "textStyle", "fontSize", "tooltip", "trigger", "axisPointer", "legend", "data", "xAxis", "axisLabel", "interval", "rotate", "yAxis", "series", "itemStyle", "color", "setOption", "formatter", "radius", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\simulation\\OptimizationComparison.vue"], "sourcesContent": ["<template>\n  <div class=\"optimization-comparison\">\n    <el-card class=\"comparison-card\" shadow=\"hover\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3><i class=\"el-icon-data-analysis\"></i> 优化效果对比分析</h3>\n          <el-button type=\"primary\" size=\"small\" @click=\"refreshData\">\n            <i class=\"el-icon-refresh\"></i> 刷新数据\n          </el-button>\n        </div>\n      </template>\n\n      <!-- 总体改善指标 -->\n      <div class=\"improvement-overview\">\n        <h4><i class=\"el-icon-trend-charts\"></i> 总体改善效果</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <div class=\"improvement-card\">\n              <div class=\"improvement-icon\">\n                <i class=\"el-icon-timer\" style=\"color: #409eff\"></i>\n              </div>\n              <div class=\"improvement-content\">\n                <div class=\"improvement-value\">{{ formatPercentage(delayReduction) }}</div>\n                <div class=\"improvement-label\">延误减少</div>\n                <div class=\"improvement-trend\" :class=\"getTrendClass(delayReduction)\">\n                  <i :class=\"getTrendIcon(delayReduction)\"></i>\n                  {{ formatChange(delayReduction) }}\n                </div>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"6\">\n            <div class=\"improvement-card\">\n              <div class=\"improvement-icon\">\n                <i class=\"el-icon-right\" style=\"color: #67c23a\"></i>\n              </div>\n              <div class=\"improvement-content\">\n                <div class=\"improvement-value\">{{ formatPercentage(throughputIncrease) }}</div>\n                <div class=\"improvement-label\">通行能力提升</div>\n                <div class=\"improvement-trend\" :class=\"getTrendClass(throughputIncrease)\">\n                  <i :class=\"getTrendIcon(throughputIncrease)\"></i>\n                  {{ formatChange(throughputIncrease) }}\n                </div>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"6\">\n            <div class=\"improvement-card\">\n              <div class=\"improvement-icon\">\n                <i class=\"el-icon-odometer\" style=\"color: #e6a23c\"></i>\n              </div>\n              <div class=\"improvement-content\">\n                <div class=\"improvement-value\">{{ formatPercentage(speedImprovement) }}</div>\n                <div class=\"improvement-label\">平均速度提升</div>\n                <div class=\"improvement-trend\" :class=\"getTrendClass(speedImprovement)\">\n                  <i :class=\"getTrendIcon(speedImprovement)\"></i>\n                  {{ formatChange(speedImprovement) }}\n                </div>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"6\">\n            <div class=\"improvement-card\">\n              <div class=\"improvement-icon\">\n                <i class=\"el-icon-cpu\" style=\"color: #f56c6c\"></i>\n              </div>\n              <div class=\"improvement-content\">\n                <div class=\"improvement-value\">{{ formatPercentage(fuelSaving) }}</div>\n                <div class=\"improvement-label\">燃油节省</div>\n                <div class=\"improvement-trend\" :class=\"getTrendClass(fuelSaving)\">\n                  <i :class=\"getTrendIcon(fuelSaving)\"></i>\n                  {{ formatChange(fuelSaving) }}\n                </div>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 对比图表 -->\n      <div class=\"comparison-charts\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"chart-section\">\n              <h4><i class=\"el-icon-data-line\"></i> 性能指标对比</h4>\n              <div ref=\"performanceChartRef\" class=\"chart-container\"></div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"12\">\n            <div class=\"chart-section\">\n              <h4><i class=\"el-icon-pie-chart\"></i> 改善效果分布</h4>\n              <div ref=\"improvementChartRef\" class=\"chart-container\"></div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 详细对比表格 -->\n      <div class=\"comparison-table\">\n        <h4><i class=\"el-icon-s-data\"></i> 详细数据对比</h4>\n        <el-table :data=\"comparisonData\" border style=\"width: 100%\">\n          <el-table-column prop=\"metric\" label=\"指标\" width=\"150\" fixed=\"left\">\n            <template #default=\"scope\">\n              <strong>{{ scope.row.metric }}</strong>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"unit\" label=\"单位\" width=\"80\"></el-table-column>\n          <el-table-column prop=\"before\" label=\"优化前\" width=\"120\">\n            <template #default=\"scope\">\n              <span class=\"value-before\">{{ formatValue(scope.row.before, scope.row.unit) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"after\" label=\"优化后\" width=\"120\">\n            <template #default=\"scope\">\n              <span class=\"value-after\">{{ formatValue(scope.row.after, scope.row.unit) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"improvement\" label=\"改善幅度\" width=\"120\">\n            <template #default=\"scope\">\n              <el-tag :type=\"getImprovementTagType(scope.row.improvement)\" size=\"small\">\n                {{ formatPercentage(scope.row.improvement) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"description\" label=\"说明\" min-width=\"200\"></el-table-column>\n        </el-table>\n      </div>\n\n      <!-- 信号配时对比 -->\n      <div class=\"signal-timing-comparison\" v-if=\"signalTimingData\">\n        <h4><i class=\"el-icon-warning\"></i> 信号配时方案对比</h4>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"timing-section\">\n              <h5>优化前配时</h5>\n              <el-table :data=\"signalTimingData.before\" size=\"small\">\n                <el-table-column prop=\"phase\" label=\"相位\" width=\"80\"></el-table-column>\n                <el-table-column prop=\"duration\" label=\"时长(秒)\" width=\"100\"></el-table-column>\n                <el-table-column prop=\"state\" label=\"状态\" width=\"100\">\n                  <template #default=\"scope\">\n                    <el-tag size=\"small\">{{ scope.row.state }}</el-tag>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"description\" label=\"描述\"></el-table-column>\n              </el-table>\n              <div class=\"cycle-info\">\n                <span>周期时长: {{ signalTimingData.beforeCycle }}秒</span>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"12\">\n            <div class=\"timing-section\">\n              <h5>优化后配时</h5>\n              <el-table :data=\"signalTimingData.after\" size=\"small\">\n                <el-table-column prop=\"phase\" label=\"相位\" width=\"80\"></el-table-column>\n                <el-table-column prop=\"duration\" label=\"时长(秒)\" width=\"100\">\n                  <template #default=\"scope\">\n                    <span :class=\"{ 'improved-value': scope.row.improved }\">\n                      {{ scope.row.duration }}\n                    </span>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"state\" label=\"状态\" width=\"100\">\n                  <template #default=\"scope\">\n                    <el-tag size=\"small\">{{ scope.row.state }}</el-tag>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"description\" label=\"描述\"></el-table-column>\n              </el-table>\n              <div class=\"cycle-info\">\n                <span>周期时长: {{ signalTimingData.afterCycle }}秒</span>\n                <el-tag v-if=\"signalTimingData.cycleImprovement\" type=\"success\" size=\"small\">\n                  优化 {{ formatPercentage(signalTimingData.cycleImprovement) }}\n                </el-tag>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 优化建议 -->\n      <div class=\"optimization-recommendations\">\n        <h4><i class=\"el-icon-document\"></i> 优化建议</h4>\n        <div class=\"recommendations-list\">\n          <div \n            v-for=\"(recommendation, index) in recommendations\" \n            :key=\"index\"\n            class=\"recommendation-item\">\n            <div class=\"recommendation-header\">\n              <el-tag :type=\"getPriorityTagType(recommendation.priority)\" size=\"small\">\n                {{ getPriorityText(recommendation.priority) }}\n              </el-tag>\n              <span class=\"recommendation-title\">{{ recommendation.title }}</span>\n            </div>\n            <div class=\"recommendation-content\">\n              <p>{{ recommendation.description }}</p>\n              <div class=\"recommendation-actions\">\n                <span class=\"action-label\">建议措施:</span>\n                <span class=\"action-text\">{{ recommendation.action }}</span>\n              </div>\n              <div class=\"recommendation-effect\" v-if=\"recommendation.expectedEffect\">\n                <span class=\"effect-label\">预期效果:</span>\n                <span class=\"effect-text\">{{ recommendation.expectedEffect }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'OptimizationComparison',\n  props: {\n    optimizationResult: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['refresh-requested'],\n  setup(props, { emit }) {\n    // 图表引用\n    const performanceChartRef = ref(null)\n    const improvementChartRef = ref(null)\n    let performanceChart = null\n    let improvementChart = null\n    \n    // 改善指标\n    const delayReduction = ref(15.6)\n    const throughputIncrease = ref(12.3)\n    const speedImprovement = ref(8.9)\n    const fuelSaving = ref(11.2)\n    \n    // 对比数据\n    const comparisonData = reactive([\n      {\n        metric: '平均延误时间',\n        unit: '秒',\n        before: 45.2,\n        after: 38.1,\n        improvement: 15.7,\n        description: '车辆在交叉口的平均等待时间'\n      },\n      {\n        metric: '通行能力',\n        unit: '辆/小时',\n        before: 1850,\n        after: 2078,\n        improvement: 12.3,\n        description: '单位时间内通过交叉口的车辆数量'\n      },\n      {\n        metric: '平均行程速度',\n        unit: 'km/h',\n        before: 28.5,\n        after: 31.0,\n        improvement: 8.8,\n        description: '车辆通过交叉口区域的平均速度'\n      },\n      {\n        metric: '燃油消耗',\n        unit: '升/百公里',\n        before: 8.9,\n        after: 7.9,\n        improvement: 11.2,\n        description: '车辆通过交叉口的燃油消耗'\n      },\n      {\n        metric: '排队长度',\n        unit: '米',\n        before: 85.3,\n        after: 68.7,\n        improvement: 19.5,\n        description: '各方向最大排队长度'\n      }\n    ])\n    \n    // 信号配时对比数据\n    const signalTimingData = reactive({\n      beforeCycle: 120,\n      afterCycle: 105,\n      cycleImprovement: 12.5,\n      before: [\n        { phase: '相位1', duration: 30, state: 'GrGr', description: '东西绿灯' },\n        { phase: '相位2', duration: 5, state: 'yryr', description: '东西黄灯' },\n        { phase: '相位3', duration: 30, state: 'rGrG', description: '南北绿灯' },\n        { phase: '相位4', duration: 5, state: 'ryry', description: '南北黄灯' }\n      ],\n      after: [\n        { phase: '相位1', duration: 35, state: 'GrGr', description: '东西绿灯', improved: true },\n        { phase: '相位2', duration: 4, state: 'yryr', description: '东西黄灯' },\n        { phase: '相位3', duration: 25, state: 'rGrG', description: '南北绿灯', improved: true },\n        { phase: '相位4', duration: 4, state: 'ryry', description: '南北黄灯' }\n      ]\n    })\n    \n    // 优化建议\n    const recommendations = reactive([\n      {\n        priority: 'high',\n        title: '应用优化信号配时方案',\n        description: '建议立即应用优化后的信号配时方案，可显著提升交叉口通行效率。',\n        action: '调整信号灯控制器参数，实施新的配时方案',\n        expectedEffect: '减少15.6%的延误时间，提升12.3%的通行能力'\n      },\n      {\n        priority: 'medium',\n        title: '优化车道功能配置',\n        description: '根据各方向流量特点，建议调整车道功能配置以进一步平衡流量。',\n        action: '增设左转专用车道或调整车道标线',\n        expectedEffect: '进一步提升5-8%的通行效率'\n      },\n      {\n        priority: 'low',\n        title: '加强交通引导',\n        description: '在高峰时段增加交通引导，帮助车辆选择最优路径。',\n        action: '部署智能交通引导系统或增加人工引导',\n        expectedEffect: '减少局部拥堵，提升整体通行体验'\n      }\n    ])\n    \n    // 方法\n    const formatPercentage = (value) => {\n      return `${value.toFixed(1)}%`\n    }\n    \n    const formatChange = (value) => {\n      return value > 0 ? `+${value.toFixed(1)}%` : `${value.toFixed(1)}%`\n    }\n    \n    const formatValue = (value, unit) => {\n      if (typeof value === 'number') {\n        return `${value.toFixed(1)} ${unit}`\n      }\n      return `${value} ${unit}`\n    }\n    \n    const getTrendClass = (value) => {\n      return value > 0 ? 'trend-up' : value < 0 ? 'trend-down' : 'trend-neutral'\n    }\n    \n    const getTrendIcon = (value) => {\n      return value > 0 ? 'el-icon-top' : value < 0 ? 'el-icon-bottom' : 'el-icon-minus'\n    }\n    \n    const getImprovementTagType = (improvement) => {\n      if (improvement > 15) return 'success'\n      if (improvement > 5) return 'warning'\n      return 'info'\n    }\n    \n    const getPriorityTagType = (priority) => {\n      const typeMap = {\n        'high': 'danger',\n        'medium': 'warning',\n        'low': 'info'\n      }\n      return typeMap[priority] || 'info'\n    }\n    \n    const getPriorityText = (priority) => {\n      const textMap = {\n        'high': '高优先级',\n        'medium': '中优先级',\n        'low': '低优先级'\n      }\n      return textMap[priority] || '未知'\n    }\n    \n    const refreshData = () => {\n      emit('refresh-requested')\n      // 这里可以添加数据刷新逻辑\n    }\n    \n    const initCharts = () => {\n      nextTick(() => {\n        // 初始化性能对比图表\n        if (performanceChartRef.value) {\n          performanceChart = echarts.init(performanceChartRef.value)\n          updatePerformanceChart()\n        }\n        \n        // 初始化改善效果图表\n        if (improvementChartRef.value) {\n          improvementChart = echarts.init(improvementChartRef.value)\n          updateImprovementChart()\n        }\n      })\n    }\n    \n    const updatePerformanceChart = () => {\n      if (!performanceChart) return\n      \n      const metrics = comparisonData.map(item => item.metric)\n      const beforeData = comparisonData.map(item => item.before)\n      const afterData = comparisonData.map(item => item.after)\n      \n      const option = {\n        title: {\n          text: '性能指标对比',\n          textStyle: { fontSize: 14 }\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: ['优化前', '优化后']\n        },\n        xAxis: {\n          type: 'category',\n          data: metrics,\n          axisLabel: {\n            interval: 0,\n            rotate: 45\n          }\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [\n          {\n            name: '优化前',\n            type: 'bar',\n            data: beforeData,\n            itemStyle: {\n              color: '#f56c6c'\n            }\n          },\n          {\n            name: '优化后',\n            type: 'bar',\n            data: afterData,\n            itemStyle: {\n              color: '#67c23a'\n            }\n          }\n        ]\n      }\n      \n      performanceChart.setOption(option)\n    }\n    \n    const updateImprovementChart = () => {\n      if (!improvementChart) return\n      \n      const data = [\n        { name: '延误减少', value: delayReduction.value },\n        { name: '通行能力提升', value: throughputIncrease.value },\n        { name: '速度提升', value: speedImprovement.value },\n        { name: '燃油节省', value: fuelSaving.value }\n      ]\n      \n      const option = {\n        title: {\n          text: '改善效果分布',\n          textStyle: { fontSize: 14 }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c}%'\n        },\n        series: [{\n          name: '改善效果',\n          type: 'pie',\n          radius: '60%',\n          data: data,\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }]\n      }\n      \n      improvementChart.setOption(option)\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      initCharts()\n    })\n    \n    return {\n      // 响应式数据\n      delayReduction,\n      throughputIncrease,\n      speedImprovement,\n      fuelSaving,\n      comparisonData,\n      signalTimingData,\n      recommendations,\n      \n      // 图表引用\n      performanceChartRef,\n      improvementChartRef,\n      \n      // 方法\n      formatPercentage,\n      formatChange,\n      formatValue,\n      getTrendClass,\n      getTrendIcon,\n      getImprovementTagType,\n      getPriorityTagType,\n      getPriorityText,\n      refreshData\n    }\n  }\n}\n</script>\n\n<style scoped>\n.optimization-comparison {\n  margin-bottom: 20px;\n}\n\n.comparison-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  color: #303133;\n}\n\n.improvement-overview,\n.comparison-charts,\n.comparison-table,\n.signal-timing-comparison,\n.optimization-recommendations {\n  margin-bottom: 30px;\n}\n\n.improvement-overview h4,\n.comparison-charts h4,\n.comparison-table h4,\n.signal-timing-comparison h4,\n.optimization-recommendations h4 {\n  margin-bottom: 20px;\n  color: #606266;\n  font-size: 16px;\n}\n\n.improvement-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 8px;\n  border-left: 4px solid #409eff;\n  transition: transform 0.2s;\n}\n\n.improvement-card:hover {\n  transform: translateY(-2px);\n}\n\n.improvement-icon {\n  font-size: 28px;\n  margin-right: 15px;\n}\n\n.improvement-content {\n  flex: 1;\n}\n\n.improvement-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1;\n}\n\n.improvement-label {\n  font-size: 12px;\n  color: #909399;\n  margin: 4px 0;\n}\n\n.improvement-trend {\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.trend-up {\n  color: #67c23a;\n}\n\n.trend-down {\n  color: #f56c6c;\n}\n\n.trend-neutral {\n  color: #909399;\n}\n\n.chart-section {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n}\n\n.chart-section h4 {\n  margin-bottom: 15px;\n  color: #303133;\n  font-size: 14px;\n}\n\n.chart-container {\n  height: 300px;\n  width: 100%;\n}\n\n.value-before {\n  color: #f56c6c;\n}\n\n.value-after {\n  color: #67c23a;\n  font-weight: bold;\n}\n\n.timing-section {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 8px;\n}\n\n.timing-section h5 {\n  margin-bottom: 15px;\n  color: #303133;\n}\n\n.cycle-info {\n  margin-top: 10px;\n  font-size: 12px;\n  color: #606266;\n}\n\n.improved-value {\n  color: #67c23a;\n  font-weight: bold;\n}\n\n.recommendations-list {\n  space-y: 15px;\n}\n\n.recommendation-item {\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border-left: 4px solid #409eff;\n  margin-bottom: 15px;\n}\n\n.recommendation-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.recommendation-title {\n  margin-left: 10px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.recommendation-content p {\n  margin-bottom: 10px;\n  color: #606266;\n  line-height: 1.5;\n}\n\n.recommendation-actions,\n.recommendation-effect {\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n\n.action-label,\n.effect-label {\n  color: #909399;\n  margin-right: 8px;\n}\n\n.action-text,\n.effect-text {\n  color: #303133;\n}\n</style>\n"], "mappings": ";;AA0NA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAI;AACvD,OAAO,KAAKC,OAAM,MAAO,SAAQ;AAEjC,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,KAAK,EAAE;IACLC,kBAAkB,EAAE;MAClBC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,mBAAmB,CAAC;EAC5BC,KAAKA,CAACN,KAAK,EAAE;IAAEO;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,mBAAkB,GAAId,GAAG,CAAC,IAAI;IACpC,MAAMe,mBAAkB,GAAIf,GAAG,CAAC,IAAI;IACpC,IAAIgB,gBAAe,GAAI,IAAG;IAC1B,IAAIC,gBAAe,GAAI,IAAG;;IAE1B;IACA,MAAMC,cAAa,GAAIlB,GAAG,CAAC,IAAI;IAC/B,MAAMmB,kBAAiB,GAAInB,GAAG,CAAC,IAAI;IACnC,MAAMoB,gBAAe,GAAIpB,GAAG,CAAC,GAAG;IAChC,MAAMqB,UAAS,GAAIrB,GAAG,CAAC,IAAI;;IAE3B;IACA,MAAMsB,cAAa,GAAIrB,QAAQ,CAAC,CAC9B;MACEsB,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE;IACf,CAAC,EACD;MACEL,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE;IACf,CAAC,EACD;MACEL,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,GAAG;MAChBC,WAAW,EAAE;IACf,CAAC,EACD;MACEL,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE,GAAG;MACXC,KAAK,EAAE,GAAG;MACVC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE;IACf,CAAC,EACD;MACEL,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE;IACf,EACD;;IAED;IACA,MAAMC,gBAAe,GAAI5B,QAAQ,CAAC;MAChC6B,WAAW,EAAE,GAAG;MAChBC,UAAU,EAAE,GAAG;MACfC,gBAAgB,EAAE,IAAI;MACtBP,MAAM,EAAE,CACN;QAAEQ,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEP,WAAW,EAAE;MAAO,CAAC,EAClE;QAAEK,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEP,WAAW,EAAE;MAAO,CAAC,EACjE;QAAEK,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEP,WAAW,EAAE;MAAO,CAAC,EAClE;QAAEK,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEP,WAAW,EAAE;MAAO,EACjE;MACDF,KAAK,EAAE,CACL;QAAEO,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEP,WAAW,EAAE,MAAM;QAAEQ,QAAQ,EAAE;MAAK,CAAC,EAClF;QAAEH,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEP,WAAW,EAAE;MAAO,CAAC,EACjE;QAAEK,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEP,WAAW,EAAE,MAAM;QAAEQ,QAAQ,EAAE;MAAK,CAAC,EAClF;QAAEH,KAAK,EAAE,KAAK;QAAEC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEP,WAAW,EAAE;MAAO;IAEpE,CAAC;;IAED;IACA,MAAMS,eAAc,GAAIpC,QAAQ,CAAC,CAC/B;MACEqC,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE,YAAY;MACnBX,WAAW,EAAE,gCAAgC;MAC7CY,MAAM,EAAE,qBAAqB;MAC7BC,cAAc,EAAE;IAClB,CAAC,EACD;MACEH,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE,UAAU;MACjBX,WAAW,EAAE,+BAA+B;MAC5CY,MAAM,EAAE,iBAAiB;MACzBC,cAAc,EAAE;IAClB,CAAC,EACD;MACEH,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,QAAQ;MACfX,WAAW,EAAE,yBAAyB;MACtCY,MAAM,EAAE,mBAAmB;MAC3BC,cAAc,EAAE;IAClB,EACD;;IAED;IACA,MAAMC,gBAAe,GAAKC,KAAK,IAAK;MAClC,OAAO,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,GAAE;IAC9B;IAEA,MAAMC,YAAW,GAAKF,KAAK,IAAK;MAC9B,OAAOA,KAAI,GAAI,IAAI,IAAIA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,GAAE,GAAI,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,GAAE;IACpE;IAEA,MAAME,WAAU,GAAIA,CAACH,KAAK,EAAEnB,IAAI,KAAK;MACnC,IAAI,OAAOmB,KAAI,KAAM,QAAQ,EAAE;QAC7B,OAAO,GAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,IAAIpB,IAAI,EAAC;MACrC;MACA,OAAO,GAAGmB,KAAK,IAAInB,IAAI,EAAC;IAC1B;IAEA,MAAMuB,aAAY,GAAKJ,KAAK,IAAK;MAC/B,OAAOA,KAAI,GAAI,IAAI,UAAS,GAAIA,KAAI,GAAI,IAAI,YAAW,GAAI,eAAc;IAC3E;IAEA,MAAMK,YAAW,GAAKL,KAAK,IAAK;MAC9B,OAAOA,KAAI,GAAI,IAAI,aAAY,GAAIA,KAAI,GAAI,IAAI,gBAAe,GAAI,eAAc;IAClF;IAEA,MAAMM,qBAAoB,GAAKtB,WAAW,IAAK;MAC7C,IAAIA,WAAU,GAAI,EAAE,EAAE,OAAO,SAAQ;MACrC,IAAIA,WAAU,GAAI,CAAC,EAAE,OAAO,SAAQ;MACpC,OAAO,MAAK;IACd;IAEA,MAAMuB,kBAAiB,GAAKZ,QAAQ,IAAK;MACvC,MAAMa,OAAM,GAAI;QACd,MAAM,EAAE,QAAQ;QAChB,QAAQ,EAAE,SAAS;QACnB,KAAK,EAAE;MACT;MACA,OAAOA,OAAO,CAACb,QAAQ,KAAK,MAAK;IACnC;IAEA,MAAMc,eAAc,GAAKd,QAAQ,IAAK;MACpC,MAAMe,OAAM,GAAI;QACd,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,MAAM;QAChB,KAAK,EAAE;MACT;MACA,OAAOA,OAAO,CAACf,QAAQ,KAAK,IAAG;IACjC;IAEA,MAAMgB,WAAU,GAAIA,CAAA,KAAM;MACxBzC,IAAI,CAAC,mBAAmB;MACxB;IACF;IAEA,MAAM0C,UAAS,GAAIA,CAAA,KAAM;MACvBpD,QAAQ,CAAC,MAAM;QACb;QACA,IAAIW,mBAAmB,CAAC6B,KAAK,EAAE;UAC7B3B,gBAAe,GAAIZ,OAAO,CAACoD,IAAI,CAAC1C,mBAAmB,CAAC6B,KAAK;UACzDc,sBAAsB,CAAC;QACzB;;QAEA;QACA,IAAI1C,mBAAmB,CAAC4B,KAAK,EAAE;UAC7B1B,gBAAe,GAAIb,OAAO,CAACoD,IAAI,CAACzC,mBAAmB,CAAC4B,KAAK;UACzDe,sBAAsB,CAAC;QACzB;MACF,CAAC;IACH;IAEA,MAAMD,sBAAqB,GAAIA,CAAA,KAAM;MACnC,IAAI,CAACzC,gBAAgB,EAAE;MAEvB,MAAM2C,OAAM,GAAIrC,cAAc,CAACsC,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACtC,MAAM;MACtD,MAAMuC,UAAS,GAAIxC,cAAc,CAACsC,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACpC,MAAM;MACzD,MAAMsC,SAAQ,GAAIzC,cAAc,CAACsC,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACnC,KAAK;MAEvD,MAAMsC,MAAK,GAAI;QACbzB,KAAK,EAAE;UACL0B,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE;YAAEC,QAAQ,EAAE;UAAG;QAC5B,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACX9D,IAAI,EAAE;UACR;QACF,CAAC;QACD+D,MAAM,EAAE;UACNC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK;QACrB,CAAC;QACDC,KAAK,EAAE;UACLjE,IAAI,EAAE,UAAU;UAChBgE,IAAI,EAAEb,OAAO;UACbe,SAAS,EAAE;YACTC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE;UACV;QACF,CAAC;QACDC,KAAK,EAAE;UACLrE,IAAI,EAAE;QACR,CAAC;QACDsE,MAAM,EAAE,CACN;UACEzE,IAAI,EAAE,KAAK;UACXG,IAAI,EAAE,KAAK;UACXgE,IAAI,EAAEV,UAAU;UAChBiB,SAAS,EAAE;YACTC,KAAK,EAAE;UACT;QACF,CAAC,EACD;UACE3E,IAAI,EAAE,KAAK;UACXG,IAAI,EAAE,KAAK;UACXgE,IAAI,EAAET,SAAS;UACfgB,SAAS,EAAE;YACTC,KAAK,EAAE;UACT;QACF;MAEJ;MAEAhE,gBAAgB,CAACiE,SAAS,CAACjB,MAAM;IACnC;IAEA,MAAMN,sBAAqB,GAAIA,CAAA,KAAM;MACnC,IAAI,CAACzC,gBAAgB,EAAE;MAEvB,MAAMuD,IAAG,GAAI,CACX;QAAEnE,IAAI,EAAE,MAAM;QAAEsC,KAAK,EAAEzB,cAAc,CAACyB;MAAM,CAAC,EAC7C;QAAEtC,IAAI,EAAE,QAAQ;QAAEsC,KAAK,EAAExB,kBAAkB,CAACwB;MAAM,CAAC,EACnD;QAAEtC,IAAI,EAAE,MAAM;QAAEsC,KAAK,EAAEvB,gBAAgB,CAACuB;MAAM,CAAC,EAC/C;QAAEtC,IAAI,EAAE,MAAM;QAAEsC,KAAK,EAAEtB,UAAU,CAACsB;MAAM,EAC1C;MAEA,MAAMqB,MAAK,GAAI;QACbzB,KAAK,EAAE;UACL0B,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE;YAAEC,QAAQ,EAAE;UAAG;QAC5B,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfa,SAAS,EAAE;QACb,CAAC;QACDJ,MAAM,EAAE,CAAC;UACPzE,IAAI,EAAE,MAAM;UACZG,IAAI,EAAE,KAAK;UACX2E,MAAM,EAAE,KAAK;UACbX,IAAI,EAAEA,IAAI;UACVY,QAAQ,EAAE;YACRL,SAAS,EAAE;cACTM,UAAU,EAAE,EAAE;cACdC,aAAa,EAAE,CAAC;cAChBC,WAAW,EAAE;YACf;UACF;QACF,CAAC;MACH;MAEAtE,gBAAgB,CAACgE,SAAS,CAACjB,MAAM;IACnC;;IAEA;IACA9D,SAAS,CAAC,MAAM;MACdqD,UAAU,CAAC;IACb,CAAC;IAED,OAAO;MACL;MACArC,cAAc;MACdC,kBAAkB;MAClBC,gBAAgB;MAChBC,UAAU;MACVC,cAAc;MACdO,gBAAgB;MAChBQ,eAAe;MAEf;MACAvB,mBAAmB;MACnBC,mBAAmB;MAEnB;MACA2B,gBAAgB;MAChBG,YAAY;MACZC,WAAW;MACXC,aAAa;MACbC,YAAY;MACZC,qBAAqB;MACrBC,kBAAkB;MAClBE,eAAe;MACfE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}