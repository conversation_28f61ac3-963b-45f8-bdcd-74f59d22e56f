{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport simulationApi from '@/api/simulation';\nimport stompService from '@/utils/stomp-service';\nimport SimulationControlPanel from '@/components/simulation/SimulationControlPanel.vue';\nimport SimulationMonitor from '@/components/simulation/SimulationMonitor.vue';\nimport OptimizationComparison from '@/components/simulation/OptimizationComparison.vue';\nimport TrafficLightController from '@/components/simulation/TrafficLightController.vue';\nimport DecisionSupportPanel from '@/components/simulation/DecisionSupportPanel.vue';\nexport default {\n  name: 'SimulationDetail',\n  components: {\n    SimulationControlPanel,\n    SimulationMonitor,\n    OptimizationComparison\n  },\n  setup() {\n    const route = useRoute();\n    const simulationId = ref(route.params.id);\n\n    // 响应式数据\n    const simulationTask = ref(null);\n    const optimizationResult = ref(null);\n    const simulationLogs = ref([]);\n    const loading = ref(false);\n\n    // WebSocket订阅\n    let simulationSubscriptions = null;\n\n    // 计算属性\n    const isSimulationRunning = computed(() => simulationTask.value?.status === 'running');\n    const hasOptimizationResult = computed(() => optimizationResult.value && Object.keys(optimizationResult.value).length > 0);\n\n    // 方法\n    const getStatusTagType = status => {\n      const statusMap = {\n        'created': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      };\n      return statusMap[status] || 'info';\n    };\n    const getStatusText = status => {\n      const statusMap = {\n        'created': '已创建',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      };\n      return statusMap[status] || '未知';\n    };\n    const getTypeTagType = type => {\n      const typeMap = {\n        'signal_timing': 'primary',\n        'flow_balance': 'success',\n        'comprehensive': 'warning'\n      };\n      return typeMap[type] || 'info';\n    };\n    const getTypeText = type => {\n      const typeMap = {\n        'signal_timing': '信号配时优化',\n        'flow_balance': '流量平衡优化',\n        'comprehensive': '综合优化分析'\n      };\n      return typeMap[type] || '未知';\n    };\n    const getProgressStatus = status => {\n      if (status === 'failed') return 'exception';\n      if (status === 'completed') return 'success';\n      return null;\n    };\n    const formatTime = timeStr => {\n      if (!timeStr) return '-';\n      const date = new Date(timeStr);\n      return date.toLocaleString('zh-CN');\n    };\n    const formatDuration = duration => {\n      if (!duration) return '-';\n      const hours = Math.floor(duration / 3600);\n      const minutes = Math.floor(duration % 3600 / 60);\n      const seconds = duration % 60;\n      if (hours > 0) {\n        return `${hours}h ${minutes}m ${seconds}s`;\n      } else if (minutes > 0) {\n        return `${minutes}m ${seconds}s`;\n      } else {\n        return `${seconds}s`;\n      }\n    };\n    const loadSimulationTask = async () => {\n      try {\n        loading.value = true;\n        const response = await simulationApi.getSimulationTask(simulationId.value);\n        if (response.status === 'success') {\n          simulationTask.value = response.simulation_task;\n        } else {\n          ElMessage.error('仿真任务不存在');\n        }\n      } catch (error) {\n        console.error('加载仿真任务失败:', error);\n        ElMessage.error('加载仿真任务失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n    const loadOptimizationResult = async () => {\n      try {\n        const response = await simulationApi.getOptimizationResult(simulationId.value);\n        if (response.status === 'success') {\n          optimizationResult.value = response.optimization_result;\n        }\n      } catch (error) {\n        console.error('加载优化结果失败:', error);\n      }\n    };\n    const setupWebSocketSubscriptions = async () => {\n      try {\n        // 确保WebSocket连接\n        await stompService.connect();\n\n        // 订阅仿真相关主题\n        simulationSubscriptions = await stompService.subscribeSimulationAll(simulationId.value, {\n          onStatusUpdate: handleWebSocketStatusUpdate,\n          onRealtimeData: handleWebSocketRealtimeData,\n          onFlowData: handleWebSocketFlowData,\n          onTrafficLights: handleWebSocketTrafficLights,\n          onOptimizationResult: handleWebSocketOptimizationResult\n        });\n        console.log('WebSocket订阅设置完成');\n      } catch (error) {\n        console.error('设置WebSocket订阅失败:', error);\n      }\n    };\n    const cleanupWebSocketSubscriptions = () => {\n      if (simulationSubscriptions) {\n        stompService.unsubscribeSimulation(simulationSubscriptions);\n        simulationSubscriptions = null;\n      }\n    };\n\n    // WebSocket事件处理\n    const handleWebSocketStatusUpdate = data => {\n      if (data.simulationId === simulationId.value) {\n        // 更新仿真状态\n        if (simulationTask.value) {\n          simulationTask.value.status = data.status;\n          simulationTask.value.progress = data.progress;\n        }\n\n        // 添加日志\n        addLog('info', data.message || `状态更新: ${data.status}`);\n      }\n    };\n    const handleWebSocketRealtimeData = data => {\n      if (data.simulationId === simulationId.value) {\n        // 处理实时数据\n        addLog('debug', `实时数据: 时间${data.simulationTime}s, 车辆${data.vehicleCount}辆`);\n      }\n    };\n    const handleWebSocketFlowData = data => {\n      if (data.simulationId === simulationId.value) {\n        // 处理流量数据\n        addLog('debug', '收到方向流量数据更新');\n      }\n    };\n    const handleWebSocketTrafficLights = data => {\n      if (data.simulationId === simulationId.value) {\n        // 处理信号灯数据\n        addLog('debug', `信号灯更新: 相位${data.currentPhase}, 剩余${data.phaseRemaining}s`);\n      }\n    };\n    const handleWebSocketOptimizationResult = data => {\n      if (data.simulationId === simulationId.value) {\n        // 处理优化结果\n        optimizationResult.value = data.optimizationResult;\n        addLog('info', '收到优化结果');\n      }\n    };\n\n    // 组件事件处理\n    const handleSimulationStarted = simId => {\n      addLog('info', '仿真已启动');\n      loadSimulationTask();\n    };\n    const handleSimulationStopped = simId => {\n      addLog('info', '仿真已停止');\n      loadSimulationTask();\n    };\n    const handleSimulationCompleted = simId => {\n      addLog('info', '仿真已完成');\n      loadSimulationTask();\n      loadOptimizationResult();\n    };\n    const handleStatusUpdated = status => {\n      // 状态更新已通过WebSocket处理\n    };\n    const handleMonitorDataUpdated = data => {\n      // 监控数据更新\n    };\n    const addLog = (level, message) => {\n      simulationLogs.value.unshift({\n        timestamp: new Date().toISOString(),\n        level,\n        message\n      });\n\n      // 保持最近100条日志\n      if (simulationLogs.value.length > 100) {\n        simulationLogs.value = simulationLogs.value.slice(0, 100);\n      }\n    };\n    const refreshLogs = () => {\n      // 这里可以从服务器获取日志\n      addLog('info', '日志已刷新');\n    };\n\n    // 生命周期\n    onMounted(async () => {\n      await loadSimulationTask();\n      await loadOptimizationResult();\n      await setupWebSocketSubscriptions();\n      addLog('info', '页面加载完成');\n    });\n    onUnmounted(() => {\n      cleanupWebSocketSubscriptions();\n    });\n    return {\n      // 响应式数据\n      simulationId,\n      simulationTask,\n      optimizationResult,\n      simulationLogs,\n      loading,\n      // 计算属性\n      isSimulationRunning,\n      hasOptimizationResult,\n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getTypeTagType,\n      getTypeText,\n      getProgressStatus,\n      formatTime,\n      formatDuration,\n      loadOptimizationResult,\n      refreshLogs,\n      // 事件处理\n      handleSimulationStarted,\n      handleSimulationStopped,\n      handleSimulationCompleted,\n      handleStatusUpdated,\n      handleMonitorDataUpdated\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "useRoute", "ElMessage", "simulationApi", "stompService", "SimulationControlPanel", "SimulationMonitor", "OptimizationComparison", "TrafficLightController", "DecisionSupportPanel", "name", "components", "setup", "route", "simulationId", "params", "id", "simulationTask", "optimizationResult", "simulationLogs", "loading", "simulationSubscriptions", "isSimulationRunning", "value", "status", "hasOptimizationResult", "Object", "keys", "length", "getStatusTagType", "statusMap", "getStatusText", "getTypeTagType", "type", "typeMap", "getTypeText", "getProgressStatus", "formatTime", "timeStr", "date", "Date", "toLocaleString", "formatDuration", "duration", "hours", "Math", "floor", "minutes", "seconds", "loadSimulationTask", "response", "getSimulationTask", "simulation_task", "error", "console", "loadOptimizationResult", "getOptimizationResult", "optimization_result", "setupWebSocketSubscriptions", "connect", "subscribeSimulationAll", "onStatusUpdate", "handleWebSocketStatusUpdate", "onRealtimeData", "handleWebSocketRealtimeData", "onFlowData", "handleWebSocketFlowData", "onTrafficLights", "handleWebSocketTrafficLights", "onOptimizationResult", "handleWebSocketOptimizationResult", "log", "cleanupWebSocketSubscriptions", "unsubscribeSimulation", "data", "progress", "addLog", "message", "simulationTime", "vehicleCount", "currentPhase", "phaseRemaining", "handleSimulationStarted", "simId", "handleSimulationStopped", "handleSimulationCompleted", "handleStatusUpdated", "handleMonitorDataUpdated", "level", "unshift", "timestamp", "toISOString", "slice", "refreshLogs"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\simulation\\SimulationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"simulation-detail\">\n    <!-- 页面头部 -->\n    <div class=\"detail-header\">\n      <el-breadcrumb separator=\"/\">\n        <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\n        <el-breadcrumb-item :to=\"{ path: '/simulation' }\">仿真分析</el-breadcrumb-item>\n        <el-breadcrumb-item>仿真详情</el-breadcrumb-item>\n      </el-breadcrumb>\n      \n      <div class=\"header-actions\">\n        <el-button @click=\"$router.go(-1)\">\n          <i class=\"el-icon-back\"></i> 返回\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 仿真基本信息 -->\n    <el-card class=\"info-card\" v-if=\"simulationTask\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>{{ simulationTask.taskName }}</h3>\n          <el-tag :type=\"getStatusTagType(simulationTask.status)\" size=\"large\">\n            {{ getStatusText(simulationTask.status) }}\n          </el-tag>\n        </div>\n      </template>\n\n      <el-descriptions :column=\"3\" border>\n        <el-descriptions-item label=\"仿真ID\">{{ simulationTask.simulationId }}</el-descriptions-item>\n        <el-descriptions-item label=\"仿真类型\">\n          <el-tag :type=\"getTypeTagType(simulationTask.simulationType)\" size=\"small\">\n            {{ getTypeText(simulationTask.simulationType) }}\n          </el-tag>\n        </el-descriptions-item>\n        <el-descriptions-item label=\"创建时间\">{{ formatTime(simulationTask.createTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"开始时间\">{{ formatTime(simulationTask.startTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"结束时间\">{{ formatTime(simulationTask.endTime) }}</el-descriptions-item>\n        <el-descriptions-item label=\"运行时长\">{{ formatDuration(simulationTask.duration) }}</el-descriptions-item>\n        <el-descriptions-item label=\"创建用户\">{{ simulationTask.username }}</el-descriptions-item>\n        <el-descriptions-item label=\"关联分析\">{{ simulationTask.analysisTaskId || '-' }}</el-descriptions-item>\n        <el-descriptions-item label=\"进度\">\n          <el-progress \n            :percentage=\"simulationTask.progress || 0\" \n            :status=\"getProgressStatus(simulationTask.status)\"\n            :stroke-width=\"8\">\n          </el-progress>\n        </el-descriptions-item>\n      </el-descriptions>\n\n      <div v-if=\"simulationTask.errorMessage\" class=\"error-message\">\n        <el-alert\n          :title=\"simulationTask.errorMessage\"\n          type=\"error\"\n          :closable=\"false\"\n          show-icon>\n        </el-alert>\n      </div>\n    </el-card>\n\n    <!-- 仿真控制面板 -->\n    <SimulationControlPanel\n      v-if=\"simulationId\"\n      :analysisTaskId=\"simulationTask?.analysisTaskId || ''\"\n      :trafficData=\"simulationTask?.trafficData || {}\"\n      @simulation-started=\"handleSimulationStarted\"\n      @simulation-stopped=\"handleSimulationStopped\"\n      @simulation-completed=\"handleSimulationCompleted\"\n      @status-updated=\"handleStatusUpdated\"\n    />\n\n    <!-- 仿真监控和信号灯控制 -->\n    <el-row :gutter=\"20\" v-if=\"simulationId && isSimulationRunning\">\n      <el-col :span=\"16\">\n        <SimulationMonitor\n          :simulationId=\"simulationId\"\n          :isRunning=\"isSimulationRunning\"\n          @data-updated=\"handleMonitorDataUpdated\"\n        />\n      </el-col>\n      <el-col :span=\"8\">\n        <TrafficLightController\n          :simulationId=\"simulationId\"\n          @status-change=\"handleTrafficLightStatusChange\"\n          @control-change=\"handleTrafficLightControlChange\"\n        />\n      </el-col>\n    </el-row>\n\n    <!-- 决策支持面板 -->\n    <DecisionSupportPanel\n      v-if=\"simulationId && isSimulationRunning\"\n      :simulationId=\"simulationId\"\n      @suggestion-applied=\"handleSuggestionApplied\"\n      @decision-made=\"handleDecisionMade\"\n    />\n\n    <!-- 优化结果对比 -->\n    <OptimizationComparison\n      v-if=\"simulationId && hasOptimizationResult\"\n      :optimizationResult=\"optimizationResult\"\n      @refresh-requested=\"loadOptimizationResult\"\n    />\n\n    <!-- 性能指标 -->\n    <el-card class=\"performance-card\" v-if=\"simulationTask?.performanceMetrics\">\n      <template #header>\n        <h4><i class=\"el-icon-data-analysis\"></i> 性能指标</h4>\n      </template>\n\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <el-statistic title=\"总车辆数\" :value=\"simulationTask.performanceMetrics.totalVehicles || 0\" suffix=\"辆\">\n            <template #prefix>\n              <i class=\"el-icon-truck\" style=\"color: #409eff\"></i>\n            </template>\n          </el-statistic>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-statistic title=\"平均速度\" :value=\"(simulationTask.performanceMetrics.averageSpeed || 0).toFixed(1)\" suffix=\"m/s\">\n            <template #prefix>\n              <i class=\"el-icon-odometer\" style=\"color: #67c23a\"></i>\n            </template>\n          </el-statistic>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-statistic title=\"通行能力\" :value=\"(simulationTask.performanceMetrics.throughput || 0).toFixed(0)\" suffix=\"辆/h\">\n            <template #prefix>\n              <i class=\"el-icon-right\" style=\"color: #e6a23c\"></i>\n            </template>\n          </el-statistic>\n        </el-col>\n        \n        <el-col :span=\"6\">\n          <el-statistic title=\"改善效果\" :value=\"(simulationTask.performanceMetrics.improvementPercentage || 0).toFixed(1)\" suffix=\"%\">\n            <template #prefix>\n              <i class=\"el-icon-trend-charts\" style=\"color: #f56c6c\"></i>\n            </template>\n          </el-statistic>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 仿真日志 -->\n    <el-card class=\"log-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h4><i class=\"el-icon-document\"></i> 仿真日志</h4>\n          <el-button size=\"small\" @click=\"refreshLogs\">\n            <i class=\"el-icon-refresh\"></i> 刷新\n          </el-button>\n        </div>\n      </template>\n\n      <div class=\"log-container\">\n        <div v-for=\"(log, index) in simulationLogs\" :key=\"index\" class=\"log-entry\">\n          <span class=\"log-time\">{{ formatTime(log.timestamp) }}</span>\n          <span :class=\"['log-level', `log-${log.level}`]\">{{ log.level.toUpperCase() }}</span>\n          <span class=\"log-message\">{{ log.message }}</span>\n        </div>\n        \n        <div v-if=\"simulationLogs.length === 0\" class=\"no-logs\">\n          暂无日志记录\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport simulationApi from '@/api/simulation'\nimport stompService from '@/utils/stomp-service'\nimport SimulationControlPanel from '@/components/simulation/SimulationControlPanel.vue'\nimport SimulationMonitor from '@/components/simulation/SimulationMonitor.vue'\nimport OptimizationComparison from '@/components/simulation/OptimizationComparison.vue'\nimport TrafficLightController from '@/components/simulation/TrafficLightController.vue'\nimport DecisionSupportPanel from '@/components/simulation/DecisionSupportPanel.vue'\n\nexport default {\n  name: 'SimulationDetail',\n  components: {\n    SimulationControlPanel,\n    SimulationMonitor,\n    OptimizationComparison\n  },\n  setup() {\n    const route = useRoute()\n    const simulationId = ref(route.params.id)\n    \n    // 响应式数据\n    const simulationTask = ref(null)\n    const optimizationResult = ref(null)\n    const simulationLogs = ref([])\n    const loading = ref(false)\n    \n    // WebSocket订阅\n    let simulationSubscriptions = null\n    \n    // 计算属性\n    const isSimulationRunning = computed(() => \n      simulationTask.value?.status === 'running'\n    )\n    \n    const hasOptimizationResult = computed(() => \n      optimizationResult.value && Object.keys(optimizationResult.value).length > 0\n    )\n    \n    // 方法\n    const getStatusTagType = (status) => {\n      const statusMap = {\n        'created': 'info',\n        'running': 'success',\n        'completed': 'success',\n        'failed': 'danger',\n        'stopped': 'warning'\n      }\n      return statusMap[status] || 'info'\n    }\n    \n    const getStatusText = (status) => {\n      const statusMap = {\n        'created': '已创建',\n        'running': '运行中',\n        'completed': '已完成',\n        'failed': '失败',\n        'stopped': '已停止'\n      }\n      return statusMap[status] || '未知'\n    }\n    \n    const getTypeTagType = (type) => {\n      const typeMap = {\n        'signal_timing': 'primary',\n        'flow_balance': 'success',\n        'comprehensive': 'warning'\n      }\n      return typeMap[type] || 'info'\n    }\n    \n    const getTypeText = (type) => {\n      const typeMap = {\n        'signal_timing': '信号配时优化',\n        'flow_balance': '流量平衡优化',\n        'comprehensive': '综合优化分析'\n      }\n      return typeMap[type] || '未知'\n    }\n    \n    const getProgressStatus = (status) => {\n      if (status === 'failed') return 'exception'\n      if (status === 'completed') return 'success'\n      return null\n    }\n    \n    const formatTime = (timeStr) => {\n      if (!timeStr) return '-'\n      const date = new Date(timeStr)\n      return date.toLocaleString('zh-CN')\n    }\n    \n    const formatDuration = (duration) => {\n      if (!duration) return '-'\n      const hours = Math.floor(duration / 3600)\n      const minutes = Math.floor((duration % 3600) / 60)\n      const seconds = duration % 60\n      \n      if (hours > 0) {\n        return `${hours}h ${minutes}m ${seconds}s`\n      } else if (minutes > 0) {\n        return `${minutes}m ${seconds}s`\n      } else {\n        return `${seconds}s`\n      }\n    }\n    \n    const loadSimulationTask = async () => {\n      try {\n        loading.value = true\n        const response = await simulationApi.getSimulationTask(simulationId.value)\n        \n        if (response.status === 'success') {\n          simulationTask.value = response.simulation_task\n        } else {\n          ElMessage.error('仿真任务不存在')\n        }\n      } catch (error) {\n        console.error('加载仿真任务失败:', error)\n        ElMessage.error('加载仿真任务失败')\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    const loadOptimizationResult = async () => {\n      try {\n        const response = await simulationApi.getOptimizationResult(simulationId.value)\n        \n        if (response.status === 'success') {\n          optimizationResult.value = response.optimization_result\n        }\n      } catch (error) {\n        console.error('加载优化结果失败:', error)\n      }\n    }\n    \n    const setupWebSocketSubscriptions = async () => {\n      try {\n        // 确保WebSocket连接\n        await stompService.connect()\n        \n        // 订阅仿真相关主题\n        simulationSubscriptions = await stompService.subscribeSimulationAll(simulationId.value, {\n          onStatusUpdate: handleWebSocketStatusUpdate,\n          onRealtimeData: handleWebSocketRealtimeData,\n          onFlowData: handleWebSocketFlowData,\n          onTrafficLights: handleWebSocketTrafficLights,\n          onOptimizationResult: handleWebSocketOptimizationResult\n        })\n        \n        console.log('WebSocket订阅设置完成')\n        \n      } catch (error) {\n        console.error('设置WebSocket订阅失败:', error)\n      }\n    }\n    \n    const cleanupWebSocketSubscriptions = () => {\n      if (simulationSubscriptions) {\n        stompService.unsubscribeSimulation(simulationSubscriptions)\n        simulationSubscriptions = null\n      }\n    }\n    \n    // WebSocket事件处理\n    const handleWebSocketStatusUpdate = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 更新仿真状态\n        if (simulationTask.value) {\n          simulationTask.value.status = data.status\n          simulationTask.value.progress = data.progress\n        }\n        \n        // 添加日志\n        addLog('info', data.message || `状态更新: ${data.status}`)\n      }\n    }\n    \n    const handleWebSocketRealtimeData = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 处理实时数据\n        addLog('debug', `实时数据: 时间${data.simulationTime}s, 车辆${data.vehicleCount}辆`)\n      }\n    }\n    \n    const handleWebSocketFlowData = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 处理流量数据\n        addLog('debug', '收到方向流量数据更新')\n      }\n    }\n    \n    const handleWebSocketTrafficLights = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 处理信号灯数据\n        addLog('debug', `信号灯更新: 相位${data.currentPhase}, 剩余${data.phaseRemaining}s`)\n      }\n    }\n    \n    const handleWebSocketOptimizationResult = (data) => {\n      if (data.simulationId === simulationId.value) {\n        // 处理优化结果\n        optimizationResult.value = data.optimizationResult\n        addLog('info', '收到优化结果')\n      }\n    }\n    \n    // 组件事件处理\n    const handleSimulationStarted = (simId) => {\n      addLog('info', '仿真已启动')\n      loadSimulationTask()\n    }\n    \n    const handleSimulationStopped = (simId) => {\n      addLog('info', '仿真已停止')\n      loadSimulationTask()\n    }\n    \n    const handleSimulationCompleted = (simId) => {\n      addLog('info', '仿真已完成')\n      loadSimulationTask()\n      loadOptimizationResult()\n    }\n    \n    const handleStatusUpdated = (status) => {\n      // 状态更新已通过WebSocket处理\n    }\n    \n    const handleMonitorDataUpdated = (data) => {\n      // 监控数据更新\n    }\n    \n    const addLog = (level, message) => {\n      simulationLogs.value.unshift({\n        timestamp: new Date().toISOString(),\n        level,\n        message\n      })\n      \n      // 保持最近100条日志\n      if (simulationLogs.value.length > 100) {\n        simulationLogs.value = simulationLogs.value.slice(0, 100)\n      }\n    }\n    \n    const refreshLogs = () => {\n      // 这里可以从服务器获取日志\n      addLog('info', '日志已刷新')\n    }\n    \n    // 生命周期\n    onMounted(async () => {\n      await loadSimulationTask()\n      await loadOptimizationResult()\n      await setupWebSocketSubscriptions()\n      \n      addLog('info', '页面加载完成')\n    })\n    \n    onUnmounted(() => {\n      cleanupWebSocketSubscriptions()\n    })\n    \n    return {\n      // 响应式数据\n      simulationId,\n      simulationTask,\n      optimizationResult,\n      simulationLogs,\n      loading,\n      \n      // 计算属性\n      isSimulationRunning,\n      hasOptimizationResult,\n      \n      // 方法\n      getStatusTagType,\n      getStatusText,\n      getTypeTagType,\n      getTypeText,\n      getProgressStatus,\n      formatTime,\n      formatDuration,\n      loadOptimizationResult,\n      refreshLogs,\n      \n      // 事件处理\n      handleSimulationStarted,\n      handleSimulationStopped,\n      handleSimulationCompleted,\n      handleStatusUpdated,\n      handleMonitorDataUpdated\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simulation-detail {\n  padding: 20px;\n}\n\n.detail-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.info-card,\n.performance-card,\n.log-card {\n  margin-bottom: 20px;\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3,\n.card-header h4 {\n  margin: 0;\n  color: #303133;\n}\n\n.error-message {\n  margin-top: 15px;\n}\n\n.log-container {\n  max-height: 400px;\n  overflow-y: auto;\n  background: #f8f9fa;\n  border-radius: 4px;\n  padding: 10px;\n}\n\n.log-entry {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n}\n\n.log-time {\n  color: #909399;\n  margin-right: 10px;\n  min-width: 150px;\n}\n\n.log-level {\n  margin-right: 10px;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-weight: bold;\n  min-width: 50px;\n  text-align: center;\n}\n\n.log-info {\n  background: #e1f3d8;\n  color: #67c23a;\n}\n\n.log-debug {\n  background: #e6f7ff;\n  color: #409eff;\n}\n\n.log-warn {\n  background: #fdf6ec;\n  color: #e6a23c;\n}\n\n.log-error {\n  background: #fef0f0;\n  color: #f56c6c;\n}\n\n.log-message {\n  flex: 1;\n  color: #303133;\n}\n\n.no-logs {\n  text-align: center;\n  color: #909399;\n  padding: 20px;\n}\n</style>\n"], "mappings": "AA4KA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC1D,SAASC,QAAO,QAAS,YAAW;AACpC,SAASC,SAAQ,QAAS,cAAa;AACvC,OAAOC,aAAY,MAAO,kBAAiB;AAC3C,OAAOC,YAAW,MAAO,uBAAsB;AAC/C,OAAOC,sBAAqB,MAAO,oDAAmD;AACtF,OAAOC,iBAAgB,MAAO,+CAA8C;AAC5E,OAAOC,sBAAqB,MAAO,oDAAmD;AACtF,OAAOC,sBAAqB,MAAO,oDAAmD;AACtF,OAAOC,oBAAmB,MAAO,kDAAiD;AAElF,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVN,sBAAsB;IACtBC,iBAAiB;IACjBC;EACF,CAAC;EACDK,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIZ,QAAQ,CAAC;IACvB,MAAMa,YAAW,GAAIjB,GAAG,CAACgB,KAAK,CAACE,MAAM,CAACC,EAAE;;IAExC;IACA,MAAMC,cAAa,GAAIpB,GAAG,CAAC,IAAI;IAC/B,MAAMqB,kBAAiB,GAAIrB,GAAG,CAAC,IAAI;IACnC,MAAMsB,cAAa,GAAItB,GAAG,CAAC,EAAE;IAC7B,MAAMuB,OAAM,GAAIvB,GAAG,CAAC,KAAK;;IAEzB;IACA,IAAIwB,uBAAsB,GAAI,IAAG;;IAEjC;IACA,MAAMC,mBAAkB,GAAIxB,QAAQ,CAAC,MACnCmB,cAAc,CAACM,KAAK,EAAEC,MAAK,KAAM,SACnC;IAEA,MAAMC,qBAAoB,GAAI3B,QAAQ,CAAC,MACrCoB,kBAAkB,CAACK,KAAI,IAAKG,MAAM,CAACC,IAAI,CAACT,kBAAkB,CAACK,KAAK,CAAC,CAACK,MAAK,GAAI,CAC7E;;IAEA;IACA,MAAMC,gBAAe,GAAKL,MAAM,IAAK;MACnC,MAAMM,SAAQ,GAAI;QAChB,SAAS,EAAE,MAAM;QACjB,SAAS,EAAE,SAAS;QACpB,WAAW,EAAE,SAAS;QACtB,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE;MACb;MACA,OAAOA,SAAS,CAACN,MAAM,KAAK,MAAK;IACnC;IAEA,MAAMO,aAAY,GAAKP,MAAM,IAAK;MAChC,MAAMM,SAAQ,GAAI;QAChB,SAAS,EAAE,KAAK;QAChB,SAAS,EAAE,KAAK;QAChB,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE;MACb;MACA,OAAOA,SAAS,CAACN,MAAM,KAAK,IAAG;IACjC;IAEA,MAAMQ,cAAa,GAAKC,IAAI,IAAK;MAC/B,MAAMC,OAAM,GAAI;QACd,eAAe,EAAE,SAAS;QAC1B,cAAc,EAAE,SAAS;QACzB,eAAe,EAAE;MACnB;MACA,OAAOA,OAAO,CAACD,IAAI,KAAK,MAAK;IAC/B;IAEA,MAAME,WAAU,GAAKF,IAAI,IAAK;MAC5B,MAAMC,OAAM,GAAI;QACd,eAAe,EAAE,QAAQ;QACzB,cAAc,EAAE,QAAQ;QACxB,eAAe,EAAE;MACnB;MACA,OAAOA,OAAO,CAACD,IAAI,KAAK,IAAG;IAC7B;IAEA,MAAMG,iBAAgB,GAAKZ,MAAM,IAAK;MACpC,IAAIA,MAAK,KAAM,QAAQ,EAAE,OAAO,WAAU;MAC1C,IAAIA,MAAK,KAAM,WAAW,EAAE,OAAO,SAAQ;MAC3C,OAAO,IAAG;IACZ;IAEA,MAAMa,UAAS,GAAKC,OAAO,IAAK;MAC9B,IAAI,CAACA,OAAO,EAAE,OAAO,GAAE;MACvB,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,OAAO;MAC7B,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO;IACpC;IAEA,MAAMC,cAAa,GAAKC,QAAQ,IAAK;MACnC,IAAI,CAACA,QAAQ,EAAE,OAAO,GAAE;MACxB,MAAMC,KAAI,GAAIC,IAAI,CAACC,KAAK,CAACH,QAAO,GAAI,IAAI;MACxC,MAAMI,OAAM,GAAIF,IAAI,CAACC,KAAK,CAAEH,QAAO,GAAI,IAAI,GAAI,EAAE;MACjD,MAAMK,OAAM,GAAIL,QAAO,GAAI,EAAC;MAE5B,IAAIC,KAAI,GAAI,CAAC,EAAE;QACb,OAAO,GAAGA,KAAK,KAAKG,OAAO,KAAKC,OAAO,GAAE;MAC3C,OAAO,IAAID,OAAM,GAAI,CAAC,EAAE;QACtB,OAAO,GAAGA,OAAO,KAAKC,OAAO,GAAE;MACjC,OAAO;QACL,OAAO,GAAGA,OAAO,GAAE;MACrB;IACF;IAEA,MAAMC,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF7B,OAAO,CAACG,KAAI,GAAI,IAAG;QACnB,MAAM2B,QAAO,GAAI,MAAM/C,aAAa,CAACgD,iBAAiB,CAACrC,YAAY,CAACS,KAAK;QAEzE,IAAI2B,QAAQ,CAAC1B,MAAK,KAAM,SAAS,EAAE;UACjCP,cAAc,CAACM,KAAI,GAAI2B,QAAQ,CAACE,eAAc;QAChD,OAAO;UACLlD,SAAS,CAACmD,KAAK,CAAC,SAAS;QAC3B;MACF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCnD,SAAS,CAACmD,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRjC,OAAO,CAACG,KAAI,GAAI,KAAI;MACtB;IACF;IAEA,MAAMgC,sBAAqB,GAAI,MAAAA,CAAA,KAAY;MACzC,IAAI;QACF,MAAML,QAAO,GAAI,MAAM/C,aAAa,CAACqD,qBAAqB,CAAC1C,YAAY,CAACS,KAAK;QAE7E,IAAI2B,QAAQ,CAAC1B,MAAK,KAAM,SAAS,EAAE;UACjCN,kBAAkB,CAACK,KAAI,GAAI2B,QAAQ,CAACO,mBAAkB;QACxD;MACF,EAAE,OAAOJ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF;IAEA,MAAMK,2BAA0B,GAAI,MAAAA,CAAA,KAAY;MAC9C,IAAI;QACF;QACA,MAAMtD,YAAY,CAACuD,OAAO,CAAC;;QAE3B;QACAtC,uBAAsB,GAAI,MAAMjB,YAAY,CAACwD,sBAAsB,CAAC9C,YAAY,CAACS,KAAK,EAAE;UACtFsC,cAAc,EAAEC,2BAA2B;UAC3CC,cAAc,EAAEC,2BAA2B;UAC3CC,UAAU,EAAEC,uBAAuB;UACnCC,eAAe,EAAEC,4BAA4B;UAC7CC,oBAAoB,EAAEC;QACxB,CAAC;QAEDhB,OAAO,CAACiB,GAAG,CAAC,iBAAiB;MAE/B,EAAE,OAAOlB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK;MACzC;IACF;IAEA,MAAMmB,6BAA4B,GAAIA,CAAA,KAAM;MAC1C,IAAInD,uBAAuB,EAAE;QAC3BjB,YAAY,CAACqE,qBAAqB,CAACpD,uBAAuB;QAC1DA,uBAAsB,GAAI,IAAG;MAC/B;IACF;;IAEA;IACA,MAAMyC,2BAA0B,GAAKY,IAAI,IAAK;MAC5C,IAAIA,IAAI,CAAC5D,YAAW,KAAMA,YAAY,CAACS,KAAK,EAAE;QAC5C;QACA,IAAIN,cAAc,CAACM,KAAK,EAAE;UACxBN,cAAc,CAACM,KAAK,CAACC,MAAK,GAAIkD,IAAI,CAAClD,MAAK;UACxCP,cAAc,CAACM,KAAK,CAACoD,QAAO,GAAID,IAAI,CAACC,QAAO;QAC9C;;QAEA;QACAC,MAAM,CAAC,MAAM,EAAEF,IAAI,CAACG,OAAM,IAAK,SAASH,IAAI,CAAClD,MAAM,EAAE;MACvD;IACF;IAEA,MAAMwC,2BAA0B,GAAKU,IAAI,IAAK;MAC5C,IAAIA,IAAI,CAAC5D,YAAW,KAAMA,YAAY,CAACS,KAAK,EAAE;QAC5C;QACAqD,MAAM,CAAC,OAAO,EAAE,WAAWF,IAAI,CAACI,cAAc,QAAQJ,IAAI,CAACK,YAAY,GAAG;MAC5E;IACF;IAEA,MAAMb,uBAAsB,GAAKQ,IAAI,IAAK;MACxC,IAAIA,IAAI,CAAC5D,YAAW,KAAMA,YAAY,CAACS,KAAK,EAAE;QAC5C;QACAqD,MAAM,CAAC,OAAO,EAAE,YAAY;MAC9B;IACF;IAEA,MAAMR,4BAA2B,GAAKM,IAAI,IAAK;MAC7C,IAAIA,IAAI,CAAC5D,YAAW,KAAMA,YAAY,CAACS,KAAK,EAAE;QAC5C;QACAqD,MAAM,CAAC,OAAO,EAAE,YAAYF,IAAI,CAACM,YAAY,OAAON,IAAI,CAACO,cAAc,GAAG;MAC5E;IACF;IAEA,MAAMX,iCAAgC,GAAKI,IAAI,IAAK;MAClD,IAAIA,IAAI,CAAC5D,YAAW,KAAMA,YAAY,CAACS,KAAK,EAAE;QAC5C;QACAL,kBAAkB,CAACK,KAAI,GAAImD,IAAI,CAACxD,kBAAiB;QACjD0D,MAAM,CAAC,MAAM,EAAE,QAAQ;MACzB;IACF;;IAEA;IACA,MAAMM,uBAAsB,GAAKC,KAAK,IAAK;MACzCP,MAAM,CAAC,MAAM,EAAE,OAAO;MACtB3B,kBAAkB,CAAC;IACrB;IAEA,MAAMmC,uBAAsB,GAAKD,KAAK,IAAK;MACzCP,MAAM,CAAC,MAAM,EAAE,OAAO;MACtB3B,kBAAkB,CAAC;IACrB;IAEA,MAAMoC,yBAAwB,GAAKF,KAAK,IAAK;MAC3CP,MAAM,CAAC,MAAM,EAAE,OAAO;MACtB3B,kBAAkB,CAAC;MACnBM,sBAAsB,CAAC;IACzB;IAEA,MAAM+B,mBAAkB,GAAK9D,MAAM,IAAK;MACtC;IAAA,CACF;IAEA,MAAM+D,wBAAuB,GAAKb,IAAI,IAAK;MACzC;IAAA,CACF;IAEA,MAAME,MAAK,GAAIA,CAACY,KAAK,EAAEX,OAAO,KAAK;MACjC1D,cAAc,CAACI,KAAK,CAACkE,OAAO,CAAC;QAC3BC,SAAS,EAAE,IAAIlD,IAAI,CAAC,CAAC,CAACmD,WAAW,CAAC,CAAC;QACnCH,KAAK;QACLX;MACF,CAAC;;MAED;MACA,IAAI1D,cAAc,CAACI,KAAK,CAACK,MAAK,GAAI,GAAG,EAAE;QACrCT,cAAc,CAACI,KAAI,GAAIJ,cAAc,CAACI,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAE,GAAG;MAC1D;IACF;IAEA,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxB;MACAjB,MAAM,CAAC,MAAM,EAAE,OAAO;IACxB;;IAEA;IACA7E,SAAS,CAAC,YAAY;MACpB,MAAMkD,kBAAkB,CAAC;MACzB,MAAMM,sBAAsB,CAAC;MAC7B,MAAMG,2BAA2B,CAAC;MAElCkB,MAAM,CAAC,MAAM,EAAE,QAAQ;IACzB,CAAC;IAED5E,WAAW,CAAC,MAAM;MAChBwE,6BAA6B,CAAC;IAChC,CAAC;IAED,OAAO;MACL;MACA1D,YAAY;MACZG,cAAc;MACdC,kBAAkB;MAClBC,cAAc;MACdC,OAAO;MAEP;MACAE,mBAAmB;MACnBG,qBAAqB;MAErB;MACAI,gBAAgB;MAChBE,aAAa;MACbC,cAAc;MACdG,WAAW;MACXC,iBAAiB;MACjBC,UAAU;MACVK,cAAc;MACda,sBAAsB;MACtBsC,WAAW;MAEX;MACAX,uBAAuB;MACvBE,uBAAuB;MACvBC,yBAAyB;MACzBC,mBAAmB;MACnBC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}